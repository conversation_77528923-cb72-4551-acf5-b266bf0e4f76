15290df9a24bf00e7f26e0ad82a2ba16
"use strict";

/* istanbul ignore next */
function cov_8o8wd8h3p() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/auth.tsx";
  var hash = "f7407971b3d95517fe584a5821440840f8450aac";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/auth.tsx",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 40,
          column: 1
        }
      },
      "68": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 62
        }
      },
      "69": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 62
        }
      },
      "70": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 29
        }
      },
      "71": {
        start: {
          line: 43,
          column: 20
        },
        end: {
          line: 43,
          column: 48
        }
      },
      "72": {
        start: {
          line: 44,
          column: 20
        },
        end: {
          line: 44,
          column: 79
        }
      },
      "73": {
        start: {
          line: 45,
          column: 14
        },
        end: {
          line: 45,
          column: 67
        }
      },
      "74": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 54
        }
      },
      "75": {
        start: {
          line: 47,
          column: 17
        },
        end: {
          line: 47,
          column: 53
        }
      },
      "76": {
        start: {
          line: 48,
          column: 15
        },
        end: {
          line: 48,
          column: 49
        }
      },
      "77": {
        start: {
          line: 49,
          column: 15
        },
        end: {
          line: 49,
          column: 55
        }
      },
      "78": {
        start: {
          line: 50,
          column: 14
        },
        end: {
          line: 50,
          column: 36
        }
      },
      "79": {
        start: {
          line: 51,
          column: 26
        },
        end: {
          line: 51,
          column: 63
        }
      },
      "80": {
        start: {
          line: 53,
          column: 22
        },
        end: {
          line: 53,
          column: 39
        }
      },
      "81": {
        start: {
          line: 54,
          column: 25
        },
        end: {
          line: 54,
          column: 37
        }
      },
      "82": {
        start: {
          line: 55,
          column: 18
        },
        end: {
          line: 55,
          column: 35
        }
      },
      "83": {
        start: {
          line: 56,
          column: 21
        },
        end: {
          line: 56,
          column: 38
        }
      },
      "84": {
        start: {
          line: 57,
          column: 0
        },
        end: {
          line: 302,
          column: 2
        }
      },
      "85": {
        start: {
          line: 67,
          column: 16
        },
        end: {
          line: 139,
          column: 19
        }
      },
      "86": {
        start: {
          line: 69,
          column: 20
        },
        end: {
          line: 138,
          column: 23
        }
      },
      "87": {
        start: {
          line: 70,
          column: 24
        },
        end: {
          line: 137,
          column: 25
        }
      },
      "88": {
        start: {
          line: 72,
          column: 32
        },
        end: {
          line: 74,
          column: 33
        }
      },
      "89": {
        start: {
          line: 73,
          column: 36
        },
        end: {
          line: 73,
          column: 64
        }
      },
      "90": {
        start: {
          line: 75,
          column: 32
        },
        end: {
          line: 77,
          column: 40
        }
      },
      "91": {
        start: {
          line: 79,
          column: 32
        },
        end: {
          line: 79,
          column: 49
        }
      },
      "92": {
        start: {
          line: 80,
          column: 32
        },
        end: {
          line: 82,
          column: 33
        }
      },
      "93": {
        start: {
          line: 81,
          column: 36
        },
        end: {
          line: 81,
          column: 64
        }
      },
      "94": {
        start: {
          line: 84,
          column: 32
        },
        end: {
          line: 86,
          column: 33
        }
      },
      "95": {
        start: {
          line: 85,
          column: 36
        },
        end: {
          line: 85,
          column: 148
        }
      },
      "96": {
        start: {
          line: 87,
          column: 32
        },
        end: {
          line: 87,
          column: 118
        }
      },
      "97": {
        start: {
          line: 89,
          column: 32
        },
        end: {
          line: 89,
          column: 60
        }
      },
      "98": {
        start: {
          line: 90,
          column: 32
        },
        end: {
          line: 90,
          column: 79
        }
      },
      "99": {
        start: {
          line: 90,
          column: 55
        },
        end: {
          line: 90,
          column: 79
        }
      },
      "100": {
        start: {
          line: 91,
          column: 32
        },
        end: {
          line: 91,
          column: 78
        }
      },
      "101": {
        start: {
          line: 92,
          column: 32
        },
        end: {
          line: 92,
          column: 48
        }
      },
      "102": {
        start: {
          line: 93,
          column: 32
        },
        end: {
          line: 93,
          column: 65
        }
      },
      "103": {
        start: {
          line: 94,
          column: 32
        },
        end: {
          line: 94,
          column: 94
        }
      },
      "104": {
        start: {
          line: 94,
          column: 70
        },
        end: {
          line: 94,
          column: 94
        }
      },
      "105": {
        start: {
          line: 96,
          column: 32
        },
        end: {
          line: 102,
          column: 40
        }
      },
      "106": {
        start: {
          line: 105,
          column: 32
        },
        end: {
          line: 105,
          column: 42
        }
      },
      "107": {
        start: {
          line: 106,
          column: 32
        },
        end: {
          line: 106,
          column: 137
        }
      },
      "108": {
        start: {
          line: 109,
          column: 28
        },
        end: {
          line: 114,
          column: 36
        }
      },
      "109": {
        start: {
          line: 117,
          column: 32
        },
        end: {
          line: 117,
          column: 42
        }
      },
      "110": {
        start: {
          line: 118,
          column: 32
        },
        end: {
          line: 118,
          column: 45
        }
      },
      "111": {
        start: {
          line: 119,
          column: 36
        },
        end: {
          line: 119,
          column: 64
        }
      },
      "112": {
        start: {
          line: 122,
          column: 32
        },
        end: {
          line: 124,
          column: 33
        }
      },
      "113": {
        start: {
          line: 123,
          column: 36
        },
        end: {
          line: 123,
          column: 149
        }
      },
      "114": {
        start: {
          line: 125,
          column: 32
        },
        end: {
          line: 125,
          column: 113
        }
      },
      "115": {
        start: {
          line: 125,
          column: 89
        },
        end: {
          line: 125,
          column: 113
        }
      },
      "116": {
        start: {
          line: 126,
          column: 32
        },
        end: {
          line: 132,
          column: 40
        }
      },
      "117": {
        start: {
          line: 134,
          column: 32
        },
        end: {
          line: 134,
          column: 42
        }
      },
      "118": {
        start: {
          line: 135,
          column: 32
        },
        end: {
          line: 135,
          column: 45
        }
      },
      "119": {
        start: {
          line: 136,
          column: 36
        },
        end: {
          line: 136,
          column: 111
        }
      },
      "120": {
        start: {
          line: 152,
          column: 53
        },
        end: {
          line: 185,
          column: 15
        }
      },
      "121": {
        start: {
          line: 154,
          column: 28
        },
        end: {
          line: 154,
          column: 41
        }
      },
      "122": {
        start: {
          line: 154,
          column: 49
        },
        end: {
          line: 154,
          column: 55
        }
      },
      "123": {
        start: {
          line: 155,
          column: 16
        },
        end: {
          line: 184,
          column: 19
        }
      },
      "124": {
        start: {
          line: 156,
          column: 20
        },
        end: {
          line: 183,
          column: 21
        }
      },
      "125": {
        start: {
          line: 158,
          column: 28
        },
        end: {
          line: 158,
          column: 54
        }
      },
      "126": {
        start: {
          line: 159,
          column: 28
        },
        end: {
          line: 161,
          column: 36
        }
      },
      "127": {
        start: {
          line: 163,
          column: 28
        },
        end: {
          line: 163,
          column: 45
        }
      },
      "128": {
        start: {
          line: 164,
          column: 28
        },
        end: {
          line: 164,
          column: 63
        }
      },
      "129": {
        start: {
          line: 164,
          column: 39
        },
        end: {
          line: 164,
          column: 63
        }
      },
      "130": {
        start: {
          line: 165,
          column: 28
        },
        end: {
          line: 169,
          column: 36
        }
      },
      "131": {
        start: {
          line: 171,
          column: 28
        },
        end: {
          line: 171,
          column: 38
        }
      },
      "132": {
        start: {
          line: 172,
          column: 28
        },
        end: {
          line: 172,
          column: 85
        }
      },
      "133": {
        start: {
          line: 173,
          column: 28
        },
        end: {
          line: 173,
          column: 52
        }
      },
      "134": {
        start: {
          line: 175,
          column: 28
        },
        end: {
          line: 175,
          column: 133
        }
      },
      "135": {
        start: {
          line: 176,
          column: 28
        },
        end: {
          line: 176,
          column: 41
        }
      },
      "136": {
        start: {
          line: 177,
          column: 32
        },
        end: {
          line: 177,
          column: 56
        }
      },
      "137": {
        start: {
          line: 179,
          column: 28
        },
        end: {
          line: 179,
          column: 48
        }
      },
      "138": {
        start: {
          line: 180,
          column: 28
        },
        end: {
          line: 180,
          column: 111
        }
      },
      "139": {
        start: {
          line: 181,
          column: 28
        },
        end: {
          line: 181,
          column: 52
        }
      },
      "140": {
        start: {
          line: 182,
          column: 32
        },
        end: {
          line: 182,
          column: 54
        }
      },
      "141": {
        start: {
          line: 231,
          column: 12
        },
        end: {
          line: 266,
          column: 15
        }
      },
      "142": {
        start: {
          line: 233,
          column: 28
        },
        end: {
          line: 233,
          column: 36
        }
      },
      "143": {
        start: {
          line: 233,
          column: 45
        },
        end: {
          line: 233,
          column: 52
        }
      },
      "144": {
        start: {
          line: 233,
          column: 64
        },
        end: {
          line: 233,
          column: 74
        }
      },
      "145": {
        start: {
          line: 234,
          column: 16
        },
        end: {
          line: 265,
          column: 19
        }
      },
      "146": {
        start: {
          line: 235,
          column: 20
        },
        end: {
          line: 235,
          column: 56
        }
      },
      "147": {
        start: {
          line: 237,
          column: 20
        },
        end: {
          line: 241,
          column: 21
        }
      },
      "148": {
        start: {
          line: 238,
          column: 24
        },
        end: {
          line: 238,
          column: 72
        }
      },
      "149": {
        start: {
          line: 239,
          column: 24
        },
        end: {
          line: 239,
          column: 40
        }
      },
      "150": {
        start: {
          line: 240,
          column: 24
        },
        end: {
          line: 240,
          column: 49
        }
      },
      "151": {
        start: {
          line: 243,
          column: 20
        },
        end: {
          line: 245,
          column: 21
        }
      },
      "152": {
        start: {
          line: 244,
          column: 24
        },
        end: {
          line: 244,
          column: 59
        }
      },
      "153": {
        start: {
          line: 246,
          column: 20
        },
        end: {
          line: 246,
          column: 67
        }
      },
      "154": {
        start: {
          line: 247,
          column: 20
        },
        end: {
          line: 254,
          column: 21
        }
      },
      "155": {
        start: {
          line: 248,
          column: 24
        },
        end: {
          line: 248,
          column: 72
        }
      },
      "156": {
        start: {
          line: 249,
          column: 24
        },
        end: {
          line: 249,
          column: 49
        }
      },
      "157": {
        start: {
          line: 251,
          column: 25
        },
        end: {
          line: 254,
          column: 21
        }
      },
      "158": {
        start: {
          line: 253,
          column: 24
        },
        end: {
          line: 253,
          column: 49
        }
      },
      "159": {
        start: {
          line: 255,
          column: 20
        },
        end: {
          line: 259,
          column: 21
        }
      },
      "160": {
        start: {
          line: 256,
          column: 24
        },
        end: {
          line: 256,
          column: 43
        }
      },
      "161": {
        start: {
          line: 257,
          column: 24
        },
        end: {
          line: 257,
          column: 49
        }
      },
      "162": {
        start: {
          line: 258,
          column: 24
        },
        end: {
          line: 258,
          column: 47
        }
      },
      "163": {
        start: {
          line: 261,
          column: 20
        },
        end: {
          line: 263,
          column: 21
        }
      },
      "164": {
        start: {
          line: 262,
          column: 24
        },
        end: {
          line: 262,
          column: 49
        }
      },
      "165": {
        start: {
          line: 264,
          column: 20
        },
        end: {
          line: 264,
          column: 49
        }
      },
      "166": {
        start: {
          line: 269,
          column: 12
        },
        end: {
          line: 286,
          column: 15
        }
      },
      "167": {
        start: {
          line: 270,
          column: 30
        },
        end: {
          line: 270,
          column: 40
        }
      },
      "168": {
        start: {
          line: 270,
          column: 50
        },
        end: {
          line: 270,
          column: 58
        }
      },
      "169": {
        start: {
          line: 271,
          column: 16
        },
        end: {
          line: 285,
          column: 19
        }
      },
      "170": {
        start: {
          line: 272,
          column: 20
        },
        end: {
          line: 280,
          column: 21
        }
      },
      "171": {
        start: {
          line: 273,
          column: 24
        },
        end: {
          line: 273,
          column: 51
        }
      },
      "172": {
        start: {
          line: 274,
          column: 24
        },
        end: {
          line: 276,
          column: 25
        }
      },
      "173": {
        start: {
          line: 275,
          column: 28
        },
        end: {
          line: 275,
          column: 61
        }
      },
      "174": {
        start: {
          line: 277,
          column: 24
        },
        end: {
          line: 279,
          column: 25
        }
      },
      "175": {
        start: {
          line: 278,
          column: 28
        },
        end: {
          line: 278,
          column: 59
        }
      },
      "176": {
        start: {
          line: 282,
          column: 20
        },
        end: {
          line: 282,
          column: 56
        }
      },
      "177": {
        start: {
          line: 283,
          column: 20
        },
        end: {
          line: 283,
          column: 62
        }
      },
      "178": {
        start: {
          line: 284,
          column: 20
        },
        end: {
          line: 284,
          column: 51
        }
      },
      "179": {
        start: {
          line: 291,
          column: 12
        },
        end: {
          line: 296,
          column: 15
        }
      },
      "180": {
        start: {
          line: 292,
          column: 16
        },
        end: {
          line: 295,
          column: 19
        }
      },
      "181": {
        start: {
          line: 293,
          column: 20
        },
        end: {
          line: 293,
          column: 69
        }
      },
      "182": {
        start: {
          line: 294,
          column: 20
        },
        end: {
          line: 294,
          column: 42
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 38,
            column: 57
          }
        },
        loc: {
          start: {
            line: 38,
            column: 71
          },
          end: {
            line: 40,
            column: 1
          }
        },
        line: 38
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 66,
            column: 23
          },
          end: {
            line: 66,
            column: 24
          }
        },
        loc: {
          start: {
            line: 66,
            column: 46
          },
          end: {
            line: 140,
            column: 13
          }
        },
        line: 66
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 67,
            column: 56
          },
          end: {
            line: 67,
            column: 57
          }
        },
        loc: {
          start: {
            line: 67,
            column: 68
          },
          end: {
            line: 139,
            column: 17
          }
        },
        line: 67
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 69,
            column: 45
          },
          end: {
            line: 69,
            column: 46
          }
        },
        loc: {
          start: {
            line: 69,
            column: 59
          },
          end: {
            line: 138,
            column: 21
          }
        },
        line: 69
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 152,
            column: 37
          },
          end: {
            line: 152,
            column: 38
          }
        },
        loc: {
          start: {
            line: 152,
            column: 51
          },
          end: {
            line: 185,
            column: 17
          }
        },
        line: 152
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 152,
            column: 92
          },
          end: {
            line: 152,
            column: 93
          }
        },
        loc: {
          start: {
            line: 152,
            column: 106
          },
          end: {
            line: 185,
            column: 13
          }
        },
        line: 152
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 155,
            column: 41
          },
          end: {
            line: 155,
            column: 42
          }
        },
        loc: {
          start: {
            line: 155,
            column: 55
          },
          end: {
            line: 184,
            column: 17
          }
        },
        line: 155
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 230,
            column: 13
          },
          end: {
            line: 230,
            column: 14
          }
        },
        loc: {
          start: {
            line: 230,
            column: 27
          },
          end: {
            line: 267,
            column: 9
          }
        },
        line: 230
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 231,
            column: 55
          },
          end: {
            line: 231,
            column: 56
          }
        },
        loc: {
          start: {
            line: 231,
            column: 69
          },
          end: {
            line: 266,
            column: 13
          }
        },
        line: 231
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 234,
            column: 41
          },
          end: {
            line: 234,
            column: 42
          }
        },
        loc: {
          start: {
            line: 234,
            column: 55
          },
          end: {
            line: 265,
            column: 17
          }
        },
        line: 234
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 268,
            column: 17
          },
          end: {
            line: 268,
            column: 18
          }
        },
        loc: {
          start: {
            line: 268,
            column: 31
          },
          end: {
            line: 287,
            column: 9
          }
        },
        line: 268
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 269,
            column: 55
          },
          end: {
            line: 269,
            column: 56
          }
        },
        loc: {
          start: {
            line: 269,
            column: 69
          },
          end: {
            line: 286,
            column: 13
          }
        },
        line: 269
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 271,
            column: 41
          },
          end: {
            line: 271,
            column: 42
          }
        },
        loc: {
          start: {
            line: 271,
            column: 55
          },
          end: {
            line: 285,
            column: 17
          }
        },
        line: 271
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 290,
            column: 20
          },
          end: {
            line: 290,
            column: 21
          }
        },
        loc: {
          start: {
            line: 290,
            column: 39
          },
          end: {
            line: 297,
            column: 9
          }
        },
        line: 290
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 291,
            column: 51
          },
          end: {
            line: 291,
            column: 52
          }
        },
        loc: {
          start: {
            line: 291,
            column: 63
          },
          end: {
            line: 296,
            column: 13
          }
        },
        line: 291
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 292,
            column: 41
          },
          end: {
            line: 292,
            column: 42
          }
        },
        loc: {
          start: {
            line: 292,
            column: 55
          },
          end: {
            line: 295,
            column: 17
          }
        },
        line: 292
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 38,
            column: 22
          },
          end: {
            line: 40,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 23
          },
          end: {
            line: 38,
            column: 27
          }
        }, {
          start: {
            line: 38,
            column: 31
          },
          end: {
            line: 38,
            column: 51
          }
        }, {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 40,
            column: 1
          }
        }],
        line: 38
      },
      "33": {
        loc: {
          start: {
            line: 39,
            column: 11
          },
          end: {
            line: 39,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 37
          },
          end: {
            line: 39,
            column: 40
          }
        }, {
          start: {
            line: 39,
            column: 43
          },
          end: {
            line: 39,
            column: 61
          }
        }],
        line: 39
      },
      "34": {
        loc: {
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 15
          }
        }, {
          start: {
            line: 39,
            column: 19
          },
          end: {
            line: 39,
            column: 33
          }
        }],
        line: 39
      },
      "35": {
        loc: {
          start: {
            line: 70,
            column: 24
          },
          end: {
            line: 137,
            column: 25
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 71,
            column: 28
          },
          end: {
            line: 77,
            column: 40
          }
        }, {
          start: {
            line: 78,
            column: 28
          },
          end: {
            line: 87,
            column: 118
          }
        }, {
          start: {
            line: 88,
            column: 28
          },
          end: {
            line: 102,
            column: 40
          }
        }, {
          start: {
            line: 103,
            column: 28
          },
          end: {
            line: 106,
            column: 137
          }
        }, {
          start: {
            line: 107,
            column: 28
          },
          end: {
            line: 114,
            column: 36
          }
        }, {
          start: {
            line: 115,
            column: 28
          },
          end: {
            line: 118,
            column: 45
          }
        }, {
          start: {
            line: 119,
            column: 28
          },
          end: {
            line: 119,
            column: 64
          }
        }, {
          start: {
            line: 120,
            column: 28
          },
          end: {
            line: 132,
            column: 40
          }
        }, {
          start: {
            line: 133,
            column: 28
          },
          end: {
            line: 135,
            column: 45
          }
        }, {
          start: {
            line: 136,
            column: 28
          },
          end: {
            line: 136,
            column: 111
          }
        }],
        line: 70
      },
      "36": {
        loc: {
          start: {
            line: 72,
            column: 32
          },
          end: {
            line: 74,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 32
          },
          end: {
            line: 74,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "37": {
        loc: {
          start: {
            line: 72,
            column: 36
          },
          end: {
            line: 72,
            column: 199
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 72,
            column: 36
          },
          end: {
            line: 72,
            column: 114
          }
        }, {
          start: {
            line: 72,
            column: 118
          },
          end: {
            line: 72,
            column: 199
          }
        }],
        line: 72
      },
      "38": {
        loc: {
          start: {
            line: 72,
            column: 38
          },
          end: {
            line: 72,
            column: 113
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 72,
            column: 87
          },
          end: {
            line: 72,
            column: 93
          }
        }, {
          start: {
            line: 72,
            column: 96
          },
          end: {
            line: 72,
            column: 113
          }
        }],
        line: 72
      },
      "39": {
        loc: {
          start: {
            line: 72,
            column: 38
          },
          end: {
            line: 72,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 72,
            column: 38
          },
          end: {
            line: 72,
            column: 58
          }
        }, {
          start: {
            line: 72,
            column: 62
          },
          end: {
            line: 72,
            column: 84
          }
        }],
        line: 72
      },
      "40": {
        loc: {
          start: {
            line: 72,
            column: 120
          },
          end: {
            line: 72,
            column: 198
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 72,
            column: 169
          },
          end: {
            line: 72,
            column: 175
          }
        }, {
          start: {
            line: 72,
            column: 178
          },
          end: {
            line: 72,
            column: 198
          }
        }],
        line: 72
      },
      "41": {
        loc: {
          start: {
            line: 72,
            column: 120
          },
          end: {
            line: 72,
            column: 166
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 72,
            column: 120
          },
          end: {
            line: 72,
            column: 140
          }
        }, {
          start: {
            line: 72,
            column: 144
          },
          end: {
            line: 72,
            column: 166
          }
        }],
        line: 72
      },
      "42": {
        loc: {
          start: {
            line: 80,
            column: 32
          },
          end: {
            line: 82,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 80,
            column: 32
          },
          end: {
            line: 82,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 80
      },
      "43": {
        loc: {
          start: {
            line: 84,
            column: 32
          },
          end: {
            line: 86,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 84,
            column: 32
          },
          end: {
            line: 86,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 84
      },
      "44": {
        loc: {
          start: {
            line: 84,
            column: 36
          },
          end: {
            line: 84,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 84,
            column: 36
          },
          end: {
            line: 84,
            column: 52
          }
        }, {
          start: {
            line: 84,
            column: 56
          },
          end: {
            line: 84,
            column: 85
          }
        }],
        line: 84
      },
      "45": {
        loc: {
          start: {
            line: 90,
            column: 32
          },
          end: {
            line: 90,
            column: 79
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 90,
            column: 32
          },
          end: {
            line: 90,
            column: 79
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 90
      },
      "46": {
        loc: {
          start: {
            line: 94,
            column: 32
          },
          end: {
            line: 94,
            column: 94
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 94,
            column: 32
          },
          end: {
            line: 94,
            column: 94
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 94
      },
      "47": {
        loc: {
          start: {
            line: 122,
            column: 32
          },
          end: {
            line: 124,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 122,
            column: 32
          },
          end: {
            line: 124,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 122
      },
      "48": {
        loc: {
          start: {
            line: 122,
            column: 36
          },
          end: {
            line: 122,
            column: 96
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 122,
            column: 36
          },
          end: {
            line: 122,
            column: 55
          }
        }, {
          start: {
            line: 122,
            column: 59
          },
          end: {
            line: 122,
            column: 96
          }
        }],
        line: 122
      },
      "49": {
        loc: {
          start: {
            line: 125,
            column: 32
          },
          end: {
            line: 125,
            column: 113
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 125,
            column: 32
          },
          end: {
            line: 125,
            column: 113
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 125
      },
      "50": {
        loc: {
          start: {
            line: 125,
            column: 38
          },
          end: {
            line: 125,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 125,
            column: 38
          },
          end: {
            line: 125,
            column: 66
          }
        }, {
          start: {
            line: 125,
            column: 70
          },
          end: {
            line: 125,
            column: 86
          }
        }],
        line: 125
      },
      "51": {
        loc: {
          start: {
            line: 145,
            column: 31
          },
          end: {
            line: 145,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 145,
            column: 31
          },
          end: {
            line: 145,
            column: 60
          }
        }, {
          start: {
            line: 145,
            column: 64
          },
          end: {
            line: 145,
            column: 69
          }
        }],
        line: 145
      },
      "52": {
        loc: {
          start: {
            line: 151,
            column: 18
          },
          end: {
            line: 151,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 151,
            column: 18
          },
          end: {
            line: 151,
            column: 40
          }
        }, {
          start: {
            line: 151,
            column: 44
          },
          end: {
            line: 151,
            column: 67
          }
        }],
        line: 151
      },
      "53": {
        loc: {
          start: {
            line: 156,
            column: 20
          },
          end: {
            line: 183,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 157,
            column: 24
          },
          end: {
            line: 161,
            column: 36
          }
        }, {
          start: {
            line: 162,
            column: 24
          },
          end: {
            line: 169,
            column: 36
          }
        }, {
          start: {
            line: 170,
            column: 24
          },
          end: {
            line: 173,
            column: 52
          }
        }, {
          start: {
            line: 174,
            column: 24
          },
          end: {
            line: 176,
            column: 41
          }
        }, {
          start: {
            line: 177,
            column: 24
          },
          end: {
            line: 177,
            column: 56
          }
        }, {
          start: {
            line: 178,
            column: 24
          },
          end: {
            line: 181,
            column: 52
          }
        }, {
          start: {
            line: 182,
            column: 24
          },
          end: {
            line: 182,
            column: 54
          }
        }],
        line: 156
      },
      "54": {
        loc: {
          start: {
            line: 164,
            column: 28
          },
          end: {
            line: 164,
            column: 63
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 164,
            column: 28
          },
          end: {
            line: 164,
            column: 63
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 164
      },
      "55": {
        loc: {
          start: {
            line: 168,
            column: 120
          },
          end: {
            line: 168,
            column: 138
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 168,
            column: 120
          },
          end: {
            line: 168,
            column: 129
          }
        }, {
          start: {
            line: 168,
            column: 133
          },
          end: {
            line: 168,
            column: 138
          }
        }],
        line: 168
      },
      "56": {
        loc: {
          start: {
            line: 205,
            column: 24
          },
          end: {
            line: 205,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 205,
            column: 65
          },
          end: {
            line: 205,
            column: 76
          }
        }, {
          start: {
            line: 205,
            column: 79
          },
          end: {
            line: 205,
            column: 88
          }
        }],
        line: 205
      },
      "57": {
        loc: {
          start: {
            line: 215,
            column: 24
          },
          end: {
            line: 215,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 215,
            column: 65
          },
          end: {
            line: 215,
            column: 76
          }
        }, {
          start: {
            line: 215,
            column: 79
          },
          end: {
            line: 215,
            column: 88
          }
        }],
        line: 215
      },
      "58": {
        loc: {
          start: {
            line: 225,
            column: 24
          },
          end: {
            line: 225,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 225,
            column: 65
          },
          end: {
            line: 225,
            column: 76
          }
        }, {
          start: {
            line: 225,
            column: 79
          },
          end: {
            line: 225,
            column: 88
          }
        }],
        line: 225
      },
      "59": {
        loc: {
          start: {
            line: 237,
            column: 20
          },
          end: {
            line: 241,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 237,
            column: 20
          },
          end: {
            line: 241,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 237
      },
      "60": {
        loc: {
          start: {
            line: 237,
            column: 24
          },
          end: {
            line: 237,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 237,
            column: 24
          },
          end: {
            line: 237,
            column: 44
          }
        }, {
          start: {
            line: 237,
            column: 48
          },
          end: {
            line: 237,
            column: 68
          }
        }],
        line: 237
      },
      "61": {
        loc: {
          start: {
            line: 243,
            column: 20
          },
          end: {
            line: 245,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 243,
            column: 20
          },
          end: {
            line: 245,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 243
      },
      "62": {
        loc: {
          start: {
            line: 243,
            column: 24
          },
          end: {
            line: 243,
            column: 105
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 243,
            column: 24
          },
          end: {
            line: 243,
            column: 33
          }
        }, {
          start: {
            line: 243,
            column: 37
          },
          end: {
            line: 243,
            column: 66
          }
        }, {
          start: {
            line: 243,
            column: 70
          },
          end: {
            line: 243,
            column: 105
          }
        }],
        line: 243
      },
      "63": {
        loc: {
          start: {
            line: 247,
            column: 20
          },
          end: {
            line: 254,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 247,
            column: 20
          },
          end: {
            line: 254,
            column: 21
          }
        }, {
          start: {
            line: 251,
            column: 25
          },
          end: {
            line: 254,
            column: 21
          }
        }],
        line: 247
      },
      "64": {
        loc: {
          start: {
            line: 247,
            column: 24
          },
          end: {
            line: 247,
            column: 144
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 247,
            column: 24
          },
          end: {
            line: 247,
            column: 42
          }
        }, {
          start: {
            line: 247,
            column: 46
          },
          end: {
            line: 247,
            column: 84
          }
        }, {
          start: {
            line: 247,
            column: 88
          },
          end: {
            line: 247,
            column: 144
          }
        }],
        line: 247
      },
      "65": {
        loc: {
          start: {
            line: 251,
            column: 25
          },
          end: {
            line: 254,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 251,
            column: 25
          },
          end: {
            line: 254,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 251
      },
      "66": {
        loc: {
          start: {
            line: 251,
            column: 29
          },
          end: {
            line: 251,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 251,
            column: 29
          },
          end: {
            line: 251,
            column: 47
          }
        }, {
          start: {
            line: 251,
            column: 51
          },
          end: {
            line: 251,
            column: 89
          }
        }],
        line: 251
      },
      "67": {
        loc: {
          start: {
            line: 255,
            column: 20
          },
          end: {
            line: 259,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 255,
            column: 20
          },
          end: {
            line: 259,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 255
      },
      "68": {
        loc: {
          start: {
            line: 261,
            column: 20
          },
          end: {
            line: 263,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 261,
            column: 20
          },
          end: {
            line: 263,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 261
      },
      "69": {
        loc: {
          start: {
            line: 261,
            column: 24
          },
          end: {
            line: 261,
            column: 93
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 261,
            column: 24
          },
          end: {
            line: 261,
            column: 43
          }
        }, {
          start: {
            line: 261,
            column: 47
          },
          end: {
            line: 261,
            column: 93
          }
        }],
        line: 261
      },
      "70": {
        loc: {
          start: {
            line: 272,
            column: 20
          },
          end: {
            line: 280,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 272,
            column: 20
          },
          end: {
            line: 280,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 272
      },
      "71": {
        loc: {
          start: {
            line: 272,
            column: 24
          },
          end: {
            line: 272,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 272,
            column: 24
          },
          end: {
            line: 272,
            column: 36
          }
        }, {
          start: {
            line: 272,
            column: 40
          },
          end: {
            line: 272,
            column: 48
          }
        }],
        line: 272
      },
      "72": {
        loc: {
          start: {
            line: 274,
            column: 24
          },
          end: {
            line: 276,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 274,
            column: 24
          },
          end: {
            line: 276,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 274
      },
      "73": {
        loc: {
          start: {
            line: 277,
            column: 24
          },
          end: {
            line: 279,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 277,
            column: 24
          },
          end: {
            line: 279,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 277
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0, 0, 0, 0, 0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0, 0],
      "63": [0, 0],
      "64": [0, 0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/auth.tsx",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,gFAAkE;AAClE,oEAAsD;AACtD,uDAAqD;AACrD,sDAA8B;AAC9B,kDAA4B;AAC5B,wDAAkC;AAClC,qCAAwC;AACxC,gEAA+D;AAE/D,sEAAsE;AACtE,IAAM,eAAe,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,qBAAqB;AAChE,IAAM,kBAAkB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,sBAAsB;AAC/D,IAAM,WAAW,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,qBAAqB;AAC5D,IAAM,cAAc,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,qBAAqB;AAqBlD,QAAA,WAAW,GAAoB;IAC1C,OAAO,EAAE,IAAA,8BAAa,EAAC,gBAAM,CAAC;IAC9B,SAAS,EAAE;QACT,IAAA,qBAAmB,EAAC;YAClB,IAAI,EAAE,aAAa;YACnB,WAAW,EAAE;gBACX,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;gBACxC,QAAQ,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE;aAClD;YACK,SAAS,YAAC,WAAW;+CAAG,OAAO;;;;;gCACnC,IAAI,CAAC,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,KAAK,CAAA,IAAI,CAAC,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,QAAQ,CAAA,EAAE,CAAC;oCAClD,sBAAO,IAAI,EAAC;gCACd,CAAC;gCAEY,qBAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wCACxC,KAAK,EAAE,EAAE,KAAK,EAAE,WAAW,CAAC,KAAK,EAAE;qCACpC,CAAC,EAAA;;gCAFI,IAAI,GAAG,SAEX;gCAEF,IAAI,CAAC,IAAI,EAAE,CAAC;oCACV,sBAAO,IAAI,EAAC;gCACd,CAAC;gCAED,6BAA6B;gCAC7B,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;oCACtD,MAAM,IAAI,KAAK,CAAC,8FAA8F,CAAC,CAAC;gCAClH,CAAC;gCAEuB,qBAAM,kBAAM,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAA;;gCAA3E,eAAe,GAAG,SAAyD;qCAE7E,CAAC,eAAe,EAAhB,wBAAgB;gCAEZ,cAAc,GAAG,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;gCAC9C,WAAW,GAAG,CAAC,CAAC;gCAChB,eAAe,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;qCAEnC,CAAA,cAAc,IAAI,WAAW,CAAA,EAA7B,wBAA6B;gCAC/B,mBAAmB;gCACnB,qBAAM,gBAAM,CAAC,IAAI,CAAC,MAAM,CAAC;wCACvB,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;wCACtB,IAAI,EAAE;4CACJ,mBAAmB,EAAE,cAAc;4CACnC,WAAW,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,eAAe,CAAC;yCACpD;qCACF,CAAC,EAAA;;gCAPF,mBAAmB;gCACnB,SAME,CAAC;gCACH,MAAM,IAAI,KAAK,CAAC,uFAAuF,CAAC,CAAC;;4BAEzG,+BAA+B;4BAC/B,qBAAM,gBAAM,CAAC,IAAI,CAAC,MAAM,CAAC;oCACvB,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;oCACtB,IAAI,EAAE;wCACJ,mBAAmB,EAAE,cAAc;qCACpC;iCACF,CAAC,EAAA;;gCANF,+BAA+B;gCAC/B,SAKE,CAAC;;oCAEL,sBAAO,IAAI,EAAC;;gCAGd,qDAAqD;gCACrD,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;oCACjE,MAAM,IAAI,KAAK,CAAC,+FAA+F,CAAC,CAAC;gCACnH,CAAC;qCAGG,CAAA,IAAI,CAAC,mBAAmB,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,CAAA,EAAhD,wBAAgD;gCAClD,qBAAM,gBAAM,CAAC,IAAI,CAAC,MAAM,CAAC;wCACvB,KAAK,EAAE,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE;wCACtB,IAAI,EAAE;4CACJ,mBAAmB,EAAE,CAAC;4CACtB,WAAW,EAAE,IAAI;yCAClB;qCACF,CAAC,EAAA;;gCANF,SAME,CAAC;;oCAGL,sBAAO,EAAE,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAU,EAAC;;;;aACpE;SACF,CAAC;QACF,IAAA,eAAa,EAAC;YACZ,MAAM,EAAE;gBACN,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB;gBACnC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,KAAK,CAAC,EAAE,4BAA4B;gBACpF,IAAI,EAAE;oBACJ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB;oBACnC,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB;iBACxC;aACF;YACD,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,uBAAuB;YACvD,uBAAuB,EAAE,iEAAO,EAA0B;;oBAAZ,KAAK,gBAAA,EAAE,GAAG,SAAA;;;;;4BAEvC,qBAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oCACxC,KAAK,EAAE,EAAE,KAAK,OAAA,EAAE;iCACjB,CAAC,EAAA;;4BAFI,IAAI,GAAG,SAEX;iCACE,IAAI,EAAJ,wBAAI;4BACN,qBAAM,IAAA,iBAAS,EAAC;oCACd,EAAE,EAAE,KAAK;oCACT,OAAO,EAAE,6CAA6C;oCACtD,QAAQ,EAAE,uBAAC,qCAAiB,IAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE,gBAAgB,EAAE,GAAG,GAAI;iCACrF,CAAC,EAAA;;4BAJF,SAIE,CAAC;4BACH,OAAO,CAAC,GAAG,CAAC,qCAA8B,KAAK,CAAE,CAAC,CAAC;;;4BAEnD,OAAO,CAAC,IAAI,CAAC,oFAA6E,KAAK,CAAE,CAAC,CAAC;;;;;4BAGrG,OAAO,CAAC,KAAK,CAAC,+CAAwC,KAAK,MAAG,EAAE,OAAK,CAAC,CAAC;;;;;iBAE1E;SACF,CAAC;KACH;IACD,OAAO,EAAE;QACP,QAAQ,EAAE,KAAK;QACf,MAAM,EAAE,eAAe,EAAE,iCAAiC;QAC1D,SAAS,EAAE,kBAAkB,EAAE,0CAA0C;KAC1E;IACD,GAAG,EAAE;QACH,MAAM,EAAE,WAAW,EAAE,iCAAiC;KACvD;IACD,OAAO,EAAE;QACP,YAAY,EAAE;YACZ,IAAI,EAAE,yBAAyB;YAC/B,OAAO,EAAE;gBACP,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,KAAK,EAAE,uCAAuC;gBACxD,IAAI,EAAE,GAAG;gBACT,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,2BAA2B;gBAC1E,MAAM,EAAE,cAAc,EAAE,UAAU;gBAClC,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,EAAE,0BAA0B;aACrG;SACF;QACD,WAAW,EAAE;YACX,IAAI,EAAE,wBAAwB;YAC9B,OAAO,EAAE;gBACP,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,KAAK,EAAE,uCAAuC;gBACxD,IAAI,EAAE,GAAG;gBACT,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;gBAC7C,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;aACzE;SACF;QACD,SAAS,EAAE;YACT,IAAI,EAAE,sBAAsB;YAC5B,OAAO,EAAE;gBACP,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,KAAK,EAAE,uCAAuC;gBACxD,IAAI,EAAE,GAAG;gBACT,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;gBAC7C,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS;aACzE;SACF;KACF;IACD,SAAS,EAAE;QACH,GAAG;8CAA4B,OAAO,YAAlC,EAAwB;;oBAAtB,KAAK,WAAA,EAAE,IAAI,UAAA,EAAE,OAAO,aAAA;;oBACxB,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;oBAI1C,8CAA8C;oBAC9C,IAAI,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,QAAQ,EAAE,CAAC;wBACjD,KAAK,CAAC,SAAS,GAAG,gBAAM,CAAC,UAAU,EAAE,CAAC;wBACtC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;wBAChB,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC;oBAC3B,CAAC;oBAED,4DAA4D;oBAC5D,IAAI,KAAK,CAAC,GAAG,IAAI,OAAO,KAAK,CAAC,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,eAAe,EAAE,CAAC;wBACtF,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;oBACrC,CAAC;oBAGK,2BAA2B,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;oBACrD,IAAI,KAAK,CAAC,YAAY,IAAI,OAAO,KAAK,CAAC,YAAY,KAAK,QAAQ,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,YAAY,CAAC,GAAG,2BAA2B,EAAE,CAAC;wBAC7H,KAAK,CAAC,SAAS,GAAG,gBAAM,CAAC,UAAU,EAAE,CAAC;wBACtC,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC;oBAC3B,CAAC;yBAAM,IAAI,KAAK,CAAC,YAAY,IAAI,OAAO,KAAK,CAAC,YAAY,KAAK,QAAQ,EAAE,CAAC;wBACxE,uDAAuD;wBACvD,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC;oBAC3B,CAAC;oBAED,IAAI,IAAI,EAAE,CAAC;wBACT,KAAK,CAAC,EAAE,GAAI,IAAa,CAAC,EAAE,CAAC;wBAC7B,KAAK,CAAC,KAAK,GAAI,IAAa,CAAC,KAAK,CAAC;wBACnC,KAAK,CAAC,IAAI,GAAI,IAAa,CAAC,IAAI,CAAC;oBACnC,CAAC;oBAED,mDAAmD;oBACnD,IAAI,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,yBAAyB;wBACpG,KAAK,CAAC,YAAY,GAAG,GAAG,CAAC;oBAC3B,CAAC;oBAED,sBAAO,KAAoB,EAAC;;;SAC7B;QACK,OAAO;8CAAsB,OAAO,YAA5B,EAAkB;oBAAhB,OAAO,aAAA,EAAE,KAAK,WAAA;;oBAC5B,IAAI,OAAO,CAAC,IAAI,IAAI,KAAK,CAAC,EAAE,EAAE,CAAC;wBAC5B,OAAO,CAAC,IAAa,CAAC,EAAE,GAAG,KAAK,CAAC,EAAY,CAAC;wBAC/C,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;4BACf,OAAO,CAAC,IAAa,CAAC,KAAK,GAAG,KAAK,CAAC,KAAe,CAAC;wBACvD,CAAC;wBACD,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;4BACd,OAAO,CAAC,IAAa,CAAC,IAAI,GAAG,KAAK,CAAC,IAAc,CAAC;wBACrD,CAAC;oBACH,CAAC;oBAED,gCAAgC;oBAC/B,OAAe,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;oBAC5C,OAAe,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;oBAEnD,sBAAO,OAA0B,EAAC;;;SACnC;KACF;IACD,MAAM,EAAE;QACA,UAAU,YAAC,OAAO;;;oBACtB,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;;;SAClD;KACF;IACD,KAAK,EAAE;QACL,MAAM,EAAE,QAAQ;KACjB;CACF,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/auth.tsx"],
      sourcesContent: ["import { NextAuthOptions, User as NextAuthUser, Session } from \"next-auth\";\nimport { JWT } from \"next-auth/jwt\";\nimport CredentialsProvider from \"next-auth/providers/credentials\";\nimport EmailProvider from \"next-auth/providers/email\";\nimport { PrismaAdapter } from \"@auth/prisma-adapter\";\nimport bcrypt from \"bcryptjs\";\nimport crypto from \"crypto\";\nimport prisma from '@/lib/prisma';\nimport { sendEmail } from '@/lib/email';\nimport { VerificationEmail } from '@/emails/VerificationEmail';\n\n// Session configuration constants - centralized to ensure consistency\nconst SESSION_MAX_AGE = 30 * 24 * 60 * 60; // 30 days in seconds\nconst SESSION_UPDATE_AGE = 24 * 60 * 60; // 24 hours in seconds\nconst JWT_MAX_AGE = 30 * 24 * 60 * 60; // 30 days in seconds\nconst COOKIE_MAX_AGE = 30 * 24 * 60 * 60; // 30 days in seconds\n\n// Augment the NextAuthUser type to include id\ninterface User extends NextAuthUser {\n  id: string;\n}\n\n// Augment the Session.user type\ninterface ExtendedSession extends Session {\n  user?: User & {\n    id: string;\n  };\n}\n\n// Augment the JWT type to include id, email, and name\ninterface ExtendedJWT extends JWT {\n  id: string;\n  email: string;\n  name: string;\n}\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    CredentialsProvider({\n      name: \"Credentials\",\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" }\n      },\n      async authorize(credentials): Promise<User | null> {\n        if (!credentials?.email || !credentials?.password) {\n          return null;\n        }\n\n        const user = await prisma.user.findUnique({\n          where: { email: credentials.email }\n        });\n\n        if (!user) {\n          return null;\n        }\n\n        // Check if account is locked\n        if (user.lockedUntil && user.lockedUntil > new Date()) {\n          throw new Error('Account is temporarily locked due to too many failed login attempts. Please try again later.');\n        }\n\n        const isPasswordValid = await bcrypt.compare(credentials.password, user.password);\n\n        if (!isPasswordValid) {\n          // Increment failed login attempts\n          const failedAttempts = user.failedLoginAttempts + 1;\n          const maxAttempts = 5;\n          const lockoutDuration = 15 * 60 * 1000; // 15 minutes in milliseconds\n\n          if (failedAttempts >= maxAttempts) {\n            // Lock the account\n            await prisma.user.update({\n              where: { id: user.id },\n              data: {\n                failedLoginAttempts: failedAttempts,\n                lockedUntil: new Date(Date.now() + lockoutDuration)\n              }\n            });\n            throw new Error('Account locked due to too many failed login attempts. Please try again in 15 minutes.');\n          } else {\n            // Update failed attempts count\n            await prisma.user.update({\n              where: { id: user.id },\n              data: {\n                failedLoginAttempts: failedAttempts\n              }\n            });\n          }\n          return null;\n        }\n\n        // Check if email is verified (bypass in development)\n        if (!user.emailVerified && process.env.NODE_ENV === 'production') {\n          throw new Error('Please verify your email address before signing in. Check your inbox for a verification link.');\n        }\n\n        // Reset failed login attempts on successful login\n        if (user.failedLoginAttempts > 0 || user.lockedUntil) {\n          await prisma.user.update({\n            where: { id: user.id },\n            data: {\n              failedLoginAttempts: 0,\n              lockedUntil: null\n            }\n          });\n        }\n\n        return { id: user.id, email: user.email, name: user.name } as User;\n      }\n    }),\n    EmailProvider({\n      server: {\n        host: process.env.EMAIL_SERVER_HOST,\n        port: parseInt(process.env.EMAIL_SERVER_PORT || \"587\"), // Default to 587 if not set\n        auth: {\n          user: process.env.EMAIL_SERVER_USER,\n          pass: process.env.EMAIL_SERVER_PASSWORD,\n        },\n      },\n      from: process.env.EMAIL_FROM || '<EMAIL>',\n      sendVerificationRequest: async ({ identifier: email, url }) => {\n        try {\n          const user = await prisma.user.findUnique({\n            where: { email },\n          });\n          if (user) {\n            await sendEmail({\n              to: email,\n              subject: \"Verify your email for FAAFO Career Platform\",\n              template: <VerificationEmail username={user.name || email} verificationLink={url} />,\n            });\n            console.log(`Verification email sent to ${email}`);\n          } else {\n            console.warn(`Attempted to send verification email to non-existent user via magic link: ${email}`);\n          }\n        } catch (error) {\n          console.error(`Failed to send verification email to ${email}:`, error);\n        }\n      },\n    }),\n  ],\n  session: {\n    strategy: \"jwt\",\n    maxAge: SESSION_MAX_AGE, // 30 days for better persistence\n    updateAge: SESSION_UPDATE_AGE, // 24 hours - less aggressive regeneration\n  },\n  jwt: {\n    maxAge: JWT_MAX_AGE, // 30 days for better persistence\n  },\n  cookies: {\n    sessionToken: {\n      name: `next-auth.session-token`,\n      options: {\n        httpOnly: true,\n        sameSite: 'lax', // Less strict for better compatibility\n        path: '/',\n        secure: process.env.NODE_ENV === 'production', // HTTPS only in production\n        maxAge: COOKIE_MAX_AGE, // 30 days\n        domain: process.env.NODE_ENV === 'development' ? 'localhost' : undefined, // Explicit domain for dev\n      },\n    },\n    callbackUrl: {\n      name: `next-auth.callback-url`,\n      options: {\n        httpOnly: true,\n        sameSite: 'lax', // Less strict for better compatibility\n        path: '/',\n        secure: process.env.NODE_ENV === 'production',\n        domain: process.env.NODE_ENV === 'development' ? 'localhost' : undefined,\n      },\n    },\n    csrfToken: {\n      name: `next-auth.csrf-token`,\n      options: {\n        httpOnly: true,\n        sameSite: 'lax', // Less strict for better compatibility\n        path: '/',\n        secure: process.env.NODE_ENV === 'production',\n        domain: process.env.NODE_ENV === 'development' ? 'localhost' : undefined,\n      },\n    },\n  },\n  callbacks: {\n    async jwt({ token, user, trigger }): Promise<ExtendedJWT> {\n      const now = Math.floor(Date.now() / 1000);\n\n\n\n      // Regenerate session ID on login for security\n      if (trigger === 'signIn' || trigger === 'signUp') {\n        token.sessionId = crypto.randomUUID();\n        token.iat = now;\n        token.lastActivity = now;\n      }\n\n      // Check for session timeout (use configured session maxAge)\n      if (token.iat && typeof token.iat === 'number' && (now - token.iat) > SESSION_MAX_AGE) {\n        throw new Error('Session expired');\n      }\n\n      // Regenerate session ID less frequently for better persistence (every 7 days)\n      const sessionRegenerationInterval = 7 * 24 * 60 * 60; // 7 days\n      if (token.lastActivity && typeof token.lastActivity === 'number' && (now - token.lastActivity) > sessionRegenerationInterval) {\n        token.sessionId = crypto.randomUUID();\n        token.lastActivity = now;\n      } else if (token.lastActivity && typeof token.lastActivity === 'number') {\n        // Update last activity without regenerating session ID\n        token.lastActivity = now;\n      }\n\n      if (user) {\n        token.id = (user as User).id;\n        token.email = (user as User).email;\n        token.name = (user as User).name;\n      }\n\n      // Update last activity timestamp less aggressively\n      if (!token.lastActivity || (now - Number(token.lastActivity)) > (60 * 60)) { // Only update every hour\n        token.lastActivity = now;\n      }\n\n      return token as ExtendedJWT;\n    },\n    async session({ session, token }): Promise<ExtendedSession> {\n      if (session.user && token.id) {\n        (session.user as User).id = token.id as string;\n        if (token.email) {\n          (session.user as User).email = token.email as string;\n        }\n        if (token.name) {\n          (session.user as User).name = token.name as string;\n        }\n      }\n\n      // Add session security metadata\n      (session as any).sessionId = token.sessionId;\n      (session as any).lastActivity = token.lastActivity;\n\n      return session as ExtendedSession;\n    },\n  },\n  events: {\n    async createUser(message) {\n      console.log(\"User created:\", message.user.email);\n    },\n  },\n  pages: {\n    signIn: '/login',\n  }\n}; "],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f7407971b3d95517fe584a5821440840f8450aac"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_8o8wd8h3p = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_8o8wd8h3p();
var __awaiter =
/* istanbul ignore next */
(cov_8o8wd8h3p().s[0]++,
/* istanbul ignore next */
(cov_8o8wd8h3p().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_8o8wd8h3p().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_8o8wd8h3p().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_8o8wd8h3p().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_8o8wd8h3p().f[1]++;
    cov_8o8wd8h3p().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_8o8wd8h3p().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_8o8wd8h3p().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_8o8wd8h3p().f[2]++;
      cov_8o8wd8h3p().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_8o8wd8h3p().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_8o8wd8h3p().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_8o8wd8h3p().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_8o8wd8h3p().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_8o8wd8h3p().f[4]++;
      cov_8o8wd8h3p().s[4]++;
      try {
        /* istanbul ignore next */
        cov_8o8wd8h3p().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_8o8wd8h3p().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_8o8wd8h3p().f[5]++;
      cov_8o8wd8h3p().s[7]++;
      try {
        /* istanbul ignore next */
        cov_8o8wd8h3p().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_8o8wd8h3p().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_8o8wd8h3p().f[6]++;
      cov_8o8wd8h3p().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_8o8wd8h3p().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_8o8wd8h3p().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_8o8wd8h3p().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_8o8wd8h3p().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_8o8wd8h3p().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_8o8wd8h3p().s[12]++,
/* istanbul ignore next */
(cov_8o8wd8h3p().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_8o8wd8h3p().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_8o8wd8h3p().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_8o8wd8h3p().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_8o8wd8h3p().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_8o8wd8h3p().f[8]++;
        cov_8o8wd8h3p().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_8o8wd8h3p().b[6][0]++;
          cov_8o8wd8h3p().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_8o8wd8h3p().b[6][1]++;
        }
        cov_8o8wd8h3p().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_8o8wd8h3p().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_8o8wd8h3p().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_8o8wd8h3p().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_8o8wd8h3p().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_8o8wd8h3p().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_8o8wd8h3p().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_8o8wd8h3p().f[9]++;
    cov_8o8wd8h3p().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_8o8wd8h3p().f[10]++;
    cov_8o8wd8h3p().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_8o8wd8h3p().f[11]++;
      cov_8o8wd8h3p().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_8o8wd8h3p().f[12]++;
    cov_8o8wd8h3p().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_8o8wd8h3p().b[9][0]++;
      cov_8o8wd8h3p().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_8o8wd8h3p().b[9][1]++;
    }
    cov_8o8wd8h3p().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_8o8wd8h3p().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_8o8wd8h3p().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_8o8wd8h3p().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_8o8wd8h3p().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_8o8wd8h3p().s[25]++;
      try {
        /* istanbul ignore next */
        cov_8o8wd8h3p().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_8o8wd8h3p().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_8o8wd8h3p().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_8o8wd8h3p().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_8o8wd8h3p().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_8o8wd8h3p().b[15][0]++,
        /* istanbul ignore next */
        (cov_8o8wd8h3p().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_8o8wd8h3p().b[16][1]++,
        /* istanbul ignore next */
        (cov_8o8wd8h3p().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_8o8wd8h3p().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_8o8wd8h3p().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_8o8wd8h3p().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_8o8wd8h3p().b[12][0]++;
          cov_8o8wd8h3p().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_8o8wd8h3p().b[12][1]++;
        }
        cov_8o8wd8h3p().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_8o8wd8h3p().b[18][0]++;
          cov_8o8wd8h3p().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_8o8wd8h3p().b[18][1]++;
        }
        cov_8o8wd8h3p().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_8o8wd8h3p().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_8o8wd8h3p().b[19][1]++;
            cov_8o8wd8h3p().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_8o8wd8h3p().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_8o8wd8h3p().b[19][2]++;
            cov_8o8wd8h3p().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_8o8wd8h3p().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_8o8wd8h3p().b[19][3]++;
            cov_8o8wd8h3p().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_8o8wd8h3p().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_8o8wd8h3p().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_8o8wd8h3p().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_8o8wd8h3p().b[19][4]++;
            cov_8o8wd8h3p().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_8o8wd8h3p().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_8o8wd8h3p().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_8o8wd8h3p().b[19][5]++;
            cov_8o8wd8h3p().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_8o8wd8h3p().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_8o8wd8h3p().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_8o8wd8h3p().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_8o8wd8h3p().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_8o8wd8h3p().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[20][0]++;
              cov_8o8wd8h3p().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_8o8wd8h3p().b[20][1]++;
            }
            cov_8o8wd8h3p().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_8o8wd8h3p().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_8o8wd8h3p().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_8o8wd8h3p().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_8o8wd8h3p().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[23][0]++;
              cov_8o8wd8h3p().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_8o8wd8h3p().b[23][1]++;
            }
            cov_8o8wd8h3p().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_8o8wd8h3p().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_8o8wd8h3p().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[25][0]++;
              cov_8o8wd8h3p().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_8o8wd8h3p().b[25][1]++;
            }
            cov_8o8wd8h3p().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_8o8wd8h3p().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_8o8wd8h3p().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[27][0]++;
              cov_8o8wd8h3p().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_8o8wd8h3p().b[27][1]++;
            }
            cov_8o8wd8h3p().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[29][0]++;
              cov_8o8wd8h3p().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_8o8wd8h3p().b[29][1]++;
            }
            cov_8o8wd8h3p().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_8o8wd8h3p().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_8o8wd8h3p().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_8o8wd8h3p().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_8o8wd8h3p().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_8o8wd8h3p().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_8o8wd8h3p().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_8o8wd8h3p().b[30][0]++;
      cov_8o8wd8h3p().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_8o8wd8h3p().b[30][1]++;
    }
    cov_8o8wd8h3p().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_8o8wd8h3p().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_8o8wd8h3p().b[31][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_8o8wd8h3p().s[67]++,
/* istanbul ignore next */
(cov_8o8wd8h3p().b[32][0]++, this) &&
/* istanbul ignore next */
(cov_8o8wd8h3p().b[32][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_8o8wd8h3p().b[32][2]++, function (mod) {
  /* istanbul ignore next */
  cov_8o8wd8h3p().f[13]++;
  cov_8o8wd8h3p().s[68]++;
  return /* istanbul ignore next */(cov_8o8wd8h3p().b[34][0]++, mod) &&
  /* istanbul ignore next */
  (cov_8o8wd8h3p().b[34][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_8o8wd8h3p().b[33][0]++, mod) :
  /* istanbul ignore next */
  (cov_8o8wd8h3p().b[33][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_8o8wd8h3p().s[69]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_8o8wd8h3p().s[70]++;
exports.authOptions = void 0;
var jsx_runtime_1 =
/* istanbul ignore next */
(cov_8o8wd8h3p().s[71]++, require("react/jsx-runtime"));
var credentials_1 =
/* istanbul ignore next */
(cov_8o8wd8h3p().s[72]++, __importDefault(require("next-auth/providers/credentials")));
var email_1 =
/* istanbul ignore next */
(cov_8o8wd8h3p().s[73]++, __importDefault(require("next-auth/providers/email")));
var prisma_adapter_1 =
/* istanbul ignore next */
(cov_8o8wd8h3p().s[74]++, require("@auth/prisma-adapter"));
var bcryptjs_1 =
/* istanbul ignore next */
(cov_8o8wd8h3p().s[75]++, __importDefault(require("bcryptjs")));
var crypto_1 =
/* istanbul ignore next */
(cov_8o8wd8h3p().s[76]++, __importDefault(require("crypto")));
var prisma_1 =
/* istanbul ignore next */
(cov_8o8wd8h3p().s[77]++, __importDefault(require("@/lib/prisma")));
var email_2 =
/* istanbul ignore next */
(cov_8o8wd8h3p().s[78]++, require("@/lib/email"));
var VerificationEmail_1 =
/* istanbul ignore next */
(cov_8o8wd8h3p().s[79]++, require("@/emails/VerificationEmail"));
// Session configuration constants - centralized to ensure consistency
var SESSION_MAX_AGE =
/* istanbul ignore next */
(cov_8o8wd8h3p().s[80]++, 30 * 24 * 60 * 60); // 30 days in seconds
var SESSION_UPDATE_AGE =
/* istanbul ignore next */
(cov_8o8wd8h3p().s[81]++, 24 * 60 * 60); // 24 hours in seconds
var JWT_MAX_AGE =
/* istanbul ignore next */
(cov_8o8wd8h3p().s[82]++, 30 * 24 * 60 * 60); // 30 days in seconds
var COOKIE_MAX_AGE =
/* istanbul ignore next */
(cov_8o8wd8h3p().s[83]++, 30 * 24 * 60 * 60); // 30 days in seconds
/* istanbul ignore next */
cov_8o8wd8h3p().s[84]++;
exports.authOptions = {
  adapter: (0, prisma_adapter_1.PrismaAdapter)(prisma_1.default),
  providers: [(0, credentials_1.default)({
    name: "Credentials",
    credentials: {
      email: {
        label: "Email",
        type: "email"
      },
      password: {
        label: "Password",
        type: "password"
      }
    },
    authorize: function (credentials) {
      /* istanbul ignore next */
      cov_8o8wd8h3p().f[14]++;
      cov_8o8wd8h3p().s[85]++;
      return __awaiter(this, void 0, Promise, function () {
        /* istanbul ignore next */
        cov_8o8wd8h3p().f[15]++;
        var user, isPasswordValid, failedAttempts, maxAttempts, lockoutDuration;
        /* istanbul ignore next */
        cov_8o8wd8h3p().s[86]++;
        return __generator(this, function (_a) {
          /* istanbul ignore next */
          cov_8o8wd8h3p().f[16]++;
          cov_8o8wd8h3p().s[87]++;
          switch (_a.label) {
            case 0:
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[35][0]++;
              cov_8o8wd8h3p().s[88]++;
              if (
              /* istanbul ignore next */
              (cov_8o8wd8h3p().b[37][0]++, !(
              /* istanbul ignore next */
              (cov_8o8wd8h3p().b[39][0]++, credentials === null) ||
              /* istanbul ignore next */
              (cov_8o8wd8h3p().b[39][1]++, credentials === void 0) ?
              /* istanbul ignore next */
              (cov_8o8wd8h3p().b[38][0]++, void 0) :
              /* istanbul ignore next */
              (cov_8o8wd8h3p().b[38][1]++, credentials.email))) ||
              /* istanbul ignore next */
              (cov_8o8wd8h3p().b[37][1]++, !(
              /* istanbul ignore next */
              (cov_8o8wd8h3p().b[41][0]++, credentials === null) ||
              /* istanbul ignore next */
              (cov_8o8wd8h3p().b[41][1]++, credentials === void 0) ?
              /* istanbul ignore next */
              (cov_8o8wd8h3p().b[40][0]++, void 0) :
              /* istanbul ignore next */
              (cov_8o8wd8h3p().b[40][1]++, credentials.password)))) {
                /* istanbul ignore next */
                cov_8o8wd8h3p().b[36][0]++;
                cov_8o8wd8h3p().s[89]++;
                return [2 /*return*/, null];
              } else
              /* istanbul ignore next */
              {
                cov_8o8wd8h3p().b[36][1]++;
              }
              cov_8o8wd8h3p().s[90]++;
              return [4 /*yield*/, prisma_1.default.user.findUnique({
                where: {
                  email: credentials.email
                }
              })];
            case 1:
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[35][1]++;
              cov_8o8wd8h3p().s[91]++;
              user = _a.sent();
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[92]++;
              if (!user) {
                /* istanbul ignore next */
                cov_8o8wd8h3p().b[42][0]++;
                cov_8o8wd8h3p().s[93]++;
                return [2 /*return*/, null];
              } else
              /* istanbul ignore next */
              {
                cov_8o8wd8h3p().b[42][1]++;
              }
              // Check if account is locked
              cov_8o8wd8h3p().s[94]++;
              if (
              /* istanbul ignore next */
              (cov_8o8wd8h3p().b[44][0]++, user.lockedUntil) &&
              /* istanbul ignore next */
              (cov_8o8wd8h3p().b[44][1]++, user.lockedUntil > new Date())) {
                /* istanbul ignore next */
                cov_8o8wd8h3p().b[43][0]++;
                cov_8o8wd8h3p().s[95]++;
                throw new Error('Account is temporarily locked due to too many failed login attempts. Please try again later.');
              } else
              /* istanbul ignore next */
              {
                cov_8o8wd8h3p().b[43][1]++;
              }
              cov_8o8wd8h3p().s[96]++;
              return [4 /*yield*/, bcryptjs_1.default.compare(credentials.password, user.password)];
            case 2:
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[35][2]++;
              cov_8o8wd8h3p().s[97]++;
              isPasswordValid = _a.sent();
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[98]++;
              if (!!isPasswordValid) {
                /* istanbul ignore next */
                cov_8o8wd8h3p().b[45][0]++;
                cov_8o8wd8h3p().s[99]++;
                return [3 /*break*/, 7];
              } else
              /* istanbul ignore next */
              {
                cov_8o8wd8h3p().b[45][1]++;
              }
              cov_8o8wd8h3p().s[100]++;
              failedAttempts = user.failedLoginAttempts + 1;
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[101]++;
              maxAttempts = 5;
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[102]++;
              lockoutDuration = 15 * 60 * 1000;
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[103]++;
              if (!(failedAttempts >= maxAttempts)) {
                /* istanbul ignore next */
                cov_8o8wd8h3p().b[46][0]++;
                cov_8o8wd8h3p().s[104]++;
                return [3 /*break*/, 4];
              } else
              /* istanbul ignore next */
              {
                cov_8o8wd8h3p().b[46][1]++;
              }
              // Lock the account
              cov_8o8wd8h3p().s[105]++;
              return [4 /*yield*/, prisma_1.default.user.update({
                where: {
                  id: user.id
                },
                data: {
                  failedLoginAttempts: failedAttempts,
                  lockedUntil: new Date(Date.now() + lockoutDuration)
                }
              })];
            case 3:
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[35][3]++;
              cov_8o8wd8h3p().s[106]++;
              // Lock the account
              _a.sent();
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[107]++;
              throw new Error('Account locked due to too many failed login attempts. Please try again in 15 minutes.');
            case 4:
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[35][4]++;
              cov_8o8wd8h3p().s[108]++;
              // Update failed attempts count
              return [4 /*yield*/, prisma_1.default.user.update({
                where: {
                  id: user.id
                },
                data: {
                  failedLoginAttempts: failedAttempts
                }
              })];
            case 5:
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[35][5]++;
              cov_8o8wd8h3p().s[109]++;
              // Update failed attempts count
              _a.sent();
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[110]++;
              _a.label = 6;
            case 6:
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[35][6]++;
              cov_8o8wd8h3p().s[111]++;
              return [2 /*return*/, null];
            case 7:
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[35][7]++;
              cov_8o8wd8h3p().s[112]++;
              // Check if email is verified (bypass in development)
              if (
              /* istanbul ignore next */
              (cov_8o8wd8h3p().b[48][0]++, !user.emailVerified) &&
              /* istanbul ignore next */
              (cov_8o8wd8h3p().b[48][1]++, process.env.NODE_ENV === 'production')) {
                /* istanbul ignore next */
                cov_8o8wd8h3p().b[47][0]++;
                cov_8o8wd8h3p().s[113]++;
                throw new Error('Please verify your email address before signing in. Check your inbox for a verification link.');
              } else
              /* istanbul ignore next */
              {
                cov_8o8wd8h3p().b[47][1]++;
              }
              cov_8o8wd8h3p().s[114]++;
              if (!(
              /* istanbul ignore next */
              (cov_8o8wd8h3p().b[50][0]++, user.failedLoginAttempts > 0) ||
              /* istanbul ignore next */
              (cov_8o8wd8h3p().b[50][1]++, user.lockedUntil))) {
                /* istanbul ignore next */
                cov_8o8wd8h3p().b[49][0]++;
                cov_8o8wd8h3p().s[115]++;
                return [3 /*break*/, 9];
              } else
              /* istanbul ignore next */
              {
                cov_8o8wd8h3p().b[49][1]++;
              }
              cov_8o8wd8h3p().s[116]++;
              return [4 /*yield*/, prisma_1.default.user.update({
                where: {
                  id: user.id
                },
                data: {
                  failedLoginAttempts: 0,
                  lockedUntil: null
                }
              })];
            case 8:
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[35][8]++;
              cov_8o8wd8h3p().s[117]++;
              _a.sent();
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[118]++;
              _a.label = 9;
            case 9:
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[35][9]++;
              cov_8o8wd8h3p().s[119]++;
              return [2 /*return*/, {
                id: user.id,
                email: user.email,
                name: user.name
              }];
          }
        });
      });
    }
  }), (0, email_1.default)({
    server: {
      host: process.env.EMAIL_SERVER_HOST,
      port: parseInt(
      /* istanbul ignore next */
      (cov_8o8wd8h3p().b[51][0]++, process.env.EMAIL_SERVER_PORT) ||
      /* istanbul ignore next */
      (cov_8o8wd8h3p().b[51][1]++, "587")),
      // Default to 587 if not set
      auth: {
        user: process.env.EMAIL_SERVER_USER,
        pass: process.env.EMAIL_SERVER_PASSWORD
      }
    },
    from:
    /* istanbul ignore next */
    (cov_8o8wd8h3p().b[52][0]++, process.env.EMAIL_FROM) ||
    /* istanbul ignore next */
    (cov_8o8wd8h3p().b[52][1]++, '<EMAIL>'),
    sendVerificationRequest: function (_a) {
      /* istanbul ignore next */
      cov_8o8wd8h3p().f[17]++;
      cov_8o8wd8h3p().s[120]++;
      return __awaiter(void 0, [_a], void 0, function (_b) {
        /* istanbul ignore next */
        cov_8o8wd8h3p().f[18]++;
        var user, error_1;
        var email =
          /* istanbul ignore next */
          (cov_8o8wd8h3p().s[121]++, _b.identifier),
          url =
          /* istanbul ignore next */
          (cov_8o8wd8h3p().s[122]++, _b.url);
        /* istanbul ignore next */
        cov_8o8wd8h3p().s[123]++;
        return __generator(this, function (_c) {
          /* istanbul ignore next */
          cov_8o8wd8h3p().f[19]++;
          cov_8o8wd8h3p().s[124]++;
          switch (_c.label) {
            case 0:
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[53][0]++;
              cov_8o8wd8h3p().s[125]++;
              _c.trys.push([0, 5,, 6]);
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[126]++;
              return [4 /*yield*/, prisma_1.default.user.findUnique({
                where: {
                  email: email
                }
              })];
            case 1:
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[53][1]++;
              cov_8o8wd8h3p().s[127]++;
              user = _c.sent();
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[128]++;
              if (!user) {
                /* istanbul ignore next */
                cov_8o8wd8h3p().b[54][0]++;
                cov_8o8wd8h3p().s[129]++;
                return [3 /*break*/, 3];
              } else
              /* istanbul ignore next */
              {
                cov_8o8wd8h3p().b[54][1]++;
              }
              cov_8o8wd8h3p().s[130]++;
              return [4 /*yield*/, (0, email_2.sendEmail)({
                to: email,
                subject: "Verify your email for FAAFO Career Platform",
                template: (0, jsx_runtime_1.jsx)(VerificationEmail_1.VerificationEmail, {
                  username:
                  /* istanbul ignore next */
                  (cov_8o8wd8h3p().b[55][0]++, user.name) ||
                  /* istanbul ignore next */
                  (cov_8o8wd8h3p().b[55][1]++, email),
                  verificationLink: url
                })
              })];
            case 2:
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[53][2]++;
              cov_8o8wd8h3p().s[131]++;
              _c.sent();
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[132]++;
              console.log("Verification email sent to ".concat(email));
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[133]++;
              return [3 /*break*/, 4];
            case 3:
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[53][3]++;
              cov_8o8wd8h3p().s[134]++;
              console.warn("Attempted to send verification email to non-existent user via magic link: ".concat(email));
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[135]++;
              _c.label = 4;
            case 4:
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[53][4]++;
              cov_8o8wd8h3p().s[136]++;
              return [3 /*break*/, 6];
            case 5:
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[53][5]++;
              cov_8o8wd8h3p().s[137]++;
              error_1 = _c.sent();
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[138]++;
              console.error("Failed to send verification email to ".concat(email, ":"), error_1);
              /* istanbul ignore next */
              cov_8o8wd8h3p().s[139]++;
              return [3 /*break*/, 6];
            case 6:
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[53][6]++;
              cov_8o8wd8h3p().s[140]++;
              return [2 /*return*/];
          }
        });
      });
    }
  })],
  session: {
    strategy: "jwt",
    maxAge: SESSION_MAX_AGE,
    // 30 days for better persistence
    updateAge: SESSION_UPDATE_AGE // 24 hours - less aggressive regeneration
  },
  jwt: {
    maxAge: JWT_MAX_AGE // 30 days for better persistence
  },
  cookies: {
    sessionToken: {
      name: "next-auth.session-token",
      options: {
        httpOnly: true,
        sameSite: 'lax',
        // Less strict for better compatibility
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        // HTTPS only in production
        maxAge: COOKIE_MAX_AGE,
        // 30 days
        domain: process.env.NODE_ENV === 'development' ?
        /* istanbul ignore next */
        (cov_8o8wd8h3p().b[56][0]++, 'localhost') :
        /* istanbul ignore next */
        (cov_8o8wd8h3p().b[56][1]++, undefined) // Explicit domain for dev
      }
    },
    callbackUrl: {
      name: "next-auth.callback-url",
      options: {
        httpOnly: true,
        sameSite: 'lax',
        // Less strict for better compatibility
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        domain: process.env.NODE_ENV === 'development' ?
        /* istanbul ignore next */
        (cov_8o8wd8h3p().b[57][0]++, 'localhost') :
        /* istanbul ignore next */
        (cov_8o8wd8h3p().b[57][1]++, undefined)
      }
    },
    csrfToken: {
      name: "next-auth.csrf-token",
      options: {
        httpOnly: true,
        sameSite: 'lax',
        // Less strict for better compatibility
        path: '/',
        secure: process.env.NODE_ENV === 'production',
        domain: process.env.NODE_ENV === 'development' ?
        /* istanbul ignore next */
        (cov_8o8wd8h3p().b[58][0]++, 'localhost') :
        /* istanbul ignore next */
        (cov_8o8wd8h3p().b[58][1]++, undefined)
      }
    }
  },
  callbacks: {
    jwt: function (_a) {
      /* istanbul ignore next */
      cov_8o8wd8h3p().f[20]++;
      cov_8o8wd8h3p().s[141]++;
      return __awaiter(this, arguments, Promise, function (_b) {
        /* istanbul ignore next */
        cov_8o8wd8h3p().f[21]++;
        var now, sessionRegenerationInterval;
        var token =
          /* istanbul ignore next */
          (cov_8o8wd8h3p().s[142]++, _b.token),
          user =
          /* istanbul ignore next */
          (cov_8o8wd8h3p().s[143]++, _b.user),
          trigger =
          /* istanbul ignore next */
          (cov_8o8wd8h3p().s[144]++, _b.trigger);
        /* istanbul ignore next */
        cov_8o8wd8h3p().s[145]++;
        return __generator(this, function (_c) {
          /* istanbul ignore next */
          cov_8o8wd8h3p().f[22]++;
          cov_8o8wd8h3p().s[146]++;
          now = Math.floor(Date.now() / 1000);
          // Regenerate session ID on login for security
          /* istanbul ignore next */
          cov_8o8wd8h3p().s[147]++;
          if (
          /* istanbul ignore next */
          (cov_8o8wd8h3p().b[60][0]++, trigger === 'signIn') ||
          /* istanbul ignore next */
          (cov_8o8wd8h3p().b[60][1]++, trigger === 'signUp')) {
            /* istanbul ignore next */
            cov_8o8wd8h3p().b[59][0]++;
            cov_8o8wd8h3p().s[148]++;
            token.sessionId = crypto_1.default.randomUUID();
            /* istanbul ignore next */
            cov_8o8wd8h3p().s[149]++;
            token.iat = now;
            /* istanbul ignore next */
            cov_8o8wd8h3p().s[150]++;
            token.lastActivity = now;
          } else
          /* istanbul ignore next */
          {
            cov_8o8wd8h3p().b[59][1]++;
          }
          // Check for session timeout (use configured session maxAge)
          cov_8o8wd8h3p().s[151]++;
          if (
          /* istanbul ignore next */
          (cov_8o8wd8h3p().b[62][0]++, token.iat) &&
          /* istanbul ignore next */
          (cov_8o8wd8h3p().b[62][1]++, typeof token.iat === 'number') &&
          /* istanbul ignore next */
          (cov_8o8wd8h3p().b[62][2]++, now - token.iat > SESSION_MAX_AGE)) {
            /* istanbul ignore next */
            cov_8o8wd8h3p().b[61][0]++;
            cov_8o8wd8h3p().s[152]++;
            throw new Error('Session expired');
          } else
          /* istanbul ignore next */
          {
            cov_8o8wd8h3p().b[61][1]++;
          }
          cov_8o8wd8h3p().s[153]++;
          sessionRegenerationInterval = 7 * 24 * 60 * 60;
          /* istanbul ignore next */
          cov_8o8wd8h3p().s[154]++;
          if (
          /* istanbul ignore next */
          (cov_8o8wd8h3p().b[64][0]++, token.lastActivity) &&
          /* istanbul ignore next */
          (cov_8o8wd8h3p().b[64][1]++, typeof token.lastActivity === 'number') &&
          /* istanbul ignore next */
          (cov_8o8wd8h3p().b[64][2]++, now - token.lastActivity > sessionRegenerationInterval)) {
            /* istanbul ignore next */
            cov_8o8wd8h3p().b[63][0]++;
            cov_8o8wd8h3p().s[155]++;
            token.sessionId = crypto_1.default.randomUUID();
            /* istanbul ignore next */
            cov_8o8wd8h3p().s[156]++;
            token.lastActivity = now;
          } else {
            /* istanbul ignore next */
            cov_8o8wd8h3p().b[63][1]++;
            cov_8o8wd8h3p().s[157]++;
            if (
            /* istanbul ignore next */
            (cov_8o8wd8h3p().b[66][0]++, token.lastActivity) &&
            /* istanbul ignore next */
            (cov_8o8wd8h3p().b[66][1]++, typeof token.lastActivity === 'number')) {
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[65][0]++;
              cov_8o8wd8h3p().s[158]++;
              // Update last activity without regenerating session ID
              token.lastActivity = now;
            } else
            /* istanbul ignore next */
            {
              cov_8o8wd8h3p().b[65][1]++;
            }
          }
          /* istanbul ignore next */
          cov_8o8wd8h3p().s[159]++;
          if (user) {
            /* istanbul ignore next */
            cov_8o8wd8h3p().b[67][0]++;
            cov_8o8wd8h3p().s[160]++;
            token.id = user.id;
            /* istanbul ignore next */
            cov_8o8wd8h3p().s[161]++;
            token.email = user.email;
            /* istanbul ignore next */
            cov_8o8wd8h3p().s[162]++;
            token.name = user.name;
          } else
          /* istanbul ignore next */
          {
            cov_8o8wd8h3p().b[67][1]++;
          }
          // Update last activity timestamp less aggressively
          cov_8o8wd8h3p().s[163]++;
          if (
          /* istanbul ignore next */
          (cov_8o8wd8h3p().b[69][0]++, !token.lastActivity) ||
          /* istanbul ignore next */
          (cov_8o8wd8h3p().b[69][1]++, now - Number(token.lastActivity) > 60 * 60)) {
            /* istanbul ignore next */
            cov_8o8wd8h3p().b[68][0]++;
            cov_8o8wd8h3p().s[164]++;
            // Only update every hour
            token.lastActivity = now;
          } else
          /* istanbul ignore next */
          {
            cov_8o8wd8h3p().b[68][1]++;
          }
          cov_8o8wd8h3p().s[165]++;
          return [2 /*return*/, token];
        });
      });
    },
    session: function (_a) {
      /* istanbul ignore next */
      cov_8o8wd8h3p().f[23]++;
      cov_8o8wd8h3p().s[166]++;
      return __awaiter(this, arguments, Promise, function (_b) {
        /* istanbul ignore next */
        cov_8o8wd8h3p().f[24]++;
        var session =
          /* istanbul ignore next */
          (cov_8o8wd8h3p().s[167]++, _b.session),
          token =
          /* istanbul ignore next */
          (cov_8o8wd8h3p().s[168]++, _b.token);
        /* istanbul ignore next */
        cov_8o8wd8h3p().s[169]++;
        return __generator(this, function (_c) {
          /* istanbul ignore next */
          cov_8o8wd8h3p().f[25]++;
          cov_8o8wd8h3p().s[170]++;
          if (
          /* istanbul ignore next */
          (cov_8o8wd8h3p().b[71][0]++, session.user) &&
          /* istanbul ignore next */
          (cov_8o8wd8h3p().b[71][1]++, token.id)) {
            /* istanbul ignore next */
            cov_8o8wd8h3p().b[70][0]++;
            cov_8o8wd8h3p().s[171]++;
            session.user.id = token.id;
            /* istanbul ignore next */
            cov_8o8wd8h3p().s[172]++;
            if (token.email) {
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[72][0]++;
              cov_8o8wd8h3p().s[173]++;
              session.user.email = token.email;
            } else
            /* istanbul ignore next */
            {
              cov_8o8wd8h3p().b[72][1]++;
            }
            cov_8o8wd8h3p().s[174]++;
            if (token.name) {
              /* istanbul ignore next */
              cov_8o8wd8h3p().b[73][0]++;
              cov_8o8wd8h3p().s[175]++;
              session.user.name = token.name;
            } else
            /* istanbul ignore next */
            {
              cov_8o8wd8h3p().b[73][1]++;
            }
          } else
          /* istanbul ignore next */
          {
            cov_8o8wd8h3p().b[70][1]++;
          }
          // Add session security metadata
          cov_8o8wd8h3p().s[176]++;
          session.sessionId = token.sessionId;
          /* istanbul ignore next */
          cov_8o8wd8h3p().s[177]++;
          session.lastActivity = token.lastActivity;
          /* istanbul ignore next */
          cov_8o8wd8h3p().s[178]++;
          return [2 /*return*/, session];
        });
      });
    }
  },
  events: {
    createUser: function (message) {
      /* istanbul ignore next */
      cov_8o8wd8h3p().f[26]++;
      cov_8o8wd8h3p().s[179]++;
      return __awaiter(this, void 0, void 0, function () {
        /* istanbul ignore next */
        cov_8o8wd8h3p().f[27]++;
        cov_8o8wd8h3p().s[180]++;
        return __generator(this, function (_a) {
          /* istanbul ignore next */
          cov_8o8wd8h3p().f[28]++;
          cov_8o8wd8h3p().s[181]++;
          console.log("User created:", message.user.email);
          /* istanbul ignore next */
          cov_8o8wd8h3p().s[182]++;
          return [2 /*return*/];
        });
      });
    }
  },
  pages: {
    signIn: '/login'
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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