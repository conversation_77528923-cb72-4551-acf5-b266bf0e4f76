{"version": 3, "names": ["credentials_1", "cov_8o8wd8h3p", "s", "__importDefault", "require", "email_1", "prisma_adapter_1", "bcryptjs_1", "crypto_1", "prisma_1", "email_2", "VerificationEmail_1", "SESSION_MAX_AGE", "SESSION_UPDATE_AGE", "JWT_MAX_AGE", "COOKIE_MAX_AGE", "exports", "authOptions", "adapter", "PrismaAdapter", "default", "providers", "name", "credentials", "email", "label", "type", "password", "authorize", "f", "Promise", "b", "user", "findUnique", "where", "_a", "sent", "lockedUntil", "Date", "Error", "compare", "isPasswordValid", "failedAttempts", "failed<PERSON><PERSON>in<PERSON><PERSON><PERSON>s", "maxAttempts", "lockoutDuration", "update", "id", "data", "now", "emailVerified", "process", "env", "NODE_ENV", "server", "host", "EMAIL_SERVER_HOST", "port", "parseInt", "EMAIL_SERVER_PORT", "auth", "EMAIL_SERVER_USER", "pass", "EMAIL_SERVER_PASSWORD", "from", "EMAIL_FROM", "sendVerificationRequest", "__awaiter", "_b", "identifier", "url", "_c", "sendEmail", "to", "subject", "template", "jsx_runtime_1", "jsx", "VerificationEmail", "username", "verificationLink", "console", "log", "concat", "warn", "error", "error_1", "session", "strategy", "maxAge", "updateAge", "jwt", "cookies", "sessionToken", "options", "httpOnly", "sameSite", "path", "secure", "domain", "undefined", "callbackUrl", "csrfToken", "callbacks", "token", "trigger", "Math", "floor", "sessionId", "randomUUID", "iat", "lastActivity", "sessionRegenerationInterval", "Number", "events", "createUser", "message", "pages", "signIn"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/auth.tsx"], "sourcesContent": ["import { NextAuthOptions, User as NextAuthUser, Session } from \"next-auth\";\nimport { JWT } from \"next-auth/jwt\";\nimport CredentialsProvider from \"next-auth/providers/credentials\";\nimport EmailProvider from \"next-auth/providers/email\";\nimport { PrismaAdapter } from \"@auth/prisma-adapter\";\nimport bcrypt from \"bcryptjs\";\nimport crypto from \"crypto\";\nimport prisma from '@/lib/prisma';\nimport { sendEmail } from '@/lib/email';\nimport { VerificationEmail } from '@/emails/VerificationEmail';\n\n// Session configuration constants - centralized to ensure consistency\nconst SESSION_MAX_AGE = 30 * 24 * 60 * 60; // 30 days in seconds\nconst SESSION_UPDATE_AGE = 24 * 60 * 60; // 24 hours in seconds\nconst JWT_MAX_AGE = 30 * 24 * 60 * 60; // 30 days in seconds\nconst COOKIE_MAX_AGE = 30 * 24 * 60 * 60; // 30 days in seconds\n\n// Augment the NextAuthUser type to include id\ninterface User extends NextAuthUser {\n  id: string;\n}\n\n// Augment the Session.user type\ninterface ExtendedSession extends Session {\n  user?: User & {\n    id: string;\n  };\n}\n\n// Augment the JWT type to include id, email, and name\ninterface ExtendedJWT extends JWT {\n  id: string;\n  email: string;\n  name: string;\n}\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma),\n  providers: [\n    CredentialsProvider({\n      name: \"Credentials\",\n      credentials: {\n        email: { label: \"Email\", type: \"email\" },\n        password: { label: \"Password\", type: \"password\" }\n      },\n      async authorize(credentials): Promise<User | null> {\n        if (!credentials?.email || !credentials?.password) {\n          return null;\n        }\n\n        const user = await prisma.user.findUnique({\n          where: { email: credentials.email }\n        });\n\n        if (!user) {\n          return null;\n        }\n\n        // Check if account is locked\n        if (user.lockedUntil && user.lockedUntil > new Date()) {\n          throw new Error('Account is temporarily locked due to too many failed login attempts. Please try again later.');\n        }\n\n        const isPasswordValid = await bcrypt.compare(credentials.password, user.password);\n\n        if (!isPasswordValid) {\n          // Increment failed login attempts\n          const failedAttempts = user.failedLoginAttempts + 1;\n          const maxAttempts = 5;\n          const lockoutDuration = 15 * 60 * 1000; // 15 minutes in milliseconds\n\n          if (failedAttempts >= maxAttempts) {\n            // Lock the account\n            await prisma.user.update({\n              where: { id: user.id },\n              data: {\n                failedLoginAttempts: failedAttempts,\n                lockedUntil: new Date(Date.now() + lockoutDuration)\n              }\n            });\n            throw new Error('Account locked due to too many failed login attempts. Please try again in 15 minutes.');\n          } else {\n            // Update failed attempts count\n            await prisma.user.update({\n              where: { id: user.id },\n              data: {\n                failedLoginAttempts: failedAttempts\n              }\n            });\n          }\n          return null;\n        }\n\n        // Check if email is verified (bypass in development)\n        if (!user.emailVerified && process.env.NODE_ENV === 'production') {\n          throw new Error('Please verify your email address before signing in. Check your inbox for a verification link.');\n        }\n\n        // Reset failed login attempts on successful login\n        if (user.failedLoginAttempts > 0 || user.lockedUntil) {\n          await prisma.user.update({\n            where: { id: user.id },\n            data: {\n              failedLoginAttempts: 0,\n              lockedUntil: null\n            }\n          });\n        }\n\n        return { id: user.id, email: user.email, name: user.name } as User;\n      }\n    }),\n    EmailProvider({\n      server: {\n        host: process.env.EMAIL_SERVER_HOST,\n        port: parseInt(process.env.EMAIL_SERVER_PORT || \"587\"), // Default to 587 if not set\n        auth: {\n          user: process.env.EMAIL_SERVER_USER,\n          pass: process.env.EMAIL_SERVER_PASSWORD,\n        },\n      },\n      from: process.env.EMAIL_FROM || '<EMAIL>',\n      sendVerificationRequest: async ({ identifier: email, url }) => {\n        try {\n          const user = await prisma.user.findUnique({\n            where: { email },\n          });\n          if (user) {\n            await sendEmail({\n              to: email,\n              subject: \"Verify your email for FAAFO Career Platform\",\n              template: <VerificationEmail username={user.name || email} verificationLink={url} />,\n            });\n            console.log(`Verification email sent to ${email}`);\n          } else {\n            console.warn(`Attempted to send verification email to non-existent user via magic link: ${email}`);\n          }\n        } catch (error) {\n          console.error(`Failed to send verification email to ${email}:`, error);\n        }\n      },\n    }),\n  ],\n  session: {\n    strategy: \"jwt\",\n    maxAge: SESSION_MAX_AGE, // 30 days for better persistence\n    updateAge: SESSION_UPDATE_AGE, // 24 hours - less aggressive regeneration\n  },\n  jwt: {\n    maxAge: JWT_MAX_AGE, // 30 days for better persistence\n  },\n  cookies: {\n    sessionToken: {\n      name: `next-auth.session-token`,\n      options: {\n        httpOnly: true,\n        sameSite: 'lax', // Less strict for better compatibility\n        path: '/',\n        secure: process.env.NODE_ENV === 'production', // HTTPS only in production\n        maxAge: COOKIE_MAX_AGE, // 30 days\n        domain: process.env.NODE_ENV === 'development' ? 'localhost' : undefined, // Explicit domain for dev\n      },\n    },\n    callbackUrl: {\n      name: `next-auth.callback-url`,\n      options: {\n        httpOnly: true,\n        sameSite: 'lax', // Less strict for better compatibility\n        path: '/',\n        secure: process.env.NODE_ENV === 'production',\n        domain: process.env.NODE_ENV === 'development' ? 'localhost' : undefined,\n      },\n    },\n    csrfToken: {\n      name: `next-auth.csrf-token`,\n      options: {\n        httpOnly: true,\n        sameSite: 'lax', // Less strict for better compatibility\n        path: '/',\n        secure: process.env.NODE_ENV === 'production',\n        domain: process.env.NODE_ENV === 'development' ? 'localhost' : undefined,\n      },\n    },\n  },\n  callbacks: {\n    async jwt({ token, user, trigger }): Promise<ExtendedJWT> {\n      const now = Math.floor(Date.now() / 1000);\n\n\n\n      // Regenerate session ID on login for security\n      if (trigger === 'signIn' || trigger === 'signUp') {\n        token.sessionId = crypto.randomUUID();\n        token.iat = now;\n        token.lastActivity = now;\n      }\n\n      // Check for session timeout (use configured session maxAge)\n      if (token.iat && typeof token.iat === 'number' && (now - token.iat) > SESSION_MAX_AGE) {\n        throw new Error('Session expired');\n      }\n\n      // Regenerate session ID less frequently for better persistence (every 7 days)\n      const sessionRegenerationInterval = 7 * 24 * 60 * 60; // 7 days\n      if (token.lastActivity && typeof token.lastActivity === 'number' && (now - token.lastActivity) > sessionRegenerationInterval) {\n        token.sessionId = crypto.randomUUID();\n        token.lastActivity = now;\n      } else if (token.lastActivity && typeof token.lastActivity === 'number') {\n        // Update last activity without regenerating session ID\n        token.lastActivity = now;\n      }\n\n      if (user) {\n        token.id = (user as User).id;\n        token.email = (user as User).email;\n        token.name = (user as User).name;\n      }\n\n      // Update last activity timestamp less aggressively\n      if (!token.lastActivity || (now - Number(token.lastActivity)) > (60 * 60)) { // Only update every hour\n        token.lastActivity = now;\n      }\n\n      return token as ExtendedJWT;\n    },\n    async session({ session, token }): Promise<ExtendedSession> {\n      if (session.user && token.id) {\n        (session.user as User).id = token.id as string;\n        if (token.email) {\n          (session.user as User).email = token.email as string;\n        }\n        if (token.name) {\n          (session.user as User).name = token.name as string;\n        }\n      }\n\n      // Add session security metadata\n      (session as any).sessionId = token.sessionId;\n      (session as any).lastActivity = token.lastActivity;\n\n      return session as ExtendedSession;\n    },\n  },\n  events: {\n    async createUser(message) {\n      console.log(\"User created:\", message.user.email);\n    },\n  },\n  pages: {\n    signIn: '/login',\n  }\n}; "], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAA,aAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,QAAAC,eAAA,CAAAC,OAAA;AACA,IAAAC,OAAA;AAAA;AAAA,CAAAJ,aAAA,GAAAC,CAAA,QAAAC,eAAA,CAAAC,OAAA;AACA,IAAAE,gBAAA;AAAA;AAAA,CAAAL,aAAA,GAAAC,CAAA,QAAAE,OAAA;AACA,IAAAG,UAAA;AAAA;AAAA,CAAAN,aAAA,GAAAC,CAAA,QAAAC,eAAA,CAAAC,OAAA;AACA,IAAAI,QAAA;AAAA;AAAA,CAAAP,aAAA,GAAAC,CAAA,QAAAC,eAAA,CAAAC,OAAA;AACA,IAAAK,QAAA;AAAA;AAAA,CAAAR,aAAA,GAAAC,CAAA,QAAAC,eAAA,CAAAC,OAAA;AACA,IAAAM,OAAA;AAAA;AAAA,CAAAT,aAAA,GAAAC,CAAA,QAAAE,OAAA;AACA,IAAAO,mBAAA;AAAA;AAAA,CAAAV,aAAA,GAAAC,CAAA,QAAAE,OAAA;AAEA;AACA,IAAMQ,eAAe;AAAA;AAAA,CAAAX,aAAA,GAAAC,CAAA,QAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAC,CAAC;AAC3C,IAAMW,kBAAkB;AAAA;AAAA,CAAAZ,aAAA,GAAAC,CAAA,QAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAC,CAAC;AACzC,IAAMY,WAAW;AAAA;AAAA,CAAAb,aAAA,GAAAC,CAAA,QAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAC,CAAC;AACvC,IAAMa,cAAc;AAAA;AAAA,CAAAd,aAAA,GAAAC,CAAA,QAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAC,CAAC;AAAA;AAAAD,aAAA,GAAAC,CAAA;AAqB7Bc,OAAA,CAAAC,WAAW,GAAoB;EAC1CC,OAAO,EAAE,IAAAZ,gBAAA,CAAAa,aAAa,EAACV,QAAA,CAAAW,OAAM,CAAC;EAC9BC,SAAS,EAAE,CACT,IAAArB,aAAA,CAAAoB,OAAmB,EAAC;IAClBE,IAAI,EAAE,aAAa;IACnBC,WAAW,EAAE;MACXC,KAAK,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE;MAAO,CAAE;MACxCC,QAAQ,EAAE;QAAEF,KAAK,EAAE,UAAU;QAAEC,IAAI,EAAE;MAAU;KAChD;IACKE,SAAS,WAAAA,CAACL,WAAW;MAAA;MAAAtB,aAAA,GAAA4B,CAAA;MAAA5B,aAAA,GAAAC,CAAA;qCAAG4B,OAAO;QAAA;QAAA7B,aAAA,GAAA4B,CAAA;;;;;;;;;;;;;cACnC;cAAI;cAAA,CAAA5B,aAAA,GAAA8B,CAAA;cAAC;cAAA,CAAA9B,aAAA,GAAA8B,CAAA,WAAAR,WAAW;cAAA;cAAA,CAAAtB,aAAA,GAAA8B,CAAA,WAAXR,WAAW;cAAA;cAAA,CAAAtB,aAAA,GAAA8B,CAAA;cAAA;cAAA,CAAA9B,aAAA,GAAA8B,CAAA,WAAXR,WAAW,CAAEC,KAAK;cAAA;cAAA,CAAAvB,aAAA,GAAA8B,CAAA,WAAI;cAAC;cAAA,CAAA9B,aAAA,GAAA8B,CAAA,WAAAR,WAAW;cAAA;cAAA,CAAAtB,aAAA,GAAA8B,CAAA,WAAXR,WAAW;cAAA;cAAA,CAAAtB,aAAA,GAAA8B,CAAA;cAAA;cAAA,CAAA9B,aAAA,GAAA8B,CAAA,WAAXR,WAAW,CAAEI,QAAQ,KAAE;gBAAA;gBAAA1B,aAAA,GAAA8B,CAAA;gBAAA9B,aAAA,GAAAC,CAAA;gBACjD,sBAAO,IAAI;cACb,CAAC;cAAA;cAAA;gBAAAD,aAAA,GAAA8B,CAAA;cAAA;cAAA9B,aAAA,GAAAC,CAAA;cAEY,qBAAMO,QAAA,CAAAW,OAAM,CAACY,IAAI,CAACC,UAAU,CAAC;gBACxCC,KAAK,EAAE;kBAAEV,KAAK,EAAED,WAAW,CAACC;gBAAK;eAClC,CAAC;;;;;cAFIQ,IAAI,GAAGG,EAAA,CAAAC,IAAA,EAEX;cAAA;cAAAnC,aAAA,GAAAC,CAAA;cAEF,IAAI,CAAC8B,IAAI,EAAE;gBAAA;gBAAA/B,aAAA,GAAA8B,CAAA;gBAAA9B,aAAA,GAAAC,CAAA;gBACT,sBAAO,IAAI;cACb,CAAC;cAAA;cAAA;gBAAAD,aAAA,GAAA8B,CAAA;cAAA;cAED;cAAA9B,aAAA,GAAAC,CAAA;cACA;cAAI;cAAA,CAAAD,aAAA,GAAA8B,CAAA,WAAAC,IAAI,CAACK,WAAW;cAAA;cAAA,CAAApC,aAAA,GAAA8B,CAAA,WAAIC,IAAI,CAACK,WAAW,GAAG,IAAIC,IAAI,EAAE,GAAE;gBAAA;gBAAArC,aAAA,GAAA8B,CAAA;gBAAA9B,aAAA,GAAAC,CAAA;gBACrD,MAAM,IAAIqC,KAAK,CAAC,8FAA8F,CAAC;cACjH,CAAC;cAAA;cAAA;gBAAAtC,aAAA,GAAA8B,CAAA;cAAA;cAAA9B,aAAA,GAAAC,CAAA;cAEuB,qBAAMK,UAAA,CAAAa,OAAM,CAACoB,OAAO,CAACjB,WAAW,CAACI,QAAQ,EAAEK,IAAI,CAACL,QAAQ,CAAC;;;;;cAA3Ec,eAAe,GAAGN,EAAA,CAAAC,IAAA,EAAyD;cAAA;cAAAnC,aAAA,GAAAC,CAAA;mBAE7E,CAACuC,eAAe,EAAhB;gBAAA;gBAAAxC,aAAA,GAAA8B,CAAA;gBAAA9B,aAAA,GAAAC,CAAA;gBAAA;cAAA,CAAgB;cAAA;cAAA;gBAAAD,aAAA,GAAA8B,CAAA;cAAA;cAAA9B,aAAA,GAAAC,CAAA;cAEZwC,cAAc,GAAGV,IAAI,CAACW,mBAAmB,GAAG,CAAC;cAAC;cAAA1C,aAAA,GAAAC,CAAA;cAC9C0C,WAAW,GAAG,CAAC;cAAC;cAAA3C,aAAA,GAAAC,CAAA;cAChB2C,eAAe,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;cAAC;cAAA5C,aAAA,GAAAC,CAAA;oBAEnCwC,cAAc,IAAIE,WAAW,GAA7B;gBAAA;gBAAA3C,aAAA,GAAA8B,CAAA;gBAAA9B,aAAA,GAAAC,CAAA;gBAAA;cAAA,CAA6B;cAAA;cAAA;gBAAAD,aAAA,GAAA8B,CAAA;cAAA;cAC/B;cAAA9B,aAAA,GAAAC,CAAA;cACA,qBAAMO,QAAA,CAAAW,OAAM,CAACY,IAAI,CAACc,MAAM,CAAC;gBACvBZ,KAAK,EAAE;kBAAEa,EAAE,EAAEf,IAAI,CAACe;gBAAE,CAAE;gBACtBC,IAAI,EAAE;kBACJL,mBAAmB,EAAED,cAAc;kBACnCL,WAAW,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACW,GAAG,EAAE,GAAGJ,eAAe;;eAErD,CAAC;;;;;cAPF;cACAV,EAAA,CAAAC,IAAA,EAME;cAAC;cAAAnC,aAAA,GAAAC,CAAA;cACH,MAAM,IAAIqC,KAAK,CAAC,uFAAuF,CAAC;;;;;cAExG;cACA,qBAAM9B,QAAA,CAAAW,OAAM,CAACY,IAAI,CAACc,MAAM,CAAC;gBACvBZ,KAAK,EAAE;kBAAEa,EAAE,EAAEf,IAAI,CAACe;gBAAE,CAAE;gBACtBC,IAAI,EAAE;kBACJL,mBAAmB,EAAED;;eAExB,CAAC;;;;;cANF;cACAP,EAAA,CAAAC,IAAA,EAKE;cAAC;cAAAnC,aAAA,GAAAC,CAAA;;;;;;cAEL,sBAAO,IAAI;;;;;cAGb;cACA;cAAI;cAAA,CAAAD,aAAA,GAAA8B,CAAA,YAACC,IAAI,CAACkB,aAAa;cAAA;cAAA,CAAAjD,aAAA,GAAA8B,CAAA,WAAIoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAE;gBAAA;gBAAApD,aAAA,GAAA8B,CAAA;gBAAA9B,aAAA,GAAAC,CAAA;gBAChE,MAAM,IAAIqC,KAAK,CAAC,+FAA+F,CAAC;cAClH,CAAC;cAAA;cAAA;gBAAAtC,aAAA,GAAA8B,CAAA;cAAA;cAAA9B,aAAA,GAAAC,CAAA;;cAGG;cAAA,CAAAD,aAAA,GAAA8B,CAAA,WAAAC,IAAI,CAACW,mBAAmB,GAAG,CAAC;cAAA;cAAA,CAAA1C,aAAA,GAAA8B,CAAA,WAAIC,IAAI,CAACK,WAAW,IAAhD;gBAAA;gBAAApC,aAAA,GAAA8B,CAAA;gBAAA9B,aAAA,GAAAC,CAAA;gBAAA;cAAA,CAAgD;cAAA;cAAA;gBAAAD,aAAA,GAAA8B,CAAA;cAAA;cAAA9B,aAAA,GAAAC,CAAA;cAClD,qBAAMO,QAAA,CAAAW,OAAM,CAACY,IAAI,CAACc,MAAM,CAAC;gBACvBZ,KAAK,EAAE;kBAAEa,EAAE,EAAEf,IAAI,CAACe;gBAAE,CAAE;gBACtBC,IAAI,EAAE;kBACJL,mBAAmB,EAAE,CAAC;kBACtBN,WAAW,EAAE;;eAEhB,CAAC;;;;;cANFF,EAAA,CAAAC,IAAA,EAME;cAAC;cAAAnC,aAAA,GAAAC,CAAA;;;;;;cAGL,sBAAO;gBAAE6C,EAAE,EAAEf,IAAI,CAACe,EAAE;gBAAEvB,KAAK,EAAEQ,IAAI,CAACR,KAAK;gBAAEF,IAAI,EAAEU,IAAI,CAACV;cAAI,CAAU;;;;;GAErE,CAAC,EACF,IAAAjB,OAAA,CAAAe,OAAa,EAAC;IACZkC,MAAM,EAAE;MACNC,IAAI,EAAEJ,OAAO,CAACC,GAAG,CAACI,iBAAiB;MACnCC,IAAI,EAAEC,QAAQ;MAAC;MAAA,CAAAzD,aAAA,GAAA8B,CAAA,WAAAoB,OAAO,CAACC,GAAG,CAACO,iBAAiB;MAAA;MAAA,CAAA1D,aAAA,GAAA8B,CAAA,WAAI,KAAK,EAAC;MAAE;MACxD6B,IAAI,EAAE;QACJ5B,IAAI,EAAEmB,OAAO,CAACC,GAAG,CAACS,iBAAiB;QACnCC,IAAI,EAAEX,OAAO,CAACC,GAAG,CAACW;;KAErB;IACDC,IAAI;IAAE;IAAA,CAAA/D,aAAA,GAAA8B,CAAA,WAAAoB,OAAO,CAACC,GAAG,CAACa,UAAU;IAAA;IAAA,CAAAhE,aAAA,GAAA8B,CAAA,WAAI,uBAAuB;IACvDmC,uBAAuB,EAAE,SAAAA,CAAA/B,EAAA;MAAA;MAAAlC,aAAA,GAAA4B,CAAA;MAAA5B,aAAA,GAAAC,CAAA;MAAA,OAAAiE,SAAA,UAAAhC,EAAA,qBAAOiC,EAA0B;QAAA;QAAAnE,aAAA,GAAA4B,CAAA;;YAAZL,KAAK;UAAA;UAAA,CAAAvB,aAAA,GAAAC,CAAA,SAAAkE,EAAA,CAAAC,UAAA;UAAEC,GAAG;UAAA;UAAA,CAAArE,aAAA,GAAAC,CAAA,SAAAkE,EAAA,CAAAE,GAAA;QAAA;QAAArE,aAAA,GAAAC,CAAA;;;;;;;;;;;;;cAEvC,qBAAMO,QAAA,CAAAW,OAAM,CAACY,IAAI,CAACC,UAAU,CAAC;gBACxCC,KAAK,EAAE;kBAAEV,KAAK,EAAAA;gBAAA;eACf,CAAC;;;;;cAFIQ,IAAI,GAAGuC,EAAA,CAAAnC,IAAA,EAEX;cAAA;cAAAnC,aAAA,GAAAC,CAAA;mBACE8B,IAAI,EAAJ;gBAAA;gBAAA/B,aAAA,GAAA8B,CAAA;gBAAA9B,aAAA,GAAAC,CAAA;gBAAA;cAAA,CAAI;cAAA;cAAA;gBAAAD,aAAA,GAAA8B,CAAA;cAAA;cAAA9B,aAAA,GAAAC,CAAA;cACN,qBAAM,IAAAQ,OAAA,CAAA8D,SAAS,EAAC;gBACdC,EAAE,EAAEjD,KAAK;gBACTkD,OAAO,EAAE,6CAA6C;gBACtDC,QAAQ,EAAE,IAAAC,aAAA,CAAAC,GAAA,EAAClE,mBAAA,CAAAmE,iBAAiB;kBAACC,QAAQ;kBAAE;kBAAA,CAAA9E,aAAA,GAAA8B,CAAA,WAAAC,IAAI,CAACV,IAAI;kBAAA;kBAAA,CAAArB,aAAA,GAAA8B,CAAA,WAAIP,KAAK;kBAAEwD,gBAAgB,EAAEV;gBAAG;eACjF,CAAC;;;;;cAJFC,EAAA,CAAAnC,IAAA,EAIE;cAAC;cAAAnC,aAAA,GAAAC,CAAA;cACH+E,OAAO,CAACC,GAAG,CAAC,8BAAAC,MAAA,CAA8B3D,KAAK,CAAE,CAAC;cAAC;cAAAvB,aAAA,GAAAC,CAAA;;;;;;cAEnD+E,OAAO,CAACG,IAAI,CAAC,6EAAAD,MAAA,CAA6E3D,KAAK,CAAE,CAAC;cAAC;cAAAvB,aAAA,GAAAC,CAAA;;;;;;;;;;;;;;cAGrG+E,OAAO,CAACI,KAAK,CAAC,wCAAAF,MAAA,CAAwC3D,KAAK,MAAG,EAAE8D,OAAK,CAAC;cAAC;cAAArF,aAAA,GAAAC,CAAA;;;;;;;;;;;GAG5E,CAAC,CACH;EACDqF,OAAO,EAAE;IACPC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE7E,eAAe;IAAE;IACzB8E,SAAS,EAAE7E,kBAAkB,CAAE;GAChC;EACD8E,GAAG,EAAE;IACHF,MAAM,EAAE3E,WAAW,CAAE;GACtB;EACD8E,OAAO,EAAE;IACPC,YAAY,EAAE;MACZvE,IAAI,EAAE,yBAAyB;MAC/BwE,OAAO,EAAE;QACPC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,KAAK;QAAE;QACjBC,IAAI,EAAE,GAAG;QACTC,MAAM,EAAE/C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;QAAE;QAC/CoC,MAAM,EAAE1E,cAAc;QAAE;QACxBoF,MAAM,EAAEhD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa;QAAA;QAAA,CAAApD,aAAA,GAAA8B,CAAA,WAAG,WAAW;QAAA;QAAA,CAAA9B,aAAA,GAAA8B,CAAA,WAAGqE,SAAS,EAAE;;KAE7E;IACDC,WAAW,EAAE;MACX/E,IAAI,EAAE,wBAAwB;MAC9BwE,OAAO,EAAE;QACPC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,KAAK;QAAE;QACjBC,IAAI,EAAE,GAAG;QACTC,MAAM,EAAE/C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;QAC7C8C,MAAM,EAAEhD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa;QAAA;QAAA,CAAApD,aAAA,GAAA8B,CAAA,WAAG,WAAW;QAAA;QAAA,CAAA9B,aAAA,GAAA8B,CAAA,WAAGqE,SAAS;;KAE3E;IACDE,SAAS,EAAE;MACThF,IAAI,EAAE,sBAAsB;MAC5BwE,OAAO,EAAE;QACPC,QAAQ,EAAE,IAAI;QACdC,QAAQ,EAAE,KAAK;QAAE;QACjBC,IAAI,EAAE,GAAG;QACTC,MAAM,EAAE/C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;QAC7C8C,MAAM,EAAEhD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa;QAAA;QAAA,CAAApD,aAAA,GAAA8B,CAAA,WAAG,WAAW;QAAA;QAAA,CAAA9B,aAAA,GAAA8B,CAAA,WAAGqE,SAAS;;;GAG7E;EACDG,SAAS,EAAE;IACHZ,GAAG,WAAAA,CAAAxD,EAAA;MAAA;MAAAlC,aAAA,GAAA4B,CAAA;MAAA5B,aAAA,GAAAC,CAAA;wCAA4B4B,OAAO,YAAlCsC,EAAwB;QAAA;QAAAnE,aAAA,GAAA4B,CAAA;;YAAtB2E,KAAK;UAAA;UAAA,CAAAvG,aAAA,GAAAC,CAAA,SAAAkE,EAAA,CAAAoC,KAAA;UAAExE,IAAI;UAAA;UAAA,CAAA/B,aAAA,GAAAC,CAAA,SAAAkE,EAAA,CAAApC,IAAA;UAAEyE,OAAO;UAAA;UAAA,CAAAxG,aAAA,GAAAC,CAAA,SAAAkE,EAAA,CAAAqC,OAAA;QAAA;QAAAxG,aAAA,GAAAC,CAAA;;;;;UACxB+C,GAAG,GAAGyD,IAAI,CAACC,KAAK,CAACrE,IAAI,CAACW,GAAG,EAAE,GAAG,IAAI,CAAC;UAIzC;UAAA;UAAAhD,aAAA,GAAAC,CAAA;UACA;UAAI;UAAA,CAAAD,aAAA,GAAA8B,CAAA,WAAA0E,OAAO,KAAK,QAAQ;UAAA;UAAA,CAAAxG,aAAA,GAAA8B,CAAA,WAAI0E,OAAO,KAAK,QAAQ,GAAE;YAAA;YAAAxG,aAAA,GAAA8B,CAAA;YAAA9B,aAAA,GAAAC,CAAA;YAChDsG,KAAK,CAACI,SAAS,GAAGpG,QAAA,CAAAY,OAAM,CAACyF,UAAU,EAAE;YAAC;YAAA5G,aAAA,GAAAC,CAAA;YACtCsG,KAAK,CAACM,GAAG,GAAG7D,GAAG;YAAC;YAAAhD,aAAA,GAAAC,CAAA;YAChBsG,KAAK,CAACO,YAAY,GAAG9D,GAAG;UAC1B,CAAC;UAAA;UAAA;YAAAhD,aAAA,GAAA8B,CAAA;UAAA;UAED;UAAA9B,aAAA,GAAAC,CAAA;UACA;UAAI;UAAA,CAAAD,aAAA,GAAA8B,CAAA,WAAAyE,KAAK,CAACM,GAAG;UAAA;UAAA,CAAA7G,aAAA,GAAA8B,CAAA,WAAI,OAAOyE,KAAK,CAACM,GAAG,KAAK,QAAQ;UAAA;UAAA,CAAA7G,aAAA,GAAA8B,CAAA,WAAKkB,GAAG,GAAGuD,KAAK,CAACM,GAAG,GAAIlG,eAAe,GAAE;YAAA;YAAAX,aAAA,GAAA8B,CAAA;YAAA9B,aAAA,GAAAC,CAAA;YACrF,MAAM,IAAIqC,KAAK,CAAC,iBAAiB,CAAC;UACpC,CAAC;UAAA;UAAA;YAAAtC,aAAA,GAAA8B,CAAA;UAAA;UAAA9B,aAAA,GAAAC,CAAA;UAGK8G,2BAA2B,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;UAAC;UAAA/G,aAAA,GAAAC,CAAA;UACrD;UAAI;UAAA,CAAAD,aAAA,GAAA8B,CAAA,WAAAyE,KAAK,CAACO,YAAY;UAAA;UAAA,CAAA9G,aAAA,GAAA8B,CAAA,WAAI,OAAOyE,KAAK,CAACO,YAAY,KAAK,QAAQ;UAAA;UAAA,CAAA9G,aAAA,GAAA8B,CAAA,WAAKkB,GAAG,GAAGuD,KAAK,CAACO,YAAY,GAAIC,2BAA2B,GAAE;YAAA;YAAA/G,aAAA,GAAA8B,CAAA;YAAA9B,aAAA,GAAAC,CAAA;YAC5HsG,KAAK,CAACI,SAAS,GAAGpG,QAAA,CAAAY,OAAM,CAACyF,UAAU,EAAE;YAAC;YAAA5G,aAAA,GAAAC,CAAA;YACtCsG,KAAK,CAACO,YAAY,GAAG9D,GAAG;UAC1B,CAAC,MAAM;YAAA;YAAAhD,aAAA,GAAA8B,CAAA;YAAA9B,aAAA,GAAAC,CAAA;YAAA;YAAI;YAAA,CAAAD,aAAA,GAAA8B,CAAA,WAAAyE,KAAK,CAACO,YAAY;YAAA;YAAA,CAAA9G,aAAA,GAAA8B,CAAA,WAAI,OAAOyE,KAAK,CAACO,YAAY,KAAK,QAAQ,GAAE;cAAA;cAAA9G,aAAA,GAAA8B,CAAA;cAAA9B,aAAA,GAAAC,CAAA;cACvE;cACAsG,KAAK,CAACO,YAAY,GAAG9D,GAAG;YAC1B,CAAC;YAAA;YAAA;cAAAhD,aAAA,GAAA8B,CAAA;YAAA;UAAD;UAAC;UAAA9B,aAAA,GAAAC,CAAA;UAED,IAAI8B,IAAI,EAAE;YAAA;YAAA/B,aAAA,GAAA8B,CAAA;YAAA9B,aAAA,GAAAC,CAAA;YACRsG,KAAK,CAACzD,EAAE,GAAIf,IAAa,CAACe,EAAE;YAAC;YAAA9C,aAAA,GAAAC,CAAA;YAC7BsG,KAAK,CAAChF,KAAK,GAAIQ,IAAa,CAACR,KAAK;YAAC;YAAAvB,aAAA,GAAAC,CAAA;YACnCsG,KAAK,CAAClF,IAAI,GAAIU,IAAa,CAACV,IAAI;UAClC,CAAC;UAAA;UAAA;YAAArB,aAAA,GAAA8B,CAAA;UAAA;UAED;UAAA9B,aAAA,GAAAC,CAAA;UACA;UAAI;UAAA,CAAAD,aAAA,GAAA8B,CAAA,YAACyE,KAAK,CAACO,YAAY;UAAA;UAAA,CAAA9G,aAAA,GAAA8B,CAAA,WAAKkB,GAAG,GAAGgE,MAAM,CAACT,KAAK,CAACO,YAAY,CAAC,GAAK,EAAE,GAAG,EAAG,GAAE;YAAA;YAAA9G,aAAA,GAAA8B,CAAA;YAAA9B,aAAA,GAAAC,CAAA;YAAE;YAC3EsG,KAAK,CAACO,YAAY,GAAG9D,GAAG;UAC1B,CAAC;UAAA;UAAA;YAAAhD,aAAA,GAAA8B,CAAA;UAAA;UAAA9B,aAAA,GAAAC,CAAA;UAED,sBAAOsG,KAAoB;;;KAC5B;IACKjB,OAAO,WAAAA,CAAApD,EAAA;MAAA;MAAAlC,aAAA,GAAA4B,CAAA;MAAA5B,aAAA,GAAAC,CAAA;wCAAsB4B,OAAO,YAA5BsC,EAAkB;QAAA;QAAAnE,aAAA,GAAA4B,CAAA;YAAhB0D,OAAO;UAAA;UAAA,CAAAtF,aAAA,GAAAC,CAAA,SAAAkE,EAAA,CAAAmB,OAAA;UAAEiB,KAAK;UAAA;UAAA,CAAAvG,aAAA,GAAAC,CAAA,SAAAkE,EAAA,CAAAoC,KAAA;QAAA;QAAAvG,aAAA,GAAAC,CAAA;;;;;UAC5B;UAAI;UAAA,CAAAD,aAAA,GAAA8B,CAAA,WAAAwD,OAAO,CAACvD,IAAI;UAAA;UAAA,CAAA/B,aAAA,GAAA8B,CAAA,WAAIyE,KAAK,CAACzD,EAAE,GAAE;YAAA;YAAA9C,aAAA,GAAA8B,CAAA;YAAA9B,aAAA,GAAAC,CAAA;YAC3BqF,OAAO,CAACvD,IAAa,CAACe,EAAE,GAAGyD,KAAK,CAACzD,EAAY;YAAC;YAAA9C,aAAA,GAAAC,CAAA;YAC/C,IAAIsG,KAAK,CAAChF,KAAK,EAAE;cAAA;cAAAvB,aAAA,GAAA8B,CAAA;cAAA9B,aAAA,GAAAC,CAAA;cACdqF,OAAO,CAACvD,IAAa,CAACR,KAAK,GAAGgF,KAAK,CAAChF,KAAe;YACtD,CAAC;YAAA;YAAA;cAAAvB,aAAA,GAAA8B,CAAA;YAAA;YAAA9B,aAAA,GAAAC,CAAA;YACD,IAAIsG,KAAK,CAAClF,IAAI,EAAE;cAAA;cAAArB,aAAA,GAAA8B,CAAA;cAAA9B,aAAA,GAAAC,CAAA;cACbqF,OAAO,CAACvD,IAAa,CAACV,IAAI,GAAGkF,KAAK,CAAClF,IAAc;YACpD,CAAC;YAAA;YAAA;cAAArB,aAAA,GAAA8B,CAAA;YAAA;UACH,CAAC;UAAA;UAAA;YAAA9B,aAAA,GAAA8B,CAAA;UAAA;UAED;UAAA9B,aAAA,GAAAC,CAAA;UACCqF,OAAe,CAACqB,SAAS,GAAGJ,KAAK,CAACI,SAAS;UAAC;UAAA3G,aAAA,GAAAC,CAAA;UAC5CqF,OAAe,CAACwB,YAAY,GAAGP,KAAK,CAACO,YAAY;UAAC;UAAA9G,aAAA,GAAAC,CAAA;UAEnD,sBAAOqF,OAA0B;;;;GAEpC;EACD2B,MAAM,EAAE;IACAC,UAAU,WAAAA,CAACC,OAAO;MAAA;MAAAnH,aAAA,GAAA4B,CAAA;MAAA5B,aAAA,GAAAC,CAAA;;;;;;;;;UACtB+E,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEkC,OAAO,CAACpF,IAAI,CAACR,KAAK,CAAC;UAAC;UAAAvB,aAAA,GAAAC,CAAA;;;;;GAEpD;EACDmH,KAAK,EAAE;IACLC,MAAM,EAAE;;CAEX", "ignoreList": []}