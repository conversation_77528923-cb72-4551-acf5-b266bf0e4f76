796c1f4e16d64c135f3e9261cdd814d2
"use strict";
/**
 * Security Vulnerability Assessment Tests - VDD Protocol
 *
 * Following Verification-Driven Development protocol to identify and prove
 * security vulnerabilities in the FAAFO Career Platform.
 *
 * Test Categories:
 * 1. Authentication & Authorization Bypasses
 * 2. Input Validation & Injection Vulnerabilities
 * 3. CSRF Protection Gaps
 * 4. Session Management Issues
 * 5. API Security Flaws
 * 6. Data Access Control Violations
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
// Mock dependencies
jest.mock('next-auth/next');
jest.mock('@/lib/prisma', function () { return ({
    user: {
        findUnique: jest.fn(),
        findFirst: jest.fn(),
        create: jest.fn(),
        update: jest.fn(),
    },
    verificationToken: {
        findUnique: jest.fn(),
        delete: jest.fn(),
    },
    profile: {
        upsert: jest.fn(),
    },
}); });
var server_1 = require("next/server");
var next_1 = require("next-auth/next");
var prisma_1 = __importDefault(require("@/lib/prisma"));
var mockGetServerSession = next_1.getServerSession;
var mockPrisma = prisma_1.default;
describe('Security Vulnerability Assessment - VDD Protocol', function () {
    beforeEach(function () {
        jest.clearAllMocks();
    });
    describe('1. Authentication & Authorization Bypass Vulnerabilities', function () {
        test('VULNERABILITY: Password reset endpoint allows token enumeration', function () { return __awaiter(void 0, void 0, void 0, function () {
            var POST, request, startTime, response, endTime, responseTime;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, Promise.resolve().then(function () { return __importStar(require('@/app/api/auth/reset-password/route')); })];
                    case 1:
                        POST = (_a.sent()).POST;
                        // Mock invalid token scenario
                        mockPrisma.user.findFirst.mockResolvedValue(null);
                        request = new server_1.NextRequest('http://localhost:3000/api/auth/reset-password', {
                            method: 'POST',
                            body: JSON.stringify({
                                token: 'invalid-token-12345',
                                password: 'newPassword123!'
                            }),
                        });
                        startTime = Date.now();
                        return [4 /*yield*/, POST(request)];
                    case 2:
                        response = _a.sent();
                        endTime = Date.now();
                        responseTime = endTime - startTime;
                        // VULNERABILITY: Response time should be consistent regardless of token validity
                        // to prevent timing attacks for token enumeration
                        expect(response.status).toBe(400);
                        // This test proves the vulnerability exists - response times may vary
                        // allowing attackers to enumerate valid tokens
                        console.log("Password reset response time: ".concat(responseTime, "ms"));
                        return [2 /*return*/];
                }
            });
        }); });
        test('VULNERABILITY: Email verification endpoint missing rate limiting', function () { return __awaiter(void 0, void 0, void 0, function () {
            var POST, request, response, response2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, Promise.resolve().then(function () { return __importStar(require('@/app/api/auth/verify-email/route')); })];
                    case 1:
                        POST = (_a.sent()).POST;
                        mockPrisma.verificationToken.findUnique.mockResolvedValue(null);
                        request = new server_1.NextRequest('http://localhost:3000/api/auth/verify-email', {
                            method: 'POST',
                            body: JSON.stringify({
                                token: 'test-token',
                                email: '<EMAIL>'
                            }),
                        });
                        return [4 /*yield*/, POST(request)];
                    case 2:
                        response = _a.sent();
                        expect(response.status).toBe(400);
                        return [4 /*yield*/, POST(request)];
                    case 3:
                        response2 = _a.sent();
                        expect(response2.status).toBe(400);
                        return [2 /*return*/];
                }
            });
        }); });
        test('VULNERABILITY: Admin check bypassed through session manipulation', function () { return __awaiter(void 0, void 0, void 0, function () {
            var GET, request, response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, Promise.resolve().then(function () { return __importStar(require('@/app/api/admin/database/route')); })];
                    case 1:
                        GET = (_a.sent()).GET;
                        // Mock non-admin user session
                        mockGetServerSession.mockResolvedValue({
                            user: { id: 'user123', email: '<EMAIL>' },
                            expires: new Date(Date.now() + 86400000).toISOString(),
                        });
                        // Mock isUserAdmin to return false
                        jest.doMock('@/lib/auth', function () { return (__assign(__assign({}, jest.requireActual('@/lib/auth')), { isUserAdmin: jest.fn().mockResolvedValue(false) })); });
                        request = new server_1.NextRequest('http://localhost:3000/api/admin/database');
                        return [4 /*yield*/, GET(request)];
                    case 2:
                        response = _a.sent();
                        expect(response.status).toBe(403);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('2. Input Validation & Injection Vulnerabilities', function () {
        test('VULNERABILITY: SQL injection through unsanitized user input', function () { return __awaiter(void 0, void 0, void 0, function () {
            var maliciousInput, PUT, request, response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        maliciousInput = "'; DROP TABLE users; --";
                        return [4 /*yield*/, Promise.resolve().then(function () { return __importStar(require('@/app/api/profile/route')); })];
                    case 1:
                        PUT = (_a.sent()).PUT;
                        mockGetServerSession.mockResolvedValue({
                            user: { id: 'user123', email: '<EMAIL>' },
                            expires: new Date(Date.now() + 86400000).toISOString(),
                        });
                        mockPrisma.user.findUnique.mockResolvedValue({
                            id: 'user123',
                            email: '<EMAIL>',
                            name: 'Test User',
                        });
                        request = new server_1.NextRequest('http://localhost:3000/api/profile', {
                            method: 'PUT',
                            headers: {
                                'x-csrf-token': 'valid-token',
                                'content-type': 'application/json',
                            },
                            body: JSON.stringify({
                                name: maliciousInput,
                                bio: 'Normal bio text',
                            }),
                        });
                        return [4 /*yield*/, PUT(request)];
                    case 2:
                        response = _a.sent();
                        // The endpoint should reject malicious input, but validation might be incomplete
                        console.log('Profile update response status:', response.status);
                        return [2 /*return*/];
                }
            });
        }); });
        test('VULNERABILITY: XSS through unsanitized HTML content', function () { return __awaiter(void 0, void 0, void 0, function () {
            var xssPayload, PUT, request, response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        xssPayload = '<script>alert("XSS")</script>';
                        return [4 /*yield*/, Promise.resolve().then(function () { return __importStar(require('@/app/api/profile/route')); })];
                    case 1:
                        PUT = (_a.sent()).PUT;
                        mockGetServerSession.mockResolvedValue({
                            user: { id: 'user123', email: '<EMAIL>' },
                            expires: new Date(Date.now() + 86400000).toISOString(),
                        });
                        mockPrisma.user.findUnique.mockResolvedValue({
                            id: 'user123',
                            email: '<EMAIL>',
                            name: 'Test User',
                        });
                        request = new server_1.NextRequest('http://localhost:3000/api/profile', {
                            method: 'PUT',
                            headers: {
                                'x-csrf-token': 'valid-token',
                                'content-type': 'application/json',
                            },
                            body: JSON.stringify({
                                bio: xssPayload,
                                location: 'Test Location',
                            }),
                        });
                        return [4 /*yield*/, PUT(request)];
                    case 2:
                        response = _a.sent();
                        // VULNERABILITY: XSS payload might not be properly sanitized
                        console.log('XSS test response status:', response.status);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('3. CSRF Protection Gaps', function () {
        test('VULNERABILITY: CSRF token validation inconsistencies', function () { return __awaiter(void 0, void 0, void 0, function () {
            var POST, request, response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, Promise.resolve().then(function () { return __importStar(require('@/app/api/achievements/route')); })];
                    case 1:
                        POST = (_a.sent()).POST;
                        mockGetServerSession.mockResolvedValue({
                            user: { id: 'user123', email: '<EMAIL>' },
                            expires: new Date(Date.now() + 86400000).toISOString(),
                        });
                        request = new server_1.NextRequest('http://localhost:3000/api/achievements', {
                            method: 'POST',
                            body: JSON.stringify({
                                achievementId: 'test-achievement',
                                progress: 100,
                            }),
                        });
                        return [4 /*yield*/, POST(request)];
                    case 2:
                        response = _a.sent();
                        // VULNERABILITY: Should return 403 for missing CSRF token
                        expect(response.status).toBe(403);
                        return [2 /*return*/];
                }
            });
        }); });
        test('VULNERABILITY: CSRF token reuse across sessions', function () { return __awaiter(void 0, void 0, void 0, function () {
            var _a, getCSRFToken, validateCSRFToken, request1, request2, token1, token2, isValid;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, Promise.resolve().then(function () { return __importStar(require('@/lib/csrf')); })];
                    case 1:
                        _a = _b.sent(), getCSRFToken = _a.getCSRFToken, validateCSRFToken = _a.validateCSRFToken;
                        // Mock different user sessions
                        mockGetServerSession
                            .mockResolvedValueOnce({
                            user: { id: 'user1', email: '<EMAIL>' },
                            expires: new Date(Date.now() + 86400000).toISOString(),
                        })
                            .mockResolvedValueOnce({
                            user: { id: 'user2', email: '<EMAIL>' },
                            expires: new Date(Date.now() + 86400000).toISOString(),
                        });
                        request1 = new server_1.NextRequest('http://localhost:3000/test');
                        request2 = new server_1.NextRequest('http://localhost:3000/test');
                        return [4 /*yield*/, getCSRFToken(request1)];
                    case 2:
                        token1 = _b.sent();
                        return [4 /*yield*/, getCSRFToken(request2)];
                    case 3:
                        token2 = _b.sent();
                        // VULNERABILITY: Tokens should be unique per session
                        expect(token1).not.toBe(token2);
                        return [4 /*yield*/, validateCSRFToken(request2, token1)];
                    case 4:
                        isValid = _b.sent();
                        // SECURITY ISSUE: Token from user1 should not be valid for user2
                        expect(isValid).toBe(false);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('4. Session Management Vulnerabilities', function () {
        test('VULNERABILITY: Session fixation attack possible', function () { return __awaiter(void 0, void 0, void 0, function () {
            var GET, request, response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, Promise.resolve().then(function () { return __importStar(require('@/app/api/auth/validate-session/route')); })];
                    case 1:
                        GET = (_a.sent()).GET;
                        // Mock session with fixed session ID
                        mockGetServerSession.mockResolvedValue({
                            user: { id: 'user123', email: '<EMAIL>' },
                            expires: new Date(Date.now() + 86400000).toISOString(),
                        });
                        mockPrisma.user.findUnique.mockResolvedValue({
                            id: 'user123',
                            email: '<EMAIL>',
                            name: 'Test User',
                            emailVerified: new Date(),
                            createdAt: new Date(),
                            updatedAt: new Date(),
                        });
                        request = new server_1.NextRequest('http://localhost:3000/api/auth/validate-session');
                        return [4 /*yield*/, GET(request)];
                    case 2:
                        response = _a.sent();
                        expect(response.status).toBe(200);
                        return [2 /*return*/];
                }
            });
        }); });
        test('VULNERABILITY: Concurrent session handling', function () { return __awaiter(void 0, void 0, void 0, function () {
            var GET, request, _a, response1, response2;
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, Promise.resolve().then(function () { return __importStar(require('@/app/api/auth/validate-session/route')); })];
                    case 1:
                        GET = (_b.sent()).GET;
                        mockGetServerSession.mockResolvedValue({
                            user: { id: 'user123', email: '<EMAIL>' },
                            expires: new Date(Date.now() + 86400000).toISOString(),
                        });
                        mockPrisma.user.findUnique.mockResolvedValue({
                            id: 'user123',
                            email: '<EMAIL>',
                            name: 'Test User',
                            emailVerified: new Date(),
                            createdAt: new Date(),
                            updatedAt: new Date(),
                        });
                        request = new server_1.NextRequest('http://localhost:3000/api/auth/validate-session');
                        return [4 /*yield*/, Promise.all([
                                GET(request),
                                GET(request),
                            ])];
                    case 2:
                        _a = _b.sent(), response1 = _a[0], response2 = _a[1];
                        expect(response1.status).toBe(200);
                        expect(response2.status).toBe(200);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('5. File Upload Security Vulnerabilities', function () {
        test('VULNERABILITY: File upload without proper validation', function () { return __awaiter(void 0, void 0, void 0, function () {
            var POST, maliciousFile, formData, request, response;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, Promise.resolve().then(function () { return __importStar(require('@/app/api/profile/photo/route')); })];
                    case 1:
                        POST = (_a.sent()).POST;
                        mockGetServerSession.mockResolvedValue({
                            user: { id: 'user123', email: '<EMAIL>' },
                            expires: new Date(Date.now() + 86400000).toISOString(),
                        });
                        mockPrisma.user.findUnique.mockResolvedValue({
                            id: 'user123',
                            email: '<EMAIL>',
                            name: 'Test User',
                        });
                        maliciousFile = new File(['<?php echo "hacked"; ?>'], 'malicious.php', {
                            type: 'image/jpeg', // Disguised as image
                        });
                        formData = new FormData();
                        formData.append('file', maliciousFile);
                        request = new server_1.NextRequest('http://localhost:3000/api/profile/photo', {
                            method: 'POST',
                            headers: {
                                'x-csrf-token': 'valid-token',
                            },
                            body: formData,
                        });
                        return [4 /*yield*/, POST(request)];
                    case 2:
                        response = _a.sent();
                        // VULNERABILITY: File type validation might be bypassable
                        console.log('File upload response status:', response.status);
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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