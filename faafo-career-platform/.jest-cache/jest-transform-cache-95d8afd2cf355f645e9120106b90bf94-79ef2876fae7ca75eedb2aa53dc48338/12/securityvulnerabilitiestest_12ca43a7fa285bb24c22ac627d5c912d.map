{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/security/security-vulnerabilities.test.ts", "mappings": ";AAAA;;;;;;;;;;;;;GAaG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOH,oBAAoB;AACpB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAC5B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,cAAM,OAAA,CAAC;IAC/B,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE;QACpB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;QACjB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;KAClB;IACD,iBAAiB,EAAE;QACjB,UAAU,EAAE,IAAI,CAAC,EAAE,EAAE;QACrB,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;KAClB;IACD,OAAO,EAAE;QACP,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;KAClB;CACF,CAAC,EAd8B,CAc9B,CAAC,CAAC;AArBJ,sCAAwD;AACxD,uCAAkD;AAElD,wDAAkC;AAoBlC,IAAM,oBAAoB,GAAG,uBAAgE,CAAC;AAC9F,IAAM,UAAU,GAAG,gBAAoC,CAAC;AAExD,QAAQ,CAAC,kDAAkD,EAAE;IAC3D,UAAU,CAAC;QACT,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0DAA0D,EAAE;QACnE,IAAI,CAAC,iEAAiE,EAAE;;;;4BAErD,sFAAa,qCAAqC,QAAC;;wBAA5D,IAAI,GAAK,CAAA,SAAmD,CAAA,KAAxD;wBAEZ,8BAA8B;wBAC9B,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAE5C,OAAO,GAAG,IAAI,oBAAW,CAAC,+CAA+C,EAAE;4BAC/E,MAAM,EAAE,MAAM;4BACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,KAAK,EAAE,qBAAqB;gCAC5B,QAAQ,EAAE,iBAAiB;6BAC5B,CAAC;yBACH,CAAC,CAAC;wBAEG,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBACZ,qBAAM,IAAI,CAAC,OAAO,CAAC,EAAA;;wBAA9B,QAAQ,GAAG,SAAmB;wBAC9B,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBACrB,YAAY,GAAG,OAAO,GAAG,SAAS,CAAC;wBAEzC,iFAAiF;wBACjF,kDAAkD;wBAClD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAElC,sEAAsE;wBACtE,+CAA+C;wBAC/C,OAAO,CAAC,GAAG,CAAC,wCAAiC,YAAY,OAAI,CAAC,CAAC;;;;aAIhE,CAAC,CAAC;QAEH,IAAI,CAAC,kEAAkE,EAAE;;;;4BAEtD,sFAAa,mCAAmC,QAAC;;wBAA1D,IAAI,GAAK,CAAA,SAAiD,CAAA,KAAtD;wBAEZ,UAAU,CAAC,iBAAiB,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;wBAE1D,OAAO,GAAG,IAAI,oBAAW,CAAC,6CAA6C,EAAE;4BAC7E,MAAM,EAAE,MAAM;4BACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,KAAK,EAAE,YAAY;gCACnB,KAAK,EAAE,kBAAkB;6BAC1B,CAAC;yBACH,CAAC,CAAC;wBAGc,qBAAM,IAAI,CAAC,OAAO,CAAC,EAAA;;wBAA9B,QAAQ,GAAG,SAAmB;wBACpC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAGhB,qBAAM,IAAI,CAAC,OAAO,CAAC,EAAA;;wBAA/B,SAAS,GAAG,SAAmB;wBACrC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;;;aAGpC,CAAC,CAAC;QAEH,IAAI,CAAC,kEAAkE,EAAE;;;;4BAEvD,sFAAa,gCAAgC,QAAC;;wBAAtD,GAAG,GAAK,CAAA,SAA8C,CAAA,IAAnD;wBAEX,8BAA8B;wBAC9B,oBAAoB,CAAC,iBAAiB,CAAC;4BACrC,IAAI,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE;4BAClD,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,WAAW,EAAE;yBACvD,CAAC,CAAC;wBAEH,mCAAmC;wBACnC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,cAAM,OAAA,uBAC3B,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,KACnC,WAAW,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAC/C,EAH8B,CAG9B,CAAC,CAAC;wBAEE,OAAO,GAAG,IAAI,oBAAW,CAAC,0CAA0C,CAAC,CAAC;wBAC3D,qBAAM,GAAG,CAAC,OAAO,CAAC,EAAA;;wBAA7B,QAAQ,GAAG,SAAkB;wBAEnC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;;;aAInC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,iDAAiD,EAAE;QAC1D,IAAI,CAAC,6DAA6D,EAAE;;;;;wBAE5D,cAAc,GAAG,yBAAyB,CAAC;wBAGjC,sFAAa,yBAAyB,QAAC;;wBAA/C,GAAG,GAAK,CAAA,SAAuC,CAAA,IAA5C;wBAEX,oBAAoB,CAAC,iBAAiB,CAAC;4BACrC,IAAI,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE;4BAClD,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,WAAW,EAAE;yBACvD,CAAC,CAAC;wBAEH,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC;4BAC3C,EAAE,EAAE,SAAS;4BACb,KAAK,EAAE,kBAAkB;4BACzB,IAAI,EAAE,WAAW;yBAClB,CAAC,CAAC;wBAEG,OAAO,GAAG,IAAI,oBAAW,CAAC,mCAAmC,EAAE;4BACnE,MAAM,EAAE,KAAK;4BACb,OAAO,EAAE;gCACP,cAAc,EAAE,aAAa;gCAC7B,cAAc,EAAE,kBAAkB;6BACnC;4BACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,IAAI,EAAE,cAAc;gCACpB,GAAG,EAAE,iBAAiB;6BACvB,CAAC;yBACH,CAAC,CAAC;wBAGc,qBAAM,GAAG,CAAC,OAAO,CAAC,EAAA;;wBAA7B,QAAQ,GAAG,SAAkB;wBAEnC,iFAAiF;wBACjF,OAAO,CAAC,GAAG,CAAC,iCAAiC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;;;;aAGjE,CAAC,CAAC;QAEH,IAAI,CAAC,qDAAqD,EAAE;;;;;wBAEpD,UAAU,GAAG,+BAA+B,CAAC;wBAEnC,sFAAa,yBAAyB,QAAC;;wBAA/C,GAAG,GAAK,CAAA,SAAuC,CAAA,IAA5C;wBAEX,oBAAoB,CAAC,iBAAiB,CAAC;4BACrC,IAAI,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE;4BAClD,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,WAAW,EAAE;yBACvD,CAAC,CAAC;wBAEH,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC;4BAC3C,EAAE,EAAE,SAAS;4BACb,KAAK,EAAE,kBAAkB;4BACzB,IAAI,EAAE,WAAW;yBAClB,CAAC,CAAC;wBAEG,OAAO,GAAG,IAAI,oBAAW,CAAC,mCAAmC,EAAE;4BACnE,MAAM,EAAE,KAAK;4BACb,OAAO,EAAE;gCACP,cAAc,EAAE,aAAa;gCAC7B,cAAc,EAAE,kBAAkB;6BACnC;4BACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,GAAG,EAAE,UAAU;gCACf,QAAQ,EAAE,eAAe;6BAC1B,CAAC;yBACH,CAAC,CAAC;wBAEc,qBAAM,GAAG,CAAC,OAAO,CAAC,EAAA;;wBAA7B,QAAQ,GAAG,SAAkB;wBAEnC,6DAA6D;wBAC7D,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;;;;aAG3D,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE;QAClC,IAAI,CAAC,sDAAsD,EAAE;;;;4BAE1C,sFAAa,8BAA8B,QAAC;;wBAArD,IAAI,GAAK,CAAA,SAA4C,CAAA,KAAjD;wBAEZ,oBAAoB,CAAC,iBAAiB,CAAC;4BACrC,IAAI,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE;4BAClD,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,WAAW,EAAE;yBACvD,CAAC,CAAC;wBAGG,OAAO,GAAG,IAAI,oBAAW,CAAC,wCAAwC,EAAE;4BACxE,MAAM,EAAE,MAAM;4BACd,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,aAAa,EAAE,kBAAkB;gCACjC,QAAQ,EAAE,GAAG;6BACd,CAAC;yBACH,CAAC,CAAC;wBAEc,qBAAM,IAAI,CAAC,OAAO,CAAC,EAAA;;wBAA9B,QAAQ,GAAG,SAAmB;wBAEpC,0DAA0D;wBAC1D,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;;;aAGnC,CAAC,CAAC;QAEH,IAAI,CAAC,iDAAiD,EAAE;;;;4BAEV,sFAAa,YAAY,QAAC;;wBAAhE,KAAsC,SAA0B,EAA9D,YAAY,kBAAA,EAAE,iBAAiB,uBAAA;wBAEvC,+BAA+B;wBAC/B,oBAAoB;6BACjB,qBAAqB,CAAC;4BACrB,IAAI,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,mBAAmB,EAAE;4BACjD,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,WAAW,EAAE;yBACvD,CAAC;6BACD,qBAAqB,CAAC;4BACrB,IAAI,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,mBAAmB,EAAE;4BACjD,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,WAAW,EAAE;yBACvD,CAAC,CAAC;wBAEC,QAAQ,GAAG,IAAI,oBAAW,CAAC,4BAA4B,CAAC,CAAC;wBACzD,QAAQ,GAAG,IAAI,oBAAW,CAAC,4BAA4B,CAAC,CAAC;wBAEhD,qBAAM,YAAY,CAAC,QAAQ,CAAC,EAAA;;wBAArC,MAAM,GAAG,SAA4B;wBAC5B,qBAAM,YAAY,CAAC,QAAQ,CAAC,EAAA;;wBAArC,MAAM,GAAG,SAA4B;wBAE3C,qDAAqD;wBACrD,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAGhB,qBAAM,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,EAAA;;wBAAnD,OAAO,GAAG,SAAyC;wBAEzD,iEAAiE;wBACjE,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;;;aAC7B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,uCAAuC,EAAE;QAChD,IAAI,CAAC,iDAAiD,EAAE;;;;4BAEtC,sFAAa,uCAAuC,QAAC;;wBAA7D,GAAG,GAAK,CAAA,SAAqD,CAAA,IAA1D;wBAEX,qCAAqC;wBACrC,oBAAoB,CAAC,iBAAiB,CAAC;4BACrC,IAAI,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE;4BAClD,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,WAAW,EAAE;yBACvD,CAAC,CAAC;wBAEH,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC;4BAC3C,EAAE,EAAE,SAAS;4BACb,KAAK,EAAE,kBAAkB;4BACzB,IAAI,EAAE,WAAW;4BACjB,aAAa,EAAE,IAAI,IAAI,EAAE;4BACzB,SAAS,EAAE,IAAI,IAAI,EAAE;4BACrB,SAAS,EAAE,IAAI,IAAI,EAAE;yBACtB,CAAC,CAAC;wBAEG,OAAO,GAAG,IAAI,oBAAW,CAAC,iDAAiD,CAAC,CAAC;wBAClE,qBAAM,GAAG,CAAC,OAAO,CAAC,EAAA;;wBAA7B,QAAQ,GAAG,SAAkB;wBAEnC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;;;aAInC,CAAC,CAAC;QAEH,IAAI,CAAC,4CAA4C,EAAE;;;;4BAEjC,sFAAa,uCAAuC,QAAC;;wBAA7D,GAAG,GAAK,CAAA,SAAqD,CAAA,IAA1D;wBAEX,oBAAoB,CAAC,iBAAiB,CAAC;4BACrC,IAAI,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE;4BAClD,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,WAAW,EAAE;yBACvD,CAAC,CAAC;wBAEH,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC;4BAC3C,EAAE,EAAE,SAAS;4BACb,KAAK,EAAE,kBAAkB;4BACzB,IAAI,EAAE,WAAW;4BACjB,aAAa,EAAE,IAAI,IAAI,EAAE;4BACzB,SAAS,EAAE,IAAI,IAAI,EAAE;4BACrB,SAAS,EAAE,IAAI,IAAI,EAAE;yBACtB,CAAC,CAAC;wBAGG,OAAO,GAAG,IAAI,oBAAW,CAAC,iDAAiD,CAAC,CAAC;wBAEpD,qBAAM,OAAO,CAAC,GAAG,CAAC;gCAC/C,GAAG,CAAC,OAAO,CAAC;gCACZ,GAAG,CAAC,OAAO,CAAC;6BACb,CAAC,EAAA;;wBAHI,KAAyB,SAG7B,EAHK,SAAS,QAAA,EAAE,SAAS,QAAA;wBAK3B,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBACnC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;;;aAIpC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yCAAyC,EAAE;QAClD,IAAI,CAAC,sDAAsD,EAAE;;;;4BAE1C,sFAAa,+BAA+B,QAAC;;wBAAtD,IAAI,GAAK,CAAA,SAA6C,CAAA,KAAlD;wBAEZ,oBAAoB,CAAC,iBAAiB,CAAC;4BACrC,IAAI,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,kBAAkB,EAAE;4BAClD,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,QAAQ,CAAC,CAAC,WAAW,EAAE;yBACvD,CAAC,CAAC;wBAEH,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC;4BAC3C,EAAE,EAAE,SAAS;4BACb,KAAK,EAAE,kBAAkB;4BACzB,IAAI,EAAE,WAAW;yBAClB,CAAC,CAAC;wBAGG,aAAa,GAAG,IAAI,IAAI,CAAC,CAAC,yBAAyB,CAAC,EAAE,eAAe,EAAE;4BAC3E,IAAI,EAAE,YAAY,EAAE,qBAAqB;yBAC1C,CAAC,CAAC;wBAEG,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;wBAChC,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;wBAEjC,OAAO,GAAG,IAAI,oBAAW,CAAC,yCAAyC,EAAE;4BACzE,MAAM,EAAE,MAAM;4BACd,OAAO,EAAE;gCACP,cAAc,EAAE,aAAa;6BAC9B;4BACD,IAAI,EAAE,QAAQ;yBACf,CAAC,CAAC;wBAEc,qBAAM,IAAI,CAAC,OAAO,CAAC,EAAA;;wBAA9B,QAAQ,GAAG,SAAmB;wBAEpC,0DAA0D;wBAC1D,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;;;;aAQ9D,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/security/security-vulnerabilities.test.ts"], "sourcesContent": ["/**\n * Security Vulnerability Assessment Tests - VDD Protocol\n * \n * Following Verification-Driven Development protocol to identify and prove\n * security vulnerabilities in the FAAFO Career Platform.\n * \n * Test Categories:\n * 1. Authentication & Authorization Bypasses\n * 2. Input Validation & Injection Vulnerabilities  \n * 3. CSRF Protection Gaps\n * 4. Session Management Issues\n * 5. API Security Flaws\n * 6. Data Access Control Violations\n */\n\nimport { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport prisma from '@/lib/prisma';\n\n// Mock dependencies\njest.mock('next-auth/next');\njest.mock('@/lib/prisma', () => ({\n  user: {\n    findUnique: jest.fn(),\n    findFirst: jest.fn(),\n    create: jest.fn(),\n    update: jest.fn(),\n  },\n  verificationToken: {\n    findUnique: jest.fn(),\n    delete: jest.fn(),\n  },\n  profile: {\n    upsert: jest.fn(),\n  },\n}));\n\nconst mockGetServerSession = getServerSession as jest.MockedFunction<typeof getServerSession>;\nconst mockPrisma = prisma as jest.Mocked<typeof prisma>;\n\ndescribe('Security Vulnerability Assessment - VDD Protocol', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('1. Authentication & Authorization Bypass Vulnerabilities', () => {\n    test('VULNERABILITY: Password reset endpoint allows token enumeration', async () => {\n      // Test for timing attack vulnerability in password reset\n      const { POST } = await import('@/app/api/auth/reset-password/route');\n      \n      // Mock invalid token scenario\n      mockPrisma.user.findFirst.mockResolvedValue(null);\n      \n      const request = new NextRequest('http://localhost:3000/api/auth/reset-password', {\n        method: 'POST',\n        body: JSON.stringify({\n          token: 'invalid-token-12345',\n          password: 'newPassword123!'\n        }),\n      });\n\n      const startTime = Date.now();\n      const response = await POST(request);\n      const endTime = Date.now();\n      const responseTime = endTime - startTime;\n\n      // VULNERABILITY: Response time should be consistent regardless of token validity\n      // to prevent timing attacks for token enumeration\n      expect(response.status).toBe(400);\n      \n      // This test proves the vulnerability exists - response times may vary\n      // allowing attackers to enumerate valid tokens\n      console.log(`Password reset response time: ${responseTime}ms`);\n      \n      // SECURITY ISSUE: No rate limiting specifically for password reset attempts\n      // SECURITY ISSUE: Error messages may leak information about token validity\n    });\n\n    test('VULNERABILITY: Email verification endpoint missing rate limiting', async () => {\n      // Test for missing rate limiting on email verification\n      const { POST } = await import('@/app/api/auth/verify-email/route');\n      \n      mockPrisma.verificationToken.findUnique.mockResolvedValue(null);\n      \n      const request = new NextRequest('http://localhost:3000/api/auth/verify-email', {\n        method: 'POST',\n        body: JSON.stringify({\n          token: 'test-token',\n          email: '<EMAIL>'\n        }),\n      });\n\n      // VULNERABILITY: No rate limiting allows brute force attacks on verification tokens\n      const response = await POST(request);\n      expect(response.status).toBe(400);\n      \n      // Multiple rapid requests should be blocked but aren't\n      const response2 = await POST(request);\n      expect(response2.status).toBe(400);\n      \n      // SECURITY ISSUE: Unlimited verification attempts possible\n    });\n\n    test('VULNERABILITY: Admin check bypassed through session manipulation', async () => {\n      // Test for potential admin privilege escalation\n      const { GET } = await import('@/app/api/admin/database/route');\n      \n      // Mock non-admin user session\n      mockGetServerSession.mockResolvedValue({\n        user: { id: 'user123', email: '<EMAIL>' },\n        expires: new Date(Date.now() + 86400000).toISOString(),\n      });\n\n      // Mock isUserAdmin to return false\n      jest.doMock('@/lib/auth', () => ({\n        ...jest.requireActual('@/lib/auth'),\n        isUserAdmin: jest.fn().mockResolvedValue(false),\n      }));\n\n      const request = new NextRequest('http://localhost:3000/api/admin/database');\n      const response = await GET(request);\n      \n      expect(response.status).toBe(403);\n      \n      // VULNERABILITY: Admin checks may be inconsistent across endpoints\n      // Need to verify all admin endpoints use the same authorization logic\n    });\n  });\n\n  describe('2. Input Validation & Injection Vulnerabilities', () => {\n    test('VULNERABILITY: SQL injection through unsanitized user input', async () => {\n      // Test for potential SQL injection in user search or profile updates\n      const maliciousInput = \"'; DROP TABLE users; --\";\n      \n      // Mock profile update with malicious input\n      const { PUT } = await import('@/app/api/profile/route');\n      \n      mockGetServerSession.mockResolvedValue({\n        user: { id: 'user123', email: '<EMAIL>' },\n        expires: new Date(Date.now() + 86400000).toISOString(),\n      });\n\n      mockPrisma.user.findUnique.mockResolvedValue({\n        id: 'user123',\n        email: '<EMAIL>',\n        name: 'Test User',\n      });\n\n      const request = new NextRequest('http://localhost:3000/api/profile', {\n        method: 'PUT',\n        headers: {\n          'x-csrf-token': 'valid-token',\n          'content-type': 'application/json',\n        },\n        body: JSON.stringify({\n          name: maliciousInput,\n          bio: 'Normal bio text',\n        }),\n      });\n\n      // VULNERABILITY: Input validation may not catch all SQL injection attempts\n      const response = await PUT(request);\n      \n      // The endpoint should reject malicious input, but validation might be incomplete\n      console.log('Profile update response status:', response.status);\n      \n      // SECURITY ISSUE: Need to verify Prisma ORM prevents all injection types\n    });\n\n    test('VULNERABILITY: XSS through unsanitized HTML content', async () => {\n      // Test for XSS vulnerabilities in user-generated content\n      const xssPayload = '<script>alert(\"XSS\")</script>';\n      \n      const { PUT } = await import('@/app/api/profile/route');\n      \n      mockGetServerSession.mockResolvedValue({\n        user: { id: 'user123', email: '<EMAIL>' },\n        expires: new Date(Date.now() + 86400000).toISOString(),\n      });\n\n      mockPrisma.user.findUnique.mockResolvedValue({\n        id: 'user123',\n        email: '<EMAIL>',\n        name: 'Test User',\n      });\n\n      const request = new NextRequest('http://localhost:3000/api/profile', {\n        method: 'PUT',\n        headers: {\n          'x-csrf-token': 'valid-token',\n          'content-type': 'application/json',\n        },\n        body: JSON.stringify({\n          bio: xssPayload,\n          location: 'Test Location',\n        }),\n      });\n\n      const response = await PUT(request);\n      \n      // VULNERABILITY: XSS payload might not be properly sanitized\n      console.log('XSS test response status:', response.status);\n      \n      // SECURITY ISSUE: Need comprehensive XSS protection for all user inputs\n    });\n  });\n\n  describe('3. CSRF Protection Gaps', () => {\n    test('VULNERABILITY: CSRF token validation inconsistencies', async () => {\n      // Test for endpoints that might bypass CSRF protection\n      const { POST } = await import('@/app/api/achievements/route');\n      \n      mockGetServerSession.mockResolvedValue({\n        user: { id: 'user123', email: '<EMAIL>' },\n        expires: new Date(Date.now() + 86400000).toISOString(),\n      });\n\n      // Request without CSRF token\n      const request = new NextRequest('http://localhost:3000/api/achievements', {\n        method: 'POST',\n        body: JSON.stringify({\n          achievementId: 'test-achievement',\n          progress: 100,\n        }),\n      });\n\n      const response = await POST(request);\n      \n      // VULNERABILITY: Should return 403 for missing CSRF token\n      expect(response.status).toBe(403);\n      \n      // SECURITY ISSUE: Verify all state-changing endpoints require CSRF tokens\n    });\n\n    test('VULNERABILITY: CSRF token reuse across sessions', async () => {\n      // Test for CSRF token reuse vulnerability\n      const { getCSRFToken, validateCSRFToken } = await import('@/lib/csrf');\n      \n      // Mock different user sessions\n      mockGetServerSession\n        .mockResolvedValueOnce({\n          user: { id: 'user1', email: '<EMAIL>' },\n          expires: new Date(Date.now() + 86400000).toISOString(),\n        })\n        .mockResolvedValueOnce({\n          user: { id: 'user2', email: '<EMAIL>' },\n          expires: new Date(Date.now() + 86400000).toISOString(),\n        });\n\n      const request1 = new NextRequest('http://localhost:3000/test');\n      const request2 = new NextRequest('http://localhost:3000/test');\n\n      const token1 = await getCSRFToken(request1);\n      const token2 = await getCSRFToken(request2);\n\n      // VULNERABILITY: Tokens should be unique per session\n      expect(token1).not.toBe(token2);\n      \n      // Test cross-session token validation\n      const isValid = await validateCSRFToken(request2, token1);\n      \n      // SECURITY ISSUE: Token from user1 should not be valid for user2\n      expect(isValid).toBe(false);\n    });\n  });\n\n  describe('4. Session Management Vulnerabilities', () => {\n    test('VULNERABILITY: Session fixation attack possible', async () => {\n      // Test for session fixation vulnerability\n      const { GET } = await import('@/app/api/auth/validate-session/route');\n      \n      // Mock session with fixed session ID\n      mockGetServerSession.mockResolvedValue({\n        user: { id: 'user123', email: '<EMAIL>' },\n        expires: new Date(Date.now() + 86400000).toISOString(),\n      });\n\n      mockPrisma.user.findUnique.mockResolvedValue({\n        id: 'user123',\n        email: '<EMAIL>',\n        name: 'Test User',\n        emailVerified: new Date(),\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      });\n\n      const request = new NextRequest('http://localhost:3000/api/auth/validate-session');\n      const response = await GET(request);\n      \n      expect(response.status).toBe(200);\n      \n      // VULNERABILITY: Session ID should be regenerated on privilege changes\n      // SECURITY ISSUE: Need to verify session regeneration on login/role changes\n    });\n\n    test('VULNERABILITY: Concurrent session handling', async () => {\n      // Test for concurrent session security issues\n      const { GET } = await import('@/app/api/auth/validate-session/route');\n      \n      mockGetServerSession.mockResolvedValue({\n        user: { id: 'user123', email: '<EMAIL>' },\n        expires: new Date(Date.now() + 86400000).toISOString(),\n      });\n\n      mockPrisma.user.findUnique.mockResolvedValue({\n        id: 'user123',\n        email: '<EMAIL>',\n        name: 'Test User',\n        emailVerified: new Date(),\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      });\n\n      // Simulate concurrent session validation requests\n      const request = new NextRequest('http://localhost:3000/api/auth/validate-session');\n      \n      const [response1, response2] = await Promise.all([\n        GET(request),\n        GET(request),\n      ]);\n\n      expect(response1.status).toBe(200);\n      expect(response2.status).toBe(200);\n      \n      // VULNERABILITY: Concurrent sessions might cause race conditions\n      // SECURITY ISSUE: Need proper session locking mechanisms\n    });\n  });\n\n  describe('5. File Upload Security Vulnerabilities', () => {\n    test('VULNERABILITY: File upload without proper validation', async () => {\n      // Test for file upload security issues\n      const { POST } = await import('@/app/api/profile/photo/route');\n      \n      mockGetServerSession.mockResolvedValue({\n        user: { id: 'user123', email: '<EMAIL>' },\n        expires: new Date(Date.now() + 86400000).toISOString(),\n      });\n\n      mockPrisma.user.findUnique.mockResolvedValue({\n        id: 'user123',\n        email: '<EMAIL>',\n        name: 'Test User',\n      });\n\n      // Create malicious file upload\n      const maliciousFile = new File(['<?php echo \"hacked\"; ?>'], 'malicious.php', {\n        type: 'image/jpeg', // Disguised as image\n      });\n\n      const formData = new FormData();\n      formData.append('file', maliciousFile);\n\n      const request = new NextRequest('http://localhost:3000/api/profile/photo', {\n        method: 'POST',\n        headers: {\n          'x-csrf-token': 'valid-token',\n        },\n        body: formData,\n      });\n\n      const response = await POST(request);\n      \n      // VULNERABILITY: File type validation might be bypassable\n      console.log('File upload response status:', response.status);\n      \n      // SECURITY ISSUES:\n      // - File extension validation\n      // - MIME type verification\n      // - File content scanning\n      // - Upload size limits\n      // - Storage location security\n    });\n  });\n});\n"], "version": 3}