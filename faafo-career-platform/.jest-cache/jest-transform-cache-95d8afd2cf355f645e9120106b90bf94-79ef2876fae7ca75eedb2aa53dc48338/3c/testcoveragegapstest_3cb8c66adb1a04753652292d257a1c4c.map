{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/architecture/test-coverage-gaps.test.ts", "mappings": ";AAAA;;;;;;;GAOG;;;;;AAEH,yCAAuE;AACvE,0CAAoB;AACpB,8CAAwB;AAExB,IAAA,kBAAQ,EAAC,4BAA4B,EAAE;IACrC,IAAA,oBAAU,EAAC;QACT,cAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,2DAA2D,EAAE;QACpE,IAAA,YAAE,EAAC,2EAA2E,EAAE;YAC9E,0DAA0D;YAC1D,IAAM,sBAAsB,GAAG;gBAC7B,kBAAkB;gBAClB,6BAA6B;gBAC7B,uCAAuC;gBACvC,oCAAoC;gBACpC,8BAA8B;gBAC9B,+BAA+B;aAChC,CAAC;YAEF,IAAM,YAAY,GAAG,EAAE,CAAC;YACxB,IAAM,eAAe,GAAG,EAAE,CAAC;YAE3B,sBAAsB,CAAC,OAAO,CAAC,UAAA,SAAS;gBACtC,IAAM,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;gBACjE,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC;gBAEpD,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC7B,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC/B,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC;wBACH,IAAM,WAAW,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;wBACtD,IAAM,SAAS,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;wBAC5D,IAAM,WAAW,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;wBAElE,sEAAsE;wBACtE,IAAI,SAAS,GAAG,EAAE,IAAI,WAAW,GAAG,EAAE,EAAE,CAAC;4BACvC,eAAe,CAAC,IAAI,CAAC,EAAE,SAAS,WAAA,EAAE,SAAS,WAAA,EAAE,WAAW,aAAA,EAAE,CAAC,CAAC;wBAC9D,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC/B,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,iFAAiF;YACjF,IAAA,gBAAM,EAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,IAAA,gBAAM,EAAC,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,wDAAwD,EAAE;YAC3D,oDAAoD;YACpD,IAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,uBAAuB,CAAC,CAAC;YAEvE,IAAI,YAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBAChC,IAAM,aAAW,GAAG,YAAE,CAAC,YAAY,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBAE1D,0CAA0C;gBAC1C,IAAM,iBAAiB,GAAG;oBACxB,iBAAiB;oBACjB,eAAe;oBACf,kBAAkB;oBAClB,mBAAmB;oBACnB,aAAa;oBACb,eAAe;oBACf,iBAAiB;oBACjB,qBAAqB;oBACrB,iBAAiB;oBACjB,iBAAiB;iBAClB,CAAC;gBAEF,IAAM,gBAAgB,GAAG,iBAAiB,CAAC,MAAM,CAAC,UAAA,QAAQ;oBACxD,OAAA,CAAC,aAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;gBAA3D,CAA2D,CAC5D,CAAC;gBAEF,mEAAmE;gBACnE,IAAA,gBAAM,EAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC1C,CAAC;iBAAM,CAAC;gBACN,0DAA0D;gBAC1D,IAAA,gBAAM,EAAC,YAAE,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACjD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,4CAA4C,EAAE;QACrD,IAAA,YAAE,EAAC,0DAA0D,EAAE;YAC7D,uDAAuD;YACvD,IAAM,iBAAiB,GAAG;gBACxB,yCAAyC;gBACzC,iCAAiC;gBACjC,8BAA8B;gBAC9B,gCAAgC;gBAChC,gCAAgC;gBAChC,mCAAmC;gBACnC,kCAAkC;aACnC,CAAC;YAEF,IAAM,uBAAuB,GAAG,EAAE,CAAC;YAEnC,iBAAiB,CAAC,OAAO,CAAC,UAAA,KAAK;gBAC7B,IAAM,mBAAmB,GAAG,KAAK,CAAC,OAAO,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,sBAAsB,CAAC,CAAC;gBACnH,IAAM,mBAAmB,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,mBAAmB,CAAC,CAAC;gBAE1E,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC,EAAE,CAAC;oBACxC,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtC,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC;wBACH,IAAM,WAAW,GAAG,YAAE,CAAC,YAAY,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;wBAEjE,8EAA8E;wBAC9E,IAAM,gBAAgB,GAAG,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;wBAC5F,IAAM,cAAc,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;wBAEvE,IAAI,CAAC,gBAAgB,IAAI,cAAc,EAAE,CAAC;4BACxC,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACtC,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACtC,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,iFAAiF;YACjF,IAAA,gBAAM,EAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,2DAA2D,EAAE;YAC9D,mDAAmD;YACnD,IAAM,gBAAgB,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,eAAe,CAAC,CAAC;YACnE,IAAM,YAAY,GAAG,EAAE,CAAC;YAExB,IAAI,YAAE,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACpC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,gBAAgB,CAAC,CAAC;gBAC/C,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBAC5D,YAAY,CAAC,IAAI,CAAC,cAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC,CAAC;oBACvD,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,IAAM,iBAAiB,GAAG,EAAE,CAAC;YAE7B,+CAA+C;YAC/C,IAAM,sBAAsB,GAAG;gBAC7B,iBAAiB;gBACjB,kBAAkB;gBAClB,eAAe;gBACf,eAAe;gBACf,gBAAgB;gBAChB,oBAAoB;gBACpB,2BAA2B;gBAC3B,kBAAkB;gBAClB,eAAe;gBACf,eAAe;aAChB,CAAC;YAEF,YAAY,CAAC,OAAO,CAAC,UAAA,QAAQ;gBAC3B,IAAI,CAAC;oBACH,IAAM,aAAW,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;oBAEtD,IAAM,gBAAgB,GAAG,sBAAsB,CAAC,MAAM,CAAC,UAAA,QAAQ;wBAC7D,OAAA,CAAC,aAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;oBAA3D,CAA2D,CAC5D,CAAC;oBAEF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,yBAAyB;wBAC1D,iBAAiB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,gBAAgB,kBAAA,EAAE,CAAC,CAAC;oBAC/D,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,iBAAiB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC,CAAC;gBAC3E,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,2DAA2D;YAC3D,IAAA,gBAAM,EAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,gDAAgD,EAAE;QACzD,IAAA,YAAE,EAAC,mEAAmE,EAAE;YACtE,iCAAiC;YACjC,IAAM,mBAAmB,GAAG;gBAC1B,mCAAmC;gBACnC,gCAAgC;gBAChC,uCAAuC;gBACvC,6CAA6C;gBAC7C,0CAA0C;aAC3C,CAAC;YAEF,IAAM,iBAAiB,GAAG,EAAE,CAAC;YAE7B,mBAAmB,CAAC,OAAO,CAAC,UAAA,SAAS;gBACnC,IAAI,YAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC7B,IAAM,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;oBACxD,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC;oBAEpD,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC7B,iBAAiB,CAAC,IAAI,CAAC,EAAE,SAAS,WAAA,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC,CAAC;oBACtE,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC;4BACH,IAAM,aAAW,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAEtD,6CAA6C;4BAC7C,IAAM,eAAe,GAAG;gCACtB,kBAAkB;gCAClB,eAAe;gCACf,gBAAgB;gCAChB,qBAAqB;gCACrB,kBAAkB;gCAClB,gBAAgB;gCAChB,aAAa;gCACb,iBAAiB;6BAClB,CAAC;4BAEF,IAAM,YAAY,GAAG,eAAe,CAAC,MAAM,CAAC,UAAA,IAAI;gCAC9C,OAAA,CAAC,aAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;4BAAvD,CAAuD,CACxD,CAAC;4BAEF,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gCAC5B,iBAAiB,CAAC,IAAI,CAAC,EAAE,SAAS,WAAA,EAAE,YAAY,cAAA,EAAE,CAAC,CAAC;4BACtD,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,iBAAiB,CAAC,IAAI,CAAC,EAAE,SAAS,WAAA,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC,CAAC;wBAC3E,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,+EAA+E;YAC/E,IAAA,gBAAM,EAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,sEAAsE,EAAE;YACzE,sCAAsC;YACtC,IAAM,oBAAoB,GAAG;gBAC3B,wCAAwC;gBACxC,yCAAyC;gBACzC,6BAA6B;gBAC7B,iDAAiD;gBACjD,gDAAgD;aACjD,CAAC;YAEF,IAAM,yBAAyB,GAAG,EAAE,CAAC;YAErC,oBAAoB,CAAC,OAAO,CAAC,UAAA,SAAS;gBACpC,IAAI,YAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC7B,IAAM,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;oBACjE,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC;oBAEpD,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC7B,yBAAyB,CAAC,IAAI,CAAC,EAAE,SAAS,WAAA,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC,CAAC;oBAC9E,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC;4BACH,IAAM,aAAW,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAEtD,0CAA0C;4BAC1C,IAAM,iBAAiB,GAAG;gCACxB,iBAAiB;gCACjB,sBAAsB;gCACtB,qBAAqB;gCACrB,oBAAoB;gCACpB,sBAAsB;gCACtB,sBAAsB;gCACtB,wBAAwB;gCACxB,0BAA0B;6BAC3B,CAAC;4BAEF,IAAM,gBAAgB,GAAG,iBAAiB,CAAC,MAAM,CAAC,UAAA,QAAQ;gCACxD,OAAA,CAAC,aAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;4BAA3D,CAA2D,CAC5D,CAAC;4BAEF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gCAChC,yBAAyB,CAAC,IAAI,CAAC,EAAE,SAAS,WAAA,EAAE,gBAAgB,kBAAA,EAAE,CAAC,CAAC;4BAClE,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,yBAAyB,CAAC,IAAI,CAAC,EAAE,SAAS,WAAA,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC,CAAC;wBACnF,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,+EAA+E;YAC/E,IAAA,gBAAM,EAAC,yBAAyB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,qDAAqD,EAAE;QAC9D,IAAA,YAAE,EAAC,sEAAsE,EAAE;YACzE,2BAA2B;YAC3B,IAAM,gBAAgB,GAAG;gBACvB,eAAe;gBACf,mBAAmB;gBACnB,gCAAgC;aACjC,CAAC;YAEF,IAAM,iBAAiB,GAAG,EAAE,CAAC;YAE7B,gBAAgB,CAAC,OAAO,CAAC,UAAA,MAAM;gBAC7B,IAAI,YAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;oBAC1B,IAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;oBACrD,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC;oBAEpD,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC7B,iBAAiB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC,CAAC;oBACzE,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC;4BACH,IAAM,aAAW,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAEtD,iDAAiD;4BACjD,IAAM,eAAe,GAAG;gCACtB,sBAAsB;gCACtB,oBAAoB;gCACpB,mBAAmB;gCACnB,uBAAuB;gCACvB,mBAAmB;gCACnB,gBAAgB;gCAChB,mBAAmB;gCACnB,oBAAoB;6BACrB,CAAC;4BAEF,IAAM,cAAc,GAAG,eAAe,CAAC,MAAM,CAAC,UAAA,IAAI;gCAChD,OAAA,CAAC,aAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;4BAAvD,CAAuD,CACxD,CAAC;4BAEF,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gCAC9B,iBAAiB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,cAAc,gBAAA,EAAE,CAAC,CAAC;4BAC3D,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,iBAAiB,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC,CAAC;wBAC9E,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,wEAAwE;YACxE,IAAA,gBAAM,EAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,uDAAuD,EAAE;QAChE,IAAA,YAAE,EAAC,8DAA8D,EAAE;YACjE,4BAA4B;YAC5B,IAAM,eAAe,GAAG;gBACtB,8BAA8B;gBAC9B,+BAA+B;gBAC/B,gDAAgD;gBAChD,iDAAiD;gBACjD,gDAAgD;gBAChD,sDAAsD;aACvD,CAAC;YAEF,IAAM,wBAAwB,GAAG,EAAE,CAAC;YAEpC,eAAe,CAAC,OAAO,CAAC,UAAA,SAAS;gBAC/B,IAAI,YAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC7B,IAAM,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;oBAC1D,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC;oBAEpD,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC7B,wBAAwB,CAAC,IAAI,CAAC,EAAE,SAAS,WAAA,EAAE,KAAK,EAAE,qBAAqB,EAAE,CAAC,CAAC;oBAC7E,CAAC;yBAAM,CAAC;wBACN,IAAI,CAAC;4BACH,IAAM,aAAW,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAEtD,iDAAiD;4BACjD,IAAM,wBAAwB,GAAG;gCAC/B,iBAAiB;gCACjB,kBAAkB;gCAClB,eAAe;gCACf,gBAAgB;gCAChB,gBAAgB;gCAChB,aAAa;gCACb,eAAe;gCACf,mBAAmB;6BACpB,CAAC;4BAEF,IAAM,uBAAuB,GAAG,wBAAwB,CAAC,MAAM,CAAC,UAAA,IAAI;gCAClE,OAAA,CAAC,aAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;4BAAvD,CAAuD,CACxD,CAAC;4BAEF,IAAI,uBAAuB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gCACvC,wBAAwB,CAAC,IAAI,CAAC,EAAE,SAAS,WAAA,EAAE,uBAAuB,yBAAA,EAAE,CAAC,CAAC;4BACxE,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,wBAAwB,CAAC,IAAI,CAAC,EAAE,SAAS,WAAA,EAAE,KAAK,EAAE,0BAA0B,EAAE,CAAC,CAAC;wBAClF,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,iFAAiF;YACjF,IAAA,gBAAM,EAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/architecture/test-coverage-gaps.test.ts"], "sourcesContent": ["/**\n * Test Coverage Gap Analysis Tests\n * \n * These tests prove critical components lack adequate test coverage,\n * including authentication components, API routes, and business logic modules.\n * \n * EXPECTED TO FAIL - These tests demonstrate coverage gaps that need fixing.\n */\n\nimport { describe, it, expect, beforeEach, jest } from '@jest/globals';\nimport fs from 'fs';\nimport path from 'path';\n\ndescribe('Test Coverage Gap Analysis', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('CRITICAL ISSUE 1: Authentication Components Coverage Gaps', () => {\n    it('should fail - critical authentication components lack comprehensive tests', () => {\n      // Critical authentication components that must have tests\n      const criticalAuthComponents = [\n        'src/lib/auth.tsx',\n        'src/lib/session-security.ts',\n        'src/lib/unified-session-management.ts',\n        'src/lib/user-validation-service.ts',\n        'src/components/LoginForm.tsx',\n        'src/components/SignupForm.tsx'\n      ];\n      \n      const missingTests = [];\n      const inadequateTests = [];\n      \n      criticalAuthComponents.forEach(component => {\n        const testFile = component.replace(/\\.(tsx?|jsx?)$/, '.test.$1');\n        const testPath = path.join(process.cwd(), testFile);\n        \n        if (!fs.existsSync(testPath)) {\n          missingTests.push(component);\n        } else {\n          try {\n            const testContent = fs.readFileSync(testPath, 'utf8');\n            const testCount = (testContent.match(/it\\(/g) || []).length;\n            const expectCount = (testContent.match(/expect\\(/g) || []).length;\n            \n            // Critical components should have at least 10 tests and 20 assertions\n            if (testCount < 10 || expectCount < 20) {\n              inadequateTests.push({ component, testCount, expectCount });\n            }\n          } catch (error) {\n            missingTests.push(component);\n          }\n        }\n      });\n      \n      // EXPECTED TO FAIL: All critical auth components should have comprehensive tests\n      expect(missingTests.length).toBe(0);\n      expect(inadequateTests.length).toBe(0);\n    });\n\n    it('should fail - authentication edge cases are not tested', () => {\n      // Check for specific authentication edge case tests\n      const authTestFile = path.join(process.cwd(), 'src/lib/auth.test.tsx');\n      \n      if (fs.existsSync(authTestFile)) {\n        const testContent = fs.readFileSync(authTestFile, 'utf8');\n        \n        // Critical edge cases that must be tested\n        const requiredEdgeCases = [\n          'expired session',\n          'invalid token',\n          'concurrent login',\n          'session hijacking',\n          'brute force',\n          'rate limiting',\n          'CSRF protection',\n          'password validation',\n          'account lockout',\n          'session timeout'\n        ];\n        \n        const missingEdgeCases = requiredEdgeCases.filter(edgeCase => \n          !testContent.toLowerCase().includes(edgeCase.toLowerCase())\n        );\n        \n        // EXPECTED TO FAIL: All authentication edge cases should be tested\n        expect(missingEdgeCases.length).toBe(0);\n      } else {\n        // EXPECTED TO FAIL: Authentication test file should exist\n        expect(fs.existsSync(authTestFile)).toBe(true);\n      }\n    });\n  });\n\n  describe('CRITICAL ISSUE 2: API Routes Coverage Gaps', () => {\n    it('should fail - critical API routes lack integration tests', () => {\n      // Critical API routes that must have integration tests\n      const criticalAPIRoutes = [\n        'src/app/api/auth/[...nextauth]/route.ts',\n        'src/app/api/assessment/route.ts',\n        'src/app/api/profile/route.ts',\n        'src/app/api/interview/route.ts',\n        'src/app/api/resources/route.ts',\n        'src/app/api/career-paths/route.ts',\n        'src/app/api/ai-insights/route.ts'\n      ];\n      \n      const missingIntegrationTests = [];\n      \n      criticalAPIRoutes.forEach(route => {\n        const integrationTestFile = route.replace('src/app/api/', '__tests__/api/').replace('.ts', '.integration.test.ts');\n        const integrationTestPath = path.join(process.cwd(), integrationTestFile);\n        \n        if (!fs.existsSync(integrationTestPath)) {\n          missingIntegrationTests.push(route);\n        } else {\n          try {\n            const testContent = fs.readFileSync(integrationTestPath, 'utf8');\n            \n            // Integration tests should test real HTTP requests, not just mocked functions\n            const hasRealHTTPTests = testContent.includes('fetch(') || testContent.includes('request(');\n            const hasMockOveruse = (testContent.match(/\\.mock/g) || []).length > 5;\n            \n            if (!hasRealHTTPTests || hasMockOveruse) {\n              missingIntegrationTests.push(route);\n            }\n          } catch (error) {\n            missingIntegrationTests.push(route);\n          }\n        }\n      });\n      \n      // EXPECTED TO FAIL: All critical API routes should have proper integration tests\n      expect(missingIntegrationTests.length).toBe(0);\n    });\n\n    it('should fail - API error handling scenarios are not tested', () => {\n      // Check API test files for error handling coverage\n      const apiTestDirectory = path.join(process.cwd(), '__tests__/api');\n      const apiTestFiles = [];\n      \n      if (fs.existsSync(apiTestDirectory)) {\n        const files = fs.readdirSync(apiTestDirectory);\n        files.forEach(file => {\n          if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            apiTestFiles.push(path.join(apiTestDirectory, file));\n          }\n        });\n      }\n      \n      const missingErrorTests = [];\n      \n      // Required error scenarios that must be tested\n      const requiredErrorScenarios = [\n        '400 bad request',\n        '401 unauthorized',\n        '403 forbidden',\n        '404 not found',\n        '429 rate limit',\n        '500 internal error',\n        'database connection error',\n        'validation error',\n        'timeout error',\n        'network error'\n      ];\n      \n      apiTestFiles.forEach(testFile => {\n        try {\n          const testContent = fs.readFileSync(testFile, 'utf8');\n          \n          const missingScenarios = requiredErrorScenarios.filter(scenario => \n            !testContent.toLowerCase().includes(scenario.toLowerCase())\n          );\n          \n          if (missingScenarios.length > 5) { // Allow some flexibility\n            missingErrorTests.push({ file: testFile, missingScenarios });\n          }\n        } catch (error) {\n          missingErrorTests.push({ file: testFile, error: 'Could not read file' });\n        }\n      });\n      \n      // EXPECTED TO FAIL: API tests should cover error scenarios\n      expect(missingErrorTests.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 3: Business Logic Coverage Gaps', () => {\n    it('should fail - AI service business logic lacks comprehensive tests', () => {\n      // Critical AI service components\n      const aiServiceComponents = [\n        'src/lib/services/geminiService.ts',\n        'src/lib/services/ai-service.ts',\n        'src/lib/services/secure-ai-service.ts',\n        'src/lib/services/self-healing-ai-service.ts',\n        'src/lib/services/optimized-ai-service.ts'\n      ];\n      \n      const inadequateAITests = [];\n      \n      aiServiceComponents.forEach(component => {\n        if (fs.existsSync(component)) {\n          const testFile = component.replace(/\\.ts$/, '.test.ts');\n          const testPath = path.join(process.cwd(), testFile);\n          \n          if (!fs.existsSync(testPath)) {\n            inadequateAITests.push({ component, issue: 'No test file exists' });\n          } else {\n            try {\n              const testContent = fs.readFileSync(testPath, 'utf8');\n              \n              // AI services should test specific scenarios\n              const requiredAITests = [\n                'prompt injection',\n                'rate limiting',\n                'error recovery',\n                'response validation',\n                'timeout handling',\n                'cache behavior',\n                'token usage',\n                'model switching'\n              ];\n              \n              const missingTests = requiredAITests.filter(test => \n                !testContent.toLowerCase().includes(test.toLowerCase())\n              );\n              \n              if (missingTests.length > 3) {\n                inadequateAITests.push({ component, missingTests });\n              }\n            } catch (error) {\n              inadequateAITests.push({ component, issue: 'Could not read test file' });\n            }\n          }\n        }\n      });\n      \n      // EXPECTED TO FAIL: AI services should have comprehensive business logic tests\n      expect(inadequateAITests.length).toBe(0);\n    });\n\n    it('should fail - assessment and interview logic lacks edge case testing', () => {\n      // Assessment and interview components\n      const assessmentComponents = [\n        'src/lib/enhanced-question-generator.ts',\n        'src/lib/interview-question-generator.ts',\n        'src/lib/assessment-logic.ts',\n        'src/components/assessment/AssessmentResults.tsx',\n        'src/components/interview/InterviewPractice.tsx'\n      ];\n      \n      const inadequateAssessmentTests = [];\n      \n      assessmentComponents.forEach(component => {\n        if (fs.existsSync(component)) {\n          const testFile = component.replace(/\\.(tsx?|jsx?)$/, '.test.$1');\n          const testPath = path.join(process.cwd(), testFile);\n          \n          if (!fs.existsSync(testPath)) {\n            inadequateAssessmentTests.push({ component, issue: 'No test file exists' });\n          } else {\n            try {\n              const testContent = fs.readFileSync(testPath, 'utf8');\n              \n              // Assessment logic should test edge cases\n              const requiredEdgeCases = [\n                'empty responses',\n                'invalid skill levels',\n                'duplicate questions',\n                'scoring edge cases',\n                'progress calculation',\n                'recommendation logic',\n                'difficulty progression',\n                'session state corruption'\n              ];\n              \n              const missingEdgeCases = requiredEdgeCases.filter(edgeCase => \n                !testContent.toLowerCase().includes(edgeCase.toLowerCase())\n              );\n              \n              if (missingEdgeCases.length > 4) {\n                inadequateAssessmentTests.push({ component, missingEdgeCases });\n              }\n            } catch (error) {\n              inadequateAssessmentTests.push({ component, issue: 'Could not read test file' });\n            }\n          }\n        }\n      });\n      \n      // EXPECTED TO FAIL: Assessment logic should have comprehensive edge case tests\n      expect(inadequateAssessmentTests.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 4: Database Operations Coverage Gaps', () => {\n    it('should fail - database operations lack transaction and error testing', () => {\n      // Database operation files\n      const dbOperationFiles = [\n        'src/lib/db.ts',\n        'src/lib/prisma.ts',\n        'src/lib/database-operations.ts'\n      ];\n      \n      const inadequateDbTests = [];\n      \n      dbOperationFiles.forEach(dbFile => {\n        if (fs.existsSync(dbFile)) {\n          const testFile = dbFile.replace(/\\.ts$/, '.test.ts');\n          const testPath = path.join(process.cwd(), testFile);\n          \n          if (!fs.existsSync(testPath)) {\n            inadequateDbTests.push({ file: dbFile, issue: 'No test file exists' });\n          } else {\n            try {\n              const testContent = fs.readFileSync(testPath, 'utf8');\n              \n              // Database tests should cover critical scenarios\n              const requiredDbTests = [\n                'transaction rollback',\n                'connection failure',\n                'deadlock handling',\n                'constraint violations',\n                'concurrent access',\n                'data integrity',\n                'migration testing',\n                'backup and restore'\n              ];\n              \n              const missingDbTests = requiredDbTests.filter(test => \n                !testContent.toLowerCase().includes(test.toLowerCase())\n              );\n              \n              if (missingDbTests.length > 4) {\n                inadequateDbTests.push({ file: dbFile, missingDbTests });\n              }\n            } catch (error) {\n              inadequateDbTests.push({ file: dbFile, issue: 'Could not read test file' });\n            }\n          }\n        }\n      });\n      \n      // EXPECTED TO FAIL: Database operations should have comprehensive tests\n      expect(inadequateDbTests.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 5: Component Integration Coverage Gaps', () => {\n    it('should fail - React components lack user interaction testing', () => {\n      // Critical React components\n      const reactComponents = [\n        'src/components/LoginForm.tsx',\n        'src/components/SignupForm.tsx',\n        'src/components/dashboard/PersonalDashboard.tsx',\n        'src/components/assessment/AssessmentResults.tsx',\n        'src/components/interview/InterviewPractice.tsx',\n        'src/components/resources/ResourceRecommendations.tsx'\n      ];\n      \n      const inadequateComponentTests = [];\n      \n      reactComponents.forEach(component => {\n        if (fs.existsSync(component)) {\n          const testFile = component.replace(/\\.tsx$/, '.test.tsx');\n          const testPath = path.join(process.cwd(), testFile);\n          \n          if (!fs.existsSync(testPath)) {\n            inadequateComponentTests.push({ component, issue: 'No test file exists' });\n          } else {\n            try {\n              const testContent = fs.readFileSync(testPath, 'utf8');\n              \n              // Component tests should cover user interactions\n              const requiredInteractionTests = [\n                'form submission',\n                'input validation',\n                'error display',\n                'loading states',\n                'success states',\n                'user events',\n                'accessibility',\n                'responsive design'\n              ];\n              \n              const missingInteractionTests = requiredInteractionTests.filter(test => \n                !testContent.toLowerCase().includes(test.toLowerCase())\n              );\n              \n              if (missingInteractionTests.length > 3) {\n                inadequateComponentTests.push({ component, missingInteractionTests });\n              }\n            } catch (error) {\n              inadequateComponentTests.push({ component, issue: 'Could not read test file' });\n            }\n          }\n        }\n      });\n      \n      // EXPECTED TO FAIL: React components should have comprehensive interaction tests\n      expect(inadequateComponentTests.length).toBe(0);\n    });\n  });\n});\n"], "version": 3}