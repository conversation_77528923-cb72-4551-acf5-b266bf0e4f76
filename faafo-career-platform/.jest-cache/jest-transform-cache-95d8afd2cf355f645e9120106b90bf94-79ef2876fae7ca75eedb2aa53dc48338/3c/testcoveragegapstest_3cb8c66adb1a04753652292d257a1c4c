1a39c3410bb2f12d0a61c6d904a7ab7c
"use strict";
/**
 * Test Coverage Gap Analysis Tests
 *
 * These tests prove critical components lack adequate test coverage,
 * including authentication components, API routes, and business logic modules.
 *
 * EXPECTED TO FAIL - These tests demonstrate coverage gaps that need fixing.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var globals_1 = require("@jest/globals");
var fs_1 = __importDefault(require("fs"));
var path_1 = __importDefault(require("path"));
(0, globals_1.describe)('Test Coverage Gap Analysis', function () {
    (0, globals_1.beforeEach)(function () {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('CRITICAL ISSUE 1: Authentication Components Coverage Gaps', function () {
        (0, globals_1.it)('should fail - critical authentication components lack comprehensive tests', function () {
            // Critical authentication components that must have tests
            var criticalAuthComponents = [
                'src/lib/auth.tsx',
                'src/lib/session-security.ts',
                'src/lib/unified-session-management.ts',
                'src/lib/user-validation-service.ts',
                'src/components/LoginForm.tsx',
                'src/components/SignupForm.tsx'
            ];
            var missingTests = [];
            var inadequateTests = [];
            criticalAuthComponents.forEach(function (component) {
                var testFile = component.replace(/\.(tsx?|jsx?)$/, '.test.$1');
                var testPath = path_1.default.join(process.cwd(), testFile);
                if (!fs_1.default.existsSync(testPath)) {
                    missingTests.push(component);
                }
                else {
                    try {
                        var testContent = fs_1.default.readFileSync(testPath, 'utf8');
                        var testCount = (testContent.match(/it\(/g) || []).length;
                        var expectCount = (testContent.match(/expect\(/g) || []).length;
                        // Critical components should have at least 10 tests and 20 assertions
                        if (testCount < 10 || expectCount < 20) {
                            inadequateTests.push({ component: component, testCount: testCount, expectCount: expectCount });
                        }
                    }
                    catch (error) {
                        missingTests.push(component);
                    }
                }
            });
            // EXPECTED TO FAIL: All critical auth components should have comprehensive tests
            (0, globals_1.expect)(missingTests.length).toBe(0);
            (0, globals_1.expect)(inadequateTests.length).toBe(0);
        });
        (0, globals_1.it)('should fail - authentication edge cases are not tested', function () {
            // Check for specific authentication edge case tests
            var authTestFile = path_1.default.join(process.cwd(), 'src/lib/auth.test.tsx');
            if (fs_1.default.existsSync(authTestFile)) {
                var testContent_1 = fs_1.default.readFileSync(authTestFile, 'utf8');
                // Critical edge cases that must be tested
                var requiredEdgeCases = [
                    'expired session',
                    'invalid token',
                    'concurrent login',
                    'session hijacking',
                    'brute force',
                    'rate limiting',
                    'CSRF protection',
                    'password validation',
                    'account lockout',
                    'session timeout'
                ];
                var missingEdgeCases = requiredEdgeCases.filter(function (edgeCase) {
                    return !testContent_1.toLowerCase().includes(edgeCase.toLowerCase());
                });
                // EXPECTED TO FAIL: All authentication edge cases should be tested
                (0, globals_1.expect)(missingEdgeCases.length).toBe(0);
            }
            else {
                // EXPECTED TO FAIL: Authentication test file should exist
                (0, globals_1.expect)(fs_1.default.existsSync(authTestFile)).toBe(true);
            }
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 2: API Routes Coverage Gaps', function () {
        (0, globals_1.it)('should fail - critical API routes lack integration tests', function () {
            // Critical API routes that must have integration tests
            var criticalAPIRoutes = [
                'src/app/api/auth/[...nextauth]/route.ts',
                'src/app/api/assessment/route.ts',
                'src/app/api/profile/route.ts',
                'src/app/api/interview/route.ts',
                'src/app/api/resources/route.ts',
                'src/app/api/career-paths/route.ts',
                'src/app/api/ai-insights/route.ts'
            ];
            var missingIntegrationTests = [];
            criticalAPIRoutes.forEach(function (route) {
                var integrationTestFile = route.replace('src/app/api/', '__tests__/api/').replace('.ts', '.integration.test.ts');
                var integrationTestPath = path_1.default.join(process.cwd(), integrationTestFile);
                if (!fs_1.default.existsSync(integrationTestPath)) {
                    missingIntegrationTests.push(route);
                }
                else {
                    try {
                        var testContent = fs_1.default.readFileSync(integrationTestPath, 'utf8');
                        // Integration tests should test real HTTP requests, not just mocked functions
                        var hasRealHTTPTests = testContent.includes('fetch(') || testContent.includes('request(');
                        var hasMockOveruse = (testContent.match(/\.mock/g) || []).length > 5;
                        if (!hasRealHTTPTests || hasMockOveruse) {
                            missingIntegrationTests.push(route);
                        }
                    }
                    catch (error) {
                        missingIntegrationTests.push(route);
                    }
                }
            });
            // EXPECTED TO FAIL: All critical API routes should have proper integration tests
            (0, globals_1.expect)(missingIntegrationTests.length).toBe(0);
        });
        (0, globals_1.it)('should fail - API error handling scenarios are not tested', function () {
            // Check API test files for error handling coverage
            var apiTestDirectory = path_1.default.join(process.cwd(), '__tests__/api');
            var apiTestFiles = [];
            if (fs_1.default.existsSync(apiTestDirectory)) {
                var files = fs_1.default.readdirSync(apiTestDirectory);
                files.forEach(function (file) {
                    if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        apiTestFiles.push(path_1.default.join(apiTestDirectory, file));
                    }
                });
            }
            var missingErrorTests = [];
            // Required error scenarios that must be tested
            var requiredErrorScenarios = [
                '400 bad request',
                '401 unauthorized',
                '403 forbidden',
                '404 not found',
                '429 rate limit',
                '500 internal error',
                'database connection error',
                'validation error',
                'timeout error',
                'network error'
            ];
            apiTestFiles.forEach(function (testFile) {
                try {
                    var testContent_2 = fs_1.default.readFileSync(testFile, 'utf8');
                    var missingScenarios = requiredErrorScenarios.filter(function (scenario) {
                        return !testContent_2.toLowerCase().includes(scenario.toLowerCase());
                    });
                    if (missingScenarios.length > 5) { // Allow some flexibility
                        missingErrorTests.push({ file: testFile, missingScenarios: missingScenarios });
                    }
                }
                catch (error) {
                    missingErrorTests.push({ file: testFile, error: 'Could not read file' });
                }
            });
            // EXPECTED TO FAIL: API tests should cover error scenarios
            (0, globals_1.expect)(missingErrorTests.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 3: Business Logic Coverage Gaps', function () {
        (0, globals_1.it)('should fail - AI service business logic lacks comprehensive tests', function () {
            // Critical AI service components
            var aiServiceComponents = [
                'src/lib/services/geminiService.ts',
                'src/lib/services/ai-service.ts',
                'src/lib/services/secure-ai-service.ts',
                'src/lib/services/self-healing-ai-service.ts',
                'src/lib/services/optimized-ai-service.ts'
            ];
            var inadequateAITests = [];
            aiServiceComponents.forEach(function (component) {
                if (fs_1.default.existsSync(component)) {
                    var testFile = component.replace(/\.ts$/, '.test.ts');
                    var testPath = path_1.default.join(process.cwd(), testFile);
                    if (!fs_1.default.existsSync(testPath)) {
                        inadequateAITests.push({ component: component, issue: 'No test file exists' });
                    }
                    else {
                        try {
                            var testContent_3 = fs_1.default.readFileSync(testPath, 'utf8');
                            // AI services should test specific scenarios
                            var requiredAITests = [
                                'prompt injection',
                                'rate limiting',
                                'error recovery',
                                'response validation',
                                'timeout handling',
                                'cache behavior',
                                'token usage',
                                'model switching'
                            ];
                            var missingTests = requiredAITests.filter(function (test) {
                                return !testContent_3.toLowerCase().includes(test.toLowerCase());
                            });
                            if (missingTests.length > 3) {
                                inadequateAITests.push({ component: component, missingTests: missingTests });
                            }
                        }
                        catch (error) {
                            inadequateAITests.push({ component: component, issue: 'Could not read test file' });
                        }
                    }
                }
            });
            // EXPECTED TO FAIL: AI services should have comprehensive business logic tests
            (0, globals_1.expect)(inadequateAITests.length).toBe(0);
        });
        (0, globals_1.it)('should fail - assessment and interview logic lacks edge case testing', function () {
            // Assessment and interview components
            var assessmentComponents = [
                'src/lib/enhanced-question-generator.ts',
                'src/lib/interview-question-generator.ts',
                'src/lib/assessment-logic.ts',
                'src/components/assessment/AssessmentResults.tsx',
                'src/components/interview/InterviewPractice.tsx'
            ];
            var inadequateAssessmentTests = [];
            assessmentComponents.forEach(function (component) {
                if (fs_1.default.existsSync(component)) {
                    var testFile = component.replace(/\.(tsx?|jsx?)$/, '.test.$1');
                    var testPath = path_1.default.join(process.cwd(), testFile);
                    if (!fs_1.default.existsSync(testPath)) {
                        inadequateAssessmentTests.push({ component: component, issue: 'No test file exists' });
                    }
                    else {
                        try {
                            var testContent_4 = fs_1.default.readFileSync(testPath, 'utf8');
                            // Assessment logic should test edge cases
                            var requiredEdgeCases = [
                                'empty responses',
                                'invalid skill levels',
                                'duplicate questions',
                                'scoring edge cases',
                                'progress calculation',
                                'recommendation logic',
                                'difficulty progression',
                                'session state corruption'
                            ];
                            var missingEdgeCases = requiredEdgeCases.filter(function (edgeCase) {
                                return !testContent_4.toLowerCase().includes(edgeCase.toLowerCase());
                            });
                            if (missingEdgeCases.length > 4) {
                                inadequateAssessmentTests.push({ component: component, missingEdgeCases: missingEdgeCases });
                            }
                        }
                        catch (error) {
                            inadequateAssessmentTests.push({ component: component, issue: 'Could not read test file' });
                        }
                    }
                }
            });
            // EXPECTED TO FAIL: Assessment logic should have comprehensive edge case tests
            (0, globals_1.expect)(inadequateAssessmentTests.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 4: Database Operations Coverage Gaps', function () {
        (0, globals_1.it)('should fail - database operations lack transaction and error testing', function () {
            // Database operation files
            var dbOperationFiles = [
                'src/lib/db.ts',
                'src/lib/prisma.ts',
                'src/lib/database-operations.ts'
            ];
            var inadequateDbTests = [];
            dbOperationFiles.forEach(function (dbFile) {
                if (fs_1.default.existsSync(dbFile)) {
                    var testFile = dbFile.replace(/\.ts$/, '.test.ts');
                    var testPath = path_1.default.join(process.cwd(), testFile);
                    if (!fs_1.default.existsSync(testPath)) {
                        inadequateDbTests.push({ file: dbFile, issue: 'No test file exists' });
                    }
                    else {
                        try {
                            var testContent_5 = fs_1.default.readFileSync(testPath, 'utf8');
                            // Database tests should cover critical scenarios
                            var requiredDbTests = [
                                'transaction rollback',
                                'connection failure',
                                'deadlock handling',
                                'constraint violations',
                                'concurrent access',
                                'data integrity',
                                'migration testing',
                                'backup and restore'
                            ];
                            var missingDbTests = requiredDbTests.filter(function (test) {
                                return !testContent_5.toLowerCase().includes(test.toLowerCase());
                            });
                            if (missingDbTests.length > 4) {
                                inadequateDbTests.push({ file: dbFile, missingDbTests: missingDbTests });
                            }
                        }
                        catch (error) {
                            inadequateDbTests.push({ file: dbFile, issue: 'Could not read test file' });
                        }
                    }
                }
            });
            // EXPECTED TO FAIL: Database operations should have comprehensive tests
            (0, globals_1.expect)(inadequateDbTests.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 5: Component Integration Coverage Gaps', function () {
        (0, globals_1.it)('should fail - React components lack user interaction testing', function () {
            // Critical React components
            var reactComponents = [
                'src/components/LoginForm.tsx',
                'src/components/SignupForm.tsx',
                'src/components/dashboard/PersonalDashboard.tsx',
                'src/components/assessment/AssessmentResults.tsx',
                'src/components/interview/InterviewPractice.tsx',
                'src/components/resources/ResourceRecommendations.tsx'
            ];
            var inadequateComponentTests = [];
            reactComponents.forEach(function (component) {
                if (fs_1.default.existsSync(component)) {
                    var testFile = component.replace(/\.tsx$/, '.test.tsx');
                    var testPath = path_1.default.join(process.cwd(), testFile);
                    if (!fs_1.default.existsSync(testPath)) {
                        inadequateComponentTests.push({ component: component, issue: 'No test file exists' });
                    }
                    else {
                        try {
                            var testContent_6 = fs_1.default.readFileSync(testPath, 'utf8');
                            // Component tests should cover user interactions
                            var requiredInteractionTests = [
                                'form submission',
                                'input validation',
                                'error display',
                                'loading states',
                                'success states',
                                'user events',
                                'accessibility',
                                'responsive design'
                            ];
                            var missingInteractionTests = requiredInteractionTests.filter(function (test) {
                                return !testContent_6.toLowerCase().includes(test.toLowerCase());
                            });
                            if (missingInteractionTests.length > 3) {
                                inadequateComponentTests.push({ component: component, missingInteractionTests: missingInteractionTests });
                            }
                        }
                        catch (error) {
                            inadequateComponentTests.push({ component: component, issue: 'Could not read test file' });
                        }
                    }
                }
            });
            // EXPECTED TO FAIL: React components should have comprehensive interaction tests
            (0, globals_1.expect)(inadequateComponentTests.length).toBe(0);
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJmaWxlIjoiL1VzZXJzL2RkNjAvZmFhZm8vZmFhZm8vZmFhZm8tY2FyZWVyLXBsYXRmb3JtL19fdGVzdHNfXy9hcmNoaXRlY3R1cmUvdGVzdC1jb3ZlcmFnZS1nYXBzLnRlc3QudHMiLCJtYXBwaW5ncyI6IjtBQUFBOzs7Ozs7O0dBT0c7Ozs7O0FBRUgseUNBQXVFO0FBQ3ZFLDBDQUFvQjtBQUNwQiw4Q0FBd0I7QUFFeEIsSUFBQSxrQkFBUSxFQUFDLDRCQUE0QixFQUFFO0lBQ3JDLElBQUEsb0JBQVUsRUFBQztRQUNULGNBQUksQ0FBQyxhQUFhLEVBQUUsQ0FBQztJQUN2QixDQUFDLENBQUMsQ0FBQztJQUVILElBQUEsa0JBQVEsRUFBQywyREFBMkQsRUFBRTtRQUNwRSxJQUFBLFlBQUUsRUFBQywyRUFBMkUsRUFBRTtZQUM5RSwwREFBMEQ7WUFDMUQsSUFBTSxzQkFBc0IsR0FBRztnQkFDN0Isa0JBQWtCO2dCQUNsQiw2QkFBNkI7Z0JBQzdCLHVDQUF1QztnQkFDdkMsb0NBQW9DO2dCQUNwQyw4QkFBOEI7Z0JBQzlCLCtCQUErQjthQUNoQyxDQUFDO1lBRUYsSUFBTSxZQUFZLEdBQUcsRUFBRSxDQUFDO1lBQ3hCLElBQU0sZUFBZSxHQUFHLEVBQUUsQ0FBQztZQUUzQixzQkFBc0IsQ0FBQyxPQUFPLENBQUMsVUFBQSxTQUFTO2dCQUN0QyxJQUFNLFFBQVEsR0FBRyxTQUFTLENBQUMsT0FBTyxDQUFDLGdCQUFnQixFQUFFLFVBQVUsQ0FBQyxDQUFDO2dCQUNqRSxJQUFNLFFBQVEsR0FBRyxjQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsRUFBRSxRQUFRLENBQUMsQ0FBQztnQkFFcEQsSUFBSSxDQUFDLFlBQUUsQ0FBQyxVQUFVLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQztvQkFDN0IsWUFBWSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FBQztnQkFDL0IsQ0FBQztxQkFBTSxDQUFDO29CQUNOLElBQUksQ0FBQzt3QkFDSCxJQUFNLFdBQVcsR0FBRyxZQUFFLENBQUMsWUFBWSxDQUFDLFFBQVEsRUFBRSxNQUFNLENBQUMsQ0FBQzt3QkFDdEQsSUFBTSxTQUFTLEdBQUcsQ0FBQyxXQUFXLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDLE1BQU0sQ0FBQzt3QkFDNUQsSUFBTSxXQUFXLEdBQUcsQ0FBQyxXQUFXLENBQUMsS0FBSyxDQUFDLFdBQVcsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDLE1BQU0sQ0FBQzt3QkFFbEUsc0VBQXNFO3dCQUN0RSxJQUFJLFNBQVMsR0FBRyxFQUFFLElBQUksV0FBVyxHQUFHLEVBQUUsRUFBRSxDQUFDOzRCQUN2QyxlQUFlLENBQUMsSUFBSSxDQUFDLEVBQUUsU0FBUyxXQUFBLEVBQUUsU0FBUyxXQUFBLEVBQUUsV0FBVyxhQUFBLEVBQUUsQ0FBQyxDQUFDO3dCQUM5RCxDQUFDO29CQUNILENBQUM7b0JBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQzt3QkFDZixZQUFZLENBQUMsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUFDO29CQUMvQixDQUFDO2dCQUNILENBQUM7WUFDSCxDQUFDLENBQUMsQ0FBQztZQUVILGlGQUFpRjtZQUNqRixJQUFBLGdCQUFNLEVBQUMsWUFBWSxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNwQyxJQUFBLGdCQUFNLEVBQUMsZUFBZSxDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUN6QyxDQUFDLENBQUMsQ0FBQztRQUVILElBQUEsWUFBRSxFQUFDLHdEQUF3RCxFQUFFO1lBQzNELG9EQUFvRDtZQUNwRCxJQUFNLFlBQVksR0FBRyxjQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsRUFBRSx1QkFBdUIsQ0FBQyxDQUFDO1lBRXZFLElBQUksWUFBRSxDQUFDLFVBQVUsQ0FBQyxZQUFZLENBQUMsRUFBRSxDQUFDO2dCQUNoQyxJQUFNLGFBQVcsR0FBRyxZQUFFLENBQUMsWUFBWSxDQUFDLFlBQVksRUFBRSxNQUFNLENBQUMsQ0FBQztnQkFFMUQsMENBQTBDO2dCQUMxQyxJQUFNLGlCQUFpQixHQUFHO29CQUN4QixpQkFBaUI7b0JBQ2pCLGVBQWU7b0JBQ2Ysa0JBQWtCO29CQUNsQixtQkFBbUI7b0JBQ25CLGFBQWE7b0JBQ2IsZUFBZTtvQkFDZixpQkFBaUI7b0JBQ2pCLHFCQUFxQjtvQkFDckIsaUJBQWlCO29CQUNqQixpQkFBaUI7aUJBQ2xCLENBQUM7Z0JBRUYsSUFBTSxnQkFBZ0IsR0FBRyxpQkFBaUIsQ0FBQyxNQUFNLENBQUMsVUFBQSxRQUFRO29CQUN4RCxPQUFBLENBQUMsYUFBVyxDQUFDLFdBQVcsRUFBRSxDQUFDLFFBQVEsQ0FBQyxRQUFRLENBQUMsV0FBVyxFQUFFLENBQUM7Z0JBQTNELENBQTJELENBQzVELENBQUM7Z0JBRUYsbUVBQW1FO2dCQUNuRSxJQUFBLGdCQUFNLEVBQUMsZ0JBQWdCLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQzFDLENBQUM7aUJBQU0sQ0FBQztnQkFDTiwwREFBMEQ7Z0JBQzFELElBQUEsZ0JBQU0sRUFBQyxZQUFFLENBQUMsVUFBVSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDLElBQUksQ0FBQyxDQUFDO1lBQ2pELENBQUM7UUFDSCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsSUFBQSxrQkFBUSxFQUFDLDRDQUE0QyxFQUFFO1FBQ3JELElBQUEsWUFBRSxFQUFDLDBEQUEwRCxFQUFFO1lBQzdELHVEQUF1RDtZQUN2RCxJQUFNLGlCQUFpQixHQUFHO2dCQUN4Qix5Q0FBeUM7Z0JBQ3pDLGlDQUFpQztnQkFDakMsOEJBQThCO2dCQUM5QixnQ0FBZ0M7Z0JBQ2hDLGdDQUFnQztnQkFDaEMsbUNBQW1DO2dCQUNuQyxrQ0FBa0M7YUFDbkMsQ0FBQztZQUVGLElBQU0sdUJBQXVCLEdBQUcsRUFBRSxDQUFDO1lBRW5DLGlCQUFpQixDQUFDLE9BQU8sQ0FBQyxVQUFBLEtBQUs7Z0JBQzdCLElBQU0sbUJBQW1CLEdBQUcsS0FBSyxDQUFDLE9BQU8sQ0FBQyxjQUFjLEVBQUUsZ0JBQWdCLENBQUMsQ0FBQyxPQUFPLENBQUMsS0FBSyxFQUFFLHNCQUFzQixDQUFDLENBQUM7Z0JBQ25ILElBQU0sbUJBQW1CLEdBQUcsY0FBSSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsR0FBRyxFQUFFLEVBQUUsbUJBQW1CLENBQUMsQ0FBQztnQkFFMUUsSUFBSSxDQUFDLFlBQUUsQ0FBQyxVQUFVLENBQUMsbUJBQW1CLENBQUMsRUFBRSxDQUFDO29CQUN4Qyx1QkFBdUIsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7Z0JBQ3RDLENBQUM7cUJBQU0sQ0FBQztvQkFDTixJQUFJLENBQUM7d0JBQ0gsSUFBTSxXQUFXLEdBQUcsWUFBRSxDQUFDLFlBQVksQ0FBQyxtQkFBbUIsRUFBRSxNQUFNLENBQUMsQ0FBQzt3QkFFakUsOEVBQThFO3dCQUM5RSxJQUFNLGdCQUFnQixHQUFHLFdBQVcsQ0FBQyxRQUFRLENBQUMsUUFBUSxDQUFDLElBQUksV0FBVyxDQUFDLFFBQVEsQ0FBQyxVQUFVLENBQUMsQ0FBQzt3QkFDNUYsSUFBTSxjQUFjLEdBQUcsQ0FBQyxXQUFXLENBQUMsS0FBSyxDQUFDLFNBQVMsQ0FBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDLE1BQU0sR0FBRyxDQUFDLENBQUM7d0JBRXZFLElBQUksQ0FBQyxnQkFBZ0IsSUFBSSxjQUFjLEVBQUUsQ0FBQzs0QkFDeEMsdUJBQXVCLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxDQUFDO3dCQUN0QyxDQUFDO29CQUNILENBQUM7b0JBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQzt3QkFDZix1QkFBdUIsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUM7b0JBQ3RDLENBQUM7Z0JBQ0gsQ0FBQztZQUNILENBQUMsQ0FBQyxDQUFDO1lBRUgsaUZBQWlGO1lBQ2pGLElBQUEsZ0JBQU0sRUFBQyx1QkFBdUIsQ0FBQyxNQUFNLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDakQsQ0FBQyxDQUFDLENBQUM7UUFFSCxJQUFBLFlBQUUsRUFBQywyREFBMkQsRUFBRTtZQUM5RCxtREFBbUQ7WUFDbkQsSUFBTSxnQkFBZ0IsR0FBRyxjQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsRUFBRSxlQUFlLENBQUMsQ0FBQztZQUNuRSxJQUFNLFlBQVksR0FBRyxFQUFFLENBQUM7WUFFeEIsSUFBSSxZQUFFLENBQUMsVUFBVSxDQUFDLGdCQUFnQixDQUFDLEVBQUUsQ0FBQztnQkFDcEMsSUFBTSxLQUFLLEdBQUcsWUFBRSxDQUFDLFdBQVcsQ0FBQyxnQkFBZ0IsQ0FBQyxDQUFDO2dCQUMvQyxLQUFLLENBQUMsT0FBTyxDQUFDLFVBQUEsSUFBSTtvQkFDaEIsSUFBSSxJQUFJLENBQUMsUUFBUSxDQUFDLFVBQVUsQ0FBQyxJQUFJLElBQUksQ0FBQyxRQUFRLENBQUMsV0FBVyxDQUFDLEVBQUUsQ0FBQzt3QkFDNUQsWUFBWSxDQUFDLElBQUksQ0FBQyxjQUFJLENBQUMsSUFBSSxDQUFDLGdCQUFnQixFQUFFLElBQUksQ0FBQyxDQUFDLENBQUM7b0JBQ3ZELENBQUM7Z0JBQ0gsQ0FBQyxDQUFDLENBQUM7WUFDTCxDQUFDO1lBRUQsSUFBTSxpQkFBaUIsR0FBRyxFQUFFLENBQUM7WUFFN0IsK0NBQStDO1lBQy9DLElBQU0sc0JBQXNCLEdBQUc7Z0JBQzdCLGlCQUFpQjtnQkFDakIsa0JBQWtCO2dCQUNsQixlQUFlO2dCQUNmLGVBQWU7Z0JBQ2YsZ0JBQWdCO2dCQUNoQixvQkFBb0I7Z0JBQ3BCLDJCQUEyQjtnQkFDM0Isa0JBQWtCO2dCQUNsQixlQUFlO2dCQUNmLGVBQWU7YUFDaEIsQ0FBQztZQUVGLFlBQVksQ0FBQyxPQUFPLENBQUMsVUFBQSxRQUFRO2dCQUMzQixJQUFJLENBQUM7b0JBQ0gsSUFBTSxhQUFXLEdBQUcsWUFBRSxDQUFDLFlBQVksQ0FBQyxRQUFRLEVBQUUsTUFBTSxDQUFDLENBQUM7b0JBRXRELElBQU0sZ0JBQWdCLEdBQUcsc0JBQXNCLENBQUMsTUFBTSxDQUFDLFVBQUEsUUFBUTt3QkFDN0QsT0FBQSxDQUFDLGFBQVcsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxRQUFRLENBQUMsUUFBUSxDQUFDLFdBQVcsRUFBRSxDQUFDO29CQUEzRCxDQUEyRCxDQUM1RCxDQUFDO29CQUVGLElBQUksZ0JBQWdCLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDLENBQUMseUJBQXlCO3dCQUMxRCxpQkFBaUIsQ0FBQyxJQUFJLENBQUMsRUFBRSxJQUFJLEVBQUUsUUFBUSxFQUFFLGdCQUFnQixrQkFBQSxFQUFFLENBQUMsQ0FBQztvQkFDL0QsQ0FBQztnQkFDSCxDQUFDO2dCQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7b0JBQ2YsaUJBQWlCLENBQUMsSUFBSSxDQUFDLEVBQUUsSUFBSSxFQUFFLFFBQVEsRUFBRSxLQUFLLEVBQUUscUJBQXFCLEVBQUUsQ0FBQyxDQUFDO2dCQUMzRSxDQUFDO1lBQ0gsQ0FBQyxDQUFDLENBQUM7WUFFSCwyREFBMkQ7WUFDM0QsSUFBQSxnQkFBTSxFQUFDLGlCQUFpQixDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUMzQyxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0lBRUgsSUFBQSxrQkFBUSxFQUFDLGdEQUFnRCxFQUFFO1FBQ3pELElBQUEsWUFBRSxFQUFDLG1FQUFtRSxFQUFFO1lBQ3RFLGlDQUFpQztZQUNqQyxJQUFNLG1CQUFtQixHQUFHO2dCQUMxQixtQ0FBbUM7Z0JBQ25DLGdDQUFnQztnQkFDaEMsdUNBQXVDO2dCQUN2Qyw2Q0FBNkM7Z0JBQzdDLDBDQUEwQzthQUMzQyxDQUFDO1lBRUYsSUFBTSxpQkFBaUIsR0FBRyxFQUFFLENBQUM7WUFFN0IsbUJBQW1CLENBQUMsT0FBTyxDQUFDLFVBQUEsU0FBUztnQkFDbkMsSUFBSSxZQUFFLENBQUMsVUFBVSxDQUFDLFNBQVMsQ0FBQyxFQUFFLENBQUM7b0JBQzdCLElBQU0sUUFBUSxHQUFHLFNBQVMsQ0FBQyxPQUFPLENBQUMsT0FBTyxFQUFFLFVBQVUsQ0FBQyxDQUFDO29CQUN4RCxJQUFNLFFBQVEsR0FBRyxjQUFJLENBQUMsSUFBSSxDQUFDLE9BQU8sQ0FBQyxHQUFHLEVBQUUsRUFBRSxRQUFRLENBQUMsQ0FBQztvQkFFcEQsSUFBSSxDQUFDLFlBQUUsQ0FBQyxVQUFVLENBQUMsUUFBUSxDQUFDLEVBQUUsQ0FBQzt3QkFDN0IsaUJBQWlCLENBQUMsSUFBSSxDQUFDLEVBQUUsU0FBUyxXQUFBLEVBQUUsS0FBSyxFQUFFLHFCQUFxQixFQUFFLENBQUMsQ0FBQztvQkFDdEUsQ0FBQzt5QkFBTSxDQUFDO3dCQUNOLElBQUksQ0FBQzs0QkFDSCxJQUFNLGFBQVcsR0FBRyxZQUFFLENBQUMsWUFBWSxDQUFDLFFBQVEsRUFBRSxNQUFNLENBQUMsQ0FBQzs0QkFFdEQsNkNBQTZDOzRCQUM3QyxJQUFNLGVBQWUsR0FBRztnQ0FDdEIsa0JBQWtCO2dDQUNsQixlQUFlO2dDQUNmLGdCQUFnQjtnQ0FDaEIscUJBQXFCO2dDQUNyQixrQkFBa0I7Z0NBQ2xCLGdCQUFnQjtnQ0FDaEIsYUFBYTtnQ0FDYixpQkFBaUI7NkJBQ2xCLENBQUM7NEJBRUYsSUFBTSxZQUFZLEdBQUcsZUFBZSxDQUFDLE1BQU0sQ0FBQyxVQUFBLElBQUk7Z0NBQzlDLE9BQUEsQ0FBQyxhQUFXLENBQUMsV0FBVyxFQUFFLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQzs0QkFBdkQsQ0FBdUQsQ0FDeEQsQ0FBQzs0QkFFRixJQUFJLFlBQVksQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7Z0NBQzVCLGlCQUFpQixDQUFDLElBQUksQ0FBQyxFQUFFLFNBQVMsV0FBQSxFQUFFLFlBQVksY0FBQSxFQUFFLENBQUMsQ0FBQzs0QkFDdEQsQ0FBQzt3QkFDSCxDQUFDO3dCQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7NEJBQ2YsaUJBQWlCLENBQUMsSUFBSSxDQUFDLEVBQUUsU0FBUyxXQUFBLEVBQUUsS0FBSyxFQUFFLDBCQUEwQixFQUFFLENBQUMsQ0FBQzt3QkFDM0UsQ0FBQztvQkFDSCxDQUFDO2dCQUNILENBQUM7WUFDSCxDQUFDLENBQUMsQ0FBQztZQUVILCtFQUErRTtZQUMvRSxJQUFBLGdCQUFNLEVBQUMsaUJBQWlCLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQzNDLENBQUMsQ0FBQyxDQUFDO1FBRUgsSUFBQSxZQUFFLEVBQUMsc0VBQXNFLEVBQUU7WUFDekUsc0NBQXNDO1lBQ3RDLElBQU0sb0JBQW9CLEdBQUc7Z0JBQzNCLHdDQUF3QztnQkFDeEMseUNBQXlDO2dCQUN6Qyw2QkFBNkI7Z0JBQzdCLGlEQUFpRDtnQkFDakQsZ0RBQWdEO2FBQ2pELENBQUM7WUFFRixJQUFNLHlCQUF5QixHQUFHLEVBQUUsQ0FBQztZQUVyQyxvQkFBb0IsQ0FBQyxPQUFPLENBQUMsVUFBQSxTQUFTO2dCQUNwQyxJQUFJLFlBQUUsQ0FBQyxVQUFVLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQztvQkFDN0IsSUFBTSxRQUFRLEdBQUcsU0FBUyxDQUFDLE9BQU8sQ0FBQyxnQkFBZ0IsRUFBRSxVQUFVLENBQUMsQ0FBQztvQkFDakUsSUFBTSxRQUFRLEdBQUcsY0FBSSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsR0FBRyxFQUFFLEVBQUUsUUFBUSxDQUFDLENBQUM7b0JBRXBELElBQUksQ0FBQyxZQUFFLENBQUMsVUFBVSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUM7d0JBQzdCLHlCQUF5QixDQUFDLElBQUksQ0FBQyxFQUFFLFNBQVMsV0FBQSxFQUFFLEtBQUssRUFBRSxxQkFBcUIsRUFBRSxDQUFDLENBQUM7b0JBQzlFLENBQUM7eUJBQU0sQ0FBQzt3QkFDTixJQUFJLENBQUM7NEJBQ0gsSUFBTSxhQUFXLEdBQUcsWUFBRSxDQUFDLFlBQVksQ0FBQyxRQUFRLEVBQUUsTUFBTSxDQUFDLENBQUM7NEJBRXRELDBDQUEwQzs0QkFDMUMsSUFBTSxpQkFBaUIsR0FBRztnQ0FDeEIsaUJBQWlCO2dDQUNqQixzQkFBc0I7Z0NBQ3RCLHFCQUFxQjtnQ0FDckIsb0JBQW9CO2dDQUNwQixzQkFBc0I7Z0NBQ3RCLHNCQUFzQjtnQ0FDdEIsd0JBQXdCO2dDQUN4QiwwQkFBMEI7NkJBQzNCLENBQUM7NEJBRUYsSUFBTSxnQkFBZ0IsR0FBRyxpQkFBaUIsQ0FBQyxNQUFNLENBQUMsVUFBQSxRQUFRO2dDQUN4RCxPQUFBLENBQUMsYUFBVyxDQUFDLFdBQVcsRUFBRSxDQUFDLFFBQVEsQ0FBQyxRQUFRLENBQUMsV0FBVyxFQUFFLENBQUM7NEJBQTNELENBQTJELENBQzVELENBQUM7NEJBRUYsSUFBSSxnQkFBZ0IsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7Z0NBQ2hDLHlCQUF5QixDQUFDLElBQUksQ0FBQyxFQUFFLFNBQVMsV0FBQSxFQUFFLGdCQUFnQixrQkFBQSxFQUFFLENBQUMsQ0FBQzs0QkFDbEUsQ0FBQzt3QkFDSCxDQUFDO3dCQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7NEJBQ2YseUJBQXlCLENBQUMsSUFBSSxDQUFDLEVBQUUsU0FBUyxXQUFBLEVBQUUsS0FBSyxFQUFFLDBCQUEwQixFQUFFLENBQUMsQ0FBQzt3QkFDbkYsQ0FBQztvQkFDSCxDQUFDO2dCQUNILENBQUM7WUFDSCxDQUFDLENBQUMsQ0FBQztZQUVILCtFQUErRTtZQUMvRSxJQUFBLGdCQUFNLEVBQUMseUJBQXlCLENBQUMsTUFBTSxDQUFDLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQ25ELENBQUMsQ0FBQyxDQUFDO0lBQ0wsQ0FBQyxDQUFDLENBQUM7SUFFSCxJQUFBLGtCQUFRLEVBQUMscURBQXFELEVBQUU7UUFDOUQsSUFBQSxZQUFFLEVBQUMsc0VBQXNFLEVBQUU7WUFDekUsMkJBQTJCO1lBQzNCLElBQU0sZ0JBQWdCLEdBQUc7Z0JBQ3ZCLGVBQWU7Z0JBQ2YsbUJBQW1CO2dCQUNuQixnQ0FBZ0M7YUFDakMsQ0FBQztZQUVGLElBQU0saUJBQWlCLEdBQUcsRUFBRSxDQUFDO1lBRTdCLGdCQUFnQixDQUFDLE9BQU8sQ0FBQyxVQUFBLE1BQU07Z0JBQzdCLElBQUksWUFBRSxDQUFDLFVBQVUsQ0FBQyxNQUFNLENBQUMsRUFBRSxDQUFDO29CQUMxQixJQUFNLFFBQVEsR0FBRyxNQUFNLENBQUMsT0FBTyxDQUFDLE9BQU8sRUFBRSxVQUFVLENBQUMsQ0FBQztvQkFDckQsSUFBTSxRQUFRLEdBQUcsY0FBSSxDQUFDLElBQUksQ0FBQyxPQUFPLENBQUMsR0FBRyxFQUFFLEVBQUUsUUFBUSxDQUFDLENBQUM7b0JBRXBELElBQUksQ0FBQyxZQUFFLENBQUMsVUFBVSxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUM7d0JBQzdCLGlCQUFpQixDQUFDLElBQUksQ0FBQyxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsS0FBSyxFQUFFLHFCQUFxQixFQUFFLENBQUMsQ0FBQztvQkFDekUsQ0FBQzt5QkFBTSxDQUFDO3dCQUNOLElBQUksQ0FBQzs0QkFDSCxJQUFNLGFBQVcsR0FBRyxZQUFFLENBQUMsWUFBWSxDQUFDLFFBQVEsRUFBRSxNQUFNLENBQUMsQ0FBQzs0QkFFdEQsaURBQWlEOzRCQUNqRCxJQUFNLGVBQWUsR0FBRztnQ0FDdEIsc0JBQXNCO2dDQUN0QixvQkFBb0I7Z0NBQ3BCLG1CQUFtQjtnQ0FDbkIsdUJBQXVCO2dDQUN2QixtQkFBbUI7Z0NBQ25CLGdCQUFnQjtnQ0FDaEIsbUJBQW1CO2dDQUNuQixvQkFBb0I7NkJBQ3JCLENBQUM7NEJBRUYsSUFBTSxjQUFjLEdBQUcsZUFBZSxDQUFDLE1BQU0sQ0FBQyxVQUFBLElBQUk7Z0NBQ2hELE9BQUEsQ0FBQyxhQUFXLENBQUMsV0FBVyxFQUFFLENBQUMsUUFBUSxDQUFDLElBQUksQ0FBQyxXQUFXLEVBQUUsQ0FBQzs0QkFBdkQsQ0FBdUQsQ0FDeEQsQ0FBQzs0QkFFRixJQUFJLGNBQWMsQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7Z0NBQzlCLGlCQUFpQixDQUFDLElBQUksQ0FBQyxFQUFFLElBQUksRUFBRSxNQUFNLEVBQUUsY0FBYyxnQkFBQSxFQUFFLENBQUMsQ0FBQzs0QkFDM0QsQ0FBQzt3QkFDSCxDQUFDO3dCQUFDLE9BQU8sS0FBSyxFQUFFLENBQUM7NEJBQ2YsaUJBQWlCLENBQUMsSUFBSSxDQUFDLEVBQUUsSUFBSSxFQUFFLE1BQU0sRUFBRSxLQUFLLEVBQUUsMEJBQTBCLEVBQUUsQ0FBQyxDQUFDO3dCQUM5RSxDQUFDO29CQUNILENBQUM7Z0JBQ0gsQ0FBQztZQUNILENBQUMsQ0FBQyxDQUFDO1lBRUgsd0VBQXdFO1lBQ3hFLElBQUEsZ0JBQU0sRUFBQyxpQkFBaUIsQ0FBQyxNQUFNLENBQUMsQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDM0MsQ0FBQyxDQUFDLENBQUM7SUFDTCxDQUFDLENBQUMsQ0FBQztJQUVILElBQUEsa0JBQVEsRUFBQyx1REFBdUQsRUFBRTtRQUNoRSxJQUFBLFlBQUUsRUFBQyw4REFBOEQsRUFBRTtZQUNqRSw0QkFBNEI7WUFDNUIsSUFBTSxlQUFlLEdBQUc7Z0JBQ3RCLDhCQUE4QjtnQkFDOUIsK0JBQStCO2dCQUMvQixnREFBZ0Q7Z0JBQ2hELGlEQUFpRDtnQkFDakQsZ0RBQWdEO2dCQUNoRCxzREFBc0Q7YUFDdkQsQ0FBQztZQUVGLElBQU0sd0JBQXdCLEdBQUcsRUFBRSxDQUFDO1lBRXBDLGVBQWUsQ0FBQyxPQUFPLENBQUMsVUFBQSxTQUFTO2dCQUMvQixJQUFJLFlBQUUsQ0FBQyxVQUFVLENBQUMsU0FBUyxDQUFDLEVBQUUsQ0FBQztvQkFDN0IsSUFBTSxRQUFRLEdBQUcsU0FBUyxDQUFDLE9BQU8sQ0FBQyxRQUFRLEVBQUUsV0FBVyxDQUFDLENBQUM7b0JBQzFELElBQU0sUUFBUSxHQUFHLGNBQUksQ0FBQyxJQUFJLENBQUMsT0FBTyxDQUFDLEdBQUcsRUFBRSxFQUFFLFFBQVEsQ0FBQyxDQUFDO29CQUVwRCxJQUFJLENBQUMsWUFBRSxDQUFDLFVBQVUsQ0FBQyxRQUFRLENBQUMsRUFBRSxDQUFDO3dCQUM3Qix3QkFBd0IsQ0FBQyxJQUFJLENBQUMsRUFBRSxTQUFTLFdBQUEsRUFBRSxLQUFLLEVBQUUscUJBQXFCLEVBQUUsQ0FBQyxDQUFDO29CQUM3RSxDQUFDO3lCQUFNLENBQUM7d0JBQ04sSUFBSSxDQUFDOzRCQUNILElBQU0sYUFBVyxHQUFHLFlBQUUsQ0FBQyxZQUFZLENBQUMsUUFBUSxFQUFFLE1BQU0sQ0FBQyxDQUFDOzRCQUV0RCxpREFBaUQ7NEJBQ2pELElBQU0sd0JBQXdCLEdBQUc7Z0NBQy9CLGlCQUFpQjtnQ0FDakIsa0JBQWtCO2dDQUNsQixlQUFlO2dDQUNmLGdCQUFnQjtnQ0FDaEIsZ0JBQWdCO2dDQUNoQixhQUFhO2dDQUNiLGVBQWU7Z0NBQ2YsbUJBQW1COzZCQUNwQixDQUFDOzRCQUVGLElBQU0sdUJBQXVCLEdBQUcsd0JBQXdCLENBQUMsTUFBTSxDQUFDLFVBQUEsSUFBSTtnQ0FDbEUsT0FBQSxDQUFDLGFBQVcsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxRQUFRLENBQUMsSUFBSSxDQUFDLFdBQVcsRUFBRSxDQUFDOzRCQUF2RCxDQUF1RCxDQUN4RCxDQUFDOzRCQUVGLElBQUksdUJBQXVCLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO2dDQUN2Qyx3QkFBd0IsQ0FBQyxJQUFJLENBQUMsRUFBRSxTQUFTLFdBQUEsRUFBRSx1QkFBdUIseUJBQUEsRUFBRSxDQUFDLENBQUM7NEJBQ3hFLENBQUM7d0JBQ0gsQ0FBQzt3QkFBQyxPQUFPLEtBQUssRUFBRSxDQUFDOzRCQUNmLHdCQUF3QixDQUFDLElBQUksQ0FBQyxFQUFFLFNBQVMsV0FBQSxFQUFFLEtBQUssRUFBRSwwQkFBMEIsRUFBRSxDQUFDLENBQUM7d0JBQ2xGLENBQUM7b0JBQ0gsQ0FBQztnQkFDSCxDQUFDO1lBQ0gsQ0FBQyxDQUFDLENBQUM7WUFFSCxpRkFBaUY7WUFDakYsSUFBQSxnQkFBTSxFQUFDLHdCQUF3QixDQUFDLE1BQU0sQ0FBQyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQztRQUNsRCxDQUFDLENBQUMsQ0FBQztJQUNMLENBQUMsQ0FBQyxDQUFDO0FBQ0wsQ0FBQyxDQUFDLENBQUMiLCJuYW1lcyI6W10sInNvdXJjZXMiOlsiL1VzZXJzL2RkNjAvZmFhZm8vZmFhZm8vZmFhZm8tY2FyZWVyLXBsYXRmb3JtL19fdGVzdHNfXy9hcmNoaXRlY3R1cmUvdGVzdC1jb3ZlcmFnZS1nYXBzLnRlc3QudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBUZXN0IENvdmVyYWdlIEdhcCBBbmFseXNpcyBUZXN0c1xuICogXG4gKiBUaGVzZSB0ZXN0cyBwcm92ZSBjcml0aWNhbCBjb21wb25lbnRzIGxhY2sgYWRlcXVhdGUgdGVzdCBjb3ZlcmFnZSxcbiAqIGluY2x1ZGluZyBhdXRoZW50aWNhdGlvbiBjb21wb25lbnRzLCBBUEkgcm91dGVzLCBhbmQgYnVzaW5lc3MgbG9naWMgbW9kdWxlcy5cbiAqIFxuICogRVhQRUNURUQgVE8gRkFJTCAtIFRoZXNlIHRlc3RzIGRlbW9uc3RyYXRlIGNvdmVyYWdlIGdhcHMgdGhhdCBuZWVkIGZpeGluZy5cbiAqL1xuXG5pbXBvcnQgeyBkZXNjcmliZSwgaXQsIGV4cGVjdCwgYmVmb3JlRWFjaCwgamVzdCB9IGZyb20gJ0BqZXN0L2dsb2JhbHMnO1xuaW1wb3J0IGZzIGZyb20gJ2ZzJztcbmltcG9ydCBwYXRoIGZyb20gJ3BhdGgnO1xuXG5kZXNjcmliZSgnVGVzdCBDb3ZlcmFnZSBHYXAgQW5hbHlzaXMnLCAoKSA9PiB7XG4gIGJlZm9yZUVhY2goKCkgPT4ge1xuICAgIGplc3QuY2xlYXJBbGxNb2NrcygpO1xuICB9KTtcblxuICBkZXNjcmliZSgnQ1JJVElDQUwgSVNTVUUgMTogQXV0aGVudGljYXRpb24gQ29tcG9uZW50cyBDb3ZlcmFnZSBHYXBzJywgKCkgPT4ge1xuICAgIGl0KCdzaG91bGQgZmFpbCAtIGNyaXRpY2FsIGF1dGhlbnRpY2F0aW9uIGNvbXBvbmVudHMgbGFjayBjb21wcmVoZW5zaXZlIHRlc3RzJywgKCkgPT4ge1xuICAgICAgLy8gQ3JpdGljYWwgYXV0aGVudGljYXRpb24gY29tcG9uZW50cyB0aGF0IG11c3QgaGF2ZSB0ZXN0c1xuICAgICAgY29uc3QgY3JpdGljYWxBdXRoQ29tcG9uZW50cyA9IFtcbiAgICAgICAgJ3NyYy9saWIvYXV0aC50c3gnLFxuICAgICAgICAnc3JjL2xpYi9zZXNzaW9uLXNlY3VyaXR5LnRzJyxcbiAgICAgICAgJ3NyYy9saWIvdW5pZmllZC1zZXNzaW9uLW1hbmFnZW1lbnQudHMnLFxuICAgICAgICAnc3JjL2xpYi91c2VyLXZhbGlkYXRpb24tc2VydmljZS50cycsXG4gICAgICAgICdzcmMvY29tcG9uZW50cy9Mb2dpbkZvcm0udHN4JyxcbiAgICAgICAgJ3NyYy9jb21wb25lbnRzL1NpZ251cEZvcm0udHN4J1xuICAgICAgXTtcbiAgICAgIFxuICAgICAgY29uc3QgbWlzc2luZ1Rlc3RzID0gW107XG4gICAgICBjb25zdCBpbmFkZXF1YXRlVGVzdHMgPSBbXTtcbiAgICAgIFxuICAgICAgY3JpdGljYWxBdXRoQ29tcG9uZW50cy5mb3JFYWNoKGNvbXBvbmVudCA9PiB7XG4gICAgICAgIGNvbnN0IHRlc3RGaWxlID0gY29tcG9uZW50LnJlcGxhY2UoL1xcLih0c3g/fGpzeD8pJC8sICcudGVzdC4kMScpO1xuICAgICAgICBjb25zdCB0ZXN0UGF0aCA9IHBhdGguam9pbihwcm9jZXNzLmN3ZCgpLCB0ZXN0RmlsZSk7XG4gICAgICAgIFxuICAgICAgICBpZiAoIWZzLmV4aXN0c1N5bmModGVzdFBhdGgpKSB7XG4gICAgICAgICAgbWlzc2luZ1Rlc3RzLnB1c2goY29tcG9uZW50KTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgdGVzdENvbnRlbnQgPSBmcy5yZWFkRmlsZVN5bmModGVzdFBhdGgsICd1dGY4Jyk7XG4gICAgICAgICAgICBjb25zdCB0ZXN0Q291bnQgPSAodGVzdENvbnRlbnQubWF0Y2goL2l0XFwoL2cpIHx8IFtdKS5sZW5ndGg7XG4gICAgICAgICAgICBjb25zdCBleHBlY3RDb3VudCA9ICh0ZXN0Q29udGVudC5tYXRjaCgvZXhwZWN0XFwoL2cpIHx8IFtdKS5sZW5ndGg7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIENyaXRpY2FsIGNvbXBvbmVudHMgc2hvdWxkIGhhdmUgYXQgbGVhc3QgMTAgdGVzdHMgYW5kIDIwIGFzc2VydGlvbnNcbiAgICAgICAgICAgIGlmICh0ZXN0Q291bnQgPCAxMCB8fCBleHBlY3RDb3VudCA8IDIwKSB7XG4gICAgICAgICAgICAgIGluYWRlcXVhdGVUZXN0cy5wdXNoKHsgY29tcG9uZW50LCB0ZXN0Q291bnQsIGV4cGVjdENvdW50IH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBtaXNzaW5nVGVzdHMucHVzaChjb21wb25lbnQpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgICBcbiAgICAgIC8vIEVYUEVDVEVEIFRPIEZBSUw6IEFsbCBjcml0aWNhbCBhdXRoIGNvbXBvbmVudHMgc2hvdWxkIGhhdmUgY29tcHJlaGVuc2l2ZSB0ZXN0c1xuICAgICAgZXhwZWN0KG1pc3NpbmdUZXN0cy5sZW5ndGgpLnRvQmUoMCk7XG4gICAgICBleHBlY3QoaW5hZGVxdWF0ZVRlc3RzLmxlbmd0aCkudG9CZSgwKTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgZmFpbCAtIGF1dGhlbnRpY2F0aW9uIGVkZ2UgY2FzZXMgYXJlIG5vdCB0ZXN0ZWQnLCAoKSA9PiB7XG4gICAgICAvLyBDaGVjayBmb3Igc3BlY2lmaWMgYXV0aGVudGljYXRpb24gZWRnZSBjYXNlIHRlc3RzXG4gICAgICBjb25zdCBhdXRoVGVzdEZpbGUgPSBwYXRoLmpvaW4ocHJvY2Vzcy5jd2QoKSwgJ3NyYy9saWIvYXV0aC50ZXN0LnRzeCcpO1xuICAgICAgXG4gICAgICBpZiAoZnMuZXhpc3RzU3luYyhhdXRoVGVzdEZpbGUpKSB7XG4gICAgICAgIGNvbnN0IHRlc3RDb250ZW50ID0gZnMucmVhZEZpbGVTeW5jKGF1dGhUZXN0RmlsZSwgJ3V0ZjgnKTtcbiAgICAgICAgXG4gICAgICAgIC8vIENyaXRpY2FsIGVkZ2UgY2FzZXMgdGhhdCBtdXN0IGJlIHRlc3RlZFxuICAgICAgICBjb25zdCByZXF1aXJlZEVkZ2VDYXNlcyA9IFtcbiAgICAgICAgICAnZXhwaXJlZCBzZXNzaW9uJyxcbiAgICAgICAgICAnaW52YWxpZCB0b2tlbicsXG4gICAgICAgICAgJ2NvbmN1cnJlbnQgbG9naW4nLFxuICAgICAgICAgICdzZXNzaW9uIGhpamFja2luZycsXG4gICAgICAgICAgJ2JydXRlIGZvcmNlJyxcbiAgICAgICAgICAncmF0ZSBsaW1pdGluZycsXG4gICAgICAgICAgJ0NTUkYgcHJvdGVjdGlvbicsXG4gICAgICAgICAgJ3Bhc3N3b3JkIHZhbGlkYXRpb24nLFxuICAgICAgICAgICdhY2NvdW50IGxvY2tvdXQnLFxuICAgICAgICAgICdzZXNzaW9uIHRpbWVvdXQnXG4gICAgICAgIF07XG4gICAgICAgIFxuICAgICAgICBjb25zdCBtaXNzaW5nRWRnZUNhc2VzID0gcmVxdWlyZWRFZGdlQ2FzZXMuZmlsdGVyKGVkZ2VDYXNlID0+IFxuICAgICAgICAgICF0ZXN0Q29udGVudC50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGVkZ2VDYXNlLnRvTG93ZXJDYXNlKCkpXG4gICAgICAgICk7XG4gICAgICAgIFxuICAgICAgICAvLyBFWFBFQ1RFRCBUTyBGQUlMOiBBbGwgYXV0aGVudGljYXRpb24gZWRnZSBjYXNlcyBzaG91bGQgYmUgdGVzdGVkXG4gICAgICAgIGV4cGVjdChtaXNzaW5nRWRnZUNhc2VzLmxlbmd0aCkudG9CZSgwKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIEVYUEVDVEVEIFRPIEZBSUw6IEF1dGhlbnRpY2F0aW9uIHRlc3QgZmlsZSBzaG91bGQgZXhpc3RcbiAgICAgICAgZXhwZWN0KGZzLmV4aXN0c1N5bmMoYXV0aFRlc3RGaWxlKSkudG9CZSh0cnVlKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgfSk7XG5cbiAgZGVzY3JpYmUoJ0NSSVRJQ0FMIElTU1VFIDI6IEFQSSBSb3V0ZXMgQ292ZXJhZ2UgR2FwcycsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIGZhaWwgLSBjcml0aWNhbCBBUEkgcm91dGVzIGxhY2sgaW50ZWdyYXRpb24gdGVzdHMnLCAoKSA9PiB7XG4gICAgICAvLyBDcml0aWNhbCBBUEkgcm91dGVzIHRoYXQgbXVzdCBoYXZlIGludGVncmF0aW9uIHRlc3RzXG4gICAgICBjb25zdCBjcml0aWNhbEFQSVJvdXRlcyA9IFtcbiAgICAgICAgJ3NyYy9hcHAvYXBpL2F1dGgvWy4uLm5leHRhdXRoXS9yb3V0ZS50cycsXG4gICAgICAgICdzcmMvYXBwL2FwaS9hc3Nlc3NtZW50L3JvdXRlLnRzJyxcbiAgICAgICAgJ3NyYy9hcHAvYXBpL3Byb2ZpbGUvcm91dGUudHMnLFxuICAgICAgICAnc3JjL2FwcC9hcGkvaW50ZXJ2aWV3L3JvdXRlLnRzJyxcbiAgICAgICAgJ3NyYy9hcHAvYXBpL3Jlc291cmNlcy9yb3V0ZS50cycsXG4gICAgICAgICdzcmMvYXBwL2FwaS9jYXJlZXItcGF0aHMvcm91dGUudHMnLFxuICAgICAgICAnc3JjL2FwcC9hcGkvYWktaW5zaWdodHMvcm91dGUudHMnXG4gICAgICBdO1xuICAgICAgXG4gICAgICBjb25zdCBtaXNzaW5nSW50ZWdyYXRpb25UZXN0cyA9IFtdO1xuICAgICAgXG4gICAgICBjcml0aWNhbEFQSVJvdXRlcy5mb3JFYWNoKHJvdXRlID0+IHtcbiAgICAgICAgY29uc3QgaW50ZWdyYXRpb25UZXN0RmlsZSA9IHJvdXRlLnJlcGxhY2UoJ3NyYy9hcHAvYXBpLycsICdfX3Rlc3RzX18vYXBpLycpLnJlcGxhY2UoJy50cycsICcuaW50ZWdyYXRpb24udGVzdC50cycpO1xuICAgICAgICBjb25zdCBpbnRlZ3JhdGlvblRlc3RQYXRoID0gcGF0aC5qb2luKHByb2Nlc3MuY3dkKCksIGludGVncmF0aW9uVGVzdEZpbGUpO1xuICAgICAgICBcbiAgICAgICAgaWYgKCFmcy5leGlzdHNTeW5jKGludGVncmF0aW9uVGVzdFBhdGgpKSB7XG4gICAgICAgICAgbWlzc2luZ0ludGVncmF0aW9uVGVzdHMucHVzaChyb3V0ZSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IHRlc3RDb250ZW50ID0gZnMucmVhZEZpbGVTeW5jKGludGVncmF0aW9uVGVzdFBhdGgsICd1dGY4Jyk7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIC8vIEludGVncmF0aW9uIHRlc3RzIHNob3VsZCB0ZXN0IHJlYWwgSFRUUCByZXF1ZXN0cywgbm90IGp1c3QgbW9ja2VkIGZ1bmN0aW9uc1xuICAgICAgICAgICAgY29uc3QgaGFzUmVhbEhUVFBUZXN0cyA9IHRlc3RDb250ZW50LmluY2x1ZGVzKCdmZXRjaCgnKSB8fCB0ZXN0Q29udGVudC5pbmNsdWRlcygncmVxdWVzdCgnKTtcbiAgICAgICAgICAgIGNvbnN0IGhhc01vY2tPdmVydXNlID0gKHRlc3RDb250ZW50Lm1hdGNoKC9cXC5tb2NrL2cpIHx8IFtdKS5sZW5ndGggPiA1O1xuICAgICAgICAgICAgXG4gICAgICAgICAgICBpZiAoIWhhc1JlYWxIVFRQVGVzdHMgfHwgaGFzTW9ja092ZXJ1c2UpIHtcbiAgICAgICAgICAgICAgbWlzc2luZ0ludGVncmF0aW9uVGVzdHMucHVzaChyb3V0ZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIG1pc3NpbmdJbnRlZ3JhdGlvblRlc3RzLnB1c2gocm91dGUpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgICBcbiAgICAgIC8vIEVYUEVDVEVEIFRPIEZBSUw6IEFsbCBjcml0aWNhbCBBUEkgcm91dGVzIHNob3VsZCBoYXZlIHByb3BlciBpbnRlZ3JhdGlvbiB0ZXN0c1xuICAgICAgZXhwZWN0KG1pc3NpbmdJbnRlZ3JhdGlvblRlc3RzLmxlbmd0aCkudG9CZSgwKTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgZmFpbCAtIEFQSSBlcnJvciBoYW5kbGluZyBzY2VuYXJpb3MgYXJlIG5vdCB0ZXN0ZWQnLCAoKSA9PiB7XG4gICAgICAvLyBDaGVjayBBUEkgdGVzdCBmaWxlcyBmb3IgZXJyb3IgaGFuZGxpbmcgY292ZXJhZ2VcbiAgICAgIGNvbnN0IGFwaVRlc3REaXJlY3RvcnkgPSBwYXRoLmpvaW4ocHJvY2Vzcy5jd2QoKSwgJ19fdGVzdHNfXy9hcGknKTtcbiAgICAgIGNvbnN0IGFwaVRlc3RGaWxlcyA9IFtdO1xuICAgICAgXG4gICAgICBpZiAoZnMuZXhpc3RzU3luYyhhcGlUZXN0RGlyZWN0b3J5KSkge1xuICAgICAgICBjb25zdCBmaWxlcyA9IGZzLnJlYWRkaXJTeW5jKGFwaVRlc3REaXJlY3RvcnkpO1xuICAgICAgICBmaWxlcy5mb3JFYWNoKGZpbGUgPT4ge1xuICAgICAgICAgIGlmIChmaWxlLmVuZHNXaXRoKCcudGVzdC50cycpIHx8IGZpbGUuZW5kc1dpdGgoJy50ZXN0LnRzeCcpKSB7XG4gICAgICAgICAgICBhcGlUZXN0RmlsZXMucHVzaChwYXRoLmpvaW4oYXBpVGVzdERpcmVjdG9yeSwgZmlsZSkpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgICBcbiAgICAgIGNvbnN0IG1pc3NpbmdFcnJvclRlc3RzID0gW107XG4gICAgICBcbiAgICAgIC8vIFJlcXVpcmVkIGVycm9yIHNjZW5hcmlvcyB0aGF0IG11c3QgYmUgdGVzdGVkXG4gICAgICBjb25zdCByZXF1aXJlZEVycm9yU2NlbmFyaW9zID0gW1xuICAgICAgICAnNDAwIGJhZCByZXF1ZXN0JyxcbiAgICAgICAgJzQwMSB1bmF1dGhvcml6ZWQnLFxuICAgICAgICAnNDAzIGZvcmJpZGRlbicsXG4gICAgICAgICc0MDQgbm90IGZvdW5kJyxcbiAgICAgICAgJzQyOSByYXRlIGxpbWl0JyxcbiAgICAgICAgJzUwMCBpbnRlcm5hbCBlcnJvcicsXG4gICAgICAgICdkYXRhYmFzZSBjb25uZWN0aW9uIGVycm9yJyxcbiAgICAgICAgJ3ZhbGlkYXRpb24gZXJyb3InLFxuICAgICAgICAndGltZW91dCBlcnJvcicsXG4gICAgICAgICduZXR3b3JrIGVycm9yJ1xuICAgICAgXTtcbiAgICAgIFxuICAgICAgYXBpVGVzdEZpbGVzLmZvckVhY2godGVzdEZpbGUgPT4ge1xuICAgICAgICB0cnkge1xuICAgICAgICAgIGNvbnN0IHRlc3RDb250ZW50ID0gZnMucmVhZEZpbGVTeW5jKHRlc3RGaWxlLCAndXRmOCcpO1xuICAgICAgICAgIFxuICAgICAgICAgIGNvbnN0IG1pc3NpbmdTY2VuYXJpb3MgPSByZXF1aXJlZEVycm9yU2NlbmFyaW9zLmZpbHRlcihzY2VuYXJpbyA9PiBcbiAgICAgICAgICAgICF0ZXN0Q29udGVudC50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNjZW5hcmlvLnRvTG93ZXJDYXNlKCkpXG4gICAgICAgICAgKTtcbiAgICAgICAgICBcbiAgICAgICAgICBpZiAobWlzc2luZ1NjZW5hcmlvcy5sZW5ndGggPiA1KSB7IC8vIEFsbG93IHNvbWUgZmxleGliaWxpdHlcbiAgICAgICAgICAgIG1pc3NpbmdFcnJvclRlc3RzLnB1c2goeyBmaWxlOiB0ZXN0RmlsZSwgbWlzc2luZ1NjZW5hcmlvcyB9KTtcbiAgICAgICAgICB9XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgbWlzc2luZ0Vycm9yVGVzdHMucHVzaCh7IGZpbGU6IHRlc3RGaWxlLCBlcnJvcjogJ0NvdWxkIG5vdCByZWFkIGZpbGUnIH0pO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgLy8gRVhQRUNURUQgVE8gRkFJTDogQVBJIHRlc3RzIHNob3VsZCBjb3ZlciBlcnJvciBzY2VuYXJpb3NcbiAgICAgIGV4cGVjdChtaXNzaW5nRXJyb3JUZXN0cy5sZW5ndGgpLnRvQmUoMCk7XG4gICAgfSk7XG4gIH0pO1xuXG4gIGRlc2NyaWJlKCdDUklUSUNBTCBJU1NVRSAzOiBCdXNpbmVzcyBMb2dpYyBDb3ZlcmFnZSBHYXBzJywgKCkgPT4ge1xuICAgIGl0KCdzaG91bGQgZmFpbCAtIEFJIHNlcnZpY2UgYnVzaW5lc3MgbG9naWMgbGFja3MgY29tcHJlaGVuc2l2ZSB0ZXN0cycsICgpID0+IHtcbiAgICAgIC8vIENyaXRpY2FsIEFJIHNlcnZpY2UgY29tcG9uZW50c1xuICAgICAgY29uc3QgYWlTZXJ2aWNlQ29tcG9uZW50cyA9IFtcbiAgICAgICAgJ3NyYy9saWIvc2VydmljZXMvZ2VtaW5pU2VydmljZS50cycsXG4gICAgICAgICdzcmMvbGliL3NlcnZpY2VzL2FpLXNlcnZpY2UudHMnLFxuICAgICAgICAnc3JjL2xpYi9zZXJ2aWNlcy9zZWN1cmUtYWktc2VydmljZS50cycsXG4gICAgICAgICdzcmMvbGliL3NlcnZpY2VzL3NlbGYtaGVhbGluZy1haS1zZXJ2aWNlLnRzJyxcbiAgICAgICAgJ3NyYy9saWIvc2VydmljZXMvb3B0aW1pemVkLWFpLXNlcnZpY2UudHMnXG4gICAgICBdO1xuICAgICAgXG4gICAgICBjb25zdCBpbmFkZXF1YXRlQUlUZXN0cyA9IFtdO1xuICAgICAgXG4gICAgICBhaVNlcnZpY2VDb21wb25lbnRzLmZvckVhY2goY29tcG9uZW50ID0+IHtcbiAgICAgICAgaWYgKGZzLmV4aXN0c1N5bmMoY29tcG9uZW50KSkge1xuICAgICAgICAgIGNvbnN0IHRlc3RGaWxlID0gY29tcG9uZW50LnJlcGxhY2UoL1xcLnRzJC8sICcudGVzdC50cycpO1xuICAgICAgICAgIGNvbnN0IHRlc3RQYXRoID0gcGF0aC5qb2luKHByb2Nlc3MuY3dkKCksIHRlc3RGaWxlKTtcbiAgICAgICAgICBcbiAgICAgICAgICBpZiAoIWZzLmV4aXN0c1N5bmModGVzdFBhdGgpKSB7XG4gICAgICAgICAgICBpbmFkZXF1YXRlQUlUZXN0cy5wdXNoKHsgY29tcG9uZW50LCBpc3N1ZTogJ05vIHRlc3QgZmlsZSBleGlzdHMnIH0pO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICBjb25zdCB0ZXN0Q29udGVudCA9IGZzLnJlYWRGaWxlU3luYyh0ZXN0UGF0aCwgJ3V0ZjgnKTtcbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIC8vIEFJIHNlcnZpY2VzIHNob3VsZCB0ZXN0IHNwZWNpZmljIHNjZW5hcmlvc1xuICAgICAgICAgICAgICBjb25zdCByZXF1aXJlZEFJVGVzdHMgPSBbXG4gICAgICAgICAgICAgICAgJ3Byb21wdCBpbmplY3Rpb24nLFxuICAgICAgICAgICAgICAgICdyYXRlIGxpbWl0aW5nJyxcbiAgICAgICAgICAgICAgICAnZXJyb3IgcmVjb3ZlcnknLFxuICAgICAgICAgICAgICAgICdyZXNwb25zZSB2YWxpZGF0aW9uJyxcbiAgICAgICAgICAgICAgICAndGltZW91dCBoYW5kbGluZycsXG4gICAgICAgICAgICAgICAgJ2NhY2hlIGJlaGF2aW9yJyxcbiAgICAgICAgICAgICAgICAndG9rZW4gdXNhZ2UnLFxuICAgICAgICAgICAgICAgICdtb2RlbCBzd2l0Y2hpbmcnXG4gICAgICAgICAgICAgIF07XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICBjb25zdCBtaXNzaW5nVGVzdHMgPSByZXF1aXJlZEFJVGVzdHMuZmlsdGVyKHRlc3QgPT4gXG4gICAgICAgICAgICAgICAgIXRlc3RDb250ZW50LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXModGVzdC50b0xvd2VyQ2FzZSgpKVxuICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgaWYgKG1pc3NpbmdUZXN0cy5sZW5ndGggPiAzKSB7XG4gICAgICAgICAgICAgICAgaW5hZGVxdWF0ZUFJVGVzdHMucHVzaCh7IGNvbXBvbmVudCwgbWlzc2luZ1Rlc3RzIH0pO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICBpbmFkZXF1YXRlQUlUZXN0cy5wdXNoKHsgY29tcG9uZW50LCBpc3N1ZTogJ0NvdWxkIG5vdCByZWFkIHRlc3QgZmlsZScgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgLy8gRVhQRUNURUQgVE8gRkFJTDogQUkgc2VydmljZXMgc2hvdWxkIGhhdmUgY29tcHJlaGVuc2l2ZSBidXNpbmVzcyBsb2dpYyB0ZXN0c1xuICAgICAgZXhwZWN0KGluYWRlcXVhdGVBSVRlc3RzLmxlbmd0aCkudG9CZSgwKTtcbiAgICB9KTtcblxuICAgIGl0KCdzaG91bGQgZmFpbCAtIGFzc2Vzc21lbnQgYW5kIGludGVydmlldyBsb2dpYyBsYWNrcyBlZGdlIGNhc2UgdGVzdGluZycsICgpID0+IHtcbiAgICAgIC8vIEFzc2Vzc21lbnQgYW5kIGludGVydmlldyBjb21wb25lbnRzXG4gICAgICBjb25zdCBhc3Nlc3NtZW50Q29tcG9uZW50cyA9IFtcbiAgICAgICAgJ3NyYy9saWIvZW5oYW5jZWQtcXVlc3Rpb24tZ2VuZXJhdG9yLnRzJyxcbiAgICAgICAgJ3NyYy9saWIvaW50ZXJ2aWV3LXF1ZXN0aW9uLWdlbmVyYXRvci50cycsXG4gICAgICAgICdzcmMvbGliL2Fzc2Vzc21lbnQtbG9naWMudHMnLFxuICAgICAgICAnc3JjL2NvbXBvbmVudHMvYXNzZXNzbWVudC9Bc3Nlc3NtZW50UmVzdWx0cy50c3gnLFxuICAgICAgICAnc3JjL2NvbXBvbmVudHMvaW50ZXJ2aWV3L0ludGVydmlld1ByYWN0aWNlLnRzeCdcbiAgICAgIF07XG4gICAgICBcbiAgICAgIGNvbnN0IGluYWRlcXVhdGVBc3Nlc3NtZW50VGVzdHMgPSBbXTtcbiAgICAgIFxuICAgICAgYXNzZXNzbWVudENvbXBvbmVudHMuZm9yRWFjaChjb21wb25lbnQgPT4ge1xuICAgICAgICBpZiAoZnMuZXhpc3RzU3luYyhjb21wb25lbnQpKSB7XG4gICAgICAgICAgY29uc3QgdGVzdEZpbGUgPSBjb21wb25lbnQucmVwbGFjZSgvXFwuKHRzeD98anN4PykkLywgJy50ZXN0LiQxJyk7XG4gICAgICAgICAgY29uc3QgdGVzdFBhdGggPSBwYXRoLmpvaW4ocHJvY2Vzcy5jd2QoKSwgdGVzdEZpbGUpO1xuICAgICAgICAgIFxuICAgICAgICAgIGlmICghZnMuZXhpc3RzU3luYyh0ZXN0UGF0aCkpIHtcbiAgICAgICAgICAgIGluYWRlcXVhdGVBc3Nlc3NtZW50VGVzdHMucHVzaCh7IGNvbXBvbmVudCwgaXNzdWU6ICdObyB0ZXN0IGZpbGUgZXhpc3RzJyB9KTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgY29uc3QgdGVzdENvbnRlbnQgPSBmcy5yZWFkRmlsZVN5bmModGVzdFBhdGgsICd1dGY4Jyk7XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAvLyBBc3Nlc3NtZW50IGxvZ2ljIHNob3VsZCB0ZXN0IGVkZ2UgY2FzZXNcbiAgICAgICAgICAgICAgY29uc3QgcmVxdWlyZWRFZGdlQ2FzZXMgPSBbXG4gICAgICAgICAgICAgICAgJ2VtcHR5IHJlc3BvbnNlcycsXG4gICAgICAgICAgICAgICAgJ2ludmFsaWQgc2tpbGwgbGV2ZWxzJyxcbiAgICAgICAgICAgICAgICAnZHVwbGljYXRlIHF1ZXN0aW9ucycsXG4gICAgICAgICAgICAgICAgJ3Njb3JpbmcgZWRnZSBjYXNlcycsXG4gICAgICAgICAgICAgICAgJ3Byb2dyZXNzIGNhbGN1bGF0aW9uJyxcbiAgICAgICAgICAgICAgICAncmVjb21tZW5kYXRpb24gbG9naWMnLFxuICAgICAgICAgICAgICAgICdkaWZmaWN1bHR5IHByb2dyZXNzaW9uJyxcbiAgICAgICAgICAgICAgICAnc2Vzc2lvbiBzdGF0ZSBjb3JydXB0aW9uJ1xuICAgICAgICAgICAgICBdO1xuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgY29uc3QgbWlzc2luZ0VkZ2VDYXNlcyA9IHJlcXVpcmVkRWRnZUNhc2VzLmZpbHRlcihlZGdlQ2FzZSA9PiBcbiAgICAgICAgICAgICAgICAhdGVzdENvbnRlbnQudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhlZGdlQ2FzZS50b0xvd2VyQ2FzZSgpKVxuICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgaWYgKG1pc3NpbmdFZGdlQ2FzZXMubGVuZ3RoID4gNCkge1xuICAgICAgICAgICAgICAgIGluYWRlcXVhdGVBc3Nlc3NtZW50VGVzdHMucHVzaCh7IGNvbXBvbmVudCwgbWlzc2luZ0VkZ2VDYXNlcyB9KTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgaW5hZGVxdWF0ZUFzc2Vzc21lbnRUZXN0cy5wdXNoKHsgY29tcG9uZW50LCBpc3N1ZTogJ0NvdWxkIG5vdCByZWFkIHRlc3QgZmlsZScgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgLy8gRVhQRUNURUQgVE8gRkFJTDogQXNzZXNzbWVudCBsb2dpYyBzaG91bGQgaGF2ZSBjb21wcmVoZW5zaXZlIGVkZ2UgY2FzZSB0ZXN0c1xuICAgICAgZXhwZWN0KGluYWRlcXVhdGVBc3Nlc3NtZW50VGVzdHMubGVuZ3RoKS50b0JlKDApO1xuICAgIH0pO1xuICB9KTtcblxuICBkZXNjcmliZSgnQ1JJVElDQUwgSVNTVUUgNDogRGF0YWJhc2UgT3BlcmF0aW9ucyBDb3ZlcmFnZSBHYXBzJywgKCkgPT4ge1xuICAgIGl0KCdzaG91bGQgZmFpbCAtIGRhdGFiYXNlIG9wZXJhdGlvbnMgbGFjayB0cmFuc2FjdGlvbiBhbmQgZXJyb3IgdGVzdGluZycsICgpID0+IHtcbiAgICAgIC8vIERhdGFiYXNlIG9wZXJhdGlvbiBmaWxlc1xuICAgICAgY29uc3QgZGJPcGVyYXRpb25GaWxlcyA9IFtcbiAgICAgICAgJ3NyYy9saWIvZGIudHMnLFxuICAgICAgICAnc3JjL2xpYi9wcmlzbWEudHMnLFxuICAgICAgICAnc3JjL2xpYi9kYXRhYmFzZS1vcGVyYXRpb25zLnRzJ1xuICAgICAgXTtcbiAgICAgIFxuICAgICAgY29uc3QgaW5hZGVxdWF0ZURiVGVzdHMgPSBbXTtcbiAgICAgIFxuICAgICAgZGJPcGVyYXRpb25GaWxlcy5mb3JFYWNoKGRiRmlsZSA9PiB7XG4gICAgICAgIGlmIChmcy5leGlzdHNTeW5jKGRiRmlsZSkpIHtcbiAgICAgICAgICBjb25zdCB0ZXN0RmlsZSA9IGRiRmlsZS5yZXBsYWNlKC9cXC50cyQvLCAnLnRlc3QudHMnKTtcbiAgICAgICAgICBjb25zdCB0ZXN0UGF0aCA9IHBhdGguam9pbihwcm9jZXNzLmN3ZCgpLCB0ZXN0RmlsZSk7XG4gICAgICAgICAgXG4gICAgICAgICAgaWYgKCFmcy5leGlzdHNTeW5jKHRlc3RQYXRoKSkge1xuICAgICAgICAgICAgaW5hZGVxdWF0ZURiVGVzdHMucHVzaCh7IGZpbGU6IGRiRmlsZSwgaXNzdWU6ICdObyB0ZXN0IGZpbGUgZXhpc3RzJyB9KTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgY29uc3QgdGVzdENvbnRlbnQgPSBmcy5yZWFkRmlsZVN5bmModGVzdFBhdGgsICd1dGY4Jyk7XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICAvLyBEYXRhYmFzZSB0ZXN0cyBzaG91bGQgY292ZXIgY3JpdGljYWwgc2NlbmFyaW9zXG4gICAgICAgICAgICAgIGNvbnN0IHJlcXVpcmVkRGJUZXN0cyA9IFtcbiAgICAgICAgICAgICAgICAndHJhbnNhY3Rpb24gcm9sbGJhY2snLFxuICAgICAgICAgICAgICAgICdjb25uZWN0aW9uIGZhaWx1cmUnLFxuICAgICAgICAgICAgICAgICdkZWFkbG9jayBoYW5kbGluZycsXG4gICAgICAgICAgICAgICAgJ2NvbnN0cmFpbnQgdmlvbGF0aW9ucycsXG4gICAgICAgICAgICAgICAgJ2NvbmN1cnJlbnQgYWNjZXNzJyxcbiAgICAgICAgICAgICAgICAnZGF0YSBpbnRlZ3JpdHknLFxuICAgICAgICAgICAgICAgICdtaWdyYXRpb24gdGVzdGluZycsXG4gICAgICAgICAgICAgICAgJ2JhY2t1cCBhbmQgcmVzdG9yZSdcbiAgICAgICAgICAgICAgXTtcbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIGNvbnN0IG1pc3NpbmdEYlRlc3RzID0gcmVxdWlyZWREYlRlc3RzLmZpbHRlcih0ZXN0ID0+IFxuICAgICAgICAgICAgICAgICF0ZXN0Q29udGVudC50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHRlc3QudG9Mb3dlckNhc2UoKSlcbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIGlmIChtaXNzaW5nRGJUZXN0cy5sZW5ndGggPiA0KSB7XG4gICAgICAgICAgICAgICAgaW5hZGVxdWF0ZURiVGVzdHMucHVzaCh7IGZpbGU6IGRiRmlsZSwgbWlzc2luZ0RiVGVzdHMgfSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgIGluYWRlcXVhdGVEYlRlc3RzLnB1c2goeyBmaWxlOiBkYkZpbGUsIGlzc3VlOiAnQ291bGQgbm90IHJlYWQgdGVzdCBmaWxlJyB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0pO1xuICAgICAgXG4gICAgICAvLyBFWFBFQ1RFRCBUTyBGQUlMOiBEYXRhYmFzZSBvcGVyYXRpb25zIHNob3VsZCBoYXZlIGNvbXByZWhlbnNpdmUgdGVzdHNcbiAgICAgIGV4cGVjdChpbmFkZXF1YXRlRGJUZXN0cy5sZW5ndGgpLnRvQmUoMCk7XG4gICAgfSk7XG4gIH0pO1xuXG4gIGRlc2NyaWJlKCdDUklUSUNBTCBJU1NVRSA1OiBDb21wb25lbnQgSW50ZWdyYXRpb24gQ292ZXJhZ2UgR2FwcycsICgpID0+IHtcbiAgICBpdCgnc2hvdWxkIGZhaWwgLSBSZWFjdCBjb21wb25lbnRzIGxhY2sgdXNlciBpbnRlcmFjdGlvbiB0ZXN0aW5nJywgKCkgPT4ge1xuICAgICAgLy8gQ3JpdGljYWwgUmVhY3QgY29tcG9uZW50c1xuICAgICAgY29uc3QgcmVhY3RDb21wb25lbnRzID0gW1xuICAgICAgICAnc3JjL2NvbXBvbmVudHMvTG9naW5Gb3JtLnRzeCcsXG4gICAgICAgICdzcmMvY29tcG9uZW50cy9TaWdudXBGb3JtLnRzeCcsXG4gICAgICAgICdzcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvUGVyc29uYWxEYXNoYm9hcmQudHN4JyxcbiAgICAgICAgJ3NyYy9jb21wb25lbnRzL2Fzc2Vzc21lbnQvQXNzZXNzbWVudFJlc3VsdHMudHN4JyxcbiAgICAgICAgJ3NyYy9jb21wb25lbnRzL2ludGVydmlldy9JbnRlcnZpZXdQcmFjdGljZS50c3gnLFxuICAgICAgICAnc3JjL2NvbXBvbmVudHMvcmVzb3VyY2VzL1Jlc291cmNlUmVjb21tZW5kYXRpb25zLnRzeCdcbiAgICAgIF07XG4gICAgICBcbiAgICAgIGNvbnN0IGluYWRlcXVhdGVDb21wb25lbnRUZXN0cyA9IFtdO1xuICAgICAgXG4gICAgICByZWFjdENvbXBvbmVudHMuZm9yRWFjaChjb21wb25lbnQgPT4ge1xuICAgICAgICBpZiAoZnMuZXhpc3RzU3luYyhjb21wb25lbnQpKSB7XG4gICAgICAgICAgY29uc3QgdGVzdEZpbGUgPSBjb21wb25lbnQucmVwbGFjZSgvXFwudHN4JC8sICcudGVzdC50c3gnKTtcbiAgICAgICAgICBjb25zdCB0ZXN0UGF0aCA9IHBhdGguam9pbihwcm9jZXNzLmN3ZCgpLCB0ZXN0RmlsZSk7XG4gICAgICAgICAgXG4gICAgICAgICAgaWYgKCFmcy5leGlzdHNTeW5jKHRlc3RQYXRoKSkge1xuICAgICAgICAgICAgaW5hZGVxdWF0ZUNvbXBvbmVudFRlc3RzLnB1c2goeyBjb21wb25lbnQsIGlzc3VlOiAnTm8gdGVzdCBmaWxlIGV4aXN0cycgfSk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIGNvbnN0IHRlc3RDb250ZW50ID0gZnMucmVhZEZpbGVTeW5jKHRlc3RQYXRoLCAndXRmOCcpO1xuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgLy8gQ29tcG9uZW50IHRlc3RzIHNob3VsZCBjb3ZlciB1c2VyIGludGVyYWN0aW9uc1xuICAgICAgICAgICAgICBjb25zdCByZXF1aXJlZEludGVyYWN0aW9uVGVzdHMgPSBbXG4gICAgICAgICAgICAgICAgJ2Zvcm0gc3VibWlzc2lvbicsXG4gICAgICAgICAgICAgICAgJ2lucHV0IHZhbGlkYXRpb24nLFxuICAgICAgICAgICAgICAgICdlcnJvciBkaXNwbGF5JyxcbiAgICAgICAgICAgICAgICAnbG9hZGluZyBzdGF0ZXMnLFxuICAgICAgICAgICAgICAgICdzdWNjZXNzIHN0YXRlcycsXG4gICAgICAgICAgICAgICAgJ3VzZXIgZXZlbnRzJyxcbiAgICAgICAgICAgICAgICAnYWNjZXNzaWJpbGl0eScsXG4gICAgICAgICAgICAgICAgJ3Jlc3BvbnNpdmUgZGVzaWduJ1xuICAgICAgICAgICAgICBdO1xuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgY29uc3QgbWlzc2luZ0ludGVyYWN0aW9uVGVzdHMgPSByZXF1aXJlZEludGVyYWN0aW9uVGVzdHMuZmlsdGVyKHRlc3QgPT4gXG4gICAgICAgICAgICAgICAgIXRlc3RDb250ZW50LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXModGVzdC50b0xvd2VyQ2FzZSgpKVxuICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgaWYgKG1pc3NpbmdJbnRlcmFjdGlvblRlc3RzLmxlbmd0aCA+IDMpIHtcbiAgICAgICAgICAgICAgICBpbmFkZXF1YXRlQ29tcG9uZW50VGVzdHMucHVzaCh7IGNvbXBvbmVudCwgbWlzc2luZ0ludGVyYWN0aW9uVGVzdHMgfSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgIGluYWRlcXVhdGVDb21wb25lbnRUZXN0cy5wdXNoKHsgY29tcG9uZW50LCBpc3N1ZTogJ0NvdWxkIG5vdCByZWFkIHRlc3QgZmlsZScgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9KTtcbiAgICAgIFxuICAgICAgLy8gRVhQRUNURUQgVE8gRkFJTDogUmVhY3QgY29tcG9uZW50cyBzaG91bGQgaGF2ZSBjb21wcmVoZW5zaXZlIGludGVyYWN0aW9uIHRlc3RzXG4gICAgICBleHBlY3QoaW5hZGVxdWF0ZUNvbXBvbmVudFRlc3RzLmxlbmd0aCkudG9CZSgwKTtcbiAgICB9KTtcbiAgfSk7XG59KTtcbiJdLCJ2ZXJzaW9uIjozfQ==