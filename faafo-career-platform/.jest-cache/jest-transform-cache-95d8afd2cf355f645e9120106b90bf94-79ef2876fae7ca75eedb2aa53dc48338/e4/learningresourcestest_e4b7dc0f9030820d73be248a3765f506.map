{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/learning-resources.test.ts", "mappings": ";AAAA;;;;;;;GAOG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,oDAAoD;AACpD,iDAAuG;AAEvG,QAAQ,CAAC,+BAA+B,EAAE;IACxC,IAAI,MAAoB,CAAC;IACzB,IAAI,QAAa,CAAC;IAElB,SAAS,CAAC;;YACR,MAAM,GAAG,IAAI,0BAAY,EAAE,CAAC;;;SAC7B,CAAC,CAAC;IAEH,UAAU,CAAC;;;wBACT,qBAAM,MAAM,CAAC,OAAO,EAAE,EAAA;;oBAAtB,SAAsB,CAAC;oBACZ,qBAAM,MAAM,CAAC,cAAc,CAAC,oBAAS,CAAC,SAAS,CAAC,EAAA;;oBAA3D,QAAQ,GAAG,SAAgD,CAAC;;;;SAC7D,CAAC,CAAC;IAEH,QAAQ,CAAC;;;wBACP,qBAAM,MAAM,CAAC,OAAO,EAAE,EAAA;;oBAAtB,SAAsB,CAAC;oBACvB,qBAAM,MAAM,CAAC,UAAU,EAAE,EAAA;;oBAAzB,SAAyB,CAAC;;;;SAC3B,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE;QAC5B,EAAE,CAAC,iDAAiD,EAAE;;;;;wBAC9C,YAAY,GAAG,gCAAqB,CAAC,mBAAmB,CAAC;wBAC9C,qBAAM,MAAM,CAAC,0BAA0B,CAAC,YAAY,CAAC,EAAA;;wBAAhE,QAAQ,GAAG,SAAqD;wBAEtE,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;wBACtC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;wBAChD,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;wBAC5D,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;wBAC5C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;wBAC9C,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;wBACtD,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;wBAC1D,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aACtC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE;;;;;wBACnC,YAAY,GAAG,gCAAqB,CAAC,mBAAmB,CAAC;wBAC/D,qBAAM,MAAM,CAAC,0BAA0B,CAAC,YAAY,CAAC,EAAA;;wBAArD,SAAqD,CAAC;wBAEtD,qBAAM,MAAM,CAAC,MAAM,CAAC,0BAA0B,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAA;;wBAA/E,SAA+E,CAAC;;;;aACjF,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE;;;;;wBACtC,eAAe,GAAG;4BACtB,KAAK,EAAE,kBAAkB;4BACzB,WAAW,EAAE,yBAAyB;4BACtC,GAAG,EAAE,6BAA6B;4BAClC,IAAI,EAAE,SAAS;4BACf,QAAQ,EAAE,eAAe;4BACzB,UAAU,EAAE,UAAU;4BACtB,MAAM,EAAE,YAAY;yBACrB,CAAC;wBAEe,qBAAM,MAAM,CAAC,0BAA0B,CAAC,eAAe,CAAC,EAAA;;wBAAnE,QAAQ,GAAG,SAAwD;wBAEzE,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;wBACnC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC;wBACrC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,gBAAgB;;;;aACrD,CAAC,CAAC;QAEH,EAAE,CAAC,6BAA6B,EAAE;;;;;;wBAC1B,UAAU,GAAG;4BACjB,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,eAAe,EAAE,UAAU,EAAE,UAAU,CAAC;4BAChG,QAAQ,EAAE,CAAC,eAAe,EAAE,cAAc,EAAE,YAAY,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,yBAAyB,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,kBAAkB,CAAC;4BAC1P,UAAU,EAAE,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC;4BAC9D,IAAI,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,cAAc,CAAC;4BAClD,MAAM,EAAE,CAAC,YAAY,EAAE,gBAAgB,EAAE,aAAa,EAAE,UAAU,EAAE,aAAa,CAAC;yBACnF,CAAC;8BAGsD,EAA1B,KAAA,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC;;;6BAA1B,CAAA,cAA0B,CAAA;wBAA7C,WAAe,EAAd,KAAK,QAAA,EAAE,MAAM,QAAA;8BACG,EAAN,iBAAM;;;6BAAN,CAAA,oBAAM,CAAA;wBAAf,KAAK;wBACR,YAAY,yBACb,gCAAqB,CAAC,mBAAmB,WAC5C,GAAG,EAAE,mCAA4B,KAAK,cAAI,KAAK,CAAE,OAChD,KAAK,IAAG,KAAK,MACf,CAAC;wBAEe,qBAAM,MAAM,CAAC,0BAA0B,CAAC,YAAY,CAAC,EAAA;;wBAAhE,QAAQ,GAAG,SAAqD;wBACtE,MAAM,CAAC,QAAQ,CAAC,KAA8B,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;;wBAR3C,IAAM,CAAA;;;wBADE,IAA0B,CAAA;;;;;aAYzD,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE;;;;;wBAChC,WAAW,yBACZ,gCAAqB,CAAC,mBAAmB,KAC5C,GAAG,EAAE,kCAAkC,EACvC,IAAI,EAAE,cAAc,EACpB,kBAAkB,EAAE,IAAI,GACzB,CAAC;wBAEF,qBAAM,MAAM,CAAC,MAAM,CAAC,0BAA0B,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAA;;wBAA9E,SAA8E,CAAC;;;;aAChF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE;QAC5B,IAAI,YAAiB,CAAC;QAEtB,UAAU,CAAC;;;4BACM,qBAAM,MAAM,CAAC,0BAA0B,CAAC,gCAAqB,CAAC,mBAAmB,CAAC,EAAA;;wBAAjG,YAAY,GAAG,SAAkF,CAAC;;;;aACnG,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE;;;;4BACrC,qBAAM,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,CAAC,EAAA;;wBAAxE,QAAQ,GAAG,SAA6D;wBAE9E,MAAM,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;wBACtC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;wBAC1C,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;wBAClD,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;wBAC5C,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,QAAQ,EAAE,CAAC;wBACxC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;wBACnC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;;;;aACpC,CAAC,CAAC;QAEH,EAAE,CAAC,2CAA2C,EAAE;;;;;wBACxC,QAAQ,GAAG,CAAC,aAAa,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;8BAE9C,EAAR,qBAAQ;;;6BAAR,CAAA,sBAAQ,CAAA;wBAAlB,MAAM;wBACT,YAAY,yBACb,2BAAgB,CAAC,gBAAgB,KACpC,MAAM,QAAA,GACP,CAAC;wBAEe,qBAAM,MAAM,CAAC,kBAAkB,CAC9C,QAAQ,CAAC,EAAE,EACX,YAAY,CAAC,EAAE,EACf,YAAY,CACb,EAAA;;wBAJK,QAAQ,GAAG,SAIhB;wBAED,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;;wBAZlB,IAAQ,CAAA;;;;;aAc9B,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE;;;;;wBACrC,aAAa,GAAG,2BAAgB,CAAC,iBAAiB,CAAC;wBACxC,qBAAM,MAAM,CAAC,kBAAkB,CAC9C,QAAQ,CAAC,EAAE,EACX,YAAY,CAAC,EAAE,EACf,aAAa,CACd,EAAA;;wBAJK,QAAQ,GAAG,SAIhB;wBAED,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;wBAC1C,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC3C,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBAChC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;;;;aACpD,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE;;;4BACpD,qBAAM,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,EAAE,EAAE,oBAAoB,EAAE,IAAI,EAAE,CAAC,EAAA;;wBAA7F,SAA6F,CAAC;wBAE9F,qBAAM,MAAM,CACV,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,EAAE,EAAE,oBAAoB,EAAE,IAAI,EAAE,CAAC,CACxF,CAAC,OAAO,CAAC,OAAO,EAAE,EAAA;;wBAFnB,SAEmB,CAAC;;;;aACrB,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE;;;;4BACpC,qBAAM,MAAM,CAAC,cAAc,uBACpC,oBAAS,CAAC,SAAS,KACtB,KAAK,EAAE,mBAAmB,IAC1B,EAAA;;wBAHI,KAAK,GAAG,SAGZ;wBAEgB,qBAAM,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,CAAC,EAAA;;wBAAzE,SAAS,GAAG,SAA6D;wBAC7D,qBAAM,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,CAAC,EAAA;;wBAAtE,SAAS,GAAG,SAA0D;wBAE5E,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;wBAC3C,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;wBACxC,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;;;;aACzD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,eAAe,EAAE;QACxB,IAAI,YAAiB,CAAC;QAEtB,UAAU,CAAC;;;4BACM,qBAAM,MAAM,CAAC,0BAA0B,CAAC,gCAAqB,CAAC,mBAAmB,CAAC,EAAA;;wBAAjG,YAAY,GAAG,SAAkF,CAAC;;;;aACnG,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE;;;;;wBACnC,UAAU,GAAG,sBAAW,CAAC,eAAe,CAAC;wBAChC,qBAAM,MAAM,CAAC,gBAAgB,CAC1C,QAAQ,CAAC,EAAE,EACX,YAAY,CAAC,EAAE,EACf,UAAU,CACX,EAAA;;wBAJK,MAAM,GAAG,SAId;wBAED,MAAM,CAAC,MAAM,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;wBACpC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;wBACxC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;wBAChD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;wBAC9C,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;wBAC9C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;;;;aACrD,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE;;;;;wBAC3B,YAAY,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;8BAEC,EAAZ,6BAAY;;;6BAAZ,CAAA,0BAAY,CAAA;wBAA3B,WAAW;wBACd,UAAU,yBACX,sBAAW,CAAC,eAAe,KAC9B,MAAM,EAAE,WAAW,GACpB,CAAC;wBAEa,qBAAM,MAAM,CAAC,gBAAgB,CAC1C,QAAQ,CAAC,EAAE,EACX,YAAY,CAAC,EAAE,EACf,UAAU,CACX,EAAA;;wBAJK,MAAM,GAAG,SAId;wBAED,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;;;wBAZhB,IAAY,CAAA;;;;;aAcvC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE;;;;;wBAClC,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;8BAEI,EAAd,iCAAc;;;6BAAd,CAAA,4BAAc,CAAA;wBAA/B,aAAa;wBAChB,UAAU,yBACX,sBAAW,CAAC,eAAe,KAC9B,MAAM,EAAE,aAAa,GACtB,CAAC;wBAEF,qBAAM,MAAM,CACV,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,EAAE,UAAU,CAAC,CAClE,CAAC,OAAO,CAAC,OAAO,EAAE,EAAA;;wBAFnB,SAEmB,CAAC;;;wBARM,IAAc,CAAA;;;;;aAU3C,CAAC,CAAC;QAEH,EAAE,CAAC,+CAA+C,EAAE;;;;;wBAC5C,mBAAmB,GAAG,sBAAW,CAAC,mBAAmB,CAAC;wBAC7C,qBAAM,MAAM,CAAC,gBAAgB,CAC1C,QAAQ,CAAC,EAAE,EACX,YAAY,CAAC,EAAE,EACf,mBAAmB,CACpB,EAAA;;wBAJK,MAAM,GAAG,SAId;wBAED,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBAC9B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;wBACjC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;;;;aACrC,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE;;;4BAC/C,qBAAM,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,wBAAO,sBAAW,CAAC,eAAe,KAAE,oBAAoB,EAAE,IAAI,IAAG,EAAA;;wBAA3H,SAA2H,CAAC;wBAE5H,qBAAM,MAAM,CACV,MAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,wBAAO,sBAAW,CAAC,UAAU,KAAE,oBAAoB,EAAE,IAAI,IAAG,CACjH,CAAC,OAAO,CAAC,OAAO,EAAE,EAAA;;wBAFnB,SAEmB,CAAC;;;;aACrB,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE;QACxC,UAAU,CAAC;;;;oBACT,gDAAgD;oBAChD,qBAAM,MAAM,CAAC,0BAA0B,CAAC,gCAAqB,CAAC,mBAAmB,CAAC,EAAA;;wBADlF,gDAAgD;wBAChD,SAAkF,CAAC;wBACnF,qBAAM,MAAM,CAAC,0BAA0B,CAAC,gCAAqB,CAAC,kBAAkB,CAAC,EAAA;;wBAAjF,SAAiF,CAAC;wBAClF,qBAAM,MAAM,CAAC,0BAA0B,CAAC,gCAAqB,CAAC,WAAW,CAAC,EAAA;;wBAA1E,SAA0E,CAAC;;;;aAC5E,CAAC,CAAC;QAEH,EAAE,CAAC,2BAA2B,EAAE;YAC9B,IAAM,SAAS,GAAG;gBAChB,gCAAqB,CAAC,mBAAmB;gBACzC,gCAAqB,CAAC,kBAAkB;gBACxC,gCAAqB,CAAC,WAAW;aAClC,CAAC;YAEF,IAAM,sBAAsB,GAAG,SAAS,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,KAAK,eAAe,EAA9B,CAA8B,CAAC,CAAC;YACrF,IAAM,oBAAoB,GAAG,SAAS,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,KAAK,cAAc,EAA7B,CAA6B,CAAC,CAAC;YAElF,MAAM,CAAC,sBAAsB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC/C,MAAM,CAAC,oBAAoB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC7C,MAAM,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC/E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,8BAA8B,EAAE;YACjC,IAAM,SAAS,GAAG;gBAChB,gCAAqB,CAAC,mBAAmB;gBACzC,gCAAqB,CAAC,kBAAkB;gBACxC,gCAAqB,CAAC,WAAW;aAClC,CAAC;YAEF,IAAM,iBAAiB,GAAG,SAAS,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,KAAK,UAAU,EAA3B,CAA2B,CAAC,CAAC;YAC7E,IAAM,iBAAiB,GAAG,SAAS,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,KAAK,UAAU,EAA3B,CAA2B,CAAC,CAAC;YAE7E,MAAM,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uBAAuB,EAAE;YAC1B,IAAM,SAAS,GAAG;gBAChB,gCAAqB,CAAC,mBAAmB;gBACzC,gCAAqB,CAAC,kBAAkB;gBACxC,gCAAqB,CAAC,WAAW;aAClC,CAAC;YAEF,IAAM,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,EAAjB,CAAiB,CAAC,CAAC;YAC/D,IAAM,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,MAAM,EAAjB,CAAiB,CAAC,CAAC;YAE/D,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACtC,MAAM,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,wCAAwC,EAAE;YAC3C,IAAM,SAAS,GAAG;gBAChB,gCAAqB,CAAC,mBAAmB;gBACzC,gCAAqB,CAAC,kBAAkB;gBACxC,gCAAqB,CAAC,WAAW;aAClC,CAAC;YAEF,IAAM,UAAU,GAAG,kBAAkB,CAAC;YACtC,IAAM,iBAAiB,GAAG,SAAS,CAAC,MAAM,CAAC,UAAA,CAAC;gBAC1C,OAAA,CAAC,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;oBAC1C,CAAC,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YADhD,CACgD,CACjD,CAAC;YAEF,MAAM,CAAC,iBAAiB,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAC9E,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,wBAAwB,EAAE;QACjC,EAAE,CAAC,wCAAwC,EAAE;;;;4BAG1B,qBAAM,MAAM,CAAC,0BAA0B,CAAC,gCAAqB,CAAC,mBAAmB,CAAC,EAAA;;wBAA7F,QAAQ,GAAG,SAAkF;wBACnG,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAChC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE;;;;4BAEpB,qBAAM,MAAM,CAAC,0BAA0B,CAAC,gCAAqB,CAAC,kBAAkB,CAAC,EAAA;;wBAA5F,QAAQ,GAAG,SAAiF;wBAClG,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAChC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/unit/learning-resources.test.ts"], "sourcesContent": ["/**\n * Learning Resources Tests\n * \n * Tests Learning Resources functionality, business logic, and edge cases.\n * \n * @category unit\n * @requires Unit testing utilities, mocking\n */\n\nimport { TestDatabase } from '../utils/testHelpers';\nimport { testLearningResources, testUsers, testProgressData, testRatings } from '../fixtures/testData';\n\ndescribe('Learning Resources Unit Tests', () => {\n  let testDb: TestDatabase;\n  let testUser: any;\n\n  beforeAll(async () => {\n    testDb = new TestDatabase();\n  });\n\n  beforeEach(async () => {\n    await testDb.cleanup();\n    testUser = await testDb.createTestUser(testUsers.validUser);\n  });\n\n  afterAll(async () => {\n    await testDb.cleanup();\n    await testDb.disconnect();\n  });\n\n  describe('Resource Creation', () => {\n    it('should create learning resource with valid data', async () => {\n      const resourceData = testLearningResources.cybersecurityCourse;\n      const resource = await testDb.createTestLearningResource(resourceData);\n      \n      expect(resource).toHaveProperty('id');\n      expect(resource.title).toBe(resourceData.title);\n      expect(resource.description).toBe(resourceData.description);\n      expect(resource.url).toBe(resourceData.url);\n      expect(resource.type).toBe(resourceData.type);\n      expect(resource.category).toBe(resourceData.category);\n      expect(resource.skillLevel).toBe(resourceData.skillLevel);\n      expect(resource.isActive).toBe(true);\n    });\n\n    it('should enforce unique URL constraint', async () => {\n      const resourceData = testLearningResources.cybersecurityCourse;\n      await testDb.createTestLearningResource(resourceData);\n      \n      await expect(testDb.createTestLearningResource(resourceData)).rejects.toThrow();\n    });\n\n    it('should handle optional fields correctly', async () => {\n      const minimalResource = {\n        title: 'Minimal Resource',\n        description: 'A minimal test resource',\n        url: 'https://example.com/minimal',\n        type: 'ARTICLE',\n        category: 'CYBERSECURITY',\n        skillLevel: 'BEGINNER',\n        format: 'SELF_PACED'\n      };\n      \n      const resource = await testDb.createTestLearningResource(minimalResource);\n      \n      expect(resource.author).toBeNull();\n      expect(resource.duration).toBeNull();\n      expect(resource.cost).toBe('FREE'); // Default value\n    });\n\n    it('should validate enum values', async () => {\n      const validEnums = {\n        type: ['COURSE', 'ARTICLE', 'VIDEO', 'PODCAST', 'BOOK', 'CERTIFICATION', 'TUTORIAL', 'WORKSHOP'],\n        category: ['CYBERSECURITY', 'DATA_SCIENCE', 'BLOCKCHAIN', 'PROJECT_MANAGEMENT', 'DIGITAL_MARKETING', 'FINANCIAL_LITERACY', 'LANGUAGE_LEARNING', 'ARTIFICIAL_INTELLIGENCE', 'WEB_DEVELOPMENT', 'MOBILE_DEVELOPMENT', 'CLOUD_COMPUTING', 'ENTREPRENEURSHIP'],\n        skillLevel: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'],\n        cost: ['FREE', 'FREEMIUM', 'PAID', 'SUBSCRIPTION'],\n        format: ['SELF_PACED', 'INSTRUCTOR_LED', 'INTERACTIVE', 'HANDS_ON', 'THEORETICAL']\n      };\n      \n      // Test valid values\n      for (const [field, values] of Object.entries(validEnums)) {\n        for (const value of values) {\n          const resourceData = {\n            ...testLearningResources.cybersecurityCourse,\n            url: `https://example.com/test-${field}-${value}`,\n            [field]: value\n          };\n          \n          const resource = await testDb.createTestLearningResource(resourceData);\n          expect(resource[field as keyof typeof resource]).toBe(value);\n        }\n      }\n    });\n\n    it('should reject invalid enum values', async () => {\n      const invalidData = {\n        ...testLearningResources.cybersecurityCourse,\n        url: 'https://example.com/invalid-enum',\n        type: 'INVALID_TYPE',\n        testEnumValidation: true\n      };\n\n      await expect(testDb.createTestLearningResource(invalidData)).rejects.toThrow();\n    });\n  });\n\n  describe('Progress Tracking', () => {\n    let testResource: any;\n\n    beforeEach(async () => {\n      testResource = await testDb.createTestLearningResource(testLearningResources.cybersecurityCourse);\n    });\n\n    it('should create progress record with default status', async () => {\n      const progress = await testDb.createTestProgress(testUser.id, testResource.id);\n      \n      expect(progress).toHaveProperty('id');\n      expect(progress.userId).toBe(testUser.id);\n      expect(progress.resourceId).toBe(testResource.id);\n      expect(progress.status).toBe('IN_PROGRESS');\n      expect(progress.completedAt).toBeNull();\n      expect(progress.rating).toBeNull();\n      expect(progress.review).toBeNull();\n    });\n\n    it('should handle different progress statuses', async () => {\n      const statuses = ['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'BOOKMARKED'];\n      \n      for (const status of statuses) {\n        const progressData = {\n          ...testProgressData.beginnerProgress,\n          status\n        };\n        \n        const progress = await testDb.createTestProgress(\n          testUser.id, \n          testResource.id, \n          progressData\n        );\n        \n        expect(progress.status).toBe(status);\n      }\n    });\n\n    it('should track completion with timestamp', async () => {\n      const completedData = testProgressData.completedProgress;\n      const progress = await testDb.createTestProgress(\n        testUser.id, \n        testResource.id, \n        completedData\n      );\n      \n      expect(progress.status).toBe('COMPLETED');\n      expect(progress.completedAt).toBeDefined();\n      expect(progress.rating).toBe(5);\n      expect(progress.review).toBe(completedData.review);\n    });\n\n    it('should enforce unique user-resource combination', async () => {\n      await testDb.createTestProgress(testUser.id, testResource.id, { testUniqueConstraint: true });\n\n      await expect(\n        testDb.createTestProgress(testUser.id, testResource.id, { testUniqueConstraint: true })\n      ).rejects.toThrow();\n    });\n\n    it('should allow multiple users for same resource', async () => {\n      const user2 = await testDb.createTestUser({\n        ...testUsers.validUser,\n        email: '<EMAIL>'\n      });\n      \n      const progress1 = await testDb.createTestProgress(testUser.id, testResource.id);\n      const progress2 = await testDb.createTestProgress(user2.id, testResource.id);\n      \n      expect(progress1.userId).toBe(testUser.id);\n      expect(progress2.userId).toBe(user2.id);\n      expect(progress1.resourceId).toBe(progress2.resourceId);\n    });\n  });\n\n  describe('Rating System', () => {\n    let testResource: any;\n\n    beforeEach(async () => {\n      testResource = await testDb.createTestLearningResource(testLearningResources.cybersecurityCourse);\n    });\n\n    it('should create rating with valid data', async () => {\n      const ratingData = testRatings.excellentRating;\n      const rating = await testDb.createTestRating(\n        testUser.id, \n        testResource.id, \n        ratingData\n      );\n      \n      expect(rating).toHaveProperty('id');\n      expect(rating.userId).toBe(testUser.id);\n      expect(rating.resourceId).toBe(testResource.id);\n      expect(rating.rating).toBe(ratingData.rating);\n      expect(rating.review).toBe(ratingData.review);\n      expect(rating.isHelpful).toBe(ratingData.isHelpful);\n    });\n\n    it('should validate rating range', async () => {\n      const validRatings = [1, 2, 3, 4, 5];\n      \n      for (const ratingValue of validRatings) {\n        const ratingData = {\n          ...testRatings.excellentRating,\n          rating: ratingValue\n        };\n        \n        const rating = await testDb.createTestRating(\n          testUser.id, \n          testResource.id, \n          ratingData\n        );\n        \n        expect(rating.rating).toBe(ratingValue);\n      }\n    });\n\n    it('should reject invalid rating values', async () => {\n      const invalidRatings = [0, 6, -1, 10];\n      \n      for (const invalidRating of invalidRatings) {\n        const ratingData = {\n          ...testRatings.excellentRating,\n          rating: invalidRating\n        };\n        \n        await expect(\n          testDb.createTestRating(testUser.id, testResource.id, ratingData)\n        ).rejects.toThrow();\n      }\n    });\n\n    it('should handle optional review and helpfulness', async () => {\n      const ratingWithoutReview = testRatings.ratingWithoutReview;\n      const rating = await testDb.createTestRating(\n        testUser.id, \n        testResource.id, \n        ratingWithoutReview\n      );\n      \n      expect(rating.rating).toBe(3);\n      expect(rating.review).toBeNull();\n      expect(rating.isHelpful).toBeNull();\n    });\n\n    it('should enforce unique user-resource rating', async () => {\n      await testDb.createTestRating(testUser.id, testResource.id, { ...testRatings.excellentRating, testUniqueConstraint: true });\n\n      await expect(\n        testDb.createTestRating(testUser.id, testResource.id, { ...testRatings.goodRating, testUniqueConstraint: true })\n      ).rejects.toThrow();\n    });\n  });\n\n  describe('Resource Filtering and Search', () => {\n    beforeEach(async () => {\n      // Create multiple resources for filtering tests\n      await testDb.createTestLearningResource(testLearningResources.cybersecurityCourse);\n      await testDb.createTestLearningResource(testLearningResources.dataScienceArticle);\n      await testDb.createTestLearningResource(testLearningResources.webDevVideo);\n    });\n\n    it('should filter by category', () => {\n      const resources = [\n        testLearningResources.cybersecurityCourse,\n        testLearningResources.dataScienceArticle,\n        testLearningResources.webDevVideo\n      ];\n      \n      const cybersecurityResources = resources.filter(r => r.category === 'CYBERSECURITY');\n      const dataScienceResources = resources.filter(r => r.category === 'DATA_SCIENCE');\n      \n      expect(cybersecurityResources).toHaveLength(1);\n      expect(dataScienceResources).toHaveLength(1);\n      expect(cybersecurityResources[0].title).toBe('Ethical Hacking Fundamentals');\n    });\n\n    it('should filter by skill level', () => {\n      const resources = [\n        testLearningResources.cybersecurityCourse,\n        testLearningResources.dataScienceArticle,\n        testLearningResources.webDevVideo\n      ];\n      \n      const beginnerResources = resources.filter(r => r.skillLevel === 'BEGINNER');\n      const advancedResources = resources.filter(r => r.skillLevel === 'ADVANCED');\n      \n      expect(beginnerResources).toHaveLength(1);\n      expect(advancedResources).toHaveLength(1);\n    });\n\n    it('should filter by cost', () => {\n      const resources = [\n        testLearningResources.cybersecurityCourse,\n        testLearningResources.dataScienceArticle,\n        testLearningResources.webDevVideo\n      ];\n      \n      const freeResources = resources.filter(r => r.cost === 'FREE');\n      const paidResources = resources.filter(r => r.cost === 'PAID');\n      \n      expect(freeResources).toHaveLength(2);\n      expect(paidResources).toHaveLength(1);\n    });\n\n    it('should search by title and description', () => {\n      const resources = [\n        testLearningResources.cybersecurityCourse,\n        testLearningResources.dataScienceArticle,\n        testLearningResources.webDevVideo\n      ];\n      \n      const searchTerm = 'machine learning';\n      const matchingResources = resources.filter(r => \n        r.title.toLowerCase().includes(searchTerm) || \n        r.description.toLowerCase().includes(searchTerm)\n      );\n      \n      expect(matchingResources).toHaveLength(1);\n      expect(matchingResources[0].title).toBe('Introduction to Machine Learning');\n    });\n  });\n\n  describe('Resource Relationships', () => {\n    it('should handle career path associations', async () => {\n      // This would test the many-to-many relationship between resources and career paths\n      // For now, we'll just verify the resource creation works\n      const resource = await testDb.createTestLearningResource(testLearningResources.cybersecurityCourse);\n      expect(resource).toBeDefined();\n    });\n\n    it('should handle skill associations', async () => {\n      // This would test the many-to-many relationship between resources and skills\n      const resource = await testDb.createTestLearningResource(testLearningResources.dataScienceArticle);\n      expect(resource).toBeDefined();\n    });\n  });\n});\n"], "version": 3}