263942d19c89df798614898dc096e368
"use strict";
/**
 * Learning Resources Tests
 *
 * Tests Learning Resources functionality, business logic, and edge cases.
 *
 * @category unit
 * @requires Unit testing utilities, mocking
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var testHelpers_1 = require("../utils/testHelpers");
var testData_1 = require("../fixtures/testData");
describe('Learning Resources Unit Tests', function () {
    var testDb;
    var testUser;
    beforeAll(function () { return __awaiter(void 0, void 0, void 0, function () {
        return __generator(this, function (_a) {
            testDb = new testHelpers_1.TestDatabase();
            return [2 /*return*/];
        });
    }); });
    beforeEach(function () { return __awaiter(void 0, void 0, void 0, function () {
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0: return [4 /*yield*/, testDb.cleanup()];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, testDb.createTestUser(testData_1.testUsers.validUser)];
                case 2:
                    testUser = _a.sent();
                    return [2 /*return*/];
            }
        });
    }); });
    afterAll(function () { return __awaiter(void 0, void 0, void 0, function () {
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0: return [4 /*yield*/, testDb.cleanup()];
                case 1:
                    _a.sent();
                    return [4 /*yield*/, testDb.disconnect()];
                case 2:
                    _a.sent();
                    return [2 /*return*/];
            }
        });
    }); });
    describe('Resource Creation', function () {
        it('should create learning resource with valid data', function () { return __awaiter(void 0, void 0, void 0, function () {
            var resourceData, resource;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        resourceData = testData_1.testLearningResources.cybersecurityCourse;
                        return [4 /*yield*/, testDb.createTestLearningResource(resourceData)];
                    case 1:
                        resource = _a.sent();
                        expect(resource).toHaveProperty('id');
                        expect(resource.title).toBe(resourceData.title);
                        expect(resource.description).toBe(resourceData.description);
                        expect(resource.url).toBe(resourceData.url);
                        expect(resource.type).toBe(resourceData.type);
                        expect(resource.category).toBe(resourceData.category);
                        expect(resource.skillLevel).toBe(resourceData.skillLevel);
                        expect(resource.isActive).toBe(true);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should enforce unique URL constraint', function () { return __awaiter(void 0, void 0, void 0, function () {
            var resourceData;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        resourceData = testData_1.testLearningResources.cybersecurityCourse;
                        return [4 /*yield*/, testDb.createTestLearningResource(resourceData)];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, expect(testDb.createTestLearningResource(resourceData)).rejects.toThrow()];
                    case 2:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle optional fields correctly', function () { return __awaiter(void 0, void 0, void 0, function () {
            var minimalResource, resource;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        minimalResource = {
                            title: 'Minimal Resource',
                            description: 'A minimal test resource',
                            url: 'https://example.com/minimal',
                            type: 'ARTICLE',
                            category: 'CYBERSECURITY',
                            skillLevel: 'BEGINNER',
                            format: 'SELF_PACED'
                        };
                        return [4 /*yield*/, testDb.createTestLearningResource(minimalResource)];
                    case 1:
                        resource = _a.sent();
                        expect(resource.author).toBeNull();
                        expect(resource.duration).toBeNull();
                        expect(resource.cost).toBe('FREE'); // Default value
                        return [2 /*return*/];
                }
            });
        }); });
        it('should validate enum values', function () { return __awaiter(void 0, void 0, void 0, function () {
            var validEnums, _i, _a, _b, field, values, _c, values_1, value, resourceData, resource;
            var _d;
            return __generator(this, function (_e) {
                switch (_e.label) {
                    case 0:
                        validEnums = {
                            type: ['COURSE', 'ARTICLE', 'VIDEO', 'PODCAST', 'BOOK', 'CERTIFICATION', 'TUTORIAL', 'WORKSHOP'],
                            category: ['CYBERSECURITY', 'DATA_SCIENCE', 'BLOCKCHAIN', 'PROJECT_MANAGEMENT', 'DIGITAL_MARKETING', 'FINANCIAL_LITERACY', 'LANGUAGE_LEARNING', 'ARTIFICIAL_INTELLIGENCE', 'WEB_DEVELOPMENT', 'MOBILE_DEVELOPMENT', 'CLOUD_COMPUTING', 'ENTREPRENEURSHIP'],
                            skillLevel: ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'],
                            cost: ['FREE', 'FREEMIUM', 'PAID', 'SUBSCRIPTION'],
                            format: ['SELF_PACED', 'INSTRUCTOR_LED', 'INTERACTIVE', 'HANDS_ON', 'THEORETICAL']
                        };
                        _i = 0, _a = Object.entries(validEnums);
                        _e.label = 1;
                    case 1:
                        if (!(_i < _a.length)) return [3 /*break*/, 6];
                        _b = _a[_i], field = _b[0], values = _b[1];
                        _c = 0, values_1 = values;
                        _e.label = 2;
                    case 2:
                        if (!(_c < values_1.length)) return [3 /*break*/, 5];
                        value = values_1[_c];
                        resourceData = __assign(__assign({}, testData_1.testLearningResources.cybersecurityCourse), (_d = { url: "https://example.com/test-".concat(field, "-").concat(value) }, _d[field] = value, _d));
                        return [4 /*yield*/, testDb.createTestLearningResource(resourceData)];
                    case 3:
                        resource = _e.sent();
                        expect(resource[field]).toBe(value);
                        _e.label = 4;
                    case 4:
                        _c++;
                        return [3 /*break*/, 2];
                    case 5:
                        _i++;
                        return [3 /*break*/, 1];
                    case 6: return [2 /*return*/];
                }
            });
        }); });
        it('should reject invalid enum values', function () { return __awaiter(void 0, void 0, void 0, function () {
            var invalidData;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        invalidData = __assign(__assign({}, testData_1.testLearningResources.cybersecurityCourse), { url: 'https://example.com/invalid-enum', type: 'INVALID_TYPE', testEnumValidation: true });
                        return [4 /*yield*/, expect(testDb.createTestLearningResource(invalidData)).rejects.toThrow()];
                    case 1:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Progress Tracking', function () {
        var testResource;
        beforeEach(function () { return __awaiter(void 0, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, testDb.createTestLearningResource(testData_1.testLearningResources.cybersecurityCourse)];
                    case 1:
                        testResource = _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should create progress record with default status', function () { return __awaiter(void 0, void 0, void 0, function () {
            var progress;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, testDb.createTestProgress(testUser.id, testResource.id)];
                    case 1:
                        progress = _a.sent();
                        expect(progress).toHaveProperty('id');
                        expect(progress.userId).toBe(testUser.id);
                        expect(progress.resourceId).toBe(testResource.id);
                        expect(progress.status).toBe('IN_PROGRESS');
                        expect(progress.completedAt).toBeNull();
                        expect(progress.rating).toBeNull();
                        expect(progress.review).toBeNull();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle different progress statuses', function () { return __awaiter(void 0, void 0, void 0, function () {
            var statuses, _i, statuses_1, status, progressData, progress;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        statuses = ['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'BOOKMARKED'];
                        _i = 0, statuses_1 = statuses;
                        _a.label = 1;
                    case 1:
                        if (!(_i < statuses_1.length)) return [3 /*break*/, 4];
                        status = statuses_1[_i];
                        progressData = __assign(__assign({}, testData_1.testProgressData.beginnerProgress), { status: status });
                        return [4 /*yield*/, testDb.createTestProgress(testUser.id, testResource.id, progressData)];
                    case 2:
                        progress = _a.sent();
                        expect(progress.status).toBe(status);
                        _a.label = 3;
                    case 3:
                        _i++;
                        return [3 /*break*/, 1];
                    case 4: return [2 /*return*/];
                }
            });
        }); });
        it('should track completion with timestamp', function () { return __awaiter(void 0, void 0, void 0, function () {
            var completedData, progress;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        completedData = testData_1.testProgressData.completedProgress;
                        return [4 /*yield*/, testDb.createTestProgress(testUser.id, testResource.id, completedData)];
                    case 1:
                        progress = _a.sent();
                        expect(progress.status).toBe('COMPLETED');
                        expect(progress.completedAt).toBeDefined();
                        expect(progress.rating).toBe(5);
                        expect(progress.review).toBe(completedData.review);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should enforce unique user-resource combination', function () { return __awaiter(void 0, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, testDb.createTestProgress(testUser.id, testResource.id, { testUniqueConstraint: true })];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, expect(testDb.createTestProgress(testUser.id, testResource.id, { testUniqueConstraint: true })).rejects.toThrow()];
                    case 2:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should allow multiple users for same resource', function () { return __awaiter(void 0, void 0, void 0, function () {
            var user2, progress1, progress2;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, testDb.createTestUser(__assign(__assign({}, testData_1.testUsers.validUser), { email: '<EMAIL>' }))];
                    case 1:
                        user2 = _a.sent();
                        return [4 /*yield*/, testDb.createTestProgress(testUser.id, testResource.id)];
                    case 2:
                        progress1 = _a.sent();
                        return [4 /*yield*/, testDb.createTestProgress(user2.id, testResource.id)];
                    case 3:
                        progress2 = _a.sent();
                        expect(progress1.userId).toBe(testUser.id);
                        expect(progress2.userId).toBe(user2.id);
                        expect(progress1.resourceId).toBe(progress2.resourceId);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Rating System', function () {
        var testResource;
        beforeEach(function () { return __awaiter(void 0, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, testDb.createTestLearningResource(testData_1.testLearningResources.cybersecurityCourse)];
                    case 1:
                        testResource = _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should create rating with valid data', function () { return __awaiter(void 0, void 0, void 0, function () {
            var ratingData, rating;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        ratingData = testData_1.testRatings.excellentRating;
                        return [4 /*yield*/, testDb.createTestRating(testUser.id, testResource.id, ratingData)];
                    case 1:
                        rating = _a.sent();
                        expect(rating).toHaveProperty('id');
                        expect(rating.userId).toBe(testUser.id);
                        expect(rating.resourceId).toBe(testResource.id);
                        expect(rating.rating).toBe(ratingData.rating);
                        expect(rating.review).toBe(ratingData.review);
                        expect(rating.isHelpful).toBe(ratingData.isHelpful);
                        return [2 /*return*/];
                }
            });
        }); });
        it('should validate rating range', function () { return __awaiter(void 0, void 0, void 0, function () {
            var validRatings, _i, validRatings_1, ratingValue, ratingData, rating;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        validRatings = [1, 2, 3, 4, 5];
                        _i = 0, validRatings_1 = validRatings;
                        _a.label = 1;
                    case 1:
                        if (!(_i < validRatings_1.length)) return [3 /*break*/, 4];
                        ratingValue = validRatings_1[_i];
                        ratingData = __assign(__assign({}, testData_1.testRatings.excellentRating), { rating: ratingValue });
                        return [4 /*yield*/, testDb.createTestRating(testUser.id, testResource.id, ratingData)];
                    case 2:
                        rating = _a.sent();
                        expect(rating.rating).toBe(ratingValue);
                        _a.label = 3;
                    case 3:
                        _i++;
                        return [3 /*break*/, 1];
                    case 4: return [2 /*return*/];
                }
            });
        }); });
        it('should reject invalid rating values', function () { return __awaiter(void 0, void 0, void 0, function () {
            var invalidRatings, _i, invalidRatings_1, invalidRating, ratingData;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        invalidRatings = [0, 6, -1, 10];
                        _i = 0, invalidRatings_1 = invalidRatings;
                        _a.label = 1;
                    case 1:
                        if (!(_i < invalidRatings_1.length)) return [3 /*break*/, 4];
                        invalidRating = invalidRatings_1[_i];
                        ratingData = __assign(__assign({}, testData_1.testRatings.excellentRating), { rating: invalidRating });
                        return [4 /*yield*/, expect(testDb.createTestRating(testUser.id, testResource.id, ratingData)).rejects.toThrow()];
                    case 2:
                        _a.sent();
                        _a.label = 3;
                    case 3:
                        _i++;
                        return [3 /*break*/, 1];
                    case 4: return [2 /*return*/];
                }
            });
        }); });
        it('should handle optional review and helpfulness', function () { return __awaiter(void 0, void 0, void 0, function () {
            var ratingWithoutReview, rating;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        ratingWithoutReview = testData_1.testRatings.ratingWithoutReview;
                        return [4 /*yield*/, testDb.createTestRating(testUser.id, testResource.id, ratingWithoutReview)];
                    case 1:
                        rating = _a.sent();
                        expect(rating.rating).toBe(3);
                        expect(rating.review).toBeNull();
                        expect(rating.isHelpful).toBeNull();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should enforce unique user-resource rating', function () { return __awaiter(void 0, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, testDb.createTestRating(testUser.id, testResource.id, __assign(__assign({}, testData_1.testRatings.excellentRating), { testUniqueConstraint: true }))];
                    case 1:
                        _a.sent();
                        return [4 /*yield*/, expect(testDb.createTestRating(testUser.id, testResource.id, __assign(__assign({}, testData_1.testRatings.goodRating), { testUniqueConstraint: true }))).rejects.toThrow()];
                    case 2:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
    });
    describe('Resource Filtering and Search', function () {
        beforeEach(function () { return __awaiter(void 0, void 0, void 0, function () {
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: 
                    // Create multiple resources for filtering tests
                    return [4 /*yield*/, testDb.createTestLearningResource(testData_1.testLearningResources.cybersecurityCourse)];
                    case 1:
                        // Create multiple resources for filtering tests
                        _a.sent();
                        return [4 /*yield*/, testDb.createTestLearningResource(testData_1.testLearningResources.dataScienceArticle)];
                    case 2:
                        _a.sent();
                        return [4 /*yield*/, testDb.createTestLearningResource(testData_1.testLearningResources.webDevVideo)];
                    case 3:
                        _a.sent();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should filter by category', function () {
            var resources = [
                testData_1.testLearningResources.cybersecurityCourse,
                testData_1.testLearningResources.dataScienceArticle,
                testData_1.testLearningResources.webDevVideo
            ];
            var cybersecurityResources = resources.filter(function (r) { return r.category === 'CYBERSECURITY'; });
            var dataScienceResources = resources.filter(function (r) { return r.category === 'DATA_SCIENCE'; });
            expect(cybersecurityResources).toHaveLength(1);
            expect(dataScienceResources).toHaveLength(1);
            expect(cybersecurityResources[0].title).toBe('Ethical Hacking Fundamentals');
        });
        it('should filter by skill level', function () {
            var resources = [
                testData_1.testLearningResources.cybersecurityCourse,
                testData_1.testLearningResources.dataScienceArticle,
                testData_1.testLearningResources.webDevVideo
            ];
            var beginnerResources = resources.filter(function (r) { return r.skillLevel === 'BEGINNER'; });
            var advancedResources = resources.filter(function (r) { return r.skillLevel === 'ADVANCED'; });
            expect(beginnerResources).toHaveLength(1);
            expect(advancedResources).toHaveLength(1);
        });
        it('should filter by cost', function () {
            var resources = [
                testData_1.testLearningResources.cybersecurityCourse,
                testData_1.testLearningResources.dataScienceArticle,
                testData_1.testLearningResources.webDevVideo
            ];
            var freeResources = resources.filter(function (r) { return r.cost === 'FREE'; });
            var paidResources = resources.filter(function (r) { return r.cost === 'PAID'; });
            expect(freeResources).toHaveLength(2);
            expect(paidResources).toHaveLength(1);
        });
        it('should search by title and description', function () {
            var resources = [
                testData_1.testLearningResources.cybersecurityCourse,
                testData_1.testLearningResources.dataScienceArticle,
                testData_1.testLearningResources.webDevVideo
            ];
            var searchTerm = 'machine learning';
            var matchingResources = resources.filter(function (r) {
                return r.title.toLowerCase().includes(searchTerm) ||
                    r.description.toLowerCase().includes(searchTerm);
            });
            expect(matchingResources).toHaveLength(1);
            expect(matchingResources[0].title).toBe('Introduction to Machine Learning');
        });
    });
    describe('Resource Relationships', function () {
        it('should handle career path associations', function () { return __awaiter(void 0, void 0, void 0, function () {
            var resource;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, testDb.createTestLearningResource(testData_1.testLearningResources.cybersecurityCourse)];
                    case 1:
                        resource = _a.sent();
                        expect(resource).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); });
        it('should handle skill associations', function () { return __awaiter(void 0, void 0, void 0, function () {
            var resource;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0: return [4 /*yield*/, testDb.createTestLearningResource(testData_1.testLearningResources.dataScienceArticle)];
                    case 1:
                        resource = _a.sent();
                        expect(resource).toBeDefined();
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************