{"version": 3, "names": ["server_1", "cov_tmiaj1jxx", "s", "require", "next_1", "auth_1", "prisma_1", "__importDefault", "unified_api_error_handler_1", "exports", "dynamic", "GET", "withUnifiedErrorHandling", "request", "f", "__awaiter", "Promise", "getServerSession", "authOptions", "session", "_b", "sent", "b", "_a", "user", "id", "error", "Error", "statusCode", "searchParams", "URL", "url", "query", "get", "limit", "parseInt", "length", "NextResponse", "json", "success", "data", "users", "total", "default", "find<PERSON>any", "where", "AND", "not", "OR", "name", "contains", "mode", "email", "profile", "bio", "select", "profilePictureUrl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "progressLevel", "take", "Math", "min", "orderBy"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/users/search/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport prisma from '@/lib/prisma';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\n\n// Force dynamic rendering for this route\nexport const dynamic = 'force-dynamic';\n\ninterface UserSearchResult {\n  id: string;\n  name: string | null;\n  email: string;\n  profile: {\n    profilePictureUrl: string | null;\n    bio: string | null;\n    currentCareerPath: string | null;\n    progressLevel: string | null;\n  } | null;\n}\n\ninterface UserSearchResponse {\n  users: UserSearchResult[];\n  total: number;\n}\n\n// GET /api/users/search - Search users for mentions\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<UserSearchResponse>>> => {\n  const session = await getServerSession(authOptions);\n\n  if (!session?.user?.id) {\n    const error = new Error('Authentication required') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  const { searchParams } = new URL(request.url);\n  const query = searchParams.get('q');\n  const limit = parseInt(searchParams.get('limit') || '10');\n\n  if (!query || query.length < 1) {\n    return NextResponse.json({\n      success: true,\n      data: {\n        users: [],\n        total: 0,\n      }\n    });\n  }\n\n  // Search users by name, email, or profile information\n  const users = await prisma.user.findMany({\n    where: {\n      AND: [\n        {\n          id: {\n            not: session.user.id, // Exclude current user\n          },\n        },\n        {\n          OR: [\n            {\n              name: {\n                contains: query,\n                mode: 'insensitive',\n              },\n            },\n            {\n              email: {\n                contains: query,\n                mode: 'insensitive',\n              },\n            },\n            {\n              profile: {\n                bio: {\n                  contains: query,\n                  mode: 'insensitive',\n                },\n              },\n            },\n          ],\n        },\n      ],\n    },\n    select: {\n      id: true,\n      name: true,\n      email: true,\n      profile: {\n        select: {\n          profilePictureUrl: true,\n          bio: true,\n          currentCareerPath: true,\n          progressLevel: true,\n        },\n      },\n    },\n    take: Math.min(limit, 20), // Max 20 results\n    orderBy: [\n      {\n        name: 'asc',\n      },\n      {\n        email: 'asc',\n      },\n    ],\n  });\n\n  return NextResponse.json({\n    success: true,\n    data: {\n      users,\n      total: users.length,\n    }\n  });\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAC,MAAA;AAAA;AAAA,CAAAH,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAE,MAAA;AAAA;AAAA,CAAAJ,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAG,QAAA;AAAA;AAAA,CAAAL,aAAA,GAAAC,CAAA,QAAAK,eAAA,CAAAJ,OAAA;AACA,IAAAK,2BAAA;AAAA;AAAA,CAAAP,aAAA,GAAAC,CAAA,QAAAC,OAAA;AAEA;AAAA;AAAAF,aAAA,GAAAC,CAAA;AACaO,OAAA,CAAAC,OAAO,GAAG,eAAe;AAmBtC;AAAA;AAAAT,aAAA,GAAAC,CAAA;AACaO,OAAA,CAAAE,GAAG,GAAG,IAAAH,2BAAA,CAAAI,wBAAwB,EAAC,UAAOC,OAAoB;EAAA;EAAAZ,aAAA,GAAAa,CAAA;EAAAb,aAAA,GAAAC,CAAA;EAAA,OAAAa,SAAA,iBAAGC,OAAO;IAAA;IAAAf,aAAA,GAAAa,CAAA;;;;;;;;;;;;;;UAC/D,qBAAM,IAAAV,MAAA,CAAAa,gBAAgB,EAACZ,MAAA,CAAAa,WAAW,CAAC;;;;;UAA7CC,OAAO,GAAGC,EAAA,CAAAC,IAAA,EAAmC;UAAA;UAAApB,aAAA,GAAAC,CAAA;UAEnD,IAAI;UAAC;UAAA,CAAAD,aAAA,GAAAqB,CAAA,YAAAC,EAAA;UAAA;UAAA,CAAAtB,aAAA,GAAAqB,CAAA,WAAAH,OAAO;UAAA;UAAA,CAAAlB,aAAA,GAAAqB,CAAA,WAAPH,OAAO;UAAA;UAAA,CAAAlB,aAAA,GAAAqB,CAAA;UAAA;UAAA,CAAArB,aAAA,GAAAqB,CAAA,WAAPH,OAAO,CAAEK,IAAI;UAAA;UAAA,CAAAvB,aAAA,GAAAqB,CAAA,WAAAC,EAAA;UAAA;UAAA,CAAAtB,aAAA,GAAAqB,CAAA;UAAA;UAAA,CAAArB,aAAA,GAAAqB,CAAA,WAAAC,EAAA,CAAEE,EAAE,IAAE;YAAA;YAAAxB,aAAA,GAAAqB,CAAA;YAAArB,aAAA,GAAAC,CAAA;YAChBwB,KAAK,GAAG,IAAIC,KAAK,CAAC,yBAAyB,CAAQ;YAAC;YAAA1B,aAAA,GAAAC,CAAA;YAC1DwB,KAAK,CAACE,UAAU,GAAG,GAAG;YAAC;YAAA3B,aAAA,GAAAC,CAAA;YACvB,MAAMwB,KAAK;UACb,CAAC;UAAA;UAAA;YAAAzB,aAAA,GAAAqB,CAAA;UAAA;UAAArB,aAAA,GAAAC,CAAA;UAEO2B,YAAY,GAAK,IAAIC,GAAG,CAACjB,OAAO,CAACkB,GAAG,CAAC,CAAAF,YAAzB;UAA0B;UAAA5B,aAAA,GAAAC,CAAA;UACxC8B,KAAK,GAAGH,YAAY,CAACI,GAAG,CAAC,GAAG,CAAC;UAAC;UAAAhC,aAAA,GAAAC,CAAA;UAC9BgC,KAAK,GAAGC,QAAQ;UAAC;UAAA,CAAAlC,aAAA,GAAAqB,CAAA,WAAAO,YAAY,CAACI,GAAG,CAAC,OAAO,CAAC;UAAA;UAAA,CAAAhC,aAAA,GAAAqB,CAAA,WAAI,IAAI,EAAC;UAAC;UAAArB,aAAA,GAAAC,CAAA;UAE1D;UAAI;UAAA,CAAAD,aAAA,GAAAqB,CAAA,YAACU,KAAK;UAAA;UAAA,CAAA/B,aAAA,GAAAqB,CAAA,WAAIU,KAAK,CAACI,MAAM,GAAG,CAAC,GAAE;YAAA;YAAAnC,aAAA,GAAAqB,CAAA;YAAArB,aAAA,GAAAC,CAAA;YAC9B,sBAAOF,QAAA,CAAAqC,YAAY,CAACC,IAAI,CAAC;cACvBC,OAAO,EAAE,IAAI;cACbC,IAAI,EAAE;gBACJC,KAAK,EAAE,EAAE;gBACTC,KAAK,EAAE;;aAEV,CAAC;UACJ,CAAC;UAAA;UAAA;YAAAzC,aAAA,GAAAqB,CAAA;UAAA;UAAArB,aAAA,GAAAC,CAAA;UAGa,qBAAMI,QAAA,CAAAqC,OAAM,CAACnB,IAAI,CAACoB,QAAQ,CAAC;YACvCC,KAAK,EAAE;cACLC,GAAG,EAAE,CACH;gBACErB,EAAE,EAAE;kBACFsB,GAAG,EAAE5B,OAAO,CAACK,IAAI,CAACC,EAAE,CAAE;;eAEzB,EACD;gBACEuB,EAAE,EAAE,CACF;kBACEC,IAAI,EAAE;oBACJC,QAAQ,EAAElB,KAAK;oBACfmB,IAAI,EAAE;;iBAET,EACD;kBACEC,KAAK,EAAE;oBACLF,QAAQ,EAAElB,KAAK;oBACfmB,IAAI,EAAE;;iBAET,EACD;kBACEE,OAAO,EAAE;oBACPC,GAAG,EAAE;sBACHJ,QAAQ,EAAElB,KAAK;sBACfmB,IAAI,EAAE;;;iBAGX;eAEJ;aAEJ;YACDI,MAAM,EAAE;cACN9B,EAAE,EAAE,IAAI;cACRwB,IAAI,EAAE,IAAI;cACVG,KAAK,EAAE,IAAI;cACXC,OAAO,EAAE;gBACPE,MAAM,EAAE;kBACNC,iBAAiB,EAAE,IAAI;kBACvBF,GAAG,EAAE,IAAI;kBACTG,iBAAiB,EAAE,IAAI;kBACvBC,aAAa,EAAE;;;aAGpB;YACDC,IAAI,EAAEC,IAAI,CAACC,GAAG,CAAC3B,KAAK,EAAE,EAAE,CAAC;YAAE;YAC3B4B,OAAO,EAAE,CACP;cACEb,IAAI,EAAE;aACP,EACD;cACEG,KAAK,EAAE;aACR;WAEJ,CAAC;;;;;UAxDIX,KAAK,GAAGrB,EAAA,CAAAC,IAAA,EAwDZ;UAAA;UAAApB,aAAA,GAAAC,CAAA;UAEF,sBAAOF,QAAA,CAAAqC,YAAY,CAACC,IAAI,CAAC;YACvBC,OAAO,EAAE,IAAI;YACbC,IAAI,EAAE;cACJC,KAAK,EAAAA,KAAA;cACLC,KAAK,EAAED,KAAK,CAACL;;WAEhB,CAAC;;;;CACH,CAAC", "ignoreList": []}