f527cb04481eabe116e0932637fbdbce
"use strict";

/* istanbul ignore next */
function cov_tmiaj1jxx() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/users/search/route.ts";
  var hash = "e4149c44be471128b71fcb833f482ffa736d0363";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/users/search/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 40,
          column: 1
        }
      },
      "68": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 62
        }
      },
      "69": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 62
        }
      },
      "70": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 39
        }
      },
      "71": {
        start: {
          line: 43,
          column: 15
        },
        end: {
          line: 43,
          column: 37
        }
      },
      "72": {
        start: {
          line: 44,
          column: 13
        },
        end: {
          line: 44,
          column: 38
        }
      },
      "73": {
        start: {
          line: 45,
          column: 13
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "74": {
        start: {
          line: 46,
          column: 15
        },
        end: {
          line: 46,
          column: 55
        }
      },
      "75": {
        start: {
          line: 47,
          column: 34
        },
        end: {
          line: 47,
          column: 76
        }
      },
      "76": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 49,
          column: 34
        }
      },
      "77": {
        start: {
          line: 51,
          column: 0
        },
        end: {
          line: 144,
          column: 7
        }
      },
      "78": {
        start: {
          line: 51,
          column: 93
        },
        end: {
          line: 144,
          column: 3
        }
      },
      "79": {
        start: {
          line: 54,
          column: 4
        },
        end: {
          line: 143,
          column: 7
        }
      },
      "80": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 142,
          column: 9
        }
      },
      "81": {
        start: {
          line: 56,
          column: 20
        },
        end: {
          line: 56,
          column: 91
        }
      },
      "82": {
        start: {
          line: 58,
          column: 16
        },
        end: {
          line: 58,
          column: 36
        }
      },
      "83": {
        start: {
          line: 59,
          column: 16
        },
        end: {
          line: 63,
          column: 17
        }
      },
      "84": {
        start: {
          line: 60,
          column: 20
        },
        end: {
          line: 60,
          column: 65
        }
      },
      "85": {
        start: {
          line: 61,
          column: 20
        },
        end: {
          line: 61,
          column: 43
        }
      },
      "86": {
        start: {
          line: 62,
          column: 20
        },
        end: {
          line: 62,
          column: 32
        }
      },
      "87": {
        start: {
          line: 64,
          column: 16
        },
        end: {
          line: 64,
          column: 65
        }
      },
      "88": {
        start: {
          line: 65,
          column: 16
        },
        end: {
          line: 65,
          column: 46
        }
      },
      "89": {
        start: {
          line: 66,
          column: 16
        },
        end: {
          line: 66,
          column: 68
        }
      },
      "90": {
        start: {
          line: 67,
          column: 16
        },
        end: {
          line: 75,
          column: 17
        }
      },
      "91": {
        start: {
          line: 68,
          column: 20
        },
        end: {
          line: 74,
          column: 28
        }
      },
      "92": {
        start: {
          line: 76,
          column: 16
        },
        end: {
          line: 132,
          column: 24
        }
      },
      "93": {
        start: {
          line: 134,
          column: 16
        },
        end: {
          line: 134,
          column: 34
        }
      },
      "94": {
        start: {
          line: 135,
          column: 16
        },
        end: {
          line: 141,
          column: 24
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 38,
            column: 57
          }
        },
        loc: {
          start: {
            line: 38,
            column: 71
          },
          end: {
            line: 40,
            column: 1
          }
        },
        line: 38
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 51,
            column: 72
          },
          end: {
            line: 51,
            column: 73
          }
        },
        loc: {
          start: {
            line: 51,
            column: 91
          },
          end: {
            line: 144,
            column: 5
          }
        },
        line: 51
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 51,
            column: 135
          },
          end: {
            line: 51,
            column: 136
          }
        },
        loc: {
          start: {
            line: 51,
            column: 147
          },
          end: {
            line: 144,
            column: 1
          }
        },
        line: 51
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 54,
            column: 29
          },
          end: {
            line: 54,
            column: 30
          }
        },
        loc: {
          start: {
            line: 54,
            column: 43
          },
          end: {
            line: 143,
            column: 5
          }
        },
        line: 54
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 38,
            column: 22
          },
          end: {
            line: 40,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 23
          },
          end: {
            line: 38,
            column: 27
          }
        }, {
          start: {
            line: 38,
            column: 31
          },
          end: {
            line: 38,
            column: 51
          }
        }, {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 40,
            column: 1
          }
        }],
        line: 38
      },
      "33": {
        loc: {
          start: {
            line: 39,
            column: 11
          },
          end: {
            line: 39,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 37
          },
          end: {
            line: 39,
            column: 40
          }
        }, {
          start: {
            line: 39,
            column: 43
          },
          end: {
            line: 39,
            column: 61
          }
        }],
        line: 39
      },
      "34": {
        loc: {
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 15
          }
        }, {
          start: {
            line: 39,
            column: 19
          },
          end: {
            line: 39,
            column: 33
          }
        }],
        line: 39
      },
      "35": {
        loc: {
          start: {
            line: 55,
            column: 8
          },
          end: {
            line: 142,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 56,
            column: 12
          },
          end: {
            line: 56,
            column: 91
          }
        }, {
          start: {
            line: 57,
            column: 12
          },
          end: {
            line: 132,
            column: 24
          }
        }, {
          start: {
            line: 133,
            column: 12
          },
          end: {
            line: 141,
            column: 24
          }
        }],
        line: 55
      },
      "36": {
        loc: {
          start: {
            line: 59,
            column: 16
          },
          end: {
            line: 63,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 59,
            column: 16
          },
          end: {
            line: 63,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 59
      },
      "37": {
        loc: {
          start: {
            line: 59,
            column: 22
          },
          end: {
            line: 59,
            column: 134
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 59,
            column: 120
          },
          end: {
            line: 59,
            column: 126
          }
        }, {
          start: {
            line: 59,
            column: 129
          },
          end: {
            line: 59,
            column: 134
          }
        }],
        line: 59
      },
      "38": {
        loc: {
          start: {
            line: 59,
            column: 22
          },
          end: {
            line: 59,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 59,
            column: 22
          },
          end: {
            line: 59,
            column: 100
          }
        }, {
          start: {
            line: 59,
            column: 104
          },
          end: {
            line: 59,
            column: 117
          }
        }],
        line: 59
      },
      "39": {
        loc: {
          start: {
            line: 59,
            column: 28
          },
          end: {
            line: 59,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 59,
            column: 69
          },
          end: {
            line: 59,
            column: 75
          }
        }, {
          start: {
            line: 59,
            column: 78
          },
          end: {
            line: 59,
            column: 90
          }
        }],
        line: 59
      },
      "40": {
        loc: {
          start: {
            line: 59,
            column: 28
          },
          end: {
            line: 59,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 59,
            column: 28
          },
          end: {
            line: 59,
            column: 44
          }
        }, {
          start: {
            line: 59,
            column: 48
          },
          end: {
            line: 59,
            column: 66
          }
        }],
        line: 59
      },
      "41": {
        loc: {
          start: {
            line: 66,
            column: 33
          },
          end: {
            line: 66,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 66,
            column: 33
          },
          end: {
            line: 66,
            column: 58
          }
        }, {
          start: {
            line: 66,
            column: 62
          },
          end: {
            line: 66,
            column: 66
          }
        }],
        line: 66
      },
      "42": {
        loc: {
          start: {
            line: 67,
            column: 16
          },
          end: {
            line: 75,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 67,
            column: 16
          },
          end: {
            line: 75,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 67
      },
      "43": {
        loc: {
          start: {
            line: 67,
            column: 20
          },
          end: {
            line: 67,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 67,
            column: 20
          },
          end: {
            line: 67,
            column: 26
          }
        }, {
          start: {
            line: 67,
            column: 30
          },
          end: {
            line: 67,
            column: 46
          }
        }],
        line: 67
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/users/search/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAAkD;AAClD,mCAAyC;AACzC,wDAAkC;AAClC,6EAAwF;AAExF,yCAAyC;AAC5B,QAAA,OAAO,GAAG,eAAe,CAAC;AAmBvC,oDAAoD;AACvC,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB,qCAAG,OAAO;;;;;oBAC/D,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;gBAA7C,OAAO,GAAG,SAAmC;gBAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;oBACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;oBAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAEO,YAAY,GAAK,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,aAAzB,CAA0B;gBACxC,KAAK,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAC9B,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC;gBAE1D,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC/B,sBAAO,qBAAY,CAAC,IAAI,CAAC;4BACvB,OAAO,EAAE,IAAI;4BACb,IAAI,EAAE;gCACJ,KAAK,EAAE,EAAE;gCACT,KAAK,EAAE,CAAC;6BACT;yBACF,CAAC,EAAC;gBACL,CAAC;gBAGa,qBAAM,gBAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;wBACvC,KAAK,EAAE;4BACL,GAAG,EAAE;gCACH;oCACE,EAAE,EAAE;wCACF,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,uBAAuB;qCAC9C;iCACF;gCACD;oCACE,EAAE,EAAE;wCACF;4CACE,IAAI,EAAE;gDACJ,QAAQ,EAAE,KAAK;gDACf,IAAI,EAAE,aAAa;6CACpB;yCACF;wCACD;4CACE,KAAK,EAAE;gDACL,QAAQ,EAAE,KAAK;gDACf,IAAI,EAAE,aAAa;6CACpB;yCACF;wCACD;4CACE,OAAO,EAAE;gDACP,GAAG,EAAE;oDACH,QAAQ,EAAE,KAAK;oDACf,IAAI,EAAE,aAAa;iDACpB;6CACF;yCACF;qCACF;iCACF;6BACF;yBACF;wBACD,MAAM,EAAE;4BACN,EAAE,EAAE,IAAI;4BACR,IAAI,EAAE,IAAI;4BACV,KAAK,EAAE,IAAI;4BACX,OAAO,EAAE;gCACP,MAAM,EAAE;oCACN,iBAAiB,EAAE,IAAI;oCACvB,GAAG,EAAE,IAAI;oCACT,iBAAiB,EAAE,IAAI;oCACvB,aAAa,EAAE,IAAI;iCACpB;6BACF;yBACF;wBACD,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,iBAAiB;wBAC5C,OAAO,EAAE;4BACP;gCACE,IAAI,EAAE,KAAK;6BACZ;4BACD;gCACE,KAAK,EAAE,KAAK;6BACb;yBACF;qBACF,CAAC,EAAA;;gBAxDI,KAAK,GAAG,SAwDZ;gBAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC;wBACvB,OAAO,EAAE,IAAI;wBACb,IAAI,EAAE;4BACJ,KAAK,OAAA;4BACL,KAAK,EAAE,KAAK,CAAC,MAAM;yBACpB;qBACF,CAAC,EAAC;;;KACJ,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/users/search/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport prisma from '@/lib/prisma';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\n\n// Force dynamic rendering for this route\nexport const dynamic = 'force-dynamic';\n\ninterface UserSearchResult {\n  id: string;\n  name: string | null;\n  email: string;\n  profile: {\n    profilePictureUrl: string | null;\n    bio: string | null;\n    currentCareerPath: string | null;\n    progressLevel: string | null;\n  } | null;\n}\n\ninterface UserSearchResponse {\n  users: UserSearchResult[];\n  total: number;\n}\n\n// GET /api/users/search - Search users for mentions\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<UserSearchResponse>>> => {\n  const session = await getServerSession(authOptions);\n\n  if (!session?.user?.id) {\n    const error = new Error('Authentication required') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  const { searchParams } = new URL(request.url);\n  const query = searchParams.get('q');\n  const limit = parseInt(searchParams.get('limit') || '10');\n\n  if (!query || query.length < 1) {\n    return NextResponse.json({\n      success: true,\n      data: {\n        users: [],\n        total: 0,\n      }\n    });\n  }\n\n  // Search users by name, email, or profile information\n  const users = await prisma.user.findMany({\n    where: {\n      AND: [\n        {\n          id: {\n            not: session.user.id, // Exclude current user\n          },\n        },\n        {\n          OR: [\n            {\n              name: {\n                contains: query,\n                mode: 'insensitive',\n              },\n            },\n            {\n              email: {\n                contains: query,\n                mode: 'insensitive',\n              },\n            },\n            {\n              profile: {\n                bio: {\n                  contains: query,\n                  mode: 'insensitive',\n                },\n              },\n            },\n          ],\n        },\n      ],\n    },\n    select: {\n      id: true,\n      name: true,\n      email: true,\n      profile: {\n        select: {\n          profilePictureUrl: true,\n          bio: true,\n          currentCareerPath: true,\n          progressLevel: true,\n        },\n      },\n    },\n    take: Math.min(limit, 20), // Max 20 results\n    orderBy: [\n      {\n        name: 'asc',\n      },\n      {\n        email: 'asc',\n      },\n    ],\n  });\n\n  return NextResponse.json({\n    success: true,\n    data: {\n      users,\n      total: users.length,\n    }\n  });\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e4149c44be471128b71fcb833f482ffa736d0363"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_tmiaj1jxx = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_tmiaj1jxx();
var __awaiter =
/* istanbul ignore next */
(cov_tmiaj1jxx().s[0]++,
/* istanbul ignore next */
(cov_tmiaj1jxx().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_tmiaj1jxx().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_tmiaj1jxx().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_tmiaj1jxx().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_tmiaj1jxx().f[1]++;
    cov_tmiaj1jxx().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_tmiaj1jxx().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_tmiaj1jxx().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_tmiaj1jxx().f[2]++;
      cov_tmiaj1jxx().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_tmiaj1jxx().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_tmiaj1jxx().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_tmiaj1jxx().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_tmiaj1jxx().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_tmiaj1jxx().f[4]++;
      cov_tmiaj1jxx().s[4]++;
      try {
        /* istanbul ignore next */
        cov_tmiaj1jxx().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_tmiaj1jxx().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_tmiaj1jxx().f[5]++;
      cov_tmiaj1jxx().s[7]++;
      try {
        /* istanbul ignore next */
        cov_tmiaj1jxx().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_tmiaj1jxx().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_tmiaj1jxx().f[6]++;
      cov_tmiaj1jxx().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_tmiaj1jxx().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_tmiaj1jxx().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_tmiaj1jxx().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_tmiaj1jxx().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_tmiaj1jxx().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_tmiaj1jxx().s[12]++,
/* istanbul ignore next */
(cov_tmiaj1jxx().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_tmiaj1jxx().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_tmiaj1jxx().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_tmiaj1jxx().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_tmiaj1jxx().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_tmiaj1jxx().f[8]++;
        cov_tmiaj1jxx().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_tmiaj1jxx().b[6][0]++;
          cov_tmiaj1jxx().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_tmiaj1jxx().b[6][1]++;
        }
        cov_tmiaj1jxx().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_tmiaj1jxx().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_tmiaj1jxx().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_tmiaj1jxx().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_tmiaj1jxx().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_tmiaj1jxx().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_tmiaj1jxx().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_tmiaj1jxx().f[9]++;
    cov_tmiaj1jxx().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_tmiaj1jxx().f[10]++;
    cov_tmiaj1jxx().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_tmiaj1jxx().f[11]++;
      cov_tmiaj1jxx().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_tmiaj1jxx().f[12]++;
    cov_tmiaj1jxx().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_tmiaj1jxx().b[9][0]++;
      cov_tmiaj1jxx().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_tmiaj1jxx().b[9][1]++;
    }
    cov_tmiaj1jxx().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_tmiaj1jxx().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_tmiaj1jxx().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_tmiaj1jxx().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_tmiaj1jxx().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_tmiaj1jxx().s[25]++;
      try {
        /* istanbul ignore next */
        cov_tmiaj1jxx().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_tmiaj1jxx().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_tmiaj1jxx().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_tmiaj1jxx().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_tmiaj1jxx().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_tmiaj1jxx().b[15][0]++,
        /* istanbul ignore next */
        (cov_tmiaj1jxx().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_tmiaj1jxx().b[16][1]++,
        /* istanbul ignore next */
        (cov_tmiaj1jxx().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_tmiaj1jxx().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_tmiaj1jxx().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_tmiaj1jxx().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_tmiaj1jxx().b[12][0]++;
          cov_tmiaj1jxx().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_tmiaj1jxx().b[12][1]++;
        }
        cov_tmiaj1jxx().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_tmiaj1jxx().b[18][0]++;
          cov_tmiaj1jxx().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_tmiaj1jxx().b[18][1]++;
        }
        cov_tmiaj1jxx().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_tmiaj1jxx().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_tmiaj1jxx().b[19][1]++;
            cov_tmiaj1jxx().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_tmiaj1jxx().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_tmiaj1jxx().b[19][2]++;
            cov_tmiaj1jxx().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_tmiaj1jxx().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_tmiaj1jxx().b[19][3]++;
            cov_tmiaj1jxx().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_tmiaj1jxx().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_tmiaj1jxx().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_tmiaj1jxx().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_tmiaj1jxx().b[19][4]++;
            cov_tmiaj1jxx().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_tmiaj1jxx().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_tmiaj1jxx().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_tmiaj1jxx().b[19][5]++;
            cov_tmiaj1jxx().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_tmiaj1jxx().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_tmiaj1jxx().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_tmiaj1jxx().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_tmiaj1jxx().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_tmiaj1jxx().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_tmiaj1jxx().b[20][0]++;
              cov_tmiaj1jxx().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_tmiaj1jxx().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_tmiaj1jxx().b[20][1]++;
            }
            cov_tmiaj1jxx().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_tmiaj1jxx().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_tmiaj1jxx().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_tmiaj1jxx().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_tmiaj1jxx().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_tmiaj1jxx().b[23][0]++;
              cov_tmiaj1jxx().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_tmiaj1jxx().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_tmiaj1jxx().b[23][1]++;
            }
            cov_tmiaj1jxx().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_tmiaj1jxx().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_tmiaj1jxx().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_tmiaj1jxx().b[25][0]++;
              cov_tmiaj1jxx().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_tmiaj1jxx().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_tmiaj1jxx().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_tmiaj1jxx().b[25][1]++;
            }
            cov_tmiaj1jxx().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_tmiaj1jxx().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_tmiaj1jxx().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_tmiaj1jxx().b[27][0]++;
              cov_tmiaj1jxx().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_tmiaj1jxx().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_tmiaj1jxx().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_tmiaj1jxx().b[27][1]++;
            }
            cov_tmiaj1jxx().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_tmiaj1jxx().b[29][0]++;
              cov_tmiaj1jxx().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_tmiaj1jxx().b[29][1]++;
            }
            cov_tmiaj1jxx().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_tmiaj1jxx().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_tmiaj1jxx().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_tmiaj1jxx().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_tmiaj1jxx().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_tmiaj1jxx().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_tmiaj1jxx().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_tmiaj1jxx().b[30][0]++;
      cov_tmiaj1jxx().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_tmiaj1jxx().b[30][1]++;
    }
    cov_tmiaj1jxx().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_tmiaj1jxx().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_tmiaj1jxx().b[31][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_tmiaj1jxx().s[67]++,
/* istanbul ignore next */
(cov_tmiaj1jxx().b[32][0]++, this) &&
/* istanbul ignore next */
(cov_tmiaj1jxx().b[32][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_tmiaj1jxx().b[32][2]++, function (mod) {
  /* istanbul ignore next */
  cov_tmiaj1jxx().f[13]++;
  cov_tmiaj1jxx().s[68]++;
  return /* istanbul ignore next */(cov_tmiaj1jxx().b[34][0]++, mod) &&
  /* istanbul ignore next */
  (cov_tmiaj1jxx().b[34][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_tmiaj1jxx().b[33][0]++, mod) :
  /* istanbul ignore next */
  (cov_tmiaj1jxx().b[33][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_tmiaj1jxx().s[69]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_tmiaj1jxx().s[70]++;
exports.GET = exports.dynamic = void 0;
var server_1 =
/* istanbul ignore next */
(cov_tmiaj1jxx().s[71]++, require("next/server"));
var next_1 =
/* istanbul ignore next */
(cov_tmiaj1jxx().s[72]++, require("next-auth/next"));
var auth_1 =
/* istanbul ignore next */
(cov_tmiaj1jxx().s[73]++, require("@/lib/auth"));
var prisma_1 =
/* istanbul ignore next */
(cov_tmiaj1jxx().s[74]++, __importDefault(require("@/lib/prisma")));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_tmiaj1jxx().s[75]++, require("@/lib/unified-api-error-handler"));
// Force dynamic rendering for this route
/* istanbul ignore next */
cov_tmiaj1jxx().s[76]++;
exports.dynamic = 'force-dynamic';
// GET /api/users/search - Search users for mentions
/* istanbul ignore next */
cov_tmiaj1jxx().s[77]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_tmiaj1jxx().f[14]++;
  cov_tmiaj1jxx().s[78]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_tmiaj1jxx().f[15]++;
    var session, error, searchParams, query, limit, users;
    var _a;
    /* istanbul ignore next */
    cov_tmiaj1jxx().s[79]++;
    return __generator(this, function (_b) {
      /* istanbul ignore next */
      cov_tmiaj1jxx().f[16]++;
      cov_tmiaj1jxx().s[80]++;
      switch (_b.label) {
        case 0:
          /* istanbul ignore next */
          cov_tmiaj1jxx().b[35][0]++;
          cov_tmiaj1jxx().s[81]++;
          return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_tmiaj1jxx().b[35][1]++;
          cov_tmiaj1jxx().s[82]++;
          session = _b.sent();
          /* istanbul ignore next */
          cov_tmiaj1jxx().s[83]++;
          if (!(
          /* istanbul ignore next */
          (cov_tmiaj1jxx().b[38][0]++, (_a =
          /* istanbul ignore next */
          (cov_tmiaj1jxx().b[40][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_tmiaj1jxx().b[40][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_tmiaj1jxx().b[39][0]++, void 0) :
          /* istanbul ignore next */
          (cov_tmiaj1jxx().b[39][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_tmiaj1jxx().b[38][1]++, _a === void 0) ?
          /* istanbul ignore next */
          (cov_tmiaj1jxx().b[37][0]++, void 0) :
          /* istanbul ignore next */
          (cov_tmiaj1jxx().b[37][1]++, _a.id))) {
            /* istanbul ignore next */
            cov_tmiaj1jxx().b[36][0]++;
            cov_tmiaj1jxx().s[84]++;
            error = new Error('Authentication required');
            /* istanbul ignore next */
            cov_tmiaj1jxx().s[85]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_tmiaj1jxx().s[86]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_tmiaj1jxx().b[36][1]++;
          }
          cov_tmiaj1jxx().s[87]++;
          searchParams = new URL(request.url).searchParams;
          /* istanbul ignore next */
          cov_tmiaj1jxx().s[88]++;
          query = searchParams.get('q');
          /* istanbul ignore next */
          cov_tmiaj1jxx().s[89]++;
          limit = parseInt(
          /* istanbul ignore next */
          (cov_tmiaj1jxx().b[41][0]++, searchParams.get('limit')) ||
          /* istanbul ignore next */
          (cov_tmiaj1jxx().b[41][1]++, '10'));
          /* istanbul ignore next */
          cov_tmiaj1jxx().s[90]++;
          if (
          /* istanbul ignore next */
          (cov_tmiaj1jxx().b[43][0]++, !query) ||
          /* istanbul ignore next */
          (cov_tmiaj1jxx().b[43][1]++, query.length < 1)) {
            /* istanbul ignore next */
            cov_tmiaj1jxx().b[42][0]++;
            cov_tmiaj1jxx().s[91]++;
            return [2 /*return*/, server_1.NextResponse.json({
              success: true,
              data: {
                users: [],
                total: 0
              }
            })];
          } else
          /* istanbul ignore next */
          {
            cov_tmiaj1jxx().b[42][1]++;
          }
          cov_tmiaj1jxx().s[92]++;
          return [4 /*yield*/, prisma_1.default.user.findMany({
            where: {
              AND: [{
                id: {
                  not: session.user.id // Exclude current user
                }
              }, {
                OR: [{
                  name: {
                    contains: query,
                    mode: 'insensitive'
                  }
                }, {
                  email: {
                    contains: query,
                    mode: 'insensitive'
                  }
                }, {
                  profile: {
                    bio: {
                      contains: query,
                      mode: 'insensitive'
                    }
                  }
                }]
              }]
            },
            select: {
              id: true,
              name: true,
              email: true,
              profile: {
                select: {
                  profilePictureUrl: true,
                  bio: true,
                  currentCareerPath: true,
                  progressLevel: true
                }
              }
            },
            take: Math.min(limit, 20),
            // Max 20 results
            orderBy: [{
              name: 'asc'
            }, {
              email: 'asc'
            }]
          })];
        case 2:
          /* istanbul ignore next */
          cov_tmiaj1jxx().b[35][2]++;
          cov_tmiaj1jxx().s[93]++;
          users = _b.sent();
          /* istanbul ignore next */
          cov_tmiaj1jxx().s[94]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: {
              users: users,
              total: users.length
            }
          })];
      }
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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