31d50038e956ffa7dcd7a97cc3652f13
"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var globals_1 = require("@jest/globals");
// Mock the GeminiService class
globals_1.jest.mock('@/lib/services/geminiService', function () {
    var actualGeminiService = globals_1.jest.requireActual('@/lib/services/geminiService');
    return __assign(__assign({}, actualGeminiService), { geminiService: __assign(__assign({}, actualGeminiService.geminiService), { generateContent: mockGenerateContent, analyzeComprehensiveSkillGap: globals_1.jest.fn(), generatePersonalizedLearningPlan: globals_1.jest.fn(), analyzeSkillMarketTrends: globals_1.jest.fn(), validateSkillAssessment: globals_1.jest.fn() }) });
});
// Mock environment variables before importing geminiService
process.env.GOOGLE_GEMINI_API_KEY = 'test-api-key-for-testing-purposes-only';
var geminiService_1 = require("@/lib/services/geminiService");
// Mock the generateContent method
var mockGenerateContent = globals_1.jest.fn();
var mockGeminiService = geminiService_1.geminiService;
(0, globals_1.describe)('GeminiService - Skill Gap Analysis', function () {
    (0, globals_1.beforeEach)(function () {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.afterEach)(function () {
        globals_1.jest.resetAllMocks();
    });
    (0, globals_1.describe)('analyzeComprehensiveSkillGap', function () {
        var mockCurrentSkills = [
            {
                skillName: 'JavaScript',
                selfRating: 7,
                confidenceLevel: 8,
                yearsOfExperience: 3,
            },
            {
                skillName: 'React',
                selfRating: 6,
                confidenceLevel: 7,
                yearsOfExperience: 2,
            },
        ];
        var mockTargetCareerPath = {
            careerPathName: 'Full Stack Developer',
            targetLevel: 'ADVANCED',
        };
        var mockPreferences = {
            timeframe: 'ONE_YEAR',
            hoursPerWeek: 10,
            learningStyle: ['VISUAL', 'HANDS_ON'],
            budget: 'FREEMIUM',
            focusAreas: ['Backend Development'],
        };
        var mockCareerPathData = {
            requiredSkills: [
                { name: 'JavaScript' },
                { name: 'Node.js' },
                { name: 'PostgreSQL' },
            ],
            learningResources: [
                {
                    title: 'Node.js Fundamentals',
                    type: 'COURSE',
                    skillLevel: 'INTERMEDIATE',
                    skills: ['Node.js'],
                },
            ],
        };
        (0, globals_1.it)('should analyze comprehensive skill gap successfully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var expectedResponse, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        expectedResponse = {
                            success: true,
                            data: {
                                skillGaps: [
                                    {
                                        skillId: 'generated-id-1',
                                        skillName: 'Node.js',
                                        currentLevel: 2,
                                        targetLevel: 8,
                                        gapSeverity: 'HIGH',
                                        priority: 90,
                                        estimatedLearningTime: 120,
                                        marketDemand: 'VERY_HIGH',
                                        salaryImpact: 15,
                                    },
                                ],
                                learningPlan: {
                                    totalEstimatedHours: 120,
                                    milestones: [
                                        {
                                            month: 3,
                                            skills: ['Node.js Basics'],
                                            estimatedHours: 60,
                                            learningPaths: ['Backend Development Path'],
                                        },
                                    ],
                                    recommendedResources: [
                                        {
                                            resourceId: 'resource-1',
                                            resourceType: 'COURSE',
                                            priority: 'HIGH',
                                            skillsAddressed: ['Node.js'],
                                            estimatedHours: 60,
                                        },
                                    ],
                                },
                                careerReadiness: {
                                    currentScore: 65,
                                    targetScore: 85,
                                    improvementPotential: 20,
                                    timeToTarget: 8,
                                },
                            },
                        };
                        mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue(expectedResponse);
                        return [4 /*yield*/, mockGeminiService.analyzeComprehensiveSkillGap(mockCurrentSkills, mockTargetCareerPath, mockPreferences, mockCareerPathData, 'test-user-id')];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.skillGaps).toHaveLength(1);
                        (0, globals_1.expect)(result.data.skillGaps[0].skillName).toBe('Node.js');
                        (0, globals_1.expect)(result.data.skillGaps[0].gapSeverity).toBe('HIGH');
                        (0, globals_1.expect)(result.data.learningPlan.totalEstimatedHours).toBe(120);
                        (0, globals_1.expect)(result.data.careerReadiness.currentScore).toBe(65);
                        (0, globals_1.expect)(mockGeminiService.analyzeComprehensiveSkillGap).toHaveBeenCalledWith(mockCurrentSkills, mockTargetCareerPath, mockPreferences, mockCareerPathData, 'test-user-id');
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should handle AI service failures', function () { return __awaiter(void 0, void 0, void 0, function () {
            var errorResponse, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        errorResponse = {
                            success: false,
                            error: 'AI service temporarily unavailable',
                        };
                        mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue(errorResponse);
                        return [4 /*yield*/, mockGeminiService.analyzeComprehensiveSkillGap(mockCurrentSkills, mockTargetCareerPath, mockPreferences, mockCareerPathData, 'test-user-id')];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(false);
                        (0, globals_1.expect)(result.error).toBe('AI service temporarily unavailable');
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should include market insights when requested', function () { return __awaiter(void 0, void 0, void 0, function () {
            var responseWithMarketData, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        responseWithMarketData = {
                            success: true,
                            data: {
                                skillGaps: [],
                                learningPlan: {
                                    totalEstimatedHours: 0,
                                    milestones: [],
                                    recommendedResources: [],
                                },
                                careerReadiness: {
                                    currentScore: 70,
                                    targetScore: 85,
                                    improvementPotential: 15,
                                    timeToTarget: 6,
                                },
                                marketInsights: {
                                    industryTrends: [
                                        {
                                            skill: 'Node.js',
                                            trend: 'GROWING',
                                            demandLevel: 'VERY_HIGH',
                                        },
                                    ],
                                    salaryProjections: {
                                        currentEstimate: 75000,
                                        targetEstimate: 95000,
                                        improvementPotential: 26.7,
                                    },
                                },
                            },
                        };
                        mockGeminiService.analyzeComprehensiveSkillGap.mockResolvedValue(responseWithMarketData);
                        return [4 /*yield*/, mockGeminiService.analyzeComprehensiveSkillGap(mockCurrentSkills, mockTargetCareerPath, mockPreferences, mockCareerPathData, 'test-user-id')];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.marketInsights).toBeDefined();
                        (0, globals_1.expect)(result.data.marketInsights.industryTrends).toHaveLength(1);
                        (0, globals_1.expect)(result.data.marketInsights.salaryProjections.improvementPotential).toBe(26.7);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    (0, globals_1.describe)('generatePersonalizedLearningPlan', function () {
        var mockSkillGaps = [
            {
                skillName: 'Node.js',
                currentLevel: 3,
                targetLevel: 8,
                gapSeverity: 'HIGH',
                priority: 90,
                estimatedLearningTime: 120,
            },
        ];
        var mockUserPreferences = {
            timeframe: 'ONE_YEAR',
            hoursPerWeek: 10,
            learningStyle: ['VISUAL'],
            budget: 'FREEMIUM',
            focusAreas: ['Backend Development'],
        };
        var mockMarketData = [
            {
                skillName: 'Node.js',
                demandLevel: 'VERY_HIGH',
                growthTrend: 'GROWING',
                salaryImpact: 15,
            },
        ];
        (0, globals_1.it)('should generate personalized learning plan successfully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var expectedResponse, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        expectedResponse = {
                            success: true,
                            data: {
                                learningPlan: {
                                    totalDuration: '12 months',
                                    totalHours: 120,
                                    weeklyCommitment: 10,
                                    phases: [
                                        {
                                            phaseNumber: 1,
                                            phaseName: 'Foundation',
                                            duration: '3 months',
                                            objectives: ['Learn Node.js fundamentals'],
                                            skills: [
                                                {
                                                    skillName: 'Node.js',
                                                    currentLevel: 3,
                                                    targetLevel: 6,
                                                    hoursAllocated: 60,
                                                    learningApproach: 'Hands-on projects with video tutorials',
                                                    resources: [
                                                        {
                                                            type: 'COURSE',
                                                            title: 'Node.js Complete Guide',
                                                            description: 'Comprehensive Node.js course',
                                                            estimatedHours: 40,
                                                            cost: 'FREEMIUM',
                                                            difficulty: 'INTERMEDIATE',
                                                            learningStyle: ['VISUAL'],
                                                            priority: 'HIGH',
                                                        },
                                                    ],
                                                    milestones: [
                                                        {
                                                            week: 4,
                                                            milestone: 'Build first Node.js API',
                                                            assessmentMethod: 'Project completion',
                                                        },
                                                    ],
                                                },
                                            ],
                                            practiceProjects: [
                                                {
                                                    projectName: 'REST API with Node.js',
                                                    description: 'Build a complete REST API',
                                                    skillsApplied: ['Node.js', 'Express.js'],
                                                    estimatedHours: 20,
                                                    difficulty: 'INTERMEDIATE',
                                                    deliverables: ['Working API', 'Documentation'],
                                                },
                                            ],
                                        },
                                    ],
                                },
                            },
                        };
                        mockGeminiService.generatePersonalizedLearningPlan.mockResolvedValue(expectedResponse);
                        return [4 /*yield*/, mockGeminiService.generatePersonalizedLearningPlan(mockSkillGaps, mockUserPreferences, mockMarketData, 'test-user-id')];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.learningPlan.totalHours).toBe(120);
                        (0, globals_1.expect)(result.data.learningPlan.phases).toHaveLength(1);
                        (0, globals_1.expect)(result.data.learningPlan.phases[0].skills[0].skillName).toBe('Node.js');
                        (0, globals_1.expect)(result.data.learningPlan.phases[0].practiceProjects).toHaveLength(1);
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should optimize for user preferences', function () { return __awaiter(void 0, void 0, void 0, function () {
            var visualLearnerResponse, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        visualLearnerResponse = {
                            success: true,
                            data: {
                                learningPlan: {
                                    phases: [
                                        {
                                            skills: [
                                                {
                                                    skillName: 'Node.js',
                                                    resources: [
                                                        {
                                                            type: 'COURSE',
                                                            learningStyle: ['VISUAL'],
                                                            priority: 'HIGH',
                                                        },
                                                    ],
                                                },
                                            ],
                                        },
                                    ],
                                },
                            },
                        };
                        mockGeminiService.generatePersonalizedLearningPlan.mockResolvedValue(visualLearnerResponse);
                        return [4 /*yield*/, mockGeminiService.generatePersonalizedLearningPlan(mockSkillGaps, mockUserPreferences, mockMarketData, 'test-user-id')];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.learningPlan.phases[0].skills[0].resources[0].learningStyle).toContain('VISUAL');
                        return [2 /*return*/];
                }
            });
        }); });
    });
    (0, globals_1.describe)('analyzeSkillMarketTrends', function () {
        var mockSkills = ['JavaScript', 'React', 'Node.js'];
        var mockTargetIndustry = 'Technology';
        var mockRegion = 'North America';
        (0, globals_1.it)('should analyze skill market trends successfully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var expectedResponse, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        expectedResponse = {
                            success: true,
                            data: {
                                marketAnalysis: {
                                    analysisDate: globals_1.expect.any(String),
                                    region: 'North America',
                                    industry: 'Technology',
                                    overallMarketHealth: 'EXCELLENT',
                                },
                                skillTrends: [
                                    {
                                        skillName: 'JavaScript',
                                        demandLevel: 'VERY_HIGH',
                                        growthTrend: 'STABLE',
                                        marketSaturation: 'MODERATE',
                                        averageSalaryImpact: 20,
                                        jobPostingsGrowth: 5,
                                        futureOutlook: {
                                            nextYear: 'Continued high demand',
                                            nextFiveYears: 'Evolving with new frameworks',
                                            emergingOpportunities: ['Full-stack development'],
                                            potentialThreats: ['Framework fragmentation'],
                                        },
                                    },
                                ],
                            },
                        };
                        mockGeminiService.analyzeSkillMarketTrends.mockResolvedValue(expectedResponse);
                        return [4 /*yield*/, mockGeminiService.analyzeSkillMarketTrends(mockSkills, mockTargetIndustry, mockRegion)];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.marketAnalysis.region).toBe('North America');
                        (0, globals_1.expect)(result.data.marketAnalysis.industry).toBe('Technology');
                        (0, globals_1.expect)(result.data.skillTrends).toHaveLength(1);
                        (0, globals_1.expect)(result.data.skillTrends[0].skillName).toBe('JavaScript');
                        (0, globals_1.expect)(result.data.skillTrends[0].demandLevel).toBe('VERY_HIGH');
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should handle empty skills array', function () { return __awaiter(void 0, void 0, void 0, function () {
            var emptySkillsResponse, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        emptySkillsResponse = {
                            success: true,
                            data: {
                                marketAnalysis: {
                                    region: 'GLOBAL',
                                    industry: 'Technology',
                                    overallMarketHealth: 'GOOD',
                                },
                                skillTrends: [],
                            },
                        };
                        mockGeminiService.analyzeSkillMarketTrends.mockResolvedValue(emptySkillsResponse);
                        return [4 /*yield*/, mockGeminiService.analyzeSkillMarketTrends([], mockTargetIndustry, 'GLOBAL')];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.skillTrends).toHaveLength(0);
                        return [2 /*return*/];
                }
            });
        }); });
    });
    (0, globals_1.describe)('validateSkillAssessment', function () {
        var mockSkillName = 'JavaScript';
        var mockSelfRating = 7;
        var mockUserContext = {
            experienceLevel: 'INTERMEDIATE',
            industry: 'Technology',
            yearsOfExperience: 3,
            relatedSkills: ['React', 'Node.js'],
            previousAssessments: [
                {
                    skillName: 'React',
                    rating: 6,
                    date: '2024-01-01',
                },
            ],
        };
        (0, globals_1.it)('should validate skill assessment successfully', function () { return __awaiter(void 0, void 0, void 0, function () {
            var expectedResponse, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        expectedResponse = {
                            success: true,
                            data: {
                                validationResult: {
                                    assessmentAccuracy: 'ACCURATE',
                                    confidenceLevel: 85,
                                    reasoning: 'Rating aligns well with experience level and related skills',
                                    suggestedRating: 7,
                                    ratingJustification: 'Consistent with 3 years experience and related skill levels',
                                },
                                skillAnalysis: {
                                    skillComplexity: 'INTERMEDIATE',
                                    learningCurve: 'MODERATE',
                                    marketValue: 'VERY_HIGH',
                                    industryRelevance: 'CRITICAL',
                                    skillCategory: 'TECHNICAL',
                                },
                                assessmentGuidance: {
                                    ratingCriteria: {
                                        'level1to2': 'Basic syntax understanding',
                                        'level3to4': 'Can write simple programs',
                                        'level5to6': 'Comfortable with frameworks',
                                        'level7to8': 'Advanced patterns and optimization',
                                        'level9to10': 'Expert-level architecture and mentoring',
                                    },
                                    selfAssessmentTips: [
                                        'Compare with concrete examples',
                                        'Consider real-world project complexity',
                                    ],
                                    validationMethods: [
                                        {
                                            method: 'Code Review',
                                            description: 'Have experienced developer review your code',
                                            timeRequired: '2-3 hours',
                                            cost: 'FREE',
                                        },
                                    ],
                                },
                            },
                        };
                        mockGeminiService.validateSkillAssessment.mockResolvedValue(expectedResponse);
                        return [4 /*yield*/, mockGeminiService.validateSkillAssessment(mockSkillName, mockSelfRating, mockUserContext)];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.validationResult.assessmentAccuracy).toBe('ACCURATE');
                        (0, globals_1.expect)(result.data.validationResult.suggestedRating).toBe(7);
                        (0, globals_1.expect)(result.data.skillAnalysis.skillComplexity).toBe('INTERMEDIATE');
                        (0, globals_1.expect)(result.data.assessmentGuidance.validationMethods).toHaveLength(1);
                        return [2 /*return*/];
                }
            });
        }); });
        (0, globals_1.it)('should identify overestimated ratings', function () { return __awaiter(void 0, void 0, void 0, function () {
            var overestimatedResponse, result;
            return __generator(this, function (_a) {
                switch (_a.label) {
                    case 0:
                        overestimatedResponse = {
                            success: true,
                            data: {
                                validationResult: {
                                    assessmentAccuracy: 'SIGNIFICANTLY_HIGH',
                                    confidenceLevel: 60,
                                    reasoning: 'Rating seems high for stated experience level',
                                    suggestedRating: 5,
                                    ratingJustification: 'More realistic for 3 years experience',
                                },
                            },
                        };
                        mockGeminiService.validateSkillAssessment.mockResolvedValue(overestimatedResponse);
                        return [4 /*yield*/, mockGeminiService.validateSkillAssessment(mockSkillName, 9, __assign(__assign({}, mockUserContext), { yearsOfExperience: 1 }))];
                    case 1:
                        result = _a.sent();
                        // Assert
                        (0, globals_1.expect)(result.success).toBe(true);
                        (0, globals_1.expect)(result.data.validationResult.assessmentAccuracy).toBe('SIGNIFICANTLY_HIGH');
                        (0, globals_1.expect)(result.data.validationResult.suggestedRating).toBe(5);
                        return [2 /*return*/];
                }
            });
        }); });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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