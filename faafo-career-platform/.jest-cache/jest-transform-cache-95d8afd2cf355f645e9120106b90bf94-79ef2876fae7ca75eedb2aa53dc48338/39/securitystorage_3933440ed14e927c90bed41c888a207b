cf106022851d6759bf96bb1bd3ee477e
"use strict";

/* istanbul ignore next */
function cov_goe0e98w7() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/security-storage.ts";
  var hash = "01be7beae03a78dd7116d02d56aa5845ddcd0dc2";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/security-storage.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 40,
          column: 1
        }
      },
      "68": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 62
        }
      },
      "69": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 62
        }
      },
      "70": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "71": {
        start: {
          line: 43,
          column: 15
        },
        end: {
          line: 43,
          column: 34
        }
      },
      "72": {
        start: {
          line: 44,
          column: 13
        },
        end: {
          line: 44,
          column: 38
        }
      },
      "73": {
        start: {
          line: 45,
          column: 13
        },
        end: {
          line: 45,
          column: 30
        }
      },
      "74": {
        start: {
          line: 46,
          column: 15
        },
        end: {
          line: 46,
          column: 49
        }
      },
      "75": {
        start: {
          line: 48,
          column: 37
        },
        end: {
          line: 388,
          column: 3
        }
      },
      "76": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 50,
          column: 40
        }
      },
      "77": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 57,
          column: 6
        }
      },
      "78": {
        start: {
          line: 53,
          column: 8
        },
        end: {
          line: 55,
          column: 9
        }
      },
      "79": {
        start: {
          line: 54,
          column: 12
        },
        end: {
          line: 54,
          column: 61
        }
      },
      "80": {
        start: {
          line: 56,
          column: 8
        },
        end: {
          line: 56,
          column: 40
        }
      },
      "81": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 81,
          column: 6
        }
      },
      "82": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 80,
          column: 11
        }
      },
      "83": {
        start: {
          line: 62,
          column: 12
        },
        end: {
          line: 79,
          column: 15
        }
      },
      "84": {
        start: {
          line: 63,
          column: 16
        },
        end: {
          line: 78,
          column: 17
        }
      },
      "85": {
        start: {
          line: 64,
          column: 28
        },
        end: {
          line: 64,
          column: 99
        }
      },
      "86": {
        start: {
          line: 66,
          column: 24
        },
        end: {
          line: 66,
          column: 44
        }
      },
      "87": {
        start: {
          line: 67,
          column: 24
        },
        end: {
          line: 69,
          column: 25
        }
      },
      "88": {
        start: {
          line: 68,
          column: 28
        },
        end: {
          line: 68,
          column: 83
        }
      },
      "89": {
        start: {
          line: 70,
          column: 24
        },
        end: {
          line: 72,
          column: 38
        }
      },
      "90": {
        start: {
          line: 73,
          column: 24
        },
        end: {
          line: 73,
          column: 83
        }
      },
      "91": {
        start: {
          line: 74,
          column: 24
        },
        end: {
          line: 74,
          column: 100
        }
      },
      "92": {
        start: {
          line: 76,
          column: 24
        },
        end: {
          line: 76,
          column: 41
        }
      },
      "93": {
        start: {
          line: 77,
          column: 24
        },
        end: {
          line: 77,
          column: 85
        }
      },
      "94": {
        start: {
          line: 82,
          column: 4
        },
        end: {
          line: 103,
          column: 6
        }
      },
      "95": {
        start: {
          line: 83,
          column: 8
        },
        end: {
          line: 102,
          column: 11
        }
      },
      "96": {
        start: {
          line: 85,
          column: 12
        },
        end: {
          line: 101,
          column: 15
        }
      },
      "97": {
        start: {
          line: 86,
          column: 16
        },
        end: {
          line: 100,
          column: 17
        }
      },
      "98": {
        start: {
          line: 88,
          column: 24
        },
        end: {
          line: 88,
          column: 86
        }
      },
      "99": {
        start: {
          line: 88,
          column: 62
        },
        end: {
          line: 88,
          column: 86
        }
      },
      "100": {
        start: {
          line: 89,
          column: 24
        },
        end: {
          line: 89,
          column: 69
        }
      },
      "101": {
        start: {
          line: 90,
          column: 24
        },
        end: {
          line: 90,
          column: 43
        }
      },
      "102": {
        start: {
          line: 91,
          column: 24
        },
        end: {
          line: 91,
          column: 66
        }
      },
      "103": {
        start: {
          line: 93,
          column: 24
        },
        end: {
          line: 93,
          column: 52
        }
      },
      "104": {
        start: {
          line: 94,
          column: 24
        },
        end: {
          line: 94,
          column: 53
        }
      },
      "105": {
        start: {
          line: 95,
          column: 24
        },
        end: {
          line: 95,
          column: 94
        }
      },
      "106": {
        start: {
          line: 97,
          column: 24
        },
        end: {
          line: 97,
          column: 47
        }
      },
      "107": {
        start: {
          line: 98,
          column: 24
        },
        end: {
          line: 98,
          column: 75
        }
      },
      "108": {
        start: {
          line: 99,
          column: 24
        },
        end: {
          line: 99,
          column: 128
        }
      },
      "109": {
        start: {
          line: 99,
          column: 75
        },
        end: {
          line: 99,
          column: 114
        }
      },
      "110": {
        start: {
          line: 105,
          column: 4
        },
        end: {
          line: 153,
          column: 6
        }
      },
      "111": {
        start: {
          line: 106,
          column: 8
        },
        end: {
          line: 152,
          column: 11
        }
      },
      "112": {
        start: {
          line: 108,
          column: 12
        },
        end: {
          line: 151,
          column: 15
        }
      },
      "113": {
        start: {
          line: 109,
          column: 16
        },
        end: {
          line: 150,
          column: 17
        }
      },
      "114": {
        start: {
          line: 110,
          column: 28
        },
        end: {
          line: 110,
          column: 84
        }
      },
      "115": {
        start: {
          line: 112,
          column: 24
        },
        end: {
          line: 112,
          column: 47
        }
      },
      "116": {
        start: {
          line: 113,
          column: 24
        },
        end: {
          line: 113,
          column: 37
        }
      },
      "117": {
        start: {
          line: 115,
          column: 24
        },
        end: {
          line: 115,
          column: 50
        }
      },
      "118": {
        start: {
          line: 117,
          column: 24
        },
        end: {
          line: 139,
          column: 32
        }
      },
      "119": {
        start: {
          line: 142,
          column: 24
        },
        end: {
          line: 142,
          column: 34
        }
      },
      "120": {
        start: {
          line: 143,
          column: 24
        },
        end: {
          line: 143,
          column: 48
        }
      },
      "121": {
        start: {
          line: 145,
          column: 24
        },
        end: {
          line: 145,
          column: 44
        }
      },
      "122": {
        start: {
          line: 146,
          column: 24
        },
        end: {
          line: 146,
          column: 102
        }
      },
      "123": {
        start: {
          line: 147,
          column: 24
        },
        end: {
          line: 147,
          column: 116
        }
      },
      "124": {
        start: {
          line: 148,
          column: 24
        },
        end: {
          line: 148,
          column: 48
        }
      },
      "125": {
        start: {
          line: 149,
          column: 28
        },
        end: {
          line: 149,
          column: 50
        }
      },
      "126": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 191,
          column: 6
        }
      },
      "127": {
        start: {
          line: 155,
          column: 8
        },
        end: {
          line: 190,
          column: 11
        }
      },
      "128": {
        start: {
          line: 157,
          column: 12
        },
        end: {
          line: 189,
          column: 15
        }
      },
      "129": {
        start: {
          line: 158,
          column: 16
        },
        end: {
          line: 188,
          column: 17
        }
      },
      "130": {
        start: {
          line: 159,
          column: 28
        },
        end: {
          line: 159,
          column: 84
        }
      },
      "131": {
        start: {
          line: 161,
          column: 24
        },
        end: {
          line: 161,
          column: 47
        }
      },
      "132": {
        start: {
          line: 162,
          column: 24
        },
        end: {
          line: 162,
          column: 37
        }
      },
      "133": {
        start: {
          line: 164,
          column: 24
        },
        end: {
          line: 164,
          column: 50
        }
      },
      "134": {
        start: {
          line: 165,
          column: 24
        },
        end: {
          line: 172,
          column: 32
        }
      },
      "135": {
        start: {
          line: 174,
          column: 24
        },
        end: {
          line: 174,
          column: 43
        }
      },
      "136": {
        start: {
          line: 175,
          column: 24
        },
        end: {
          line: 177,
          column: 25
        }
      },
      "137": {
        start: {
          line: 176,
          column: 28
        },
        end: {
          line: 176,
          column: 64
        }
      },
      "138": {
        start: {
          line: 178,
          column: 24
        },
        end: {
          line: 178,
          column: 48
        }
      },
      "139": {
        start: {
          line: 180,
          column: 24
        },
        end: {
          line: 180,
          column: 44
        }
      },
      "140": {
        start: {
          line: 181,
          column: 24
        },
        end: {
          line: 181,
          column: 104
        }
      },
      "141": {
        start: {
          line: 182,
          column: 24
        },
        end: {
          line: 182,
          column: 87
        }
      },
      "142": {
        start: {
          line: 183,
          column: 24
        },
        end: {
          line: 185,
          column: 25
        }
      },
      "143": {
        start: {
          line: 184,
          column: 28
        },
        end: {
          line: 184,
          column: 66
        }
      },
      "144": {
        start: {
          line: 186,
          column: 24
        },
        end: {
          line: 186,
          column: 48
        }
      },
      "145": {
        start: {
          line: 187,
          column: 28
        },
        end: {
          line: 187,
          column: 56
        }
      },
      "146": {
        start: {
          line: 192,
          column: 4
        },
        end: {
          line: 229,
          column: 6
        }
      },
      "147": {
        start: {
          line: 193,
          column: 8
        },
        end: {
          line: 228,
          column: 11
        }
      },
      "148": {
        start: {
          line: 195,
          column: 12
        },
        end: {
          line: 227,
          column: 15
        }
      },
      "149": {
        start: {
          line: 196,
          column: 16
        },
        end: {
          line: 226,
          column: 17
        }
      },
      "150": {
        start: {
          line: 197,
          column: 28
        },
        end: {
          line: 197,
          column: 84
        }
      },
      "151": {
        start: {
          line: 199,
          column: 24
        },
        end: {
          line: 199,
          column: 47
        }
      },
      "152": {
        start: {
          line: 200,
          column: 24
        },
        end: {
          line: 200,
          column: 37
        }
      },
      "153": {
        start: {
          line: 202,
          column: 24
        },
        end: {
          line: 202,
          column: 50
        }
      },
      "154": {
        start: {
          line: 203,
          column: 24
        },
        end: {
          line: 210,
          column: 32
        }
      },
      "155": {
        start: {
          line: 212,
          column: 24
        },
        end: {
          line: 212,
          column: 43
        }
      },
      "156": {
        start: {
          line: 213,
          column: 24
        },
        end: {
          line: 215,
          column: 25
        }
      },
      "157": {
        start: {
          line: 214,
          column: 28
        },
        end: {
          line: 214,
          column: 56
        }
      },
      "158": {
        start: {
          line: 216,
          column: 24
        },
        end: {
          line: 216,
          column: 48
        }
      },
      "159": {
        start: {
          line: 218,
          column: 24
        },
        end: {
          line: 218,
          column: 44
        }
      },
      "160": {
        start: {
          line: 219,
          column: 24
        },
        end: {
          line: 219,
          column: 105
        }
      },
      "161": {
        start: {
          line: 220,
          column: 24
        },
        end: {
          line: 220,
          column: 87
        }
      },
      "162": {
        start: {
          line: 221,
          column: 24
        },
        end: {
          line: 223,
          column: 25
        }
      },
      "163": {
        start: {
          line: 222,
          column: 28
        },
        end: {
          line: 222,
          column: 56
        }
      },
      "164": {
        start: {
          line: 224,
          column: 24
        },
        end: {
          line: 224,
          column: 48
        }
      },
      "165": {
        start: {
          line: 225,
          column: 28
        },
        end: {
          line: 225,
          column: 57
        }
      },
      "166": {
        start: {
          line: 231,
          column: 4
        },
        end: {
          line: 310,
          column: 6
        }
      },
      "167": {
        start: {
          line: 232,
          column: 8
        },
        end: {
          line: 309,
          column: 11
        }
      },
      "168": {
        start: {
          line: 234,
          column: 12
        },
        end: {
          line: 308,
          column: 15
        }
      },
      "169": {
        start: {
          line: 235,
          column: 16
        },
        end: {
          line: 307,
          column: 17
        }
      },
      "170": {
        start: {
          line: 236,
          column: 28
        },
        end: {
          line: 236,
          column: 84
        }
      },
      "171": {
        start: {
          line: 238,
          column: 24
        },
        end: {
          line: 238,
          column: 47
        }
      },
      "172": {
        start: {
          line: 239,
          column: 24
        },
        end: {
          line: 239,
          column: 41
        }
      },
      "173": {
        start: {
          line: 240,
          column: 24
        },
        end: {
          line: 240,
          column: 73
        }
      },
      "174": {
        start: {
          line: 241,
          column: 24
        },
        end: {
          line: 241,
          column: 37
        }
      },
      "175": {
        start: {
          line: 243,
          column: 24
        },
        end: {
          line: 243,
          column: 50
        }
      },
      "176": {
        start: {
          line: 244,
          column: 24
        },
        end: {
          line: 251,
          column: 32
        }
      },
      "177": {
        start: {
          line: 253,
          column: 24
        },
        end: {
          line: 253,
          column: 42
        }
      },
      "178": {
        start: {
          line: 254,
          column: 24
        },
        end: {
          line: 254,
          column: 77
        }
      },
      "179": {
        start: {
          line: 254,
          column: 53
        },
        end: {
          line: 254,
          column: 77
        }
      },
      "180": {
        start: {
          line: 255,
          column: 24
        },
        end: {
          line: 258,
          column: 32
        }
      },
      "181": {
        start: {
          line: 260,
          column: 24
        },
        end: {
          line: 260,
          column: 48
        }
      },
      "182": {
        start: {
          line: 261,
          column: 24
        },
        end: {
          line: 265,
          column: 31
        }
      },
      "183": {
        start: {
          line: 268,
          column: 20
        },
        end: {
          line: 277,
          column: 28
        }
      },
      "184": {
        start: {
          line: 280,
          column: 24
        },
        end: {
          line: 280,
          column: 34
        }
      },
      "185": {
        start: {
          line: 281,
          column: 24
        },
        end: {
          line: 285,
          column: 31
        }
      },
      "186": {
        start: {
          line: 287,
          column: 24
        },
        end: {
          line: 287,
          column: 44
        }
      },
      "187": {
        start: {
          line: 288,
          column: 24
        },
        end: {
          line: 288,
          column: 103
        }
      },
      "188": {
        start: {
          line: 289,
          column: 24
        },
        end: {
          line: 289,
          column: 57
        }
      },
      "189": {
        start: {
          line: 290,
          column: 24
        },
        end: {
          line: 290,
          column: 69
        }
      },
      "190": {
        start: {
          line: 291,
          column: 24
        },
        end: {
          line: 291,
          column: 127
        }
      },
      "191": {
        start: {
          line: 291,
          column: 73
        },
        end: {
          line: 291,
          column: 123
        }
      },
      "192": {
        start: {
          line: 292,
          column: 24
        },
        end: {
          line: 298,
          column: 25
        }
      },
      "193": {
        start: {
          line: 293,
          column: 28
        },
        end: {
          line: 297,
          column: 35
        }
      },
      "194": {
        start: {
          line: 299,
          column: 24
        },
        end: {
          line: 299,
          column: 72
        }
      },
      "195": {
        start: {
          line: 300,
          column: 24
        },
        end: {
          line: 300,
          column: 67
        }
      },
      "196": {
        start: {
          line: 301,
          column: 24
        },
        end: {
          line: 305,
          column: 31
        }
      },
      "197": {
        start: {
          line: 306,
          column: 28
        },
        end: {
          line: 306,
          column: 50
        }
      },
      "198": {
        start: {
          line: 312,
          column: 4
        },
        end: {
          line: 386,
          column: 6
        }
      },
      "199": {
        start: {
          line: 313,
          column: 8
        },
        end: {
          line: 385,
          column: 11
        }
      },
      "200": {
        start: {
          line: 315,
          column: 24
        },
        end: {
          line: 315,
          column: 28
        }
      },
      "201": {
        start: {
          line: 316,
          column: 12
        },
        end: {
          line: 384,
          column: 15
        }
      },
      "202": {
        start: {
          line: 317,
          column: 16
        },
        end: {
          line: 383,
          column: 17
        }
      },
      "203": {
        start: {
          line: 319,
          column: 24
        },
        end: {
          line: 319,
          column: 50
        }
      },
      "204": {
        start: {
          line: 320,
          column: 24
        },
        end: {
          line: 320,
          column: 43
        }
      },
      "205": {
        start: {
          line: 322,
          column: 24
        },
        end: {
          line: 337,
          column: 45
        }
      },
      "206": {
        start: {
          line: 322,
          column: 91
        },
        end: {
          line: 337,
          column: 31
        }
      },
      "207": {
        start: {
          line: 323,
          column: 32
        },
        end: {
          line: 336,
          column: 35
        }
      },
      "208": {
        start: {
          line: 324,
          column: 36
        },
        end: {
          line: 335,
          column: 37
        }
      },
      "209": {
        start: {
          line: 325,
          column: 48
        },
        end: {
          line: 331,
          column: 48
        }
      },
      "210": {
        start: {
          line: 333,
          column: 44
        },
        end: {
          line: 333,
          column: 54
        }
      },
      "211": {
        start: {
          line: 334,
          column: 44
        },
        end: {
          line: 334,
          column: 66
        }
      },
      "212": {
        start: {
          line: 340,
          column: 24
        },
        end: {
          line: 340,
          column: 34
        }
      },
      "213": {
        start: {
          line: 341,
          column: 24
        },
        end: {
          line: 341,
          column: 86
        }
      },
      "214": {
        start: {
          line: 342,
          column: 24
        },
        end: {
          line: 357,
          column: 45
        }
      },
      "215": {
        start: {
          line: 342,
          column: 91
        },
        end: {
          line: 357,
          column: 31
        }
      },
      "216": {
        start: {
          line: 343,
          column: 32
        },
        end: {
          line: 356,
          column: 35
        }
      },
      "217": {
        start: {
          line: 344,
          column: 36
        },
        end: {
          line: 355,
          column: 37
        }
      },
      "218": {
        start: {
          line: 345,
          column: 48
        },
        end: {
          line: 351,
          column: 48
        }
      },
      "219": {
        start: {
          line: 353,
          column: 44
        },
        end: {
          line: 353,
          column: 54
        }
      },
      "220": {
        start: {
          line: 354,
          column: 44
        },
        end: {
          line: 354,
          column: 66
        }
      },
      "221": {
        start: {
          line: 359,
          column: 24
        },
        end: {
          line: 359,
          column: 34
        }
      },
      "222": {
        start: {
          line: 360,
          column: 24
        },
        end: {
          line: 360,
          column: 48
        }
      },
      "223": {
        start: {
          line: 362,
          column: 24
        },
        end: {
          line: 362,
          column: 44
        }
      },
      "224": {
        start: {
          line: 363,
          column: 24
        },
        end: {
          line: 363,
          column: 88
        }
      },
      "225": {
        start: {
          line: 364,
          column: 24
        },
        end: {
          line: 364,
          column: 48
        }
      },
      "226": {
        start: {
          line: 366,
          column: 24
        },
        end: {
          line: 366,
          column: 41
        }
      },
      "227": {
        start: {
          line: 367,
          column: 24
        },
        end: {
          line: 381,
          column: 27
        }
      },
      "228": {
        start: {
          line: 368,
          column: 38
        },
        end: {
          line: 368,
          column: 43
        }
      },
      "229": {
        start: {
          line: 368,
          column: 53
        },
        end: {
          line: 368,
          column: 58
        }
      },
      "230": {
        start: {
          line: 369,
          column: 28
        },
        end: {
          line: 380,
          column: 29
        }
      },
      "231": {
        start: {
          line: 370,
          column: 32
        },
        end: {
          line: 370,
          column: 65
        }
      },
      "232": {
        start: {
          line: 372,
          column: 33
        },
        end: {
          line: 380,
          column: 29
        }
      },
      "233": {
        start: {
          line: 373,
          column: 51
        },
        end: {
          line: 373,
          column: 137
        }
      },
      "234": {
        start: {
          line: 373,
          column: 83
        },
        end: {
          line: 373,
          column: 134
        }
      },
      "235": {
        start: {
          line: 374,
          column: 32
        },
        end: {
          line: 379,
          column: 33
        }
      },
      "236": {
        start: {
          line: 375,
          column: 36
        },
        end: {
          line: 375,
          column: 69
        }
      },
      "237": {
        start: {
          line: 378,
          column: 36
        },
        end: {
          line: 378,
          column: 80
        }
      },
      "238": {
        start: {
          line: 382,
          column: 24
        },
        end: {
          line: 382,
          column: 46
        }
      },
      "239": {
        start: {
          line: 387,
          column: 4
        },
        end: {
          line: 387,
          column: 27
        }
      },
      "240": {
        start: {
          line: 389,
          column: 0
        },
        end: {
          line: 389,
          column: 42
        }
      },
      "241": {
        start: {
          line: 391,
          column: 22
        },
        end: {
          line: 391,
          column: 51
        }
      },
      "242": {
        start: {
          line: 392,
          column: 0
        },
        end: {
          line: 394,
          column: 19
        }
      },
      "243": {
        start: {
          line: 393,
          column: 4
        },
        end: {
          line: 393,
          column: 30
        }
      },
      "244": {
        start: {
          line: 395,
          column: 0
        },
        end: {
          line: 395,
          column: 34
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 38,
            column: 57
          }
        },
        loc: {
          start: {
            line: 38,
            column: 71
          },
          end: {
            line: 40,
            column: 1
          }
        },
        line: 38
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 48,
            column: 37
          },
          end: {
            line: 48,
            column: 38
          }
        },
        loc: {
          start: {
            line: 48,
            column: 49
          },
          end: {
            line: 388,
            column: 1
          }
        },
        line: 48
      },
      "15": {
        name: "SecurityStorage",
        decl: {
          start: {
            line: 49,
            column: 13
          },
          end: {
            line: 49,
            column: 28
          }
        },
        loc: {
          start: {
            line: 49,
            column: 31
          },
          end: {
            line: 51,
            column: 5
          }
        },
        line: 49
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 52,
            column: 34
          },
          end: {
            line: 52,
            column: 35
          }
        },
        loc: {
          start: {
            line: 52,
            column: 46
          },
          end: {
            line: 57,
            column: 5
          }
        },
        line: 52
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 58,
            column: 52
          },
          end: {
            line: 58,
            column: 53
          }
        },
        loc: {
          start: {
            line: 58,
            column: 71
          },
          end: {
            line: 81,
            column: 5
          }
        },
        line: 58
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 59,
            column: 48
          },
          end: {
            line: 59,
            column: 49
          }
        },
        loc: {
          start: {
            line: 59,
            column: 60
          },
          end: {
            line: 80,
            column: 9
          }
        },
        line: 59
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 62,
            column: 37
          },
          end: {
            line: 62,
            column: 38
          }
        },
        loc: {
          start: {
            line: 62,
            column: 51
          },
          end: {
            line: 79,
            column: 13
          }
        },
        line: 62
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 82,
            column: 43
          },
          end: {
            line: 82,
            column: 44
          }
        },
        loc: {
          start: {
            line: 82,
            column: 60
          },
          end: {
            line: 103,
            column: 5
          }
        },
        line: 82
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 83,
            column: 48
          },
          end: {
            line: 83,
            column: 49
          }
        },
        loc: {
          start: {
            line: 83,
            column: 60
          },
          end: {
            line: 102,
            column: 9
          }
        },
        line: 83
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 85,
            column: 37
          },
          end: {
            line: 85,
            column: 38
          }
        },
        loc: {
          start: {
            line: 85,
            column: 51
          },
          end: {
            line: 101,
            column: 13
          }
        },
        line: 85
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 99,
            column: 60
          },
          end: {
            line: 99,
            column: 61
          }
        },
        loc: {
          start: {
            line: 99,
            column: 73
          },
          end: {
            line: 99,
            column: 116
          }
        },
        line: 99
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 105,
            column: 47
          },
          end: {
            line: 105,
            column: 48
          }
        },
        loc: {
          start: {
            line: 105,
            column: 84
          },
          end: {
            line: 153,
            column: 5
          }
        },
        line: 105
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 106,
            column: 48
          },
          end: {
            line: 106,
            column: 49
          }
        },
        loc: {
          start: {
            line: 106,
            column: 60
          },
          end: {
            line: 152,
            column: 9
          }
        },
        line: 106
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 108,
            column: 37
          },
          end: {
            line: 108,
            column: 38
          }
        },
        loc: {
          start: {
            line: 108,
            column: 51
          },
          end: {
            line: 151,
            column: 13
          }
        },
        line: 108
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 154,
            column: 45
          },
          end: {
            line: 154,
            column: 46
          }
        },
        loc: {
          start: {
            line: 154,
            column: 64
          },
          end: {
            line: 191,
            column: 5
          }
        },
        line: 154
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 155,
            column: 48
          },
          end: {
            line: 155,
            column: 49
          }
        },
        loc: {
          start: {
            line: 155,
            column: 60
          },
          end: {
            line: 190,
            column: 9
          }
        },
        line: 155
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 157,
            column: 37
          },
          end: {
            line: 157,
            column: 38
          }
        },
        loc: {
          start: {
            line: 157,
            column: 51
          },
          end: {
            line: 189,
            column: 13
          }
        },
        line: 157
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 192,
            column: 50
          },
          end: {
            line: 192,
            column: 51
          }
        },
        loc: {
          start: {
            line: 192,
            column: 76
          },
          end: {
            line: 229,
            column: 5
          }
        },
        line: 192
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 193,
            column: 48
          },
          end: {
            line: 193,
            column: 49
          }
        },
        loc: {
          start: {
            line: 193,
            column: 60
          },
          end: {
            line: 228,
            column: 9
          }
        },
        line: 193
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 195,
            column: 37
          },
          end: {
            line: 195,
            column: 38
          }
        },
        loc: {
          start: {
            line: 195,
            column: 51
          },
          end: {
            line: 227,
            column: 13
          }
        },
        line: 195
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 231,
            column: 47
          },
          end: {
            line: 231,
            column: 48
          }
        },
        loc: {
          start: {
            line: 231,
            column: 89
          },
          end: {
            line: 310,
            column: 5
          }
        },
        line: 231
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 232,
            column: 48
          },
          end: {
            line: 232,
            column: 49
          }
        },
        loc: {
          start: {
            line: 232,
            column: 60
          },
          end: {
            line: 309,
            column: 9
          }
        },
        line: 232
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 234,
            column: 37
          },
          end: {
            line: 234,
            column: 38
          }
        },
        loc: {
          start: {
            line: 234,
            column: 51
          },
          end: {
            line: 308,
            column: 13
          }
        },
        line: 234
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 291,
            column: 54
          },
          end: {
            line: 291,
            column: 55
          }
        },
        loc: {
          start: {
            line: 291,
            column: 71
          },
          end: {
            line: 291,
            column: 125
          }
        },
        line: 291
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 312,
            column: 40
          },
          end: {
            line: 312,
            column: 41
          }
        },
        loc: {
          start: {
            line: 312,
            column: 52
          },
          end: {
            line: 386,
            column: 5
          }
        },
        line: 312
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 313,
            column: 48
          },
          end: {
            line: 313,
            column: 49
          }
        },
        loc: {
          start: {
            line: 313,
            column: 60
          },
          end: {
            line: 385,
            column: 9
          }
        },
        line: 313
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 316,
            column: 37
          },
          end: {
            line: 316,
            column: 38
          }
        },
        loc: {
          start: {
            line: 316,
            column: 51
          },
          end: {
            line: 384,
            column: 13
          }
        },
        line: 316
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 322,
            column: 77
          },
          end: {
            line: 322,
            column: 78
          }
        },
        loc: {
          start: {
            line: 322,
            column: 89
          },
          end: {
            line: 337,
            column: 33
          }
        },
        line: 322
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 322,
            column: 131
          },
          end: {
            line: 322,
            column: 132
          }
        },
        loc: {
          start: {
            line: 322,
            column: 143
          },
          end: {
            line: 337,
            column: 29
          }
        },
        line: 322
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 323,
            column: 57
          },
          end: {
            line: 323,
            column: 58
          }
        },
        loc: {
          start: {
            line: 323,
            column: 71
          },
          end: {
            line: 336,
            column: 33
          }
        },
        line: 323
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 342,
            column: 77
          },
          end: {
            line: 342,
            column: 78
          }
        },
        loc: {
          start: {
            line: 342,
            column: 89
          },
          end: {
            line: 357,
            column: 33
          }
        },
        line: 342
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 342,
            column: 131
          },
          end: {
            line: 342,
            column: 132
          }
        },
        loc: {
          start: {
            line: 342,
            column: 143
          },
          end: {
            line: 357,
            column: 29
          }
        },
        line: 342
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 343,
            column: 57
          },
          end: {
            line: 343,
            column: 58
          }
        },
        loc: {
          start: {
            line: 343,
            column: 71
          },
          end: {
            line: 356,
            column: 33
          }
        },
        line: 343
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 367,
            column: 74
          },
          end: {
            line: 367,
            column: 75
          }
        },
        loc: {
          start: {
            line: 367,
            column: 88
          },
          end: {
            line: 381,
            column: 25
          }
        },
        line: 367
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 373,
            column: 64
          },
          end: {
            line: 373,
            column: 65
          }
        },
        loc: {
          start: {
            line: 373,
            column: 81
          },
          end: {
            line: 373,
            column: 136
          }
        },
        line: 373
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 392,
            column: 12
          },
          end: {
            line: 392,
            column: 13
          }
        },
        loc: {
          start: {
            line: 392,
            column: 24
          },
          end: {
            line: 394,
            column: 1
          }
        },
        line: 392
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 38,
            column: 22
          },
          end: {
            line: 40,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 23
          },
          end: {
            line: 38,
            column: 27
          }
        }, {
          start: {
            line: 38,
            column: 31
          },
          end: {
            line: 38,
            column: 51
          }
        }, {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 40,
            column: 1
          }
        }],
        line: 38
      },
      "33": {
        loc: {
          start: {
            line: 39,
            column: 11
          },
          end: {
            line: 39,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 37
          },
          end: {
            line: 39,
            column: 40
          }
        }, {
          start: {
            line: 39,
            column: 43
          },
          end: {
            line: 39,
            column: 61
          }
        }],
        line: 39
      },
      "34": {
        loc: {
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 15
          }
        }, {
          start: {
            line: 39,
            column: 19
          },
          end: {
            line: 39,
            column: 33
          }
        }],
        line: 39
      },
      "35": {
        loc: {
          start: {
            line: 53,
            column: 8
          },
          end: {
            line: 55,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 53,
            column: 8
          },
          end: {
            line: 55,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 53
      },
      "36": {
        loc: {
          start: {
            line: 63,
            column: 16
          },
          end: {
            line: 78,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 64,
            column: 20
          },
          end: {
            line: 64,
            column: 99
          }
        }, {
          start: {
            line: 65,
            column: 20
          },
          end: {
            line: 74,
            column: 100
          }
        }, {
          start: {
            line: 75,
            column: 20
          },
          end: {
            line: 77,
            column: 85
          }
        }],
        line: 63
      },
      "37": {
        loc: {
          start: {
            line: 67,
            column: 24
          },
          end: {
            line: 69,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 67,
            column: 24
          },
          end: {
            line: 69,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 67
      },
      "38": {
        loc: {
          start: {
            line: 67,
            column: 28
          },
          end: {
            line: 67,
            column: 140
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 67,
            column: 126
          },
          end: {
            line: 67,
            column: 132
          }
        }, {
          start: {
            line: 67,
            column: 135
          },
          end: {
            line: 67,
            column: 140
          }
        }],
        line: 67
      },
      "39": {
        loc: {
          start: {
            line: 67,
            column: 28
          },
          end: {
            line: 67,
            column: 123
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 67,
            column: 28
          },
          end: {
            line: 67,
            column: 106
          }
        }, {
          start: {
            line: 67,
            column: 110
          },
          end: {
            line: 67,
            column: 123
          }
        }],
        line: 67
      },
      "40": {
        loc: {
          start: {
            line: 67,
            column: 34
          },
          end: {
            line: 67,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 67,
            column: 75
          },
          end: {
            line: 67,
            column: 81
          }
        }, {
          start: {
            line: 67,
            column: 84
          },
          end: {
            line: 67,
            column: 96
          }
        }],
        line: 67
      },
      "41": {
        loc: {
          start: {
            line: 67,
            column: 34
          },
          end: {
            line: 67,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 67,
            column: 34
          },
          end: {
            line: 67,
            column: 50
          }
        }, {
          start: {
            line: 67,
            column: 54
          },
          end: {
            line: 67,
            column: 72
          }
        }],
        line: 67
      },
      "42": {
        loc: {
          start: {
            line: 70,
            column: 29
          },
          end: {
            line: 72,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 70,
            column: 29
          },
          end: {
            line: 70,
            column: 67
          }
        }, {
          start: {
            line: 71,
            column: 28
          },
          end: {
            line: 71,
            column: 60
          }
        }, {
          start: {
            line: 72,
            column: 28
          },
          end: {
            line: 72,
            column: 37
          }
        }],
        line: 70
      },
      "43": {
        loc: {
          start: {
            line: 73,
            column: 36
          },
          end: {
            line: 73,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 73,
            column: 36
          },
          end: {
            line: 73,
            column: 69
          }
        }, {
          start: {
            line: 73,
            column: 73
          },
          end: {
            line: 73,
            column: 82
          }
        }],
        line: 73
      },
      "44": {
        loc: {
          start: {
            line: 86,
            column: 16
          },
          end: {
            line: 100,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 87,
            column: 20
          },
          end: {
            line: 91,
            column: 66
          }
        }, {
          start: {
            line: 92,
            column: 20
          },
          end: {
            line: 95,
            column: 94
          }
        }, {
          start: {
            line: 96,
            column: 20
          },
          end: {
            line: 99,
            column: 128
          }
        }],
        line: 86
      },
      "45": {
        loc: {
          start: {
            line: 88,
            column: 24
          },
          end: {
            line: 88,
            column: 86
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 88,
            column: 24
          },
          end: {
            line: 88,
            column: 86
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 88
      },
      "46": {
        loc: {
          start: {
            line: 109,
            column: 16
          },
          end: {
            line: 150,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 110,
            column: 20
          },
          end: {
            line: 110,
            column: 84
          }
        }, {
          start: {
            line: 111,
            column: 20
          },
          end: {
            line: 113,
            column: 37
          }
        }, {
          start: {
            line: 114,
            column: 20
          },
          end: {
            line: 139,
            column: 32
          }
        }, {
          start: {
            line: 140,
            column: 20
          },
          end: {
            line: 143,
            column: 48
          }
        }, {
          start: {
            line: 144,
            column: 20
          },
          end: {
            line: 148,
            column: 48
          }
        }, {
          start: {
            line: 149,
            column: 20
          },
          end: {
            line: 149,
            column: 50
          }
        }],
        line: 109
      },
      "47": {
        loc: {
          start: {
            line: 136,
            column: 44
          },
          end: {
            line: 136,
            column: 118
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 136,
            column: 44
          },
          end: {
            line: 136,
            column: 82
          }
        }, {
          start: {
            line: 136,
            column: 86
          },
          end: {
            line: 136,
            column: 118
          }
        }],
        line: 136
      },
      "48": {
        loc: {
          start: {
            line: 158,
            column: 16
          },
          end: {
            line: 188,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 159,
            column: 20
          },
          end: {
            line: 159,
            column: 84
          }
        }, {
          start: {
            line: 160,
            column: 20
          },
          end: {
            line: 162,
            column: 37
          }
        }, {
          start: {
            line: 163,
            column: 20
          },
          end: {
            line: 172,
            column: 32
          }
        }, {
          start: {
            line: 173,
            column: 20
          },
          end: {
            line: 178,
            column: 48
          }
        }, {
          start: {
            line: 179,
            column: 20
          },
          end: {
            line: 186,
            column: 48
          }
        }, {
          start: {
            line: 187,
            column: 20
          },
          end: {
            line: 187,
            column: 56
          }
        }],
        line: 158
      },
      "49": {
        loc: {
          start: {
            line: 175,
            column: 24
          },
          end: {
            line: 177,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 175,
            column: 24
          },
          end: {
            line: 177,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 175
      },
      "50": {
        loc: {
          start: {
            line: 175,
            column: 28
          },
          end: {
            line: 175,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 175,
            column: 28
          },
          end: {
            line: 175,
            column: 34
          }
        }, {
          start: {
            line: 175,
            column: 38
          },
          end: {
            line: 175,
            column: 67
          }
        }],
        line: 175
      },
      "51": {
        loc: {
          start: {
            line: 183,
            column: 24
          },
          end: {
            line: 185,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 183,
            column: 24
          },
          end: {
            line: 185,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 183
      },
      "52": {
        loc: {
          start: {
            line: 183,
            column: 28
          },
          end: {
            line: 183,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 183,
            column: 28
          },
          end: {
            line: 183,
            column: 36
          }
        }, {
          start: {
            line: 183,
            column: 40
          },
          end: {
            line: 183,
            column: 71
          }
        }],
        line: 183
      },
      "53": {
        loc: {
          start: {
            line: 196,
            column: 16
          },
          end: {
            line: 226,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 197,
            column: 20
          },
          end: {
            line: 197,
            column: 84
          }
        }, {
          start: {
            line: 198,
            column: 20
          },
          end: {
            line: 200,
            column: 37
          }
        }, {
          start: {
            line: 201,
            column: 20
          },
          end: {
            line: 210,
            column: 32
          }
        }, {
          start: {
            line: 211,
            column: 20
          },
          end: {
            line: 216,
            column: 48
          }
        }, {
          start: {
            line: 217,
            column: 20
          },
          end: {
            line: 224,
            column: 48
          }
        }, {
          start: {
            line: 225,
            column: 20
          },
          end: {
            line: 225,
            column: 57
          }
        }],
        line: 196
      },
      "54": {
        loc: {
          start: {
            line: 213,
            column: 24
          },
          end: {
            line: 215,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 213,
            column: 24
          },
          end: {
            line: 215,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 213
      },
      "55": {
        loc: {
          start: {
            line: 213,
            column: 28
          },
          end: {
            line: 213,
            column: 93
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 213,
            column: 28
          },
          end: {
            line: 213,
            column: 34
          }
        }, {
          start: {
            line: 213,
            column: 38
          },
          end: {
            line: 213,
            column: 67
          }
        }, {
          start: {
            line: 213,
            column: 71
          },
          end: {
            line: 213,
            column: 93
          }
        }],
        line: 213
      },
      "56": {
        loc: {
          start: {
            line: 221,
            column: 24
          },
          end: {
            line: 223,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 221,
            column: 24
          },
          end: {
            line: 223,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 221
      },
      "57": {
        loc: {
          start: {
            line: 221,
            column: 28
          },
          end: {
            line: 221,
            column: 99
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 221,
            column: 28
          },
          end: {
            line: 221,
            column: 36
          }
        }, {
          start: {
            line: 221,
            column: 40
          },
          end: {
            line: 221,
            column: 71
          }
        }, {
          start: {
            line: 221,
            column: 75
          },
          end: {
            line: 221,
            column: 99
          }
        }],
        line: 221
      },
      "58": {
        loc: {
          start: {
            line: 235,
            column: 16
          },
          end: {
            line: 307,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 236,
            column: 20
          },
          end: {
            line: 236,
            column: 84
          }
        }, {
          start: {
            line: 237,
            column: 20
          },
          end: {
            line: 241,
            column: 37
          }
        }, {
          start: {
            line: 242,
            column: 20
          },
          end: {
            line: 251,
            column: 32
          }
        }, {
          start: {
            line: 252,
            column: 20
          },
          end: {
            line: 258,
            column: 32
          }
        }, {
          start: {
            line: 259,
            column: 20
          },
          end: {
            line: 265,
            column: 31
          }
        }, {
          start: {
            line: 266,
            column: 20
          },
          end: {
            line: 277,
            column: 28
          }
        }, {
          start: {
            line: 278,
            column: 20
          },
          end: {
            line: 285,
            column: 31
          }
        }, {
          start: {
            line: 286,
            column: 20
          },
          end: {
            line: 305,
            column: 31
          }
        }, {
          start: {
            line: 306,
            column: 20
          },
          end: {
            line: 306,
            column: 50
          }
        }],
        line: 235
      },
      "59": {
        loc: {
          start: {
            line: 254,
            column: 24
          },
          end: {
            line: 254,
            column: 77
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 254,
            column: 24
          },
          end: {
            line: 254,
            column: 77
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 254
      },
      "60": {
        loc: {
          start: {
            line: 264,
            column: 43
          },
          end: {
            line: 264,
            column: 126
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 264,
            column: 57
          },
          end: {
            line: 264,
            column: 99
          }
        }, {
          start: {
            line: 264,
            column: 102
          },
          end: {
            line: 264,
            column: 126
          }
        }],
        line: 264
      },
      "61": {
        loc: {
          start: {
            line: 273,
            column: 40
          },
          end: {
            line: 273,
            column: 114
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 273,
            column: 40
          },
          end: {
            line: 273,
            column: 78
          }
        }, {
          start: {
            line: 273,
            column: 82
          },
          end: {
            line: 273,
            column: 114
          }
        }],
        line: 273
      },
      "62": {
        loc: {
          start: {
            line: 290,
            column: 34
          },
          end: {
            line: 290,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 290,
            column: 34
          },
          end: {
            line: 290,
            column: 62
          }
        }, {
          start: {
            line: 290,
            column: 66
          },
          end: {
            line: 290,
            column: 68
          }
        }],
        line: 290
      },
      "63": {
        loc: {
          start: {
            line: 292,
            column: 24
          },
          end: {
            line: 298,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 292,
            column: 24
          },
          end: {
            line: 298,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 292
      },
      "64": {
        loc: {
          start: {
            line: 317,
            column: 16
          },
          end: {
            line: 383,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 318,
            column: 20
          },
          end: {
            line: 337,
            column: 45
          }
        }, {
          start: {
            line: 338,
            column: 20
          },
          end: {
            line: 357,
            column: 45
          }
        }, {
          start: {
            line: 358,
            column: 20
          },
          end: {
            line: 360,
            column: 48
          }
        }, {
          start: {
            line: 361,
            column: 20
          },
          end: {
            line: 364,
            column: 48
          }
        }, {
          start: {
            line: 365,
            column: 20
          },
          end: {
            line: 382,
            column: 46
          }
        }],
        line: 317
      },
      "65": {
        loc: {
          start: {
            line: 324,
            column: 36
          },
          end: {
            line: 335,
            column: 37
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 325,
            column: 40
          },
          end: {
            line: 331,
            column: 48
          }
        }, {
          start: {
            line: 332,
            column: 40
          },
          end: {
            line: 334,
            column: 66
          }
        }],
        line: 324
      },
      "66": {
        loc: {
          start: {
            line: 344,
            column: 36
          },
          end: {
            line: 355,
            column: 37
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 345,
            column: 40
          },
          end: {
            line: 351,
            column: 48
          }
        }, {
          start: {
            line: 352,
            column: 40
          },
          end: {
            line: 354,
            column: 66
          }
        }],
        line: 344
      },
      "67": {
        loc: {
          start: {
            line: 369,
            column: 28
          },
          end: {
            line: 380,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 369,
            column: 28
          },
          end: {
            line: 380,
            column: 29
          }
        }, {
          start: {
            line: 372,
            column: 33
          },
          end: {
            line: 380,
            column: 29
          }
        }],
        line: 369
      },
      "68": {
        loc: {
          start: {
            line: 369,
            column: 32
          },
          end: {
            line: 369,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 369,
            column: 32
          },
          end: {
            line: 369,
            column: 55
          }
        }, {
          start: {
            line: 369,
            column: 59
          },
          end: {
            line: 369,
            column: 80
          }
        }],
        line: 369
      },
      "69": {
        loc: {
          start: {
            line: 372,
            column: 33
          },
          end: {
            line: 380,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 372,
            column: 33
          },
          end: {
            line: 380,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 372
      },
      "70": {
        loc: {
          start: {
            line: 374,
            column: 32
          },
          end: {
            line: 379,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 374,
            column: 32
          },
          end: {
            line: 379,
            column: 33
          }
        }, {
          start: {
            line: 377,
            column: 37
          },
          end: {
            line: 379,
            column: 33
          }
        }],
        line: 374
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0, 0],
      "43": [0, 0],
      "44": [0, 0, 0],
      "45": [0, 0],
      "46": [0, 0, 0, 0, 0, 0],
      "47": [0, 0],
      "48": [0, 0, 0, 0, 0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0, 0, 0, 0, 0],
      "54": [0, 0],
      "55": [0, 0, 0],
      "56": [0, 0],
      "57": [0, 0, 0],
      "58": [0, 0, 0, 0, 0, 0, 0, 0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0, 0, 0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/security-storage.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mCAAqD;AAErD,uCAAkD;AAClD,+BAAqC;AACrC,kDAA4B;AAE5B,sDAAsD;AACtD;IAAA;QAEU,mBAAc,GAAG,IAAI,GAAG,EAAe,CAAC;IAmQlD,CAAC;IAjQQ,2BAAW,GAAlB;QACE,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;YAC9B,eAAe,CAAC,QAAQ,GAAG,IAAI,eAAe,EAAE,CAAC;QACnD,CAAC;QACD,OAAO,eAAe,CAAC,QAAQ,CAAC;IAClC,CAAC;IAEa,6CAAmB,GAAjC,UAAkC,OAAoB;uCAAG,OAAO;;;;;4BAC9C,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;wBAA7C,OAAO,GAAG,SAAmC;wBACnD,IAAI,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,EAAE,CAAC;4BACtB,sBAAO,eAAQ,OAAO,CAAC,IAAI,CAAC,EAAE,CAAE,EAAC;wBACnC,CAAC;wBAGK,EAAE,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC;4BACtC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;4BAChC,SAAS,CAAC;wBACf,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,SAAS,CAAC;wBACpD,qBAAM,IAAI,CAAC,UAAU,CAAC,UAAG,EAAE,cAAI,SAAS,CAAE,CAAC,EAAA;;wBAAlD,IAAI,GAAG,SAA2C;wBACxD,sBAAO,eAAQ,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAE,EAAC;;;;KACxC;IAEa,oCAAU,GAAxB,UAAyB,KAAa;uCAAG,OAAO;;;;;6BAE1C,CAAA,OAAO,MAAM,KAAK,WAAW,CAAA,EAA7B,wBAA6B;wBAEzB,IAAI,GAAG,gBAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;wBACzC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;wBACnB,sBAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,EAAC;;wBAGpB,OAAO,GAAG,IAAI,WAAW,EAAE,CAAC;wBAC5B,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;wBAChB,qBAAM,gBAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,EAAA;;wBAAxD,UAAU,GAAG,SAA2C;wBACxD,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;wBACzD,sBAAO,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAA/B,CAA+B,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAC;;;;KAEvE;IAED,wBAAwB;IAClB,wCAAc,GAApB,UAAqB,OAAoB,EAAE,KAAa,EAAE,SAAe;uCAAG,OAAO;;;;4BAC9D,qBAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAA;;wBAApD,UAAU,GAAG,SAAuC;;;;wBAGxD,qBAAqB;wBACrB,qBAAM,eAAM,CAAC,aAAa,CAAC,MAAM,CAAC;gCAChC,KAAK,EAAE;oCACL,eAAe,EAAE;wCACf,UAAU,YAAA;wCACV,IAAI,EAAE,MAAM;qCACb;iCACF;gCACD,MAAM,EAAE;oCACN,KAAK,OAAA;oCACL,SAAS,WAAA;oCACT,SAAS,EAAE,IAAI,IAAI,EAAE;iCACtB;gCACD,MAAM,EAAE;oCACN,UAAU,YAAA;oCACV,IAAI,EAAE,MAAM;oCACZ,KAAK,OAAA;oCACL,SAAS,WAAA;oCACT,QAAQ,EAAE;wCACR,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;wCAC5C,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;qCAC/E;iCACF;6BACF,CAAC,EAAA;;wBAvBF,qBAAqB;wBACrB,SAsBE,CAAC;;;;wBAEH,OAAO,CAAC,IAAI,CAAC,sDAAsD,EAAE,OAAK,CAAC,CAAC;wBAC5E,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,eAAQ,UAAU,CAAE,EAAE,EAAE,KAAK,OAAA,EAAE,SAAS,WAAA,EAAE,CAAC,CAAC;;;;;;KAEvE;IAEK,sCAAY,GAAlB,UAAmB,OAAoB;uCAAG,OAAO;;;;4BAC5B,qBAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAA;;wBAApD,UAAU,GAAG,SAAuC;;;;wBAIzC,qBAAM,eAAM,CAAC,aAAa,CAAC,UAAU,CAAC;gCACnD,KAAK,EAAE;oCACL,eAAe,EAAE;wCACf,UAAU,YAAA;wCACV,IAAI,EAAE,MAAM;qCACb;iCACF;6BACF,CAAC,EAAA;;wBAPI,MAAM,GAAG,SAOb;wBAEF,IAAI,MAAM,IAAI,MAAM,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;4BAC5C,sBAAO,MAAM,CAAC,KAAK,EAAC;wBACtB,CAAC;;;;wBAED,OAAO,CAAC,IAAI,CAAC,wDAAwD,EAAE,OAAK,CAAC,CAAC;wBACxE,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,eAAQ,UAAU,CAAE,CAAC,CAAC;wBAC/D,IAAI,QAAQ,IAAI,QAAQ,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,EAAE,CAAC;4BAChD,sBAAO,QAAQ,CAAC,KAAK,EAAC;wBACxB,CAAC;;4BAGH,sBAAO,IAAI,EAAC;;;;KACb;IAEK,2CAAiB,GAAvB,UAAwB,OAAoB,EAAE,KAAa;uCAAG,OAAO;;;;4BAChD,qBAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAA;;wBAApD,UAAU,GAAG,SAAuC;;;;wBAIzC,qBAAM,eAAM,CAAC,aAAa,CAAC,UAAU,CAAC;gCACnD,KAAK,EAAE;oCACL,eAAe,EAAE;wCACf,UAAU,YAAA;wCACV,IAAI,EAAE,MAAM;qCACb;iCACF;6BACF,CAAC,EAAA;;wBAPI,MAAM,GAAG,SAOb;wBAEF,IAAI,MAAM,IAAI,MAAM,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,IAAI,MAAM,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC;4BACtE,sBAAO,IAAI,EAAC;wBACd,CAAC;;;;wBAED,OAAO,CAAC,IAAI,CAAC,yDAAyD,EAAE,OAAK,CAAC,CAAC;wBACzE,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,eAAQ,UAAU,CAAE,CAAC,CAAC;wBAC/D,IAAI,QAAQ,IAAI,QAAQ,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,IAAI,QAAQ,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC;4BAC5E,sBAAO,IAAI,EAAC;wBACd,CAAC;;4BAGH,sBAAO,KAAK,EAAC;;;;KACd;IAED,gBAAgB;IACV,wCAAc,GAApB,UAAqB,OAAoB,EAAE,QAAgB,EAAE,WAAmB;uCAAG,OAAO;;;;4BAKrE,qBAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,EAAA;;wBAApD,UAAU,GAAG,SAAuC;wBACpD,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;wBACjB,WAAW,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,CAAC;;;;wBAIvC,qBAAM,eAAM,CAAC,cAAc,CAAC,KAAK,CAAC;gCAC9C,KAAK,EAAE;oCACL,UAAU,YAAA;oCACV,SAAS,EAAE;wCACT,GAAG,EAAE,WAAW;qCACjB;iCACF;6BACF,CAAC,EAAA;;wBAPI,KAAK,GAAG,SAOZ;6BAEE,CAAA,KAAK,IAAI,WAAW,CAAA,EAApB,wBAAoB;wBACF,qBAAM,eAAM,CAAC,cAAc,CAAC,SAAS,CAAC;gCACxD,KAAK,EAAE,EAAE,UAAU,YAAA,EAAE;gCACrB,OAAO,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE;6BAC9B,CAAC,EAAA;;wBAHI,WAAW,GAAG,SAGlB;wBAEF,sBAAO;gCACL,OAAO,EAAE,KAAK;gCACd,SAAS,EAAE,CAAC;gCACZ,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,QAAQ;6BAC/F,EAAC;;oBAGJ,sBAAsB;oBACtB,qBAAM,eAAM,CAAC,cAAc,CAAC,MAAM,CAAC;4BACjC,IAAI,EAAE;gCACJ,UAAU,YAAA;gCACV,QAAQ,EAAE;oCACR,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;oCAC5C,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;oCAC9E,IAAI,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ;iCAC/B;6BACF;yBACF,CAAC,EAAA;;wBAVF,sBAAsB;wBACtB,SASE,CAAC;wBAEH,sBAAO;gCACL,OAAO,EAAE,IAAI;gCACb,SAAS,EAAE,WAAW,GAAG,KAAK,GAAG,CAAC;gCAClC,SAAS,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,QAAQ;6BACpC,EAAC;;;wBAGF,OAAO,CAAC,IAAI,CAAC,uDAAuD,EAAE,OAAK,CAAC,CAAC;wBAGvE,GAAG,GAAG,eAAQ,UAAU,CAAE,CAAC;wBAC3B,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;wBAC7C,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,UAAC,KAAU,IAAK,OAAA,KAAK,CAAC,SAAS,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,QAAQ,EAA1C,CAA0C,CAAC,CAAC;wBAEhG,IAAI,YAAY,CAAC,MAAM,IAAI,WAAW,EAAE,CAAC;4BACvC,sBAAO;oCACL,OAAO,EAAE,KAAK;oCACd,SAAS,EAAE,CAAC;oCACZ,SAAS,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,QAAQ;iCAChD,EAAC;wBACJ,CAAC;wBAED,YAAY,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,GAAG,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;wBAChD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;wBAE3C,sBAAO;gCACL,OAAO,EAAE,IAAI;gCACb,SAAS,EAAE,WAAW,GAAG,YAAY,CAAC,MAAM;gCAC5C,SAAS,EAAE,GAAG,CAAC,OAAO,EAAE,GAAG,QAAQ;6BACpC,EAAC;;;;;KAEL;IAED,qDAAqD;IAC/C,iCAAO,GAAb;uCAAiB,OAAO;;;;;;;wBAEd,QAAM,IAAI,IAAI,EAAE,CAAC;wBAEvB,oDAAoD;wBACpD,qBAAM,IAAA,0BAAiB,EAAC;;;gDACtB,qBAAM,eAAM,CAAC,aAAa,CAAC,UAAU,CAAC;gDACpC,KAAK,EAAE;oDACL,SAAS,EAAE;wDACT,EAAE,EAAE,KAAG;qDACR;iDACF;6CACF,CAAC,EAAA;;4CANF,SAME,CAAC;;;;iCACJ,EAAE,CAAC,EAAE,IAAI,CAAC,EAAA;;wBATX,oDAAoD;wBACpD,SAQW,CAAC,CAAC,qCAAqC;wBAG5C,cAAY,IAAI,IAAI,CAAC,KAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;wBAChE,qBAAM,IAAA,0BAAiB,EAAC;;;gDACtB,qBAAM,eAAM,CAAC,cAAc,CAAC,UAAU,CAAC;gDACrC,KAAK,EAAE;oDACL,SAAS,EAAE;wDACT,EAAE,EAAE,WAAS;qDACd;iDACF;6CACF,CAAC,EAAA;;4CANF,SAME,CAAC;;;;iCACJ,EAAE,CAAC,EAAE,IAAI,CAAC,EAAA;;wBARX,SAQW,CAAC,CAAC,qCAAqC;;;;wBAGlD,OAAO,CAAC,IAAI,CAAC,wCAAwC,EAAE,OAAK,CAAC,CAAC;;;wBAK1D,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBACvB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,UAAC,EAAY;gCAAX,GAAG,QAAA,EAAE,KAAK,QAAA;4BAC5D,IAAI,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,SAAS,GAAG,GAAG,EAAE,CAAC;gCACrD,KAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;4BAClC,CAAC;iCAAM,IAAI,GAAG,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;gCACnC,IAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,UAAC,KAAU,IAAK,OAAA,KAAK,CAAC,SAAS,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAA3C,CAA2C,CAAC,CAAC;gCAC/F,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oCAC9B,KAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gCAClC,CAAC;qCAAM,CAAC;oCACN,KAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC;gCAC7C,CAAC;4BACH,CAAC;wBACH,CAAC,CAAC,CAAC;;;;;KACJ;IACH,sBAAC;AAAD,CAAC,AArQD,IAqQC;AArQY,0CAAe;AAuQ5B,8BAA8B;AAC9B,IAAM,eAAe,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC;AACtD,WAAW,CAAC;IACV,eAAe,CAAC,OAAO,EAAE,CAAC;AAC5B,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,4BAA4B;AAEhD,kBAAe,eAAe,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/security-storage.ts"],
      sourcesContent: ["import { prisma, withDatabaseRetry } from './prisma';\nimport { NextRequest } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from './auth';\nimport crypto from 'crypto';\n\n// Database-backed security storage for production use\nexport class SecurityStorage {\n  private static instance: SecurityStorage;\n  private memoryFallback = new Map<string, any>();\n\n  static getInstance(): SecurityStorage {\n    if (!SecurityStorage.instance) {\n      SecurityStorage.instance = new SecurityStorage();\n    }\n    return SecurityStorage.instance;\n  }\n\n  private async getClientIdentifier(request: NextRequest): Promise<string> {\n    const session = await getServerSession(authOptions);\n    if (session?.user?.id) {\n      return `user_${session.user.id}`;\n    }\n    \n    // For anonymous users, use IP + User-Agent hash\n    const ip = request.headers.get('x-forwarded-for') || \n               request.headers.get('x-real-ip') || \n               'unknown';\n    const userAgent = request.headers.get('user-agent') || 'unknown';\n    const hash = await this.hashString(`${ip}_${userAgent}`);\n    return `anon_${hash.substring(0, 16)}`;\n  }\n\n  private async hashString(input: string): Promise<string> {\n    // Use Node.js crypto for server-side hashing (more reliable in test environment)\n    if (typeof window === 'undefined') {\n      // Server-side: use Node.js crypto\n      const hash = crypto.createHash('sha256');\n      hash.update(input);\n      return hash.digest('hex');\n    } else {\n      // Client-side: use Web Crypto API\n      const encoder = new TextEncoder();\n      const data = encoder.encode(input);\n      const hashBuffer = await crypto.subtle.digest('SHA-256', data);\n      const hashArray = Array.from(new Uint8Array(hashBuffer));\n      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');\n    }\n  }\n\n  // CSRF Token Management\n  async storeCSRFToken(request: NextRequest, token: string, expiresAt: Date): Promise<void> {\n    const identifier = await this.getClientIdentifier(request);\n    \n    try {\n      // Try database first\n      await prisma.securityToken.upsert({\n        where: {\n          identifier_type: {\n            identifier,\n            type: 'CSRF'\n          }\n        },\n        update: {\n          token,\n          expiresAt,\n          updatedAt: new Date()\n        },\n        create: {\n          identifier,\n          type: 'CSRF',\n          token,\n          expiresAt,\n          metadata: {\n            userAgent: request.headers.get('user-agent'),\n            ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')\n          }\n        }\n      });\n    } catch (error) {\n      console.warn('Database CSRF storage failed, using memory fallback:', error);\n      this.memoryFallback.set(`csrf_${identifier}`, { token, expiresAt });\n    }\n  }\n\n  async getCSRFToken(request: NextRequest): Promise<string | null> {\n    const identifier = await this.getClientIdentifier(request);\n    \n    try {\n      // Try database first\n      const stored = await prisma.securityToken.findUnique({\n        where: {\n          identifier_type: {\n            identifier,\n            type: 'CSRF'\n          }\n        }\n      });\n\n      if (stored && stored.expiresAt > new Date()) {\n        return stored.token;\n      }\n    } catch (error) {\n      console.warn('Database CSRF retrieval failed, using memory fallback:', error);\n      const fallback = this.memoryFallback.get(`csrf_${identifier}`);\n      if (fallback && fallback.expiresAt > new Date()) {\n        return fallback.token;\n      }\n    }\n\n    return null;\n  }\n\n  async validateCSRFToken(request: NextRequest, token: string): Promise<boolean> {\n    const identifier = await this.getClientIdentifier(request);\n    \n    try {\n      // Try database first\n      const stored = await prisma.securityToken.findUnique({\n        where: {\n          identifier_type: {\n            identifier,\n            type: 'CSRF'\n          }\n        }\n      });\n\n      if (stored && stored.expiresAt > new Date() && stored.token === token) {\n        return true;\n      }\n    } catch (error) {\n      console.warn('Database CSRF validation failed, using memory fallback:', error);\n      const fallback = this.memoryFallback.get(`csrf_${identifier}`);\n      if (fallback && fallback.expiresAt > new Date() && fallback.token === token) {\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  // Rate Limiting\n  async checkRateLimit(request: NextRequest, windowMs: number, maxRequests: number): Promise<{\n    allowed: boolean;\n    remaining: number;\n    resetTime: number;\n  }> {\n    const identifier = await this.getClientIdentifier(request);\n    const now = new Date();\n    const windowStart = new Date(now.getTime() - windowMs);\n\n    try {\n      // Try database first\n      const count = await prisma.rateLimitEntry.count({\n        where: {\n          identifier,\n          createdAt: {\n            gte: windowStart\n          }\n        }\n      });\n\n      if (count >= maxRequests) {\n        const oldestEntry = await prisma.rateLimitEntry.findFirst({\n          where: { identifier },\n          orderBy: { createdAt: 'asc' }\n        });\n\n        return {\n          allowed: false,\n          remaining: 0,\n          resetTime: oldestEntry ? oldestEntry.createdAt.getTime() + windowMs : now.getTime() + windowMs\n        };\n      }\n\n      // Record this request\n      await prisma.rateLimitEntry.create({\n        data: {\n          identifier,\n          metadata: {\n            userAgent: request.headers.get('user-agent'),\n            ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),\n            path: request.nextUrl.pathname\n          }\n        }\n      });\n\n      return {\n        allowed: true,\n        remaining: maxRequests - count - 1,\n        resetTime: now.getTime() + windowMs\n      };\n\n    } catch (error) {\n      console.warn('Database rate limiting failed, using memory fallback:', error);\n      \n      // Memory fallback\n      const key = `rate_${identifier}`;\n      const entries = this.memoryFallback.get(key) || [];\n      const validEntries = entries.filter((entry: any) => entry.timestamp > now.getTime() - windowMs);\n      \n      if (validEntries.length >= maxRequests) {\n        return {\n          allowed: false,\n          remaining: 0,\n          resetTime: validEntries[0].timestamp + windowMs\n        };\n      }\n\n      validEntries.push({ timestamp: now.getTime() });\n      this.memoryFallback.set(key, validEntries);\n\n      return {\n        allowed: true,\n        remaining: maxRequests - validEntries.length,\n        resetTime: now.getTime() + windowMs\n      };\n    }\n  }\n\n  // Cleanup expired entries with robust error handling\n  async cleanup(): Promise<void> {\n    try {\n      const now = new Date();\n\n      // Clean up expired security tokens with retry logic\n      await withDatabaseRetry(async () => {\n        await prisma.securityToken.deleteMany({\n          where: {\n            expiresAt: {\n              lt: now\n            }\n          }\n        });\n      }, 2, 1000); // 2 retries with 1 second base delay\n\n      // Clean up old rate limit entries (keep last 24 hours)\n      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);\n      await withDatabaseRetry(async () => {\n        await prisma.rateLimitEntry.deleteMany({\n          where: {\n            createdAt: {\n              lt: oneDayAgo\n            }\n          }\n        });\n      }, 2, 1000); // 2 retries with 1 second base delay\n\n    } catch (error) {\n      console.warn('Database cleanup failed after retries:', error);\n      // Continue with memory cleanup even if database cleanup fails\n    }\n\n    // Clean up memory fallback\n    const now = Date.now();\n    Array.from(this.memoryFallback.entries()).forEach(([key, value]) => {\n      if (key.startsWith('csrf_') && value.expiresAt < now) {\n        this.memoryFallback.delete(key);\n      } else if (key.startsWith('rate_')) {\n        const validEntries = value.filter((entry: any) => entry.timestamp > now - 24 * 60 * 60 * 1000);\n        if (validEntries.length === 0) {\n          this.memoryFallback.delete(key);\n        } else {\n          this.memoryFallback.set(key, validEntries);\n        }\n      }\n    });\n  }\n}\n\n// Initialize cleanup interval\nconst securityStorage = SecurityStorage.getInstance();\nsetInterval(() => {\n  securityStorage.cleanup();\n}, 15 * 60 * 1000); // Clean up every 15 minutes\n\nexport default securityStorage;\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "01be7beae03a78dd7116d02d56aa5845ddcd0dc2"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_goe0e98w7 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_goe0e98w7();
var __awaiter =
/* istanbul ignore next */
(cov_goe0e98w7().s[0]++,
/* istanbul ignore next */
(cov_goe0e98w7().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_goe0e98w7().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_goe0e98w7().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_goe0e98w7().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_goe0e98w7().f[1]++;
    cov_goe0e98w7().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_goe0e98w7().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_goe0e98w7().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_goe0e98w7().f[2]++;
      cov_goe0e98w7().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_goe0e98w7().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_goe0e98w7().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_goe0e98w7().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_goe0e98w7().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_goe0e98w7().f[4]++;
      cov_goe0e98w7().s[4]++;
      try {
        /* istanbul ignore next */
        cov_goe0e98w7().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_goe0e98w7().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_goe0e98w7().f[5]++;
      cov_goe0e98w7().s[7]++;
      try {
        /* istanbul ignore next */
        cov_goe0e98w7().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_goe0e98w7().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_goe0e98w7().f[6]++;
      cov_goe0e98w7().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_goe0e98w7().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_goe0e98w7().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_goe0e98w7().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_goe0e98w7().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_goe0e98w7().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_goe0e98w7().s[12]++,
/* istanbul ignore next */
(cov_goe0e98w7().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_goe0e98w7().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_goe0e98w7().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_goe0e98w7().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_goe0e98w7().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_goe0e98w7().f[8]++;
        cov_goe0e98w7().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_goe0e98w7().b[6][0]++;
          cov_goe0e98w7().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_goe0e98w7().b[6][1]++;
        }
        cov_goe0e98w7().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_goe0e98w7().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_goe0e98w7().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_goe0e98w7().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_goe0e98w7().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_goe0e98w7().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_goe0e98w7().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_goe0e98w7().f[9]++;
    cov_goe0e98w7().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_goe0e98w7().f[10]++;
    cov_goe0e98w7().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_goe0e98w7().f[11]++;
      cov_goe0e98w7().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_goe0e98w7().f[12]++;
    cov_goe0e98w7().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_goe0e98w7().b[9][0]++;
      cov_goe0e98w7().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_goe0e98w7().b[9][1]++;
    }
    cov_goe0e98w7().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_goe0e98w7().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_goe0e98w7().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_goe0e98w7().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_goe0e98w7().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_goe0e98w7().s[25]++;
      try {
        /* istanbul ignore next */
        cov_goe0e98w7().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_goe0e98w7().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_goe0e98w7().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_goe0e98w7().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_goe0e98w7().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_goe0e98w7().b[15][0]++,
        /* istanbul ignore next */
        (cov_goe0e98w7().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_goe0e98w7().b[16][1]++,
        /* istanbul ignore next */
        (cov_goe0e98w7().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_goe0e98w7().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_goe0e98w7().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_goe0e98w7().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_goe0e98w7().b[12][0]++;
          cov_goe0e98w7().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_goe0e98w7().b[12][1]++;
        }
        cov_goe0e98w7().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_goe0e98w7().b[18][0]++;
          cov_goe0e98w7().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_goe0e98w7().b[18][1]++;
        }
        cov_goe0e98w7().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_goe0e98w7().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_goe0e98w7().b[19][1]++;
            cov_goe0e98w7().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_goe0e98w7().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_goe0e98w7().b[19][2]++;
            cov_goe0e98w7().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_goe0e98w7().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_goe0e98w7().b[19][3]++;
            cov_goe0e98w7().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_goe0e98w7().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_goe0e98w7().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_goe0e98w7().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_goe0e98w7().b[19][4]++;
            cov_goe0e98w7().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_goe0e98w7().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_goe0e98w7().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_goe0e98w7().b[19][5]++;
            cov_goe0e98w7().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_goe0e98w7().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_goe0e98w7().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_goe0e98w7().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_goe0e98w7().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_goe0e98w7().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_goe0e98w7().b[20][0]++;
              cov_goe0e98w7().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_goe0e98w7().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_goe0e98w7().b[20][1]++;
            }
            cov_goe0e98w7().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_goe0e98w7().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_goe0e98w7().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_goe0e98w7().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_goe0e98w7().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_goe0e98w7().b[23][0]++;
              cov_goe0e98w7().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_goe0e98w7().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_goe0e98w7().b[23][1]++;
            }
            cov_goe0e98w7().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_goe0e98w7().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_goe0e98w7().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_goe0e98w7().b[25][0]++;
              cov_goe0e98w7().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_goe0e98w7().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_goe0e98w7().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_goe0e98w7().b[25][1]++;
            }
            cov_goe0e98w7().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_goe0e98w7().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_goe0e98w7().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_goe0e98w7().b[27][0]++;
              cov_goe0e98w7().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_goe0e98w7().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_goe0e98w7().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_goe0e98w7().b[27][1]++;
            }
            cov_goe0e98w7().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_goe0e98w7().b[29][0]++;
              cov_goe0e98w7().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_goe0e98w7().b[29][1]++;
            }
            cov_goe0e98w7().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_goe0e98w7().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_goe0e98w7().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_goe0e98w7().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_goe0e98w7().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_goe0e98w7().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_goe0e98w7().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_goe0e98w7().b[30][0]++;
      cov_goe0e98w7().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_goe0e98w7().b[30][1]++;
    }
    cov_goe0e98w7().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_goe0e98w7().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_goe0e98w7().b[31][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_goe0e98w7().s[67]++,
/* istanbul ignore next */
(cov_goe0e98w7().b[32][0]++, this) &&
/* istanbul ignore next */
(cov_goe0e98w7().b[32][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_goe0e98w7().b[32][2]++, function (mod) {
  /* istanbul ignore next */
  cov_goe0e98w7().f[13]++;
  cov_goe0e98w7().s[68]++;
  return /* istanbul ignore next */(cov_goe0e98w7().b[34][0]++, mod) &&
  /* istanbul ignore next */
  (cov_goe0e98w7().b[34][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_goe0e98w7().b[33][0]++, mod) :
  /* istanbul ignore next */
  (cov_goe0e98w7().b[33][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_goe0e98w7().s[69]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_goe0e98w7().s[70]++;
exports.SecurityStorage = void 0;
var prisma_1 =
/* istanbul ignore next */
(cov_goe0e98w7().s[71]++, require("./prisma"));
var next_1 =
/* istanbul ignore next */
(cov_goe0e98w7().s[72]++, require("next-auth/next"));
var auth_1 =
/* istanbul ignore next */
(cov_goe0e98w7().s[73]++, require("./auth"));
var crypto_1 =
/* istanbul ignore next */
(cov_goe0e98w7().s[74]++, __importDefault(require("crypto")));
// Database-backed security storage for production use
var SecurityStorage =
/* istanbul ignore next */
(/** @class */cov_goe0e98w7().s[75]++, function () {
  /* istanbul ignore next */
  cov_goe0e98w7().f[14]++;
  function SecurityStorage() {
    /* istanbul ignore next */
    cov_goe0e98w7().f[15]++;
    cov_goe0e98w7().s[76]++;
    this.memoryFallback = new Map();
  }
  /* istanbul ignore next */
  cov_goe0e98w7().s[77]++;
  SecurityStorage.getInstance = function () {
    /* istanbul ignore next */
    cov_goe0e98w7().f[16]++;
    cov_goe0e98w7().s[78]++;
    if (!SecurityStorage.instance) {
      /* istanbul ignore next */
      cov_goe0e98w7().b[35][0]++;
      cov_goe0e98w7().s[79]++;
      SecurityStorage.instance = new SecurityStorage();
    } else
    /* istanbul ignore next */
    {
      cov_goe0e98w7().b[35][1]++;
    }
    cov_goe0e98w7().s[80]++;
    return SecurityStorage.instance;
  };
  /* istanbul ignore next */
  cov_goe0e98w7().s[81]++;
  SecurityStorage.prototype.getClientIdentifier = function (request) {
    /* istanbul ignore next */
    cov_goe0e98w7().f[17]++;
    cov_goe0e98w7().s[82]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_goe0e98w7().f[18]++;
      var session, ip, userAgent, hash;
      var _a;
      /* istanbul ignore next */
      cov_goe0e98w7().s[83]++;
      return __generator(this, function (_b) {
        /* istanbul ignore next */
        cov_goe0e98w7().f[19]++;
        cov_goe0e98w7().s[84]++;
        switch (_b.label) {
          case 0:
            /* istanbul ignore next */
            cov_goe0e98w7().b[36][0]++;
            cov_goe0e98w7().s[85]++;
            return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
          case 1:
            /* istanbul ignore next */
            cov_goe0e98w7().b[36][1]++;
            cov_goe0e98w7().s[86]++;
            session = _b.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[87]++;
            if (
            /* istanbul ignore next */
            (cov_goe0e98w7().b[39][0]++, (_a =
            /* istanbul ignore next */
            (cov_goe0e98w7().b[41][0]++, session === null) ||
            /* istanbul ignore next */
            (cov_goe0e98w7().b[41][1]++, session === void 0) ?
            /* istanbul ignore next */
            (cov_goe0e98w7().b[40][0]++, void 0) :
            /* istanbul ignore next */
            (cov_goe0e98w7().b[40][1]++, session.user)) === null) ||
            /* istanbul ignore next */
            (cov_goe0e98w7().b[39][1]++, _a === void 0) ?
            /* istanbul ignore next */
            (cov_goe0e98w7().b[38][0]++, void 0) :
            /* istanbul ignore next */
            (cov_goe0e98w7().b[38][1]++, _a.id)) {
              /* istanbul ignore next */
              cov_goe0e98w7().b[37][0]++;
              cov_goe0e98w7().s[88]++;
              return [2 /*return*/, "user_".concat(session.user.id)];
            } else
            /* istanbul ignore next */
            {
              cov_goe0e98w7().b[37][1]++;
            }
            cov_goe0e98w7().s[89]++;
            ip =
            /* istanbul ignore next */
            (cov_goe0e98w7().b[42][0]++, request.headers.get('x-forwarded-for')) ||
            /* istanbul ignore next */
            (cov_goe0e98w7().b[42][1]++, request.headers.get('x-real-ip')) ||
            /* istanbul ignore next */
            (cov_goe0e98w7().b[42][2]++, 'unknown');
            /* istanbul ignore next */
            cov_goe0e98w7().s[90]++;
            userAgent =
            /* istanbul ignore next */
            (cov_goe0e98w7().b[43][0]++, request.headers.get('user-agent')) ||
            /* istanbul ignore next */
            (cov_goe0e98w7().b[43][1]++, 'unknown');
            /* istanbul ignore next */
            cov_goe0e98w7().s[91]++;
            return [4 /*yield*/, this.hashString("".concat(ip, "_").concat(userAgent))];
          case 2:
            /* istanbul ignore next */
            cov_goe0e98w7().b[36][2]++;
            cov_goe0e98w7().s[92]++;
            hash = _b.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[93]++;
            return [2 /*return*/, "anon_".concat(hash.substring(0, 16))];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_goe0e98w7().s[94]++;
  SecurityStorage.prototype.hashString = function (input) {
    /* istanbul ignore next */
    cov_goe0e98w7().f[20]++;
    cov_goe0e98w7().s[95]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_goe0e98w7().f[21]++;
      var hash, encoder, data, hashBuffer, hashArray;
      /* istanbul ignore next */
      cov_goe0e98w7().s[96]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_goe0e98w7().f[22]++;
        cov_goe0e98w7().s[97]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_goe0e98w7().b[44][0]++;
            cov_goe0e98w7().s[98]++;
            if (!(typeof window === 'undefined')) {
              /* istanbul ignore next */
              cov_goe0e98w7().b[45][0]++;
              cov_goe0e98w7().s[99]++;
              return [3 /*break*/, 1];
            } else
            /* istanbul ignore next */
            {
              cov_goe0e98w7().b[45][1]++;
            }
            cov_goe0e98w7().s[100]++;
            hash = crypto_1.default.createHash('sha256');
            /* istanbul ignore next */
            cov_goe0e98w7().s[101]++;
            hash.update(input);
            /* istanbul ignore next */
            cov_goe0e98w7().s[102]++;
            return [2 /*return*/, hash.digest('hex')];
          case 1:
            /* istanbul ignore next */
            cov_goe0e98w7().b[44][1]++;
            cov_goe0e98w7().s[103]++;
            encoder = new TextEncoder();
            /* istanbul ignore next */
            cov_goe0e98w7().s[104]++;
            data = encoder.encode(input);
            /* istanbul ignore next */
            cov_goe0e98w7().s[105]++;
            return [4 /*yield*/, crypto_1.default.subtle.digest('SHA-256', data)];
          case 2:
            /* istanbul ignore next */
            cov_goe0e98w7().b[44][2]++;
            cov_goe0e98w7().s[106]++;
            hashBuffer = _a.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[107]++;
            hashArray = Array.from(new Uint8Array(hashBuffer));
            /* istanbul ignore next */
            cov_goe0e98w7().s[108]++;
            return [2 /*return*/, hashArray.map(function (b) {
              /* istanbul ignore next */
              cov_goe0e98w7().f[23]++;
              cov_goe0e98w7().s[109]++;
              return b.toString(16).padStart(2, '0');
            }).join('')];
        }
      });
    });
  };
  // CSRF Token Management
  /* istanbul ignore next */
  cov_goe0e98w7().s[110]++;
  SecurityStorage.prototype.storeCSRFToken = function (request, token, expiresAt) {
    /* istanbul ignore next */
    cov_goe0e98w7().f[24]++;
    cov_goe0e98w7().s[111]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_goe0e98w7().f[25]++;
      var identifier, error_1;
      /* istanbul ignore next */
      cov_goe0e98w7().s[112]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_goe0e98w7().f[26]++;
        cov_goe0e98w7().s[113]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_goe0e98w7().b[46][0]++;
            cov_goe0e98w7().s[114]++;
            return [4 /*yield*/, this.getClientIdentifier(request)];
          case 1:
            /* istanbul ignore next */
            cov_goe0e98w7().b[46][1]++;
            cov_goe0e98w7().s[115]++;
            identifier = _a.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[116]++;
            _a.label = 2;
          case 2:
            /* istanbul ignore next */
            cov_goe0e98w7().b[46][2]++;
            cov_goe0e98w7().s[117]++;
            _a.trys.push([2, 4,, 5]);
            // Try database first
            /* istanbul ignore next */
            cov_goe0e98w7().s[118]++;
            return [4 /*yield*/, prisma_1.prisma.securityToken.upsert({
              where: {
                identifier_type: {
                  identifier: identifier,
                  type: 'CSRF'
                }
              },
              update: {
                token: token,
                expiresAt: expiresAt,
                updatedAt: new Date()
              },
              create: {
                identifier: identifier,
                type: 'CSRF',
                token: token,
                expiresAt: expiresAt,
                metadata: {
                  userAgent: request.headers.get('user-agent'),
                  ip:
                  /* istanbul ignore next */
                  (cov_goe0e98w7().b[47][0]++, request.headers.get('x-forwarded-for')) ||
                  /* istanbul ignore next */
                  (cov_goe0e98w7().b[47][1]++, request.headers.get('x-real-ip'))
                }
              }
            })];
          case 3:
            /* istanbul ignore next */
            cov_goe0e98w7().b[46][3]++;
            cov_goe0e98w7().s[119]++;
            // Try database first
            _a.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[120]++;
            return [3 /*break*/, 5];
          case 4:
            /* istanbul ignore next */
            cov_goe0e98w7().b[46][4]++;
            cov_goe0e98w7().s[121]++;
            error_1 = _a.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[122]++;
            console.warn('Database CSRF storage failed, using memory fallback:', error_1);
            /* istanbul ignore next */
            cov_goe0e98w7().s[123]++;
            this.memoryFallback.set("csrf_".concat(identifier), {
              token: token,
              expiresAt: expiresAt
            });
            /* istanbul ignore next */
            cov_goe0e98w7().s[124]++;
            return [3 /*break*/, 5];
          case 5:
            /* istanbul ignore next */
            cov_goe0e98w7().b[46][5]++;
            cov_goe0e98w7().s[125]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_goe0e98w7().s[126]++;
  SecurityStorage.prototype.getCSRFToken = function (request) {
    /* istanbul ignore next */
    cov_goe0e98w7().f[27]++;
    cov_goe0e98w7().s[127]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_goe0e98w7().f[28]++;
      var identifier, stored, error_2, fallback;
      /* istanbul ignore next */
      cov_goe0e98w7().s[128]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_goe0e98w7().f[29]++;
        cov_goe0e98w7().s[129]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_goe0e98w7().b[48][0]++;
            cov_goe0e98w7().s[130]++;
            return [4 /*yield*/, this.getClientIdentifier(request)];
          case 1:
            /* istanbul ignore next */
            cov_goe0e98w7().b[48][1]++;
            cov_goe0e98w7().s[131]++;
            identifier = _a.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[132]++;
            _a.label = 2;
          case 2:
            /* istanbul ignore next */
            cov_goe0e98w7().b[48][2]++;
            cov_goe0e98w7().s[133]++;
            _a.trys.push([2, 4,, 5]);
            /* istanbul ignore next */
            cov_goe0e98w7().s[134]++;
            return [4 /*yield*/, prisma_1.prisma.securityToken.findUnique({
              where: {
                identifier_type: {
                  identifier: identifier,
                  type: 'CSRF'
                }
              }
            })];
          case 3:
            /* istanbul ignore next */
            cov_goe0e98w7().b[48][3]++;
            cov_goe0e98w7().s[135]++;
            stored = _a.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[136]++;
            if (
            /* istanbul ignore next */
            (cov_goe0e98w7().b[50][0]++, stored) &&
            /* istanbul ignore next */
            (cov_goe0e98w7().b[50][1]++, stored.expiresAt > new Date())) {
              /* istanbul ignore next */
              cov_goe0e98w7().b[49][0]++;
              cov_goe0e98w7().s[137]++;
              return [2 /*return*/, stored.token];
            } else
            /* istanbul ignore next */
            {
              cov_goe0e98w7().b[49][1]++;
            }
            cov_goe0e98w7().s[138]++;
            return [3 /*break*/, 5];
          case 4:
            /* istanbul ignore next */
            cov_goe0e98w7().b[48][4]++;
            cov_goe0e98w7().s[139]++;
            error_2 = _a.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[140]++;
            console.warn('Database CSRF retrieval failed, using memory fallback:', error_2);
            /* istanbul ignore next */
            cov_goe0e98w7().s[141]++;
            fallback = this.memoryFallback.get("csrf_".concat(identifier));
            /* istanbul ignore next */
            cov_goe0e98w7().s[142]++;
            if (
            /* istanbul ignore next */
            (cov_goe0e98w7().b[52][0]++, fallback) &&
            /* istanbul ignore next */
            (cov_goe0e98w7().b[52][1]++, fallback.expiresAt > new Date())) {
              /* istanbul ignore next */
              cov_goe0e98w7().b[51][0]++;
              cov_goe0e98w7().s[143]++;
              return [2 /*return*/, fallback.token];
            } else
            /* istanbul ignore next */
            {
              cov_goe0e98w7().b[51][1]++;
            }
            cov_goe0e98w7().s[144]++;
            return [3 /*break*/, 5];
          case 5:
            /* istanbul ignore next */
            cov_goe0e98w7().b[48][5]++;
            cov_goe0e98w7().s[145]++;
            return [2 /*return*/, null];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_goe0e98w7().s[146]++;
  SecurityStorage.prototype.validateCSRFToken = function (request, token) {
    /* istanbul ignore next */
    cov_goe0e98w7().f[30]++;
    cov_goe0e98w7().s[147]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_goe0e98w7().f[31]++;
      var identifier, stored, error_3, fallback;
      /* istanbul ignore next */
      cov_goe0e98w7().s[148]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_goe0e98w7().f[32]++;
        cov_goe0e98w7().s[149]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_goe0e98w7().b[53][0]++;
            cov_goe0e98w7().s[150]++;
            return [4 /*yield*/, this.getClientIdentifier(request)];
          case 1:
            /* istanbul ignore next */
            cov_goe0e98w7().b[53][1]++;
            cov_goe0e98w7().s[151]++;
            identifier = _a.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[152]++;
            _a.label = 2;
          case 2:
            /* istanbul ignore next */
            cov_goe0e98w7().b[53][2]++;
            cov_goe0e98w7().s[153]++;
            _a.trys.push([2, 4,, 5]);
            /* istanbul ignore next */
            cov_goe0e98w7().s[154]++;
            return [4 /*yield*/, prisma_1.prisma.securityToken.findUnique({
              where: {
                identifier_type: {
                  identifier: identifier,
                  type: 'CSRF'
                }
              }
            })];
          case 3:
            /* istanbul ignore next */
            cov_goe0e98w7().b[53][3]++;
            cov_goe0e98w7().s[155]++;
            stored = _a.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[156]++;
            if (
            /* istanbul ignore next */
            (cov_goe0e98w7().b[55][0]++, stored) &&
            /* istanbul ignore next */
            (cov_goe0e98w7().b[55][1]++, stored.expiresAt > new Date()) &&
            /* istanbul ignore next */
            (cov_goe0e98w7().b[55][2]++, stored.token === token)) {
              /* istanbul ignore next */
              cov_goe0e98w7().b[54][0]++;
              cov_goe0e98w7().s[157]++;
              return [2 /*return*/, true];
            } else
            /* istanbul ignore next */
            {
              cov_goe0e98w7().b[54][1]++;
            }
            cov_goe0e98w7().s[158]++;
            return [3 /*break*/, 5];
          case 4:
            /* istanbul ignore next */
            cov_goe0e98w7().b[53][4]++;
            cov_goe0e98w7().s[159]++;
            error_3 = _a.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[160]++;
            console.warn('Database CSRF validation failed, using memory fallback:', error_3);
            /* istanbul ignore next */
            cov_goe0e98w7().s[161]++;
            fallback = this.memoryFallback.get("csrf_".concat(identifier));
            /* istanbul ignore next */
            cov_goe0e98w7().s[162]++;
            if (
            /* istanbul ignore next */
            (cov_goe0e98w7().b[57][0]++, fallback) &&
            /* istanbul ignore next */
            (cov_goe0e98w7().b[57][1]++, fallback.expiresAt > new Date()) &&
            /* istanbul ignore next */
            (cov_goe0e98w7().b[57][2]++, fallback.token === token)) {
              /* istanbul ignore next */
              cov_goe0e98w7().b[56][0]++;
              cov_goe0e98w7().s[163]++;
              return [2 /*return*/, true];
            } else
            /* istanbul ignore next */
            {
              cov_goe0e98w7().b[56][1]++;
            }
            cov_goe0e98w7().s[164]++;
            return [3 /*break*/, 5];
          case 5:
            /* istanbul ignore next */
            cov_goe0e98w7().b[53][5]++;
            cov_goe0e98w7().s[165]++;
            return [2 /*return*/, false];
        }
      });
    });
  };
  // Rate Limiting
  /* istanbul ignore next */
  cov_goe0e98w7().s[166]++;
  SecurityStorage.prototype.checkRateLimit = function (request, windowMs, maxRequests) {
    /* istanbul ignore next */
    cov_goe0e98w7().f[33]++;
    cov_goe0e98w7().s[167]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_goe0e98w7().f[34]++;
      var identifier, now, windowStart, count, oldestEntry, error_4, key, entries, validEntries;
      /* istanbul ignore next */
      cov_goe0e98w7().s[168]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_goe0e98w7().f[35]++;
        cov_goe0e98w7().s[169]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_goe0e98w7().b[58][0]++;
            cov_goe0e98w7().s[170]++;
            return [4 /*yield*/, this.getClientIdentifier(request)];
          case 1:
            /* istanbul ignore next */
            cov_goe0e98w7().b[58][1]++;
            cov_goe0e98w7().s[171]++;
            identifier = _a.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[172]++;
            now = new Date();
            /* istanbul ignore next */
            cov_goe0e98w7().s[173]++;
            windowStart = new Date(now.getTime() - windowMs);
            /* istanbul ignore next */
            cov_goe0e98w7().s[174]++;
            _a.label = 2;
          case 2:
            /* istanbul ignore next */
            cov_goe0e98w7().b[58][2]++;
            cov_goe0e98w7().s[175]++;
            _a.trys.push([2, 7,, 8]);
            /* istanbul ignore next */
            cov_goe0e98w7().s[176]++;
            return [4 /*yield*/, prisma_1.prisma.rateLimitEntry.count({
              where: {
                identifier: identifier,
                createdAt: {
                  gte: windowStart
                }
              }
            })];
          case 3:
            /* istanbul ignore next */
            cov_goe0e98w7().b[58][3]++;
            cov_goe0e98w7().s[177]++;
            count = _a.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[178]++;
            if (!(count >= maxRequests)) {
              /* istanbul ignore next */
              cov_goe0e98w7().b[59][0]++;
              cov_goe0e98w7().s[179]++;
              return [3 /*break*/, 5];
            } else
            /* istanbul ignore next */
            {
              cov_goe0e98w7().b[59][1]++;
            }
            cov_goe0e98w7().s[180]++;
            return [4 /*yield*/, prisma_1.prisma.rateLimitEntry.findFirst({
              where: {
                identifier: identifier
              },
              orderBy: {
                createdAt: 'asc'
              }
            })];
          case 4:
            /* istanbul ignore next */
            cov_goe0e98w7().b[58][4]++;
            cov_goe0e98w7().s[181]++;
            oldestEntry = _a.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[182]++;
            return [2 /*return*/, {
              allowed: false,
              remaining: 0,
              resetTime: oldestEntry ?
              /* istanbul ignore next */
              (cov_goe0e98w7().b[60][0]++, oldestEntry.createdAt.getTime() + windowMs) :
              /* istanbul ignore next */
              (cov_goe0e98w7().b[60][1]++, now.getTime() + windowMs)
            }];
          case 5:
            /* istanbul ignore next */
            cov_goe0e98w7().b[58][5]++;
            cov_goe0e98w7().s[183]++;
            // Record this request
            return [4 /*yield*/, prisma_1.prisma.rateLimitEntry.create({
              data: {
                identifier: identifier,
                metadata: {
                  userAgent: request.headers.get('user-agent'),
                  ip:
                  /* istanbul ignore next */
                  (cov_goe0e98w7().b[61][0]++, request.headers.get('x-forwarded-for')) ||
                  /* istanbul ignore next */
                  (cov_goe0e98w7().b[61][1]++, request.headers.get('x-real-ip')),
                  path: request.nextUrl.pathname
                }
              }
            })];
          case 6:
            /* istanbul ignore next */
            cov_goe0e98w7().b[58][6]++;
            cov_goe0e98w7().s[184]++;
            // Record this request
            _a.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[185]++;
            return [2 /*return*/, {
              allowed: true,
              remaining: maxRequests - count - 1,
              resetTime: now.getTime() + windowMs
            }];
          case 7:
            /* istanbul ignore next */
            cov_goe0e98w7().b[58][7]++;
            cov_goe0e98w7().s[186]++;
            error_4 = _a.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[187]++;
            console.warn('Database rate limiting failed, using memory fallback:', error_4);
            /* istanbul ignore next */
            cov_goe0e98w7().s[188]++;
            key = "rate_".concat(identifier);
            /* istanbul ignore next */
            cov_goe0e98w7().s[189]++;
            entries =
            /* istanbul ignore next */
            (cov_goe0e98w7().b[62][0]++, this.memoryFallback.get(key)) ||
            /* istanbul ignore next */
            (cov_goe0e98w7().b[62][1]++, []);
            /* istanbul ignore next */
            cov_goe0e98w7().s[190]++;
            validEntries = entries.filter(function (entry) {
              /* istanbul ignore next */
              cov_goe0e98w7().f[36]++;
              cov_goe0e98w7().s[191]++;
              return entry.timestamp > now.getTime() - windowMs;
            });
            /* istanbul ignore next */
            cov_goe0e98w7().s[192]++;
            if (validEntries.length >= maxRequests) {
              /* istanbul ignore next */
              cov_goe0e98w7().b[63][0]++;
              cov_goe0e98w7().s[193]++;
              return [2 /*return*/, {
                allowed: false,
                remaining: 0,
                resetTime: validEntries[0].timestamp + windowMs
              }];
            } else
            /* istanbul ignore next */
            {
              cov_goe0e98w7().b[63][1]++;
            }
            cov_goe0e98w7().s[194]++;
            validEntries.push({
              timestamp: now.getTime()
            });
            /* istanbul ignore next */
            cov_goe0e98w7().s[195]++;
            this.memoryFallback.set(key, validEntries);
            /* istanbul ignore next */
            cov_goe0e98w7().s[196]++;
            return [2 /*return*/, {
              allowed: true,
              remaining: maxRequests - validEntries.length,
              resetTime: now.getTime() + windowMs
            }];
          case 8:
            /* istanbul ignore next */
            cov_goe0e98w7().b[58][8]++;
            cov_goe0e98w7().s[197]++;
            return [2 /*return*/];
        }
      });
    });
  };
  // Cleanup expired entries with robust error handling
  /* istanbul ignore next */
  cov_goe0e98w7().s[198]++;
  SecurityStorage.prototype.cleanup = function () {
    /* istanbul ignore next */
    cov_goe0e98w7().f[37]++;
    cov_goe0e98w7().s[199]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_goe0e98w7().f[38]++;
      var now_1, oneDayAgo_1, error_5, now;
      var _this =
      /* istanbul ignore next */
      (cov_goe0e98w7().s[200]++, this);
      /* istanbul ignore next */
      cov_goe0e98w7().s[201]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_goe0e98w7().f[39]++;
        cov_goe0e98w7().s[202]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_goe0e98w7().b[64][0]++;
            cov_goe0e98w7().s[203]++;
            _a.trys.push([0, 3,, 4]);
            /* istanbul ignore next */
            cov_goe0e98w7().s[204]++;
            now_1 = new Date();
            // Clean up expired security tokens with retry logic
            /* istanbul ignore next */
            cov_goe0e98w7().s[205]++;
            return [4 /*yield*/, (0, prisma_1.withDatabaseRetry)(function () {
              /* istanbul ignore next */
              cov_goe0e98w7().f[40]++;
              cov_goe0e98w7().s[206]++;
              return __awaiter(_this, void 0, void 0, function () {
                /* istanbul ignore next */
                cov_goe0e98w7().f[41]++;
                cov_goe0e98w7().s[207]++;
                return __generator(this, function (_a) {
                  /* istanbul ignore next */
                  cov_goe0e98w7().f[42]++;
                  cov_goe0e98w7().s[208]++;
                  switch (_a.label) {
                    case 0:
                      /* istanbul ignore next */
                      cov_goe0e98w7().b[65][0]++;
                      cov_goe0e98w7().s[209]++;
                      return [4 /*yield*/, prisma_1.prisma.securityToken.deleteMany({
                        where: {
                          expiresAt: {
                            lt: now_1
                          }
                        }
                      })];
                    case 1:
                      /* istanbul ignore next */
                      cov_goe0e98w7().b[65][1]++;
                      cov_goe0e98w7().s[210]++;
                      _a.sent();
                      /* istanbul ignore next */
                      cov_goe0e98w7().s[211]++;
                      return [2 /*return*/];
                  }
                });
              });
            }, 2, 1000)];
          case 1:
            /* istanbul ignore next */
            cov_goe0e98w7().b[64][1]++;
            cov_goe0e98w7().s[212]++;
            // Clean up expired security tokens with retry logic
            _a.sent(); // 2 retries with 1 second base delay
            /* istanbul ignore next */
            cov_goe0e98w7().s[213]++;
            oneDayAgo_1 = new Date(now_1.getTime() - 24 * 60 * 60 * 1000);
            /* istanbul ignore next */
            cov_goe0e98w7().s[214]++;
            return [4 /*yield*/, (0, prisma_1.withDatabaseRetry)(function () {
              /* istanbul ignore next */
              cov_goe0e98w7().f[43]++;
              cov_goe0e98w7().s[215]++;
              return __awaiter(_this, void 0, void 0, function () {
                /* istanbul ignore next */
                cov_goe0e98w7().f[44]++;
                cov_goe0e98w7().s[216]++;
                return __generator(this, function (_a) {
                  /* istanbul ignore next */
                  cov_goe0e98w7().f[45]++;
                  cov_goe0e98w7().s[217]++;
                  switch (_a.label) {
                    case 0:
                      /* istanbul ignore next */
                      cov_goe0e98w7().b[66][0]++;
                      cov_goe0e98w7().s[218]++;
                      return [4 /*yield*/, prisma_1.prisma.rateLimitEntry.deleteMany({
                        where: {
                          createdAt: {
                            lt: oneDayAgo_1
                          }
                        }
                      })];
                    case 1:
                      /* istanbul ignore next */
                      cov_goe0e98w7().b[66][1]++;
                      cov_goe0e98w7().s[219]++;
                      _a.sent();
                      /* istanbul ignore next */
                      cov_goe0e98w7().s[220]++;
                      return [2 /*return*/];
                  }
                });
              });
            }, 2, 1000)];
          case 2:
            /* istanbul ignore next */
            cov_goe0e98w7().b[64][2]++;
            cov_goe0e98w7().s[221]++;
            _a.sent(); // 2 retries with 1 second base delay
            /* istanbul ignore next */
            cov_goe0e98w7().s[222]++;
            return [3 /*break*/, 4];
          case 3:
            /* istanbul ignore next */
            cov_goe0e98w7().b[64][3]++;
            cov_goe0e98w7().s[223]++;
            error_5 = _a.sent();
            /* istanbul ignore next */
            cov_goe0e98w7().s[224]++;
            console.warn('Database cleanup failed after retries:', error_5);
            /* istanbul ignore next */
            cov_goe0e98w7().s[225]++;
            return [3 /*break*/, 4];
          case 4:
            /* istanbul ignore next */
            cov_goe0e98w7().b[64][4]++;
            cov_goe0e98w7().s[226]++;
            now = Date.now();
            /* istanbul ignore next */
            cov_goe0e98w7().s[227]++;
            Array.from(this.memoryFallback.entries()).forEach(function (_a) {
              /* istanbul ignore next */
              cov_goe0e98w7().f[46]++;
              var key =
                /* istanbul ignore next */
                (cov_goe0e98w7().s[228]++, _a[0]),
                value =
                /* istanbul ignore next */
                (cov_goe0e98w7().s[229]++, _a[1]);
              /* istanbul ignore next */
              cov_goe0e98w7().s[230]++;
              if (
              /* istanbul ignore next */
              (cov_goe0e98w7().b[68][0]++, key.startsWith('csrf_')) &&
              /* istanbul ignore next */
              (cov_goe0e98w7().b[68][1]++, value.expiresAt < now)) {
                /* istanbul ignore next */
                cov_goe0e98w7().b[67][0]++;
                cov_goe0e98w7().s[231]++;
                _this.memoryFallback.delete(key);
              } else {
                /* istanbul ignore next */
                cov_goe0e98w7().b[67][1]++;
                cov_goe0e98w7().s[232]++;
                if (key.startsWith('rate_')) {
                  /* istanbul ignore next */
                  cov_goe0e98w7().b[69][0]++;
                  var validEntries =
                  /* istanbul ignore next */
                  (cov_goe0e98w7().s[233]++, value.filter(function (entry) {
                    /* istanbul ignore next */
                    cov_goe0e98w7().f[47]++;
                    cov_goe0e98w7().s[234]++;
                    return entry.timestamp > now - 24 * 60 * 60 * 1000;
                  }));
                  /* istanbul ignore next */
                  cov_goe0e98w7().s[235]++;
                  if (validEntries.length === 0) {
                    /* istanbul ignore next */
                    cov_goe0e98w7().b[70][0]++;
                    cov_goe0e98w7().s[236]++;
                    _this.memoryFallback.delete(key);
                  } else {
                    /* istanbul ignore next */
                    cov_goe0e98w7().b[70][1]++;
                    cov_goe0e98w7().s[237]++;
                    _this.memoryFallback.set(key, validEntries);
                  }
                } else
                /* istanbul ignore next */
                {
                  cov_goe0e98w7().b[69][1]++;
                }
              }
            });
            /* istanbul ignore next */
            cov_goe0e98w7().s[238]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_goe0e98w7().s[239]++;
  return SecurityStorage;
}());
/* istanbul ignore next */
cov_goe0e98w7().s[240]++;
exports.SecurityStorage = SecurityStorage;
// Initialize cleanup interval
var securityStorage =
/* istanbul ignore next */
(cov_goe0e98w7().s[241]++, SecurityStorage.getInstance());
/* istanbul ignore next */
cov_goe0e98w7().s[242]++;
setInterval(function () {
  /* istanbul ignore next */
  cov_goe0e98w7().f[48]++;
  cov_goe0e98w7().s[243]++;
  securityStorage.cleanup();
}, 15 * 60 * 1000); // Clean up every 15 minutes
/* istanbul ignore next */
cov_goe0e98w7().s[244]++;
exports.default = securityStorage;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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