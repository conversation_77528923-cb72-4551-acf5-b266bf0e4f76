5bfe82e8ed7dc783ad85d037c819a509
"use strict";
/**
 * Testing Infrastructure and Coverage Issues Tests
 *
 * These tests prove critical problems with the testing infrastructure,
 * coverage gaps, and testing best practices violations.
 *
 * EXPECTED TO FAIL - These tests demonstrate testing quality issues that need fixing.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var globals_1 = require("@jest/globals");
var fs_1 = __importDefault(require("fs"));
var path_1 = __importDefault(require("path"));
(0, globals_1.describe)('Testing Infrastructure and Coverage Issues', function () {
    (0, globals_1.beforeEach)(function () {
        globals_1.jest.clearAllMocks();
    });
    (0, globals_1.describe)('CRITICAL ISSUE 1: Test Coverage Gaps for Critical Components', function () {
        (0, globals_1.it)('should fail - critical authentication components lack comprehensive tests', function () {
            // Critical authentication components that must have tests
            var criticalAuthComponents = [
                'src/lib/auth.tsx',
                'src/lib/session-security.ts',
                'src/lib/unified-session-management.ts',
                'src/lib/user-validation-service.ts',
                'src/components/LoginForm.tsx',
                'src/components/SignupForm.tsx'
            ];
            var missingTests = [];
            var inadequateTests = [];
            criticalAuthComponents.forEach(function (component) {
                var testFile = component.replace(/\.(tsx?|jsx?)$/, '.test.$1');
                var testPath = path_1.default.join(process.cwd(), testFile);
                if (!fs_1.default.existsSync(testPath)) {
                    missingTests.push(component);
                }
                else {
                    try {
                        var testContent = fs_1.default.readFileSync(testPath, 'utf8');
                        var testCount = (testContent.match(/it\(/g) || []).length;
                        var expectCount = (testContent.match(/expect\(/g) || []).length;
                        // Critical components should have at least 10 tests and 20 assertions
                        if (testCount < 10 || expectCount < 20) {
                            inadequateTests.push({ component: component, testCount: testCount, expectCount: expectCount });
                        }
                    }
                    catch (error) {
                        missingTests.push(component);
                    }
                }
            });
            // EXPECTED TO FAIL: All critical auth components should have comprehensive tests
            (0, globals_1.expect)(missingTests.length).toBe(0);
            (0, globals_1.expect)(inadequateTests.length).toBe(0);
        });
        (0, globals_1.it)('should fail - API routes lack proper integration tests', function () {
            // Critical API routes that must have integration tests
            var criticalAPIRoutes = [
                'src/app/api/auth/[...nextauth]/route.ts',
                'src/app/api/assessment/route.ts',
                'src/app/api/profile/route.ts',
                'src/app/api/interview/route.ts',
                'src/app/api/resources/route.ts'
            ];
            var missingIntegrationTests = [];
            criticalAPIRoutes.forEach(function (route) {
                var integrationTestFile = route.replace('src/app/api/', '__tests__/api/').replace('.ts', '.integration.test.ts');
                var integrationTestPath = path_1.default.join(process.cwd(), integrationTestFile);
                if (!fs_1.default.existsSync(integrationTestPath)) {
                    missingIntegrationTests.push(route);
                }
                else {
                    try {
                        var testContent = fs_1.default.readFileSync(integrationTestPath, 'utf8');
                        // Integration tests should test real HTTP requests, not just mocked functions
                        var hasRealHTTPTests = testContent.includes('fetch(') || testContent.includes('request(');
                        var hasMockOveruse = (testContent.match(/\.mock/g) || []).length > 5;
                        if (!hasRealHTTPTests || hasMockOveruse) {
                            missingIntegrationTests.push(route);
                        }
                    }
                    catch (error) {
                        missingIntegrationTests.push(route);
                    }
                }
            });
            // EXPECTED TO FAIL: All critical API routes should have proper integration tests
            (0, globals_1.expect)(missingIntegrationTests.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 2: Test Quality and Reliability Problems', function () {
        (0, globals_1.it)('should fail - tests over-rely on mocks instead of real implementations', function () {
            // Analyze test files for mock overuse
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var testFiles = [];
            function findTestFiles(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        findTestFiles(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        testFiles.push(filePath);
                    }
                });
            }
            findTestFiles(testDirectory);
            var overMockedTests = [];
            testFiles.forEach(function (testFile) {
                try {
                    var content = fs_1.default.readFileSync(testFile, 'utf8');
                    var mockCount = (content.match(/\.mock|jest\.mock|mockImplementation|mockReturnValue/g) || []).length;
                    var realCallCount = (content.match(/fetch\(|prisma\.|await.*\(/g) || []).length;
                    // If mocks outnumber real calls by more than 3:1, it's over-mocked
                    if (mockCount > realCallCount * 3 && mockCount > 10) {
                        overMockedTests.push({ file: testFile, mockCount: mockCount, realCallCount: realCallCount });
                    }
                }
                catch (error) {
                    // Skip files that can't be read
                }
            });
            // EXPECTED TO FAIL: Tests should not over-rely on mocks
            (0, globals_1.expect)(overMockedTests.length).toBe(0);
        });
        (0, globals_1.it)('should fail - test timeouts are too aggressive for real operations', function () {
            // Check Jest configuration for realistic timeouts
            var jestConfigPath = path_1.default.join(process.cwd(), 'jest.config.js');
            if (fs_1.default.existsSync(jestConfigPath)) {
                var configContent = fs_1.default.readFileSync(jestConfigPath, 'utf8');
                // Extract timeout value
                var timeoutMatch = configContent.match(/testTimeout:\s*(\d+)/);
                var timeout = timeoutMatch ? parseInt(timeoutMatch[1]) : 5000; // Default Jest timeout
                // EXPECTED TO FAIL: Timeout should be reasonable for real operations (at least 30 seconds)
                (0, globals_1.expect)(timeout).toBeGreaterThanOrEqual(30000);
                // Check for AI operations that need longer timeouts
                var hasAITests = configContent.includes('ai') || configContent.includes('gemini');
                if (hasAITests) {
                    (0, globals_1.expect)(timeout).toBeGreaterThanOrEqual(60000); // AI operations need at least 1 minute
                }
            }
            else {
                // EXPECTED TO FAIL: Jest config should exist
                (0, globals_1.expect)(fs_1.default.existsSync(jestConfigPath)).toBe(true);
            }
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 3: Test Environment Configuration Problems', function () {
        (0, globals_1.it)('should fail - test environment variables are inconsistent', function () {
            // Check jest.setup.js for proper environment configuration
            var setupPath = path_1.default.join(process.cwd(), 'jest.setup.js');
            if (fs_1.default.existsSync(setupPath)) {
                var setupContent_1 = fs_1.default.readFileSync(setupPath, 'utf8');
                // Required environment variables for testing
                var requiredEnvVars = [
                    'NEXTAUTH_URL',
                    'NEXTAUTH_SECRET',
                    'DATABASE_URL',
                    'NODE_ENV'
                ];
                var missingEnvVars = requiredEnvVars.filter(function (envVar) {
                    return !setupContent_1.includes("process.env.".concat(envVar));
                });
                // EXPECTED TO FAIL: All required environment variables should be configured
                (0, globals_1.expect)(missingEnvVars.length).toBe(0);
                // Check for hardcoded test values that might cause issues
                var hasHardcodedSecrets = setupContent_1.includes('test-secret') ||
                    setupContent_1.includes('localhost:5432');
                // EXPECTED TO FAIL: Should not use hardcoded secrets in production-like tests
                (0, globals_1.expect)(hasHardcodedSecrets).toBe(false);
            }
            else {
                // EXPECTED TO FAIL: Jest setup file should exist
                (0, globals_1.expect)(fs_1.default.existsSync(setupPath)).toBe(true);
            }
        });
        (0, globals_1.it)('should fail - test database configuration is unsafe', function () {
            // Check for proper test database isolation
            var setupFiles = [
                'jest.setup.js',
                'jest.setup.integration.js'
            ];
            var unsafeConfigurations = [];
            setupFiles.forEach(function (setupFile) {
                var setupPath = path_1.default.join(process.cwd(), setupFile);
                if (fs_1.default.existsSync(setupPath)) {
                    var content = fs_1.default.readFileSync(setupPath, 'utf8');
                    // Check for unsafe database configurations
                    if (content.includes('DATABASE_URL') && !content.includes('test')) {
                        unsafeConfigurations.push("".concat(setupFile, ": DATABASE_URL doesn't include 'test'"));
                    }
                    // Check for production database references
                    if (content.includes('production') || content.includes('prod')) {
                        unsafeConfigurations.push("".concat(setupFile, ": Contains production references"));
                    }
                    // Check for missing database cleanup
                    if (!content.includes('beforeEach') && !content.includes('afterEach')) {
                        unsafeConfigurations.push("".concat(setupFile, ": Missing database cleanup hooks"));
                    }
                }
            });
            // EXPECTED TO FAIL: Test database configuration should be safe
            (0, globals_1.expect)(unsafeConfigurations.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 4: Test Performance and Scalability Issues', function () {
        (0, globals_1.it)('should fail - test suite execution time is too slow', function () {
            // Mock test execution metrics
            var testSuiteMetrics = {
                totalTests: 150,
                totalExecutionTime: 300000, // 5 minutes
                slowTests: [
                    { name: 'AI integration tests', duration: 120000 }, // 2 minutes
                    { name: 'Database migration tests', duration: 90000 }, // 1.5 minutes
                    { name: 'End-to-end user flows', duration: 60000 } // 1 minute
                ]
            };
            // Calculate average test time
            var averageTestTime = testSuiteMetrics.totalExecutionTime / testSuiteMetrics.totalTests;
            // EXPECTED TO FAIL: Average test time should be under 1 second
            (0, globals_1.expect)(averageTestTime).toBeLessThan(1000);
            // Individual tests should not take more than 30 seconds
            var testsOverThreshold = testSuiteMetrics.slowTests.filter(function (test) { return test.duration > 30000; });
            (0, globals_1.expect)(testsOverThreshold.length).toBe(0);
        });
        (0, globals_1.it)('should fail - test memory usage grows unbounded', function () {
            // Mock memory usage during test execution
            var memoryMetrics = {
                initialMemory: 50, // MB
                peakMemory: 2000, // MB - too high
                finalMemory: 1500, // MB - memory leak
                testCount: 100
            };
            var memoryGrowth = memoryMetrics.finalMemory - memoryMetrics.initialMemory;
            var memoryPerTest = memoryGrowth / memoryMetrics.testCount;
            // EXPECTED TO FAIL: Memory growth should be reasonable
            (0, globals_1.expect)(memoryGrowth).toBeLessThan(500); // Should not grow more than 500MB
            (0, globals_1.expect)(memoryPerTest).toBeLessThan(5); // Should not use more than 5MB per test
            (0, globals_1.expect)(memoryMetrics.peakMemory).toBeLessThan(1000); // Peak should be under 1GB
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 5: Test Organization and Maintenance Problems', function () {
        (0, globals_1.it)('should fail - test file naming conventions are inconsistent', function () {
            // Check test file naming patterns
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var testFiles = [];
            function findAllTestFiles(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        findAllTestFiles(filePath);
                    }
                    else if (file.includes('.test.') || file.includes('.spec.')) {
                        testFiles.push(file);
                    }
                });
            }
            findAllTestFiles(testDirectory);
            // Check for consistent naming patterns
            var namingIssues = [];
            var validPatterns = [
                /\.test\.(ts|tsx|js|jsx)$/,
                /\.spec\.(ts|tsx|js|jsx)$/
            ];
            testFiles.forEach(function (file) {
                var hasValidPattern = validPatterns.some(function (pattern) { return pattern.test(file); });
                if (!hasValidPattern) {
                    namingIssues.push(file);
                }
                // Check for inconsistent naming (mixing .test. and .spec.)
                if (file.includes('.test.') && testFiles.some(function (f) { return f.includes('.spec.'); })) {
                    namingIssues.push("Inconsistent naming: ".concat(file));
                }
            });
            // EXPECTED TO FAIL: All test files should follow consistent naming conventions
            (0, globals_1.expect)(namingIssues.length).toBe(0);
        });
        (0, globals_1.it)('should fail - test documentation and comments are inadequate', function () {
            // Check test files for proper documentation
            var testDirectory = path_1.default.join(process.cwd(), '__tests__');
            var undocumentedTests = [];
            function checkTestDocumentation(dir) {
                if (!fs_1.default.existsSync(dir))
                    return;
                var files = fs_1.default.readdirSync(dir);
                files.forEach(function (file) {
                    var filePath = path_1.default.join(dir, file);
                    var stat = fs_1.default.statSync(filePath);
                    if (stat.isDirectory()) {
                        checkTestDocumentation(filePath);
                    }
                    else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {
                        try {
                            var content = fs_1.default.readFileSync(filePath, 'utf8');
                            // Check for file-level documentation
                            var hasFileComment = content.includes('/**') || content.includes('/*');
                            var hasDescribeBlocks = content.includes('describe(');
                            var hasTestDescriptions = (content.match(/it\(['"`]/g) || []).length > 0;
                            if (!hasFileComment || !hasDescribeBlocks || !hasTestDescriptions) {
                                undocumentedTests.push({
                                    file: filePath,
                                    hasFileComment: hasFileComment,
                                    hasDescribeBlocks: hasDescribeBlocks,
                                    hasTestDescriptions: hasTestDescriptions
                                });
                            }
                        }
                        catch (error) {
                            // Skip files that can't be read
                        }
                    }
                });
            }
            checkTestDocumentation(testDirectory);
            // EXPECTED TO FAIL: All test files should be properly documented
            (0, globals_1.expect)(undocumentedTests.length).toBe(0);
        });
    });
    (0, globals_1.describe)('CRITICAL ISSUE 6: CI/CD Testing Pipeline Problems', function () {
        (0, globals_1.it)('should fail - package.json test scripts are incomplete', function () {
            var packageJsonPath = path_1.default.join(process.cwd(), 'package.json');
            if (fs_1.default.existsSync(packageJsonPath)) {
                var packageJson = JSON.parse(fs_1.default.readFileSync(packageJsonPath, 'utf8'));
                var scripts_1 = packageJson.scripts || {};
                // Required test scripts for a comprehensive testing pipeline
                var requiredTestScripts = [
                    'test',
                    'test:coverage',
                    'test:watch',
                    'test:ci',
                    'test:integration',
                    'test:e2e'
                ];
                var missingScripts = requiredTestScripts.filter(function (script) { return !scripts_1[script]; });
                // EXPECTED TO FAIL: All required test scripts should be present
                (0, globals_1.expect)(missingScripts.length).toBe(0);
                // Check for proper CI configuration
                var ciScript = scripts_1['test:ci'];
                if (ciScript) {
                    var hasCoverageReporting = ciScript.includes('--coverage');
                    var hasWatchDisabled = ciScript.includes('--watchAll=false');
                    (0, globals_1.expect)(hasCoverageReporting).toBe(true);
                    (0, globals_1.expect)(hasWatchDisabled).toBe(true);
                }
            }
            else {
                // EXPECTED TO FAIL: package.json should exist
                (0, globals_1.expect)(fs_1.default.existsSync(packageJsonPath)).toBe(true);
            }
        });
        (0, globals_1.it)('should fail - test coverage thresholds are too low', function () {
            var jestConfigPath = path_1.default.join(process.cwd(), 'jest.config.js');
            if (fs_1.default.existsSync(jestConfigPath)) {
                var configContent = fs_1.default.readFileSync(jestConfigPath, 'utf8');
                // Extract coverage thresholds
                var thresholdMatch = configContent.match(/coverageThreshold:\s*{[^}]+global:\s*{([^}]+)}/s);
                if (thresholdMatch) {
                    var thresholds = thresholdMatch[1];
                    // Extract individual threshold values
                    var branchesMatch = thresholds.match(/branches:\s*(\d+)/);
                    var functionsMatch = thresholds.match(/functions:\s*(\d+)/);
                    var linesMatch = thresholds.match(/lines:\s*(\d+)/);
                    var statementsMatch = thresholds.match(/statements:\s*(\d+)/);
                    var branches = branchesMatch ? parseInt(branchesMatch[1]) : 0;
                    var functions = functionsMatch ? parseInt(functionsMatch[1]) : 0;
                    var lines = linesMatch ? parseInt(linesMatch[1]) : 0;
                    var statements = statementsMatch ? parseInt(statementsMatch[1]) : 0;
                    // EXPECTED TO FAIL: Coverage thresholds should be high for critical applications
                    (0, globals_1.expect)(branches).toBeGreaterThanOrEqual(90); // Should be at least 90%
                    (0, globals_1.expect)(functions).toBeGreaterThanOrEqual(95); // Should be at least 95%
                    (0, globals_1.expect)(lines).toBeGreaterThanOrEqual(95); // Should be at least 95%
                    (0, globals_1.expect)(statements).toBeGreaterThanOrEqual(95); // Should be at least 95%
                }
                else {
                    // EXPECTED TO FAIL: Coverage thresholds should be configured
                    (0, globals_1.expect)(thresholdMatch).toBeTruthy();
                }
            }
            else {
                // EXPECTED TO FAIL: Jest config should exist
                (0, globals_1.expect)(fs_1.default.existsSync(jestConfigPath)).toBe(true);
            }
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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