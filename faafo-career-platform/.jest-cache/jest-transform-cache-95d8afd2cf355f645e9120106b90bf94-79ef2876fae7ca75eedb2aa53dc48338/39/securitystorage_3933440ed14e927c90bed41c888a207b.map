{"version": 3, "names": ["prisma_1", "cov_goe0e98w7", "s", "require", "next_1", "auth_1", "crypto_1", "__importDefault", "SecurityStorage", "f", "memoryFallback", "Map", "getInstance", "instance", "b", "prototype", "getClientIdentifier", "request", "Promise", "getServerSession", "authOptions", "session", "_b", "sent", "_a", "user", "id", "concat", "ip", "headers", "get", "userAgent", "hashString", "hash", "substring", "input", "window", "default", "createHash", "update", "digest", "encoder", "TextEncoder", "data", "encode", "subtle", "hash<PERSON><PERSON><PERSON>", "hashArray", "Array", "from", "Uint8Array", "map", "toString", "padStart", "join", "storeCSRFToken", "token", "expiresAt", "identifier", "prisma", "securityToken", "upsert", "where", "identifier_type", "type", "updatedAt", "Date", "create", "metadata", "console", "warn", "error_1", "set", "getCSRFToken", "findUnique", "stored", "error_2", "fallback", "validateCSRFToken", "error_3", "checkRateLimit", "windowMs", "maxRequests", "now", "windowStart", "getTime", "rateLimitEntry", "count", "createdAt", "gte", "<PERSON><PERSON><PERSON><PERSON>", "orderBy", "oldestEntry", "allowed", "remaining", "resetTime", "path", "nextUrl", "pathname", "error_4", "key", "entries", "validEntries", "filter", "entry", "timestamp", "length", "push", "cleanup", "now_1", "withDatabaseRetry", "__awaiter", "_this", "deleteMany", "lt", "oneDayAgo_1", "error_5", "for<PERSON>ach", "value", "startsWith", "delete", "exports", "securityStorage", "setInterval"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/security-storage.ts"], "sourcesContent": ["import { prisma, withDatabaseRetry } from './prisma';\nimport { NextRequest } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from './auth';\nimport crypto from 'crypto';\n\n// Database-backed security storage for production use\nexport class SecurityStorage {\n  private static instance: SecurityStorage;\n  private memoryFallback = new Map<string, any>();\n\n  static getInstance(): SecurityStorage {\n    if (!SecurityStorage.instance) {\n      SecurityStorage.instance = new SecurityStorage();\n    }\n    return SecurityStorage.instance;\n  }\n\n  private async getClientIdentifier(request: NextRequest): Promise<string> {\n    const session = await getServerSession(authOptions);\n    if (session?.user?.id) {\n      return `user_${session.user.id}`;\n    }\n    \n    // For anonymous users, use IP + User-Agent hash\n    const ip = request.headers.get('x-forwarded-for') || \n               request.headers.get('x-real-ip') || \n               'unknown';\n    const userAgent = request.headers.get('user-agent') || 'unknown';\n    const hash = await this.hashString(`${ip}_${userAgent}`);\n    return `anon_${hash.substring(0, 16)}`;\n  }\n\n  private async hashString(input: string): Promise<string> {\n    // Use Node.js crypto for server-side hashing (more reliable in test environment)\n    if (typeof window === 'undefined') {\n      // Server-side: use Node.js crypto\n      const hash = crypto.createHash('sha256');\n      hash.update(input);\n      return hash.digest('hex');\n    } else {\n      // Client-side: use Web Crypto API\n      const encoder = new TextEncoder();\n      const data = encoder.encode(input);\n      const hashBuffer = await crypto.subtle.digest('SHA-256', data);\n      const hashArray = Array.from(new Uint8Array(hashBuffer));\n      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');\n    }\n  }\n\n  // CSRF Token Management\n  async storeCSRFToken(request: NextRequest, token: string, expiresAt: Date): Promise<void> {\n    const identifier = await this.getClientIdentifier(request);\n    \n    try {\n      // Try database first\n      await prisma.securityToken.upsert({\n        where: {\n          identifier_type: {\n            identifier,\n            type: 'CSRF'\n          }\n        },\n        update: {\n          token,\n          expiresAt,\n          updatedAt: new Date()\n        },\n        create: {\n          identifier,\n          type: 'CSRF',\n          token,\n          expiresAt,\n          metadata: {\n            userAgent: request.headers.get('user-agent'),\n            ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip')\n          }\n        }\n      });\n    } catch (error) {\n      console.warn('Database CSRF storage failed, using memory fallback:', error);\n      this.memoryFallback.set(`csrf_${identifier}`, { token, expiresAt });\n    }\n  }\n\n  async getCSRFToken(request: NextRequest): Promise<string | null> {\n    const identifier = await this.getClientIdentifier(request);\n    \n    try {\n      // Try database first\n      const stored = await prisma.securityToken.findUnique({\n        where: {\n          identifier_type: {\n            identifier,\n            type: 'CSRF'\n          }\n        }\n      });\n\n      if (stored && stored.expiresAt > new Date()) {\n        return stored.token;\n      }\n    } catch (error) {\n      console.warn('Database CSRF retrieval failed, using memory fallback:', error);\n      const fallback = this.memoryFallback.get(`csrf_${identifier}`);\n      if (fallback && fallback.expiresAt > new Date()) {\n        return fallback.token;\n      }\n    }\n\n    return null;\n  }\n\n  async validateCSRFToken(request: NextRequest, token: string): Promise<boolean> {\n    const identifier = await this.getClientIdentifier(request);\n    \n    try {\n      // Try database first\n      const stored = await prisma.securityToken.findUnique({\n        where: {\n          identifier_type: {\n            identifier,\n            type: 'CSRF'\n          }\n        }\n      });\n\n      if (stored && stored.expiresAt > new Date() && stored.token === token) {\n        return true;\n      }\n    } catch (error) {\n      console.warn('Database CSRF validation failed, using memory fallback:', error);\n      const fallback = this.memoryFallback.get(`csrf_${identifier}`);\n      if (fallback && fallback.expiresAt > new Date() && fallback.token === token) {\n        return true;\n      }\n    }\n\n    return false;\n  }\n\n  // Rate Limiting\n  async checkRateLimit(request: NextRequest, windowMs: number, maxRequests: number): Promise<{\n    allowed: boolean;\n    remaining: number;\n    resetTime: number;\n  }> {\n    const identifier = await this.getClientIdentifier(request);\n    const now = new Date();\n    const windowStart = new Date(now.getTime() - windowMs);\n\n    try {\n      // Try database first\n      const count = await prisma.rateLimitEntry.count({\n        where: {\n          identifier,\n          createdAt: {\n            gte: windowStart\n          }\n        }\n      });\n\n      if (count >= maxRequests) {\n        const oldestEntry = await prisma.rateLimitEntry.findFirst({\n          where: { identifier },\n          orderBy: { createdAt: 'asc' }\n        });\n\n        return {\n          allowed: false,\n          remaining: 0,\n          resetTime: oldestEntry ? oldestEntry.createdAt.getTime() + windowMs : now.getTime() + windowMs\n        };\n      }\n\n      // Record this request\n      await prisma.rateLimitEntry.create({\n        data: {\n          identifier,\n          metadata: {\n            userAgent: request.headers.get('user-agent'),\n            ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),\n            path: request.nextUrl.pathname\n          }\n        }\n      });\n\n      return {\n        allowed: true,\n        remaining: maxRequests - count - 1,\n        resetTime: now.getTime() + windowMs\n      };\n\n    } catch (error) {\n      console.warn('Database rate limiting failed, using memory fallback:', error);\n      \n      // Memory fallback\n      const key = `rate_${identifier}`;\n      const entries = this.memoryFallback.get(key) || [];\n      const validEntries = entries.filter((entry: any) => entry.timestamp > now.getTime() - windowMs);\n      \n      if (validEntries.length >= maxRequests) {\n        return {\n          allowed: false,\n          remaining: 0,\n          resetTime: validEntries[0].timestamp + windowMs\n        };\n      }\n\n      validEntries.push({ timestamp: now.getTime() });\n      this.memoryFallback.set(key, validEntries);\n\n      return {\n        allowed: true,\n        remaining: maxRequests - validEntries.length,\n        resetTime: now.getTime() + windowMs\n      };\n    }\n  }\n\n  // Cleanup expired entries with robust error handling\n  async cleanup(): Promise<void> {\n    try {\n      const now = new Date();\n\n      // Clean up expired security tokens with retry logic\n      await withDatabaseRetry(async () => {\n        await prisma.securityToken.deleteMany({\n          where: {\n            expiresAt: {\n              lt: now\n            }\n          }\n        });\n      }, 2, 1000); // 2 retries with 1 second base delay\n\n      // Clean up old rate limit entries (keep last 24 hours)\n      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);\n      await withDatabaseRetry(async () => {\n        await prisma.rateLimitEntry.deleteMany({\n          where: {\n            createdAt: {\n              lt: oneDayAgo\n            }\n          }\n        });\n      }, 2, 1000); // 2 retries with 1 second base delay\n\n    } catch (error) {\n      console.warn('Database cleanup failed after retries:', error);\n      // Continue with memory cleanup even if database cleanup fails\n    }\n\n    // Clean up memory fallback\n    const now = Date.now();\n    Array.from(this.memoryFallback.entries()).forEach(([key, value]) => {\n      if (key.startsWith('csrf_') && value.expiresAt < now) {\n        this.memoryFallback.delete(key);\n      } else if (key.startsWith('rate_')) {\n        const validEntries = value.filter((entry: any) => entry.timestamp > now - 24 * 60 * 60 * 1000);\n        if (validEntries.length === 0) {\n          this.memoryFallback.delete(key);\n        } else {\n          this.memoryFallback.set(key, validEntries);\n        }\n      }\n    });\n  }\n}\n\n// Initialize cleanup interval\nconst securityStorage = SecurityStorage.getInstance();\nsetInterval(() => {\n  securityStorage.cleanup();\n}, 15 * 60 * 1000); // Clean up every 15 minutes\n\nexport default securityStorage;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,QAAAC,OAAA;AAEA,IAAAC,MAAA;AAAA;AAAA,CAAAH,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAE,MAAA;AAAA;AAAA,CAAAJ,aAAA,GAAAC,CAAA,QAAAC,OAAA;AACA,IAAAG,QAAA;AAAA;AAAA,CAAAL,aAAA,GAAAC,CAAA,QAAAK,eAAA,CAAAJ,OAAA;AAEA;AACA,IAAAK,eAAA;AAAA;AAAA,cAAAP,aAAA,GAAAC,CAAA;EAAA;EAAAD,aAAA,GAAAQ,CAAA;EAAA,SAAAD,gBAAA;IAAA;IAAAP,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAC,CAAA;IAEU,KAAAQ,cAAc,GAAG,IAAIC,GAAG,EAAe;EAmQjD;EAAC;EAAAV,aAAA,GAAAC,CAAA;EAjQQM,eAAA,CAAAI,WAAW,GAAlB;IAAA;IAAAX,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAC,CAAA;IACE,IAAI,CAACM,eAAe,CAACK,QAAQ,EAAE;MAAA;MAAAZ,aAAA,GAAAa,CAAA;MAAAb,aAAA,GAAAC,CAAA;MAC7BM,eAAe,CAACK,QAAQ,GAAG,IAAIL,eAAe,EAAE;IAClD,CAAC;IAAA;IAAA;MAAAP,aAAA,GAAAa,CAAA;IAAA;IAAAb,aAAA,GAAAC,CAAA;IACD,OAAOM,eAAe,CAACK,QAAQ;EACjC,CAAC;EAAA;EAAAZ,aAAA,GAAAC,CAAA;EAEaM,eAAA,CAAAO,SAAA,CAAAC,mBAAmB,GAAjC,UAAkCC,OAAoB;IAAA;IAAAhB,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAC,CAAA;mCAAGgB,OAAO;MAAA;MAAAjB,aAAA,GAAAQ,CAAA;;;;;;;;;;;;;;YAC9C,qBAAM,IAAAL,MAAA,CAAAe,gBAAgB,EAACd,MAAA,CAAAe,WAAW,CAAC;;;;;YAA7CC,OAAO,GAAGC,EAAA,CAAAC,IAAA,EAAmC;YAAA;YAAAtB,aAAA,GAAAC,CAAA;YACnD;YAAI;YAAA,CAAAD,aAAA,GAAAa,CAAA,YAAAU,EAAA;YAAA;YAAA,CAAAvB,aAAA,GAAAa,CAAA,WAAAO,OAAO;YAAA;YAAA,CAAApB,aAAA,GAAAa,CAAA,WAAPO,OAAO;YAAA;YAAA,CAAApB,aAAA,GAAAa,CAAA;YAAA;YAAA,CAAAb,aAAA,GAAAa,CAAA,WAAPO,OAAO,CAAEI,IAAI;YAAA;YAAA,CAAAxB,aAAA,GAAAa,CAAA,WAAAU,EAAA;YAAA;YAAA,CAAAvB,aAAA,GAAAa,CAAA;YAAA;YAAA,CAAAb,aAAA,GAAAa,CAAA,WAAAU,EAAA,CAAEE,EAAE,GAAE;cAAA;cAAAzB,aAAA,GAAAa,CAAA;cAAAb,aAAA,GAAAC,CAAA;cACrB,sBAAO,QAAAyB,MAAA,CAAQN,OAAO,CAACI,IAAI,CAACC,EAAE,CAAE;YAClC,CAAC;YAAA;YAAA;cAAAzB,aAAA,GAAAa,CAAA;YAAA;YAAAb,aAAA,GAAAC,CAAA;YAGK0B,EAAE;YAAG;YAAA,CAAA3B,aAAA,GAAAa,CAAA,WAAAG,OAAO,CAACY,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;YAAA;YAAA,CAAA7B,aAAA,GAAAa,CAAA,WACtCG,OAAO,CAACY,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;YAAA;YAAA,CAAA7B,aAAA,GAAAa,CAAA,WAChC,SAAS;YAAC;YAAAb,aAAA,GAAAC,CAAA;YACf6B,SAAS;YAAG;YAAA,CAAA9B,aAAA,GAAAa,CAAA,WAAAG,OAAO,CAACY,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;YAAA;YAAA,CAAA7B,aAAA,GAAAa,CAAA,WAAI,SAAS;YAAC;YAAAb,aAAA,GAAAC,CAAA;YACpD,qBAAM,IAAI,CAAC8B,UAAU,CAAC,GAAAL,MAAA,CAAGC,EAAE,OAAAD,MAAA,CAAII,SAAS,CAAE,CAAC;;;;;YAAlDE,IAAI,GAAGX,EAAA,CAAAC,IAAA,EAA2C;YAAA;YAAAtB,aAAA,GAAAC,CAAA;YACxD,sBAAO,QAAAyB,MAAA,CAAQM,IAAI,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAE;;;;GACvC;EAAA;EAAAjC,aAAA,GAAAC,CAAA;EAEaM,eAAA,CAAAO,SAAA,CAAAiB,UAAU,GAAxB,UAAyBG,KAAa;IAAA;IAAAlC,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAC,CAAA;mCAAGgB,OAAO;MAAA;MAAAjB,aAAA,GAAAQ,CAAA;;;;;;;;;;;;;kBAE1C,OAAO2B,MAAM,KAAK,WAAW,GAA7B;cAAA;cAAAnC,aAAA,GAAAa,CAAA;cAAAb,aAAA,GAAAC,CAAA;cAAA;YAAA,CAA6B;YAAA;YAAA;cAAAD,aAAA,GAAAa,CAAA;YAAA;YAAAb,aAAA,GAAAC,CAAA;YAEzB+B,IAAI,GAAG3B,QAAA,CAAA+B,OAAM,CAACC,UAAU,CAAC,QAAQ,CAAC;YAAC;YAAArC,aAAA,GAAAC,CAAA;YACzC+B,IAAI,CAACM,MAAM,CAACJ,KAAK,CAAC;YAAC;YAAAlC,aAAA,GAAAC,CAAA;YACnB,sBAAO+B,IAAI,CAACO,MAAM,CAAC,KAAK,CAAC;;;;;YAGnBC,OAAO,GAAG,IAAIC,WAAW,EAAE;YAAC;YAAAzC,aAAA,GAAAC,CAAA;YAC5ByC,IAAI,GAAGF,OAAO,CAACG,MAAM,CAACT,KAAK,CAAC;YAAC;YAAAlC,aAAA,GAAAC,CAAA;YAChB,qBAAMI,QAAA,CAAA+B,OAAM,CAACQ,MAAM,CAACL,MAAM,CAAC,SAAS,EAAEG,IAAI,CAAC;;;;;YAAxDG,UAAU,GAAGtB,EAAA,CAAAD,IAAA,EAA2C;YAAA;YAAAtB,aAAA,GAAAC,CAAA;YACxD6C,SAAS,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,UAAU,CAACJ,UAAU,CAAC,CAAC;YAAC;YAAA7C,aAAA,GAAAC,CAAA;YACzD,sBAAO6C,SAAS,CAACI,GAAG,CAAC,UAAArC,CAAC;cAAA;cAAAb,aAAA,GAAAQ,CAAA;cAAAR,aAAA,GAAAC,CAAA;cAAI,OAAAY,CAAC,CAACsC,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;YAA/B,CAA+B,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;;;;GAEtE;EAED;EAAA;EAAArD,aAAA,GAAAC,CAAA;EACMM,eAAA,CAAAO,SAAA,CAAAwC,cAAc,GAApB,UAAqBtC,OAAoB,EAAEuC,KAAa,EAAEC,SAAe;IAAA;IAAAxD,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAC,CAAA;mCAAGgB,OAAO;MAAA;MAAAjB,aAAA,GAAAQ,CAAA;;;;;;;;;;;;;YAC9D,qBAAM,IAAI,CAACO,mBAAmB,CAACC,OAAO,CAAC;;;;;YAApDyC,UAAU,GAAGlC,EAAA,CAAAD,IAAA,EAAuC;YAAA;YAAAtB,aAAA,GAAAC,CAAA;;;;;;;YAGxD;YAAA;YAAAD,aAAA,GAAAC,CAAA;YACA,qBAAMF,QAAA,CAAA2D,MAAM,CAACC,aAAa,CAACC,MAAM,CAAC;cAChCC,KAAK,EAAE;gBACLC,eAAe,EAAE;kBACfL,UAAU,EAAAA,UAAA;kBACVM,IAAI,EAAE;;eAET;cACDzB,MAAM,EAAE;gBACNiB,KAAK,EAAAA,KAAA;gBACLC,SAAS,EAAAA,SAAA;gBACTQ,SAAS,EAAE,IAAIC,IAAI;eACpB;cACDC,MAAM,EAAE;gBACNT,UAAU,EAAAA,UAAA;gBACVM,IAAI,EAAE,MAAM;gBACZR,KAAK,EAAAA,KAAA;gBACLC,SAAS,EAAAA,SAAA;gBACTW,QAAQ,EAAE;kBACRrC,SAAS,EAAEd,OAAO,CAACY,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;kBAC5CF,EAAE;kBAAE;kBAAA,CAAA3B,aAAA,GAAAa,CAAA,WAAAG,OAAO,CAACY,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;kBAAA;kBAAA,CAAA7B,aAAA,GAAAa,CAAA,WAAIG,OAAO,CAACY,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;;;aAGnF,CAAC;;;;;YAvBF;YACAN,EAAA,CAAAD,IAAA,EAsBE;YAAC;YAAAtB,aAAA,GAAAC,CAAA;;;;;;;;;YAEHmE,OAAO,CAACC,IAAI,CAAC,sDAAsD,EAAEC,OAAK,CAAC;YAAC;YAAAtE,aAAA,GAAAC,CAAA;YAC5E,IAAI,CAACQ,cAAc,CAAC8D,GAAG,CAAC,QAAA7C,MAAA,CAAQ+B,UAAU,CAAE,EAAE;cAAEF,KAAK,EAAAA,KAAA;cAAEC,SAAS,EAAAA;YAAA,CAAE,CAAC;YAAC;YAAAxD,aAAA,GAAAC,CAAA;;;;;;;;;;GAEvE;EAAA;EAAAD,aAAA,GAAAC,CAAA;EAEKM,eAAA,CAAAO,SAAA,CAAA0D,YAAY,GAAlB,UAAmBxD,OAAoB;IAAA;IAAAhB,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAC,CAAA;mCAAGgB,OAAO;MAAA;MAAAjB,aAAA,GAAAQ,CAAA;;;;;;;;;;;;;YAC5B,qBAAM,IAAI,CAACO,mBAAmB,CAACC,OAAO,CAAC;;;;;YAApDyC,UAAU,GAAGlC,EAAA,CAAAD,IAAA,EAAuC;YAAA;YAAAtB,aAAA,GAAAC,CAAA;;;;;;;;;YAIzC,qBAAMF,QAAA,CAAA2D,MAAM,CAACC,aAAa,CAACc,UAAU,CAAC;cACnDZ,KAAK,EAAE;gBACLC,eAAe,EAAE;kBACfL,UAAU,EAAAA,UAAA;kBACVM,IAAI,EAAE;;;aAGX,CAAC;;;;;YAPIW,MAAM,GAAGnD,EAAA,CAAAD,IAAA,EAOb;YAAA;YAAAtB,aAAA,GAAAC,CAAA;YAEF;YAAI;YAAA,CAAAD,aAAA,GAAAa,CAAA,WAAA6D,MAAM;YAAA;YAAA,CAAA1E,aAAA,GAAAa,CAAA,WAAI6D,MAAM,CAAClB,SAAS,GAAG,IAAIS,IAAI,EAAE,GAAE;cAAA;cAAAjE,aAAA,GAAAa,CAAA;cAAAb,aAAA,GAAAC,CAAA;cAC3C,sBAAOyE,MAAM,CAACnB,KAAK;YACrB,CAAC;YAAA;YAAA;cAAAvD,aAAA,GAAAa,CAAA;YAAA;YAAAb,aAAA,GAAAC,CAAA;;;;;;;;;YAEDmE,OAAO,CAACC,IAAI,CAAC,wDAAwD,EAAEM,OAAK,CAAC;YAAC;YAAA3E,aAAA,GAAAC,CAAA;YACxE2E,QAAQ,GAAG,IAAI,CAACnE,cAAc,CAACoB,GAAG,CAAC,QAAAH,MAAA,CAAQ+B,UAAU,CAAE,CAAC;YAAC;YAAAzD,aAAA,GAAAC,CAAA;YAC/D;YAAI;YAAA,CAAAD,aAAA,GAAAa,CAAA,WAAA+D,QAAQ;YAAA;YAAA,CAAA5E,aAAA,GAAAa,CAAA,WAAI+D,QAAQ,CAACpB,SAAS,GAAG,IAAIS,IAAI,EAAE,GAAE;cAAA;cAAAjE,aAAA,GAAAa,CAAA;cAAAb,aAAA,GAAAC,CAAA;cAC/C,sBAAO2E,QAAQ,CAACrB,KAAK;YACvB,CAAC;YAAA;YAAA;cAAAvD,aAAA,GAAAa,CAAA;YAAA;YAAAb,aAAA,GAAAC,CAAA;;;;;;YAGH,sBAAO,IAAI;;;;GACZ;EAAA;EAAAD,aAAA,GAAAC,CAAA;EAEKM,eAAA,CAAAO,SAAA,CAAA+D,iBAAiB,GAAvB,UAAwB7D,OAAoB,EAAEuC,KAAa;IAAA;IAAAvD,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAC,CAAA;mCAAGgB,OAAO;MAAA;MAAAjB,aAAA,GAAAQ,CAAA;;;;;;;;;;;;;YAChD,qBAAM,IAAI,CAACO,mBAAmB,CAACC,OAAO,CAAC;;;;;YAApDyC,UAAU,GAAGlC,EAAA,CAAAD,IAAA,EAAuC;YAAA;YAAAtB,aAAA,GAAAC,CAAA;;;;;;;;;YAIzC,qBAAMF,QAAA,CAAA2D,MAAM,CAACC,aAAa,CAACc,UAAU,CAAC;cACnDZ,KAAK,EAAE;gBACLC,eAAe,EAAE;kBACfL,UAAU,EAAAA,UAAA;kBACVM,IAAI,EAAE;;;aAGX,CAAC;;;;;YAPIW,MAAM,GAAGnD,EAAA,CAAAD,IAAA,EAOb;YAAA;YAAAtB,aAAA,GAAAC,CAAA;YAEF;YAAI;YAAA,CAAAD,aAAA,GAAAa,CAAA,WAAA6D,MAAM;YAAA;YAAA,CAAA1E,aAAA,GAAAa,CAAA,WAAI6D,MAAM,CAAClB,SAAS,GAAG,IAAIS,IAAI,EAAE;YAAA;YAAA,CAAAjE,aAAA,GAAAa,CAAA,WAAI6D,MAAM,CAACnB,KAAK,KAAKA,KAAK,GAAE;cAAA;cAAAvD,aAAA,GAAAa,CAAA;cAAAb,aAAA,GAAAC,CAAA;cACrE,sBAAO,IAAI;YACb,CAAC;YAAA;YAAA;cAAAD,aAAA,GAAAa,CAAA;YAAA;YAAAb,aAAA,GAAAC,CAAA;;;;;;;;;YAEDmE,OAAO,CAACC,IAAI,CAAC,yDAAyD,EAAES,OAAK,CAAC;YAAC;YAAA9E,aAAA,GAAAC,CAAA;YACzE2E,QAAQ,GAAG,IAAI,CAACnE,cAAc,CAACoB,GAAG,CAAC,QAAAH,MAAA,CAAQ+B,UAAU,CAAE,CAAC;YAAC;YAAAzD,aAAA,GAAAC,CAAA;YAC/D;YAAI;YAAA,CAAAD,aAAA,GAAAa,CAAA,WAAA+D,QAAQ;YAAA;YAAA,CAAA5E,aAAA,GAAAa,CAAA,WAAI+D,QAAQ,CAACpB,SAAS,GAAG,IAAIS,IAAI,EAAE;YAAA;YAAA,CAAAjE,aAAA,GAAAa,CAAA,WAAI+D,QAAQ,CAACrB,KAAK,KAAKA,KAAK,GAAE;cAAA;cAAAvD,aAAA,GAAAa,CAAA;cAAAb,aAAA,GAAAC,CAAA;cAC3E,sBAAO,IAAI;YACb,CAAC;YAAA;YAAA;cAAAD,aAAA,GAAAa,CAAA;YAAA;YAAAb,aAAA,GAAAC,CAAA;;;;;;YAGH,sBAAO,KAAK;;;;GACb;EAED;EAAA;EAAAD,aAAA,GAAAC,CAAA;EACMM,eAAA,CAAAO,SAAA,CAAAiE,cAAc,GAApB,UAAqB/D,OAAoB,EAAEgE,QAAgB,EAAEC,WAAmB;IAAA;IAAAjF,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAC,CAAA;mCAAGgB,OAAO;MAAA;MAAAjB,aAAA,GAAAQ,CAAA;;;;;;;;;;;;;YAKrE,qBAAM,IAAI,CAACO,mBAAmB,CAACC,OAAO,CAAC;;;;;YAApDyC,UAAU,GAAGlC,EAAA,CAAAD,IAAA,EAAuC;YAAA;YAAAtB,aAAA,GAAAC,CAAA;YACpDiF,GAAG,GAAG,IAAIjB,IAAI,EAAE;YAAC;YAAAjE,aAAA,GAAAC,CAAA;YACjBkF,WAAW,GAAG,IAAIlB,IAAI,CAACiB,GAAG,CAACE,OAAO,EAAE,GAAGJ,QAAQ,CAAC;YAAC;YAAAhF,aAAA,GAAAC,CAAA;;;;;;;;;YAIvC,qBAAMF,QAAA,CAAA2D,MAAM,CAAC2B,cAAc,CAACC,KAAK,CAAC;cAC9CzB,KAAK,EAAE;gBACLJ,UAAU,EAAAA,UAAA;gBACV8B,SAAS,EAAE;kBACTC,GAAG,EAAEL;;;aAGV,CAAC;;;;;YAPIG,KAAK,GAAG/D,EAAA,CAAAD,IAAA,EAOZ;YAAA;YAAAtB,aAAA,GAAAC,CAAA;kBAEEqF,KAAK,IAAIL,WAAW,GAApB;cAAA;cAAAjF,aAAA,GAAAa,CAAA;cAAAb,aAAA,GAAAC,CAAA;cAAA;YAAA,CAAoB;YAAA;YAAA;cAAAD,aAAA,GAAAa,CAAA;YAAA;YAAAb,aAAA,GAAAC,CAAA;YACF,qBAAMF,QAAA,CAAA2D,MAAM,CAAC2B,cAAc,CAACI,SAAS,CAAC;cACxD5B,KAAK,EAAE;gBAAEJ,UAAU,EAAAA;cAAA,CAAE;cACrBiC,OAAO,EAAE;gBAAEH,SAAS,EAAE;cAAK;aAC5B,CAAC;;;;;YAHII,WAAW,GAAGpE,EAAA,CAAAD,IAAA,EAGlB;YAAA;YAAAtB,aAAA,GAAAC,CAAA;YAEF,sBAAO;cACL2F,OAAO,EAAE,KAAK;cACdC,SAAS,EAAE,CAAC;cACZC,SAAS,EAAEH,WAAW;cAAA;cAAA,CAAA3F,aAAA,GAAAa,CAAA,WAAG8E,WAAW,CAACJ,SAAS,CAACH,OAAO,EAAE,GAAGJ,QAAQ;cAAA;cAAA,CAAAhF,aAAA,GAAAa,CAAA,WAAGqE,GAAG,CAACE,OAAO,EAAE,GAAGJ,QAAQ;aAC/F;;;;;YAGH;YACA,qBAAMjF,QAAA,CAAA2D,MAAM,CAAC2B,cAAc,CAACnB,MAAM,CAAC;cACjCxB,IAAI,EAAE;gBACJe,UAAU,EAAAA,UAAA;gBACVU,QAAQ,EAAE;kBACRrC,SAAS,EAAEd,OAAO,CAACY,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;kBAC5CF,EAAE;kBAAE;kBAAA,CAAA3B,aAAA,GAAAa,CAAA,WAAAG,OAAO,CAACY,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;kBAAA;kBAAA,CAAA7B,aAAA,GAAAa,CAAA,WAAIG,OAAO,CAACY,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;kBAC9EkE,IAAI,EAAE/E,OAAO,CAACgF,OAAO,CAACC;;;aAG3B,CAAC;;;;;YAVF;YACA1E,EAAA,CAAAD,IAAA,EASE;YAAC;YAAAtB,aAAA,GAAAC,CAAA;YAEH,sBAAO;cACL2F,OAAO,EAAE,IAAI;cACbC,SAAS,EAAEZ,WAAW,GAAGK,KAAK,GAAG,CAAC;cAClCQ,SAAS,EAAEZ,GAAG,CAACE,OAAO,EAAE,GAAGJ;aAC5B;;;;;;;;YAGDZ,OAAO,CAACC,IAAI,CAAC,uDAAuD,EAAE6B,OAAK,CAAC;YAAC;YAAAlG,aAAA,GAAAC,CAAA;YAGvEkG,GAAG,GAAG,QAAAzE,MAAA,CAAQ+B,UAAU,CAAE;YAAC;YAAAzD,aAAA,GAAAC,CAAA;YAC3BmG,OAAO;YAAG;YAAA,CAAApG,aAAA,GAAAa,CAAA,eAAI,CAACJ,cAAc,CAACoB,GAAG,CAACsE,GAAG,CAAC;YAAA;YAAA,CAAAnG,aAAA,GAAAa,CAAA,WAAI,EAAE;YAAC;YAAAb,aAAA,GAAAC,CAAA;YAC7CoG,YAAY,GAAGD,OAAO,CAACE,MAAM,CAAC,UAACC,KAAU;cAAA;cAAAvG,aAAA,GAAAQ,CAAA;cAAAR,aAAA,GAAAC,CAAA;cAAK,OAAAsG,KAAK,CAACC,SAAS,GAAGtB,GAAG,CAACE,OAAO,EAAE,GAAGJ,QAAQ;YAA1C,CAA0C,CAAC;YAAC;YAAAhF,aAAA,GAAAC,CAAA;YAEhG,IAAIoG,YAAY,CAACI,MAAM,IAAIxB,WAAW,EAAE;cAAA;cAAAjF,aAAA,GAAAa,CAAA;cAAAb,aAAA,GAAAC,CAAA;cACtC,sBAAO;gBACL2F,OAAO,EAAE,KAAK;gBACdC,SAAS,EAAE,CAAC;gBACZC,SAAS,EAAEO,YAAY,CAAC,CAAC,CAAC,CAACG,SAAS,GAAGxB;eACxC;YACH,CAAC;YAAA;YAAA;cAAAhF,aAAA,GAAAa,CAAA;YAAA;YAAAb,aAAA,GAAAC,CAAA;YAEDoG,YAAY,CAACK,IAAI,CAAC;cAAEF,SAAS,EAAEtB,GAAG,CAACE,OAAO;YAAE,CAAE,CAAC;YAAC;YAAApF,aAAA,GAAAC,CAAA;YAChD,IAAI,CAACQ,cAAc,CAAC8D,GAAG,CAAC4B,GAAG,EAAEE,YAAY,CAAC;YAAC;YAAArG,aAAA,GAAAC,CAAA;YAE3C,sBAAO;cACL2F,OAAO,EAAE,IAAI;cACbC,SAAS,EAAEZ,WAAW,GAAGoB,YAAY,CAACI,MAAM;cAC5CX,SAAS,EAAEZ,GAAG,CAACE,OAAO,EAAE,GAAGJ;aAC5B;;;;;;;;;GAEJ;EAED;EAAA;EAAAhF,aAAA,GAAAC,CAAA;EACMM,eAAA,CAAAO,SAAA,CAAA6F,OAAO,GAAb;IAAA;IAAA3G,aAAA,GAAAQ,CAAA;IAAAR,aAAA,GAAAC,CAAA;mCAAiBgB,OAAO;MAAA;MAAAjB,aAAA,GAAAQ,CAAA;;;;;;;;;;;;;;;;;;;YAEdoG,KAAA,GAAM,IAAI3C,IAAI,EAAE;YAEtB;YAAA;YAAAjE,aAAA,GAAAC,CAAA;YACA,qBAAM,IAAAF,QAAA,CAAA8G,iBAAiB,EAAC;cAAA;cAAA7G,aAAA,GAAAQ,CAAA;cAAAR,aAAA,GAAAC,CAAA;cAAA,OAAA6G,SAAA,CAAAC,KAAA;gBAAA;gBAAA/G,aAAA,GAAAQ,CAAA;gBAAAR,aAAA,GAAAC,CAAA;;;;;;;;;;sBACtB,qBAAMF,QAAA,CAAA2D,MAAM,CAACC,aAAa,CAACqD,UAAU,CAAC;wBACpCnD,KAAK,EAAE;0BACLL,SAAS,EAAE;4BACTyD,EAAE,EAAEL;;;uBAGT,CAAC;;;;;sBANFrF,EAAA,CAAAD,IAAA,EAME;sBAAC;sBAAAtB,aAAA,GAAAC,CAAA;;;;;aACJ,EAAE,CAAC,EAAE,IAAI,CAAC;;;;;YATX;YACAsB,EAAA,CAAAD,IAAA,EAQW,CAAC,CAAC;YAAA;YAAAtB,aAAA,GAAAC,CAAA;YAGPiH,WAAA,GAAY,IAAIjD,IAAI,CAAC2C,KAAG,CAACxB,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YAAC;YAAApF,aAAA,GAAAC,CAAA;YAChE,qBAAM,IAAAF,QAAA,CAAA8G,iBAAiB,EAAC;cAAA;cAAA7G,aAAA,GAAAQ,CAAA;cAAAR,aAAA,GAAAC,CAAA;cAAA,OAAA6G,SAAA,CAAAC,KAAA;gBAAA;gBAAA/G,aAAA,GAAAQ,CAAA;gBAAAR,aAAA,GAAAC,CAAA;;;;;;;;;;sBACtB,qBAAMF,QAAA,CAAA2D,MAAM,CAAC2B,cAAc,CAAC2B,UAAU,CAAC;wBACrCnD,KAAK,EAAE;0BACL0B,SAAS,EAAE;4BACT0B,EAAE,EAAEC;;;uBAGT,CAAC;;;;;sBANF3F,EAAA,CAAAD,IAAA,EAME;sBAAC;sBAAAtB,aAAA,GAAAC,CAAA;;;;;aACJ,EAAE,CAAC,EAAE,IAAI,CAAC;;;;;YARXsB,EAAA,CAAAD,IAAA,EAQW,CAAC,CAAC;YAAA;YAAAtB,aAAA,GAAAC,CAAA;;;;;;;;;YAGbmE,OAAO,CAACC,IAAI,CAAC,wCAAwC,EAAE8C,OAAK,CAAC;YAAC;YAAAnH,aAAA,GAAAC,CAAA;;;;;;YAK1DiF,GAAG,GAAGjB,IAAI,CAACiB,GAAG,EAAE;YAAC;YAAAlF,aAAA,GAAAC,CAAA;YACvB8C,KAAK,CAACC,IAAI,CAAC,IAAI,CAACvC,cAAc,CAAC2F,OAAO,EAAE,CAAC,CAACgB,OAAO,CAAC,UAAC7F,EAAY;cAAA;cAAAvB,aAAA,GAAAQ,CAAA;kBAAX2F,GAAG;gBAAA;gBAAA,CAAAnG,aAAA,GAAAC,CAAA,SAAAsB,EAAA;gBAAE8F,KAAK;gBAAA;gBAAA,CAAArH,aAAA,GAAAC,CAAA,SAAAsB,EAAA;cAAA;cAAAvB,aAAA,GAAAC,CAAA;cAC5D;cAAI;cAAA,CAAAD,aAAA,GAAAa,CAAA,WAAAsF,GAAG,CAACmB,UAAU,CAAC,OAAO,CAAC;cAAA;cAAA,CAAAtH,aAAA,GAAAa,CAAA,WAAIwG,KAAK,CAAC7D,SAAS,GAAG0B,GAAG,GAAE;gBAAA;gBAAAlF,aAAA,GAAAa,CAAA;gBAAAb,aAAA,GAAAC,CAAA;gBACpD8G,KAAI,CAACtG,cAAc,CAAC8G,MAAM,CAACpB,GAAG,CAAC;cACjC,CAAC,MAAM;gBAAA;gBAAAnG,aAAA,GAAAa,CAAA;gBAAAb,aAAA,GAAAC,CAAA;gBAAA,IAAIkG,GAAG,CAACmB,UAAU,CAAC,OAAO,CAAC,EAAE;kBAAA;kBAAAtH,aAAA,GAAAa,CAAA;kBAClC,IAAMwF,YAAY;kBAAA;kBAAA,CAAArG,aAAA,GAAAC,CAAA,SAAGoH,KAAK,CAACf,MAAM,CAAC,UAACC,KAAU;oBAAA;oBAAAvG,aAAA,GAAAQ,CAAA;oBAAAR,aAAA,GAAAC,CAAA;oBAAK,OAAAsG,KAAK,CAACC,SAAS,GAAGtB,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;kBAA3C,CAA2C,CAAC;kBAAC;kBAAAlF,aAAA,GAAAC,CAAA;kBAC/F,IAAIoG,YAAY,CAACI,MAAM,KAAK,CAAC,EAAE;oBAAA;oBAAAzG,aAAA,GAAAa,CAAA;oBAAAb,aAAA,GAAAC,CAAA;oBAC7B8G,KAAI,CAACtG,cAAc,CAAC8G,MAAM,CAACpB,GAAG,CAAC;kBACjC,CAAC,MAAM;oBAAA;oBAAAnG,aAAA,GAAAa,CAAA;oBAAAb,aAAA,GAAAC,CAAA;oBACL8G,KAAI,CAACtG,cAAc,CAAC8D,GAAG,CAAC4B,GAAG,EAAEE,YAAY,CAAC;kBAC5C;gBACF,CAAC;gBAAA;gBAAA;kBAAArG,aAAA,GAAAa,CAAA;gBAAA;cAAD;YACF,CAAC,CAAC;YAAC;YAAAb,aAAA,GAAAC,CAAA;;;;;GACJ;EAAA;EAAAD,aAAA,GAAAC,CAAA;EACH,OAAAM,eAAC;AAAD,CAAC,CArQD;AAqQC;AAAAP,aAAA,GAAAC,CAAA;AArQYuH,OAAA,CAAAjH,eAAA,GAAAA,eAAA;AAuQb;AACA,IAAMkH,eAAe;AAAA;AAAA,CAAAzH,aAAA,GAAAC,CAAA,SAAGM,eAAe,CAACI,WAAW,EAAE;AAAC;AAAAX,aAAA,GAAAC,CAAA;AACtDyH,WAAW,CAAC;EAAA;EAAA1H,aAAA,GAAAQ,CAAA;EAAAR,aAAA,GAAAC,CAAA;EACVwH,eAAe,CAACd,OAAO,EAAE;AAC3B,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC;AAAA;AAAA3G,aAAA,GAAAC,CAAA;AAEpBuH,OAAA,CAAApF,OAAA,GAAeqF,eAAe", "ignoreList": []}