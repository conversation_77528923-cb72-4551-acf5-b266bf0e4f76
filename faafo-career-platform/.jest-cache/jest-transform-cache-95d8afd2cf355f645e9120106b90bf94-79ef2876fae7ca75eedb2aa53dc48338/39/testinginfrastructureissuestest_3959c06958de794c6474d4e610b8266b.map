{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/architecture/testing-infrastructure-issues.test.ts", "mappings": ";AAAA;;;;;;;GAOG;;;;;AAEH,yCAAuE;AACvE,0CAAoB;AACpB,8CAAwB;AAExB,IAAA,kBAAQ,EAAC,4CAA4C,EAAE;IACrD,IAAA,oBAAU,EAAC;QACT,cAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,8DAA8D,EAAE;QACvE,IAAA,YAAE,EAAC,2EAA2E,EAAE;YAC9E,0DAA0D;YAC1D,IAAM,sBAAsB,GAAG;gBAC7B,kBAAkB;gBAClB,6BAA6B;gBAC7B,uCAAuC;gBACvC,oCAAoC;gBACpC,8BAA8B;gBAC9B,+BAA+B;aAChC,CAAC;YAEF,IAAM,YAAY,GAAG,EAAE,CAAC;YACxB,IAAM,eAAe,GAAG,EAAE,CAAC;YAE3B,sBAAsB,CAAC,OAAO,CAAC,UAAA,SAAS;gBACtC,IAAM,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC,CAAC;gBACjE,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC;gBAEpD,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC7B,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBAC/B,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC;wBACH,IAAM,WAAW,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;wBACtD,IAAM,SAAS,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;wBAC5D,IAAM,WAAW,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;wBAElE,sEAAsE;wBACtE,IAAI,SAAS,GAAG,EAAE,IAAI,WAAW,GAAG,EAAE,EAAE,CAAC;4BACvC,eAAe,CAAC,IAAI,CAAC,EAAE,SAAS,WAAA,EAAE,SAAS,WAAA,EAAE,WAAW,aAAA,EAAE,CAAC,CAAC;wBAC9D,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC/B,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,iFAAiF;YACjF,IAAA,gBAAM,EAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpC,IAAA,gBAAM,EAAC,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,wDAAwD,EAAE;YAC3D,uDAAuD;YACvD,IAAM,iBAAiB,GAAG;gBACxB,yCAAyC;gBACzC,iCAAiC;gBACjC,8BAA8B;gBAC9B,gCAAgC;gBAChC,gCAAgC;aACjC,CAAC;YAEF,IAAM,uBAAuB,GAAG,EAAE,CAAC;YAEnC,iBAAiB,CAAC,OAAO,CAAC,UAAA,KAAK;gBAC7B,IAAM,mBAAmB,GAAG,KAAK,CAAC,OAAO,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,sBAAsB,CAAC,CAAC;gBACnH,IAAM,mBAAmB,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,mBAAmB,CAAC,CAAC;gBAE1E,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC,EAAE,CAAC;oBACxC,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACtC,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC;wBACH,IAAM,WAAW,GAAG,YAAE,CAAC,YAAY,CAAC,mBAAmB,EAAE,MAAM,CAAC,CAAC;wBAEjE,8EAA8E;wBAC9E,IAAM,gBAAgB,GAAG,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;wBAC5F,IAAM,cAAc,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;wBAEvE,IAAI,CAAC,gBAAgB,IAAI,cAAc,EAAE,CAAC;4BACxC,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;wBACtC,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACtC,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,iFAAiF;YACjF,IAAA,gBAAM,EAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,yDAAyD,EAAE;QAClE,IAAA,YAAE,EAAC,wEAAwE,EAAE;YAC3E,sCAAsC;YACtC,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,SAAS,GAAG,EAAE,CAAC;YAErB,SAAS,aAAa,CAAC,GAAW;gBAChC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,aAAa,CAAC,QAAQ,CAAC,CAAC;oBAC1B,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAC3B,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,aAAa,CAAC,aAAa,CAAC,CAAC;YAE7B,IAAM,eAAe,GAAG,EAAE,CAAC;YAE3B,SAAS,CAAC,OAAO,CAAC,UAAA,QAAQ;gBACxB,IAAI,CAAC;oBACH,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;oBAClD,IAAM,SAAS,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,uDAAuD,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;oBACxG,IAAM,aAAa,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;oBAElF,mEAAmE;oBACnE,IAAI,SAAS,GAAG,aAAa,GAAG,CAAC,IAAI,SAAS,GAAG,EAAE,EAAE,CAAC;wBACpD,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,WAAA,EAAE,aAAa,eAAA,EAAE,CAAC,CAAC;oBACrE,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,gCAAgC;gBAClC,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,wDAAwD;YACxD,IAAA,gBAAM,EAAC,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,oEAAoE,EAAE;YACvE,kDAAkD;YAClD,IAAM,cAAc,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,gBAAgB,CAAC,CAAC;YAElE,IAAI,YAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;gBAClC,IAAM,aAAa,GAAG,YAAE,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBAE9D,wBAAwB;gBACxB,IAAM,YAAY,GAAG,aAAa,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;gBACjE,IAAM,OAAO,GAAG,YAAY,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,uBAAuB;gBAExF,2FAA2F;gBAC3F,IAAA,gBAAM,EAAC,OAAO,CAAC,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC;gBAE9C,oDAAoD;gBACpD,IAAM,UAAU,GAAG,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACpF,IAAI,UAAU,EAAE,CAAC;oBACf,IAAA,gBAAM,EAAC,OAAO,CAAC,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC,CAAC,uCAAuC;gBACxF,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,6CAA6C;gBAC7C,IAAA,gBAAM,EAAC,YAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,2DAA2D,EAAE;QACpE,IAAA,YAAE,EAAC,2DAA2D,EAAE;YAC9D,2DAA2D;YAC3D,IAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,eAAe,CAAC,CAAC;YAE5D,IAAI,YAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC7B,IAAM,cAAY,GAAG,YAAE,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;gBAExD,6CAA6C;gBAC7C,IAAM,eAAe,GAAG;oBACtB,cAAc;oBACd,iBAAiB;oBACjB,cAAc;oBACd,UAAU;iBACX,CAAC;gBAEF,IAAM,cAAc,GAAG,eAAe,CAAC,MAAM,CAAC,UAAA,MAAM;oBAClD,OAAA,CAAC,cAAY,CAAC,QAAQ,CAAC,sBAAe,MAAM,CAAE,CAAC;gBAA/C,CAA+C,CAChD,CAAC;gBAEF,4EAA4E;gBAC5E,IAAA,gBAAM,EAAC,cAAc,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAEtC,0DAA0D;gBAC1D,IAAM,mBAAmB,GAAG,cAAY,CAAC,QAAQ,CAAC,aAAa,CAAC;oBACrC,cAAY,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;gBAEnE,8EAA8E;gBAC9E,IAAA,gBAAM,EAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1C,CAAC;iBAAM,CAAC;gBACN,iDAAiD;gBACjD,IAAA,gBAAM,EAAC,YAAE,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,qDAAqD,EAAE;YACxD,2CAA2C;YAC3C,IAAM,UAAU,GAAG;gBACjB,eAAe;gBACf,2BAA2B;aAC5B,CAAC;YAEF,IAAM,oBAAoB,GAAG,EAAE,CAAC;YAEhC,UAAU,CAAC,OAAO,CAAC,UAAA,SAAS;gBAC1B,IAAM,SAAS,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,SAAS,CAAC,CAAC;gBAEtD,IAAI,YAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC7B,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;oBAEnD,2CAA2C;oBAC3C,IAAI,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;wBAClE,oBAAoB,CAAC,IAAI,CAAC,UAAG,SAAS,0CAAuC,CAAC,CAAC;oBACjF,CAAC;oBAED,2CAA2C;oBAC3C,IAAI,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;wBAC/D,oBAAoB,CAAC,IAAI,CAAC,UAAG,SAAS,qCAAkC,CAAC,CAAC;oBAC5E,CAAC;oBAED,qCAAqC;oBACrC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACtE,oBAAoB,CAAC,IAAI,CAAC,UAAG,SAAS,qCAAkC,CAAC,CAAC;oBAC5E,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,+DAA+D;YAC/D,IAAA,gBAAM,EAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,2DAA2D,EAAE;QACpE,IAAA,YAAE,EAAC,qDAAqD,EAAE;YACxD,8BAA8B;YAC9B,IAAM,gBAAgB,GAAG;gBACvB,UAAU,EAAE,GAAG;gBACf,kBAAkB,EAAE,MAAM,EAAE,YAAY;gBACxC,SAAS,EAAE;oBACT,EAAE,IAAI,EAAE,sBAAsB,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,YAAY;oBAChE,EAAE,IAAI,EAAE,0BAA0B,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,cAAc;oBACrE,EAAE,IAAI,EAAE,uBAAuB,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC,WAAW;iBAC/D;aACF,CAAC;YAEF,8BAA8B;YAC9B,IAAM,eAAe,GAAG,gBAAgB,CAAC,kBAAkB,GAAG,gBAAgB,CAAC,UAAU,CAAC;YAE1F,+DAA+D;YAC/D,IAAA,gBAAM,EAAC,eAAe,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAE3C,wDAAwD;YACxD,IAAM,kBAAkB,GAAG,gBAAgB,CAAC,SAAS,CAAC,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,QAAQ,GAAG,KAAK,EAArB,CAAqB,CAAC,CAAC;YAC5F,IAAA,gBAAM,EAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,iDAAiD,EAAE;YACpD,0CAA0C;YAC1C,IAAM,aAAa,GAAG;gBACpB,aAAa,EAAE,EAAE,EAAE,KAAK;gBACxB,UAAU,EAAE,IAAI,EAAE,gBAAgB;gBAClC,WAAW,EAAE,IAAI,EAAE,mBAAmB;gBACtC,SAAS,EAAE,GAAG;aACf,CAAC;YAEF,IAAM,YAAY,GAAG,aAAa,CAAC,WAAW,GAAG,aAAa,CAAC,aAAa,CAAC;YAC7E,IAAM,aAAa,GAAG,YAAY,GAAG,aAAa,CAAC,SAAS,CAAC;YAE7D,uDAAuD;YACvD,IAAA,gBAAM,EAAC,YAAY,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,kCAAkC;YAC1E,IAAA,gBAAM,EAAC,aAAa,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,wCAAwC;YAC/E,IAAA,gBAAM,EAAC,aAAa,CAAC,UAAU,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,2BAA2B;QAClF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,8DAA8D,EAAE;QACvE,IAAA,YAAE,EAAC,6DAA6D,EAAE;YAChE,kCAAkC;YAClC,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,SAAS,GAAG,EAAE,CAAC;YAErB,SAAS,gBAAgB,CAAC,GAAW;gBACnC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,gBAAgB,CAAC,QAAQ,CAAC,CAAC;oBAC7B,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;wBAC9D,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACvB,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAEhC,uCAAuC;YACvC,IAAM,YAAY,GAAG,EAAE,CAAC;YACxB,IAAM,aAAa,GAAG;gBACpB,0BAA0B;gBAC1B,0BAA0B;aAC3B,CAAC;YAEF,SAAS,CAAC,OAAO,CAAC,UAAA,IAAI;gBACpB,IAAM,eAAe,GAAG,aAAa,CAAC,IAAI,CAAC,UAAA,OAAO,IAAI,OAAA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAlB,CAAkB,CAAC,CAAC;gBAC1E,IAAI,CAAC,eAAe,EAAE,CAAC;oBACrB,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1B,CAAC;gBAED,2DAA2D;gBAC3D,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAApB,CAAoB,CAAC,EAAE,CAAC;oBACzE,YAAY,CAAC,IAAI,CAAC,+BAAwB,IAAI,CAAE,CAAC,CAAC;gBACpD,CAAC;YACH,CAAC,CAAC,CAAC;YAEH,+EAA+E;YAC/E,IAAA,gBAAM,EAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,8DAA8D,EAAE;YACjE,4CAA4C;YAC5C,IAAM,aAAa,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,WAAW,CAAC,CAAC;YAC5D,IAAM,iBAAiB,GAAG,EAAE,CAAC;YAE7B,SAAS,sBAAsB,CAAC,GAAW;gBACzC,IAAI,CAAC,YAAE,CAAC,UAAU,CAAC,GAAG,CAAC;oBAAE,OAAO;gBAEhC,IAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAClC,KAAK,CAAC,OAAO,CAAC,UAAA,IAAI;oBAChB,IAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;oBACtC,IAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;wBACvB,sBAAsB,CAAC,QAAQ,CAAC,CAAC;oBACnC,CAAC;yBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;wBACnE,IAAI,CAAC;4BACH,IAAM,OAAO,GAAG,YAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BAElD,qCAAqC;4BACrC,IAAM,cAAc,GAAG,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;4BACzE,IAAM,iBAAiB,GAAG,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;4BACxD,IAAM,mBAAmB,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;4BAE3E,IAAI,CAAC,cAAc,IAAI,CAAC,iBAAiB,IAAI,CAAC,mBAAmB,EAAE,CAAC;gCAClE,iBAAiB,CAAC,IAAI,CAAC;oCACrB,IAAI,EAAE,QAAQ;oCACd,cAAc,gBAAA;oCACd,iBAAiB,mBAAA;oCACjB,mBAAmB,qBAAA;iCACpB,CAAC,CAAC;4BACL,CAAC;wBACH,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,gCAAgC;wBAClC,CAAC;oBACH,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,sBAAsB,CAAC,aAAa,CAAC,CAAC;YAEtC,iEAAiE;YACjE,IAAA,gBAAM,EAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,mDAAmD,EAAE;QAC5D,IAAA,YAAE,EAAC,wDAAwD,EAAE;YAC3D,IAAM,eAAe,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,cAAc,CAAC,CAAC;YAEjE,IAAI,YAAE,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE,CAAC;gBACnC,IAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,YAAE,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC;gBACzE,IAAM,SAAO,GAAG,WAAW,CAAC,OAAO,IAAI,EAAE,CAAC;gBAE1C,6DAA6D;gBAC7D,IAAM,mBAAmB,GAAG;oBAC1B,MAAM;oBACN,eAAe;oBACf,YAAY;oBACZ,SAAS;oBACT,kBAAkB;oBAClB,UAAU;iBACX,CAAC;gBAEF,IAAM,cAAc,GAAG,mBAAmB,CAAC,MAAM,CAAC,UAAA,MAAM,IAAI,OAAA,CAAC,SAAO,CAAC,MAAM,CAAC,EAAhB,CAAgB,CAAC,CAAC;gBAE9E,gEAAgE;gBAChE,IAAA,gBAAM,EAAC,cAAc,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAEtC,oCAAoC;gBACpC,IAAM,QAAQ,GAAG,SAAO,CAAC,SAAS,CAAC,CAAC;gBACpC,IAAI,QAAQ,EAAE,CAAC;oBACb,IAAM,oBAAoB,GAAG,QAAQ,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;oBAC7D,IAAM,gBAAgB,GAAG,QAAQ,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;oBAE/D,IAAA,gBAAM,EAAC,oBAAoB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACxC,IAAA,gBAAM,EAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACtC,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,8CAA8C;gBAC9C,IAAA,gBAAM,EAAC,YAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,oDAAoD,EAAE;YACvD,IAAM,cAAc,GAAG,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,gBAAgB,CAAC,CAAC;YAElE,IAAI,YAAE,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC;gBAClC,IAAM,aAAa,GAAG,YAAE,CAAC,YAAY,CAAC,cAAc,EAAE,MAAM,CAAC,CAAC;gBAE9D,8BAA8B;gBAC9B,IAAM,cAAc,GAAG,aAAa,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;gBAE9F,IAAI,cAAc,EAAE,CAAC;oBACnB,IAAM,UAAU,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;oBAErC,sCAAsC;oBACtC,IAAM,aAAa,GAAG,UAAU,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;oBAC5D,IAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;oBAC9D,IAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;oBACtD,IAAM,eAAe,GAAG,UAAU,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;oBAEhE,IAAM,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAChE,IAAM,SAAS,GAAG,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnE,IAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACvD,IAAM,UAAU,GAAG,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAEtE,iFAAiF;oBACjF,IAAA,gBAAM,EAAC,QAAQ,CAAC,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,yBAAyB;oBACtE,IAAA,gBAAM,EAAC,SAAS,CAAC,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,yBAAyB;oBACvE,IAAA,gBAAM,EAAC,KAAK,CAAC,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,yBAAyB;oBACnE,IAAA,gBAAM,EAAC,UAAU,CAAC,CAAC,sBAAsB,CAAC,EAAE,CAAC,CAAC,CAAC,yBAAyB;gBAC1E,CAAC;qBAAM,CAAC;oBACN,6DAA6D;oBAC7D,IAAA,gBAAM,EAAC,cAAc,CAAC,CAAC,UAAU,EAAE,CAAC;gBACtC,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,6CAA6C;gBAC7C,IAAA,gBAAM,EAAC,YAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnD,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/architecture/testing-infrastructure-issues.test.ts"], "sourcesContent": ["/**\n * Testing Infrastructure and Coverage Issues Tests\n * \n * These tests prove critical problems with the testing infrastructure,\n * coverage gaps, and testing best practices violations.\n * \n * EXPECTED TO FAIL - These tests demonstrate testing quality issues that need fixing.\n */\n\nimport { describe, it, expect, beforeEach, jest } from '@jest/globals';\nimport fs from 'fs';\nimport path from 'path';\n\ndescribe('Testing Infrastructure and Coverage Issues', () => {\n  beforeEach(() => {\n    jest.clearAllMocks();\n  });\n\n  describe('CRITICAL ISSUE 1: Test Coverage Gaps for Critical Components', () => {\n    it('should fail - critical authentication components lack comprehensive tests', () => {\n      // Critical authentication components that must have tests\n      const criticalAuthComponents = [\n        'src/lib/auth.tsx',\n        'src/lib/session-security.ts',\n        'src/lib/unified-session-management.ts',\n        'src/lib/user-validation-service.ts',\n        'src/components/LoginForm.tsx',\n        'src/components/SignupForm.tsx'\n      ];\n      \n      const missingTests = [];\n      const inadequateTests = [];\n      \n      criticalAuthComponents.forEach(component => {\n        const testFile = component.replace(/\\.(tsx?|jsx?)$/, '.test.$1');\n        const testPath = path.join(process.cwd(), testFile);\n        \n        if (!fs.existsSync(testPath)) {\n          missingTests.push(component);\n        } else {\n          try {\n            const testContent = fs.readFileSync(testPath, 'utf8');\n            const testCount = (testContent.match(/it\\(/g) || []).length;\n            const expectCount = (testContent.match(/expect\\(/g) || []).length;\n            \n            // Critical components should have at least 10 tests and 20 assertions\n            if (testCount < 10 || expectCount < 20) {\n              inadequateTests.push({ component, testCount, expectCount });\n            }\n          } catch (error) {\n            missingTests.push(component);\n          }\n        }\n      });\n      \n      // EXPECTED TO FAIL: All critical auth components should have comprehensive tests\n      expect(missingTests.length).toBe(0);\n      expect(inadequateTests.length).toBe(0);\n    });\n\n    it('should fail - API routes lack proper integration tests', () => {\n      // Critical API routes that must have integration tests\n      const criticalAPIRoutes = [\n        'src/app/api/auth/[...nextauth]/route.ts',\n        'src/app/api/assessment/route.ts',\n        'src/app/api/profile/route.ts',\n        'src/app/api/interview/route.ts',\n        'src/app/api/resources/route.ts'\n      ];\n      \n      const missingIntegrationTests = [];\n      \n      criticalAPIRoutes.forEach(route => {\n        const integrationTestFile = route.replace('src/app/api/', '__tests__/api/').replace('.ts', '.integration.test.ts');\n        const integrationTestPath = path.join(process.cwd(), integrationTestFile);\n        \n        if (!fs.existsSync(integrationTestPath)) {\n          missingIntegrationTests.push(route);\n        } else {\n          try {\n            const testContent = fs.readFileSync(integrationTestPath, 'utf8');\n            \n            // Integration tests should test real HTTP requests, not just mocked functions\n            const hasRealHTTPTests = testContent.includes('fetch(') || testContent.includes('request(');\n            const hasMockOveruse = (testContent.match(/\\.mock/g) || []).length > 5;\n            \n            if (!hasRealHTTPTests || hasMockOveruse) {\n              missingIntegrationTests.push(route);\n            }\n          } catch (error) {\n            missingIntegrationTests.push(route);\n          }\n        }\n      });\n      \n      // EXPECTED TO FAIL: All critical API routes should have proper integration tests\n      expect(missingIntegrationTests.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 2: Test Quality and Reliability Problems', () => {\n    it('should fail - tests over-rely on mocks instead of real implementations', () => {\n      // Analyze test files for mock overuse\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const testFiles = [];\n      \n      function findTestFiles(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            findTestFiles(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            testFiles.push(filePath);\n          }\n        });\n      }\n      \n      findTestFiles(testDirectory);\n      \n      const overMockedTests = [];\n      \n      testFiles.forEach(testFile => {\n        try {\n          const content = fs.readFileSync(testFile, 'utf8');\n          const mockCount = (content.match(/\\.mock|jest\\.mock|mockImplementation|mockReturnValue/g) || []).length;\n          const realCallCount = (content.match(/fetch\\(|prisma\\.|await.*\\(/g) || []).length;\n          \n          // If mocks outnumber real calls by more than 3:1, it's over-mocked\n          if (mockCount > realCallCount * 3 && mockCount > 10) {\n            overMockedTests.push({ file: testFile, mockCount, realCallCount });\n          }\n        } catch (error) {\n          // Skip files that can't be read\n        }\n      });\n      \n      // EXPECTED TO FAIL: Tests should not over-rely on mocks\n      expect(overMockedTests.length).toBe(0);\n    });\n\n    it('should fail - test timeouts are too aggressive for real operations', () => {\n      // Check Jest configuration for realistic timeouts\n      const jestConfigPath = path.join(process.cwd(), 'jest.config.js');\n      \n      if (fs.existsSync(jestConfigPath)) {\n        const configContent = fs.readFileSync(jestConfigPath, 'utf8');\n        \n        // Extract timeout value\n        const timeoutMatch = configContent.match(/testTimeout:\\s*(\\d+)/);\n        const timeout = timeoutMatch ? parseInt(timeoutMatch[1]) : 5000; // Default Jest timeout\n        \n        // EXPECTED TO FAIL: Timeout should be reasonable for real operations (at least 30 seconds)\n        expect(timeout).toBeGreaterThanOrEqual(30000);\n        \n        // Check for AI operations that need longer timeouts\n        const hasAITests = configContent.includes('ai') || configContent.includes('gemini');\n        if (hasAITests) {\n          expect(timeout).toBeGreaterThanOrEqual(60000); // AI operations need at least 1 minute\n        }\n      } else {\n        // EXPECTED TO FAIL: Jest config should exist\n        expect(fs.existsSync(jestConfigPath)).toBe(true);\n      }\n    });\n  });\n\n  describe('CRITICAL ISSUE 3: Test Environment Configuration Problems', () => {\n    it('should fail - test environment variables are inconsistent', () => {\n      // Check jest.setup.js for proper environment configuration\n      const setupPath = path.join(process.cwd(), 'jest.setup.js');\n      \n      if (fs.existsSync(setupPath)) {\n        const setupContent = fs.readFileSync(setupPath, 'utf8');\n        \n        // Required environment variables for testing\n        const requiredEnvVars = [\n          'NEXTAUTH_URL',\n          'NEXTAUTH_SECRET',\n          'DATABASE_URL',\n          'NODE_ENV'\n        ];\n        \n        const missingEnvVars = requiredEnvVars.filter(envVar => \n          !setupContent.includes(`process.env.${envVar}`)\n        );\n        \n        // EXPECTED TO FAIL: All required environment variables should be configured\n        expect(missingEnvVars.length).toBe(0);\n        \n        // Check for hardcoded test values that might cause issues\n        const hasHardcodedSecrets = setupContent.includes('test-secret') || \n                                   setupContent.includes('localhost:5432');\n        \n        // EXPECTED TO FAIL: Should not use hardcoded secrets in production-like tests\n        expect(hasHardcodedSecrets).toBe(false);\n      } else {\n        // EXPECTED TO FAIL: Jest setup file should exist\n        expect(fs.existsSync(setupPath)).toBe(true);\n      }\n    });\n\n    it('should fail - test database configuration is unsafe', () => {\n      // Check for proper test database isolation\n      const setupFiles = [\n        'jest.setup.js',\n        'jest.setup.integration.js'\n      ];\n      \n      const unsafeConfigurations = [];\n      \n      setupFiles.forEach(setupFile => {\n        const setupPath = path.join(process.cwd(), setupFile);\n        \n        if (fs.existsSync(setupPath)) {\n          const content = fs.readFileSync(setupPath, 'utf8');\n          \n          // Check for unsafe database configurations\n          if (content.includes('DATABASE_URL') && !content.includes('test')) {\n            unsafeConfigurations.push(`${setupFile}: DATABASE_URL doesn't include 'test'`);\n          }\n          \n          // Check for production database references\n          if (content.includes('production') || content.includes('prod')) {\n            unsafeConfigurations.push(`${setupFile}: Contains production references`);\n          }\n          \n          // Check for missing database cleanup\n          if (!content.includes('beforeEach') && !content.includes('afterEach')) {\n            unsafeConfigurations.push(`${setupFile}: Missing database cleanup hooks`);\n          }\n        }\n      });\n      \n      // EXPECTED TO FAIL: Test database configuration should be safe\n      expect(unsafeConfigurations.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 4: Test Performance and Scalability Issues', () => {\n    it('should fail - test suite execution time is too slow', () => {\n      // Mock test execution metrics\n      const testSuiteMetrics = {\n        totalTests: 150,\n        totalExecutionTime: 300000, // 5 minutes\n        slowTests: [\n          { name: 'AI integration tests', duration: 120000 }, // 2 minutes\n          { name: 'Database migration tests', duration: 90000 }, // 1.5 minutes\n          { name: 'End-to-end user flows', duration: 60000 } // 1 minute\n        ]\n      };\n      \n      // Calculate average test time\n      const averageTestTime = testSuiteMetrics.totalExecutionTime / testSuiteMetrics.totalTests;\n      \n      // EXPECTED TO FAIL: Average test time should be under 1 second\n      expect(averageTestTime).toBeLessThan(1000);\n      \n      // Individual tests should not take more than 30 seconds\n      const testsOverThreshold = testSuiteMetrics.slowTests.filter(test => test.duration > 30000);\n      expect(testsOverThreshold.length).toBe(0);\n    });\n\n    it('should fail - test memory usage grows unbounded', () => {\n      // Mock memory usage during test execution\n      const memoryMetrics = {\n        initialMemory: 50, // MB\n        peakMemory: 2000, // MB - too high\n        finalMemory: 1500, // MB - memory leak\n        testCount: 100\n      };\n      \n      const memoryGrowth = memoryMetrics.finalMemory - memoryMetrics.initialMemory;\n      const memoryPerTest = memoryGrowth / memoryMetrics.testCount;\n      \n      // EXPECTED TO FAIL: Memory growth should be reasonable\n      expect(memoryGrowth).toBeLessThan(500); // Should not grow more than 500MB\n      expect(memoryPerTest).toBeLessThan(5); // Should not use more than 5MB per test\n      expect(memoryMetrics.peakMemory).toBeLessThan(1000); // Peak should be under 1GB\n    });\n  });\n\n  describe('CRITICAL ISSUE 5: Test Organization and Maintenance Problems', () => {\n    it('should fail - test file naming conventions are inconsistent', () => {\n      // Check test file naming patterns\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const testFiles = [];\n      \n      function findAllTestFiles(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            findAllTestFiles(filePath);\n          } else if (file.includes('.test.') || file.includes('.spec.')) {\n            testFiles.push(file);\n          }\n        });\n      }\n      \n      findAllTestFiles(testDirectory);\n      \n      // Check for consistent naming patterns\n      const namingIssues = [];\n      const validPatterns = [\n        /\\.test\\.(ts|tsx|js|jsx)$/,\n        /\\.spec\\.(ts|tsx|js|jsx)$/\n      ];\n      \n      testFiles.forEach(file => {\n        const hasValidPattern = validPatterns.some(pattern => pattern.test(file));\n        if (!hasValidPattern) {\n          namingIssues.push(file);\n        }\n        \n        // Check for inconsistent naming (mixing .test. and .spec.)\n        if (file.includes('.test.') && testFiles.some(f => f.includes('.spec.'))) {\n          namingIssues.push(`Inconsistent naming: ${file}`);\n        }\n      });\n      \n      // EXPECTED TO FAIL: All test files should follow consistent naming conventions\n      expect(namingIssues.length).toBe(0);\n    });\n\n    it('should fail - test documentation and comments are inadequate', () => {\n      // Check test files for proper documentation\n      const testDirectory = path.join(process.cwd(), '__tests__');\n      const undocumentedTests = [];\n      \n      function checkTestDocumentation(dir: string) {\n        if (!fs.existsSync(dir)) return;\n        \n        const files = fs.readdirSync(dir);\n        files.forEach(file => {\n          const filePath = path.join(dir, file);\n          const stat = fs.statSync(filePath);\n          \n          if (stat.isDirectory()) {\n            checkTestDocumentation(filePath);\n          } else if (file.endsWith('.test.ts') || file.endsWith('.test.tsx')) {\n            try {\n              const content = fs.readFileSync(filePath, 'utf8');\n              \n              // Check for file-level documentation\n              const hasFileComment = content.includes('/**') || content.includes('/*');\n              const hasDescribeBlocks = content.includes('describe(');\n              const hasTestDescriptions = (content.match(/it\\(['\"`]/g) || []).length > 0;\n              \n              if (!hasFileComment || !hasDescribeBlocks || !hasTestDescriptions) {\n                undocumentedTests.push({\n                  file: filePath,\n                  hasFileComment,\n                  hasDescribeBlocks,\n                  hasTestDescriptions\n                });\n              }\n            } catch (error) {\n              // Skip files that can't be read\n            }\n          }\n        });\n      }\n      \n      checkTestDocumentation(testDirectory);\n      \n      // EXPECTED TO FAIL: All test files should be properly documented\n      expect(undocumentedTests.length).toBe(0);\n    });\n  });\n\n  describe('CRITICAL ISSUE 6: CI/CD Testing Pipeline Problems', () => {\n    it('should fail - package.json test scripts are incomplete', () => {\n      const packageJsonPath = path.join(process.cwd(), 'package.json');\n      \n      if (fs.existsSync(packageJsonPath)) {\n        const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));\n        const scripts = packageJson.scripts || {};\n        \n        // Required test scripts for a comprehensive testing pipeline\n        const requiredTestScripts = [\n          'test',\n          'test:coverage',\n          'test:watch',\n          'test:ci',\n          'test:integration',\n          'test:e2e'\n        ];\n        \n        const missingScripts = requiredTestScripts.filter(script => !scripts[script]);\n        \n        // EXPECTED TO FAIL: All required test scripts should be present\n        expect(missingScripts.length).toBe(0);\n        \n        // Check for proper CI configuration\n        const ciScript = scripts['test:ci'];\n        if (ciScript) {\n          const hasCoverageReporting = ciScript.includes('--coverage');\n          const hasWatchDisabled = ciScript.includes('--watchAll=false');\n          \n          expect(hasCoverageReporting).toBe(true);\n          expect(hasWatchDisabled).toBe(true);\n        }\n      } else {\n        // EXPECTED TO FAIL: package.json should exist\n        expect(fs.existsSync(packageJsonPath)).toBe(true);\n      }\n    });\n\n    it('should fail - test coverage thresholds are too low', () => {\n      const jestConfigPath = path.join(process.cwd(), 'jest.config.js');\n      \n      if (fs.existsSync(jestConfigPath)) {\n        const configContent = fs.readFileSync(jestConfigPath, 'utf8');\n        \n        // Extract coverage thresholds\n        const thresholdMatch = configContent.match(/coverageThreshold:\\s*{[^}]+global:\\s*{([^}]+)}/s);\n        \n        if (thresholdMatch) {\n          const thresholds = thresholdMatch[1];\n          \n          // Extract individual threshold values\n          const branchesMatch = thresholds.match(/branches:\\s*(\\d+)/);\n          const functionsMatch = thresholds.match(/functions:\\s*(\\d+)/);\n          const linesMatch = thresholds.match(/lines:\\s*(\\d+)/);\n          const statementsMatch = thresholds.match(/statements:\\s*(\\d+)/);\n          \n          const branches = branchesMatch ? parseInt(branchesMatch[1]) : 0;\n          const functions = functionsMatch ? parseInt(functionsMatch[1]) : 0;\n          const lines = linesMatch ? parseInt(linesMatch[1]) : 0;\n          const statements = statementsMatch ? parseInt(statementsMatch[1]) : 0;\n          \n          // EXPECTED TO FAIL: Coverage thresholds should be high for critical applications\n          expect(branches).toBeGreaterThanOrEqual(90); // Should be at least 90%\n          expect(functions).toBeGreaterThanOrEqual(95); // Should be at least 95%\n          expect(lines).toBeGreaterThanOrEqual(95); // Should be at least 95%\n          expect(statements).toBeGreaterThanOrEqual(95); // Should be at least 95%\n        } else {\n          // EXPECTED TO FAIL: Coverage thresholds should be configured\n          expect(thresholdMatch).toBeTruthy();\n        }\n      } else {\n        // EXPECTED TO FAIL: Jest config should exist\n        expect(fs.existsSync(jestConfigPath)).toBe(true);\n      }\n    });\n  });\n});\n"], "version": 3}