094455260be3ca75465c76d9ea5ebed2
"use strict";
'use client';

/* istanbul ignore next */
function cov_28vvjgow87() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/forum/posts/[postId]/page.tsx";
  var hash = "36d39a854befeaacb4b91d8f48425413a9d07d10";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/forum/posts/[postId]/page.tsx",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 15
        },
        end: {
          line: 13,
          column: 1
        }
      },
      "1": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 11,
          column: 6
        }
      },
      "2": {
        start: {
          line: 5,
          column: 8
        },
        end: {
          line: 9,
          column: 9
        }
      },
      "3": {
        start: {
          line: 5,
          column: 24
        },
        end: {
          line: 5,
          column: 25
        }
      },
      "4": {
        start: {
          line: 5,
          column: 31
        },
        end: {
          line: 5,
          column: 47
        }
      },
      "5": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 6,
          column: 29
        }
      },
      "6": {
        start: {
          line: 7,
          column: 12
        },
        end: {
          line: 8,
          column: 28
        }
      },
      "7": {
        start: {
          line: 7,
          column: 29
        },
        end: {
          line: 8,
          column: 28
        }
      },
      "8": {
        start: {
          line: 8,
          column: 16
        },
        end: {
          line: 8,
          column: 28
        }
      },
      "9": {
        start: {
          line: 10,
          column: 8
        },
        end: {
          line: 10,
          column: 17
        }
      },
      "10": {
        start: {
          line: 12,
          column: 4
        },
        end: {
          line: 12,
          column: 43
        }
      },
      "11": {
        start: {
          line: 14,
          column: 22
        },
        end: {
          line: 24,
          column: 3
        }
      },
      "12": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "13": {
        start: {
          line: 15,
          column: 26
        },
        end: {
          line: 15,
          column: 33
        }
      },
      "14": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 52
        }
      },
      "15": {
        start: {
          line: 17,
          column: 4
        },
        end: {
          line: 19,
          column: 5
        }
      },
      "16": {
        start: {
          line: 18,
          column: 6
        },
        end: {
          line: 18,
          column: 68
        }
      },
      "17": {
        start: {
          line: 18,
          column: 51
        },
        end: {
          line: 18,
          column: 63
        }
      },
      "18": {
        start: {
          line: 20,
          column: 4
        },
        end: {
          line: 20,
          column: 39
        }
      },
      "19": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 33
        }
      },
      "20": {
        start: {
          line: 22,
          column: 26
        },
        end: {
          line: 22,
          column: 33
        }
      },
      "21": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 23,
          column: 17
        }
      },
      "22": {
        start: {
          line: 25,
          column: 25
        },
        end: {
          line: 29,
          column: 2
        }
      },
      "23": {
        start: {
          line: 26,
          column: 4
        },
        end: {
          line: 26,
          column: 72
        }
      },
      "24": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 28,
          column: 21
        }
      },
      "25": {
        start: {
          line: 30,
          column: 19
        },
        end: {
          line: 46,
          column: 4
        }
      },
      "26": {
        start: {
          line: 31,
          column: 18
        },
        end: {
          line: 38,
          column: 5
        }
      },
      "27": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 36,
          column: 10
        }
      },
      "28": {
        start: {
          line: 33,
          column: 21
        },
        end: {
          line: 33,
          column: 23
        }
      },
      "29": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 95
        }
      },
      "30": {
        start: {
          line: 34,
          column: 29
        },
        end: {
          line: 34,
          column: 95
        }
      },
      "31": {
        start: {
          line: 34,
          column: 77
        },
        end: {
          line: 34,
          column: 95
        }
      },
      "32": {
        start: {
          line: 35,
          column: 12
        },
        end: {
          line: 35,
          column: 22
        }
      },
      "33": {
        start: {
          line: 37,
          column: 8
        },
        end: {
          line: 37,
          column: 26
        }
      },
      "34": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 45,
          column: 6
        }
      },
      "35": {
        start: {
          line: 40,
          column: 8
        },
        end: {
          line: 40,
          column: 46
        }
      },
      "36": {
        start: {
          line: 40,
          column: 35
        },
        end: {
          line: 40,
          column: 46
        }
      },
      "37": {
        start: {
          line: 41,
          column: 21
        },
        end: {
          line: 41,
          column: 23
        }
      },
      "38": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 42,
          column: 137
        }
      },
      "39": {
        start: {
          line: 42,
          column: 25
        },
        end: {
          line: 42,
          column: 137
        }
      },
      "40": {
        start: {
          line: 42,
          column: 38
        },
        end: {
          line: 42,
          column: 50
        }
      },
      "41": {
        start: {
          line: 42,
          column: 56
        },
        end: {
          line: 42,
          column: 57
        }
      },
      "42": {
        start: {
          line: 42,
          column: 78
        },
        end: {
          line: 42,
          column: 137
        }
      },
      "43": {
        start: {
          line: 42,
          column: 102
        },
        end: {
          line: 42,
          column: 137
        }
      },
      "44": {
        start: {
          line: 43,
          column: 8
        },
        end: {
          line: 43,
          column: 40
        }
      },
      "45": {
        start: {
          line: 44,
          column: 8
        },
        end: {
          line: 44,
          column: 22
        }
      },
      "46": {
        start: {
          line: 47,
          column: 16
        },
        end: {
          line: 55,
          column: 1
        }
      },
      "47": {
        start: {
          line: 48,
          column: 28
        },
        end: {
          line: 48,
          column: 110
        }
      },
      "48": {
        start: {
          line: 48,
          column: 91
        },
        end: {
          line: 48,
          column: 106
        }
      },
      "49": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 54,
          column: 7
        }
      },
      "50": {
        start: {
          line: 50,
          column: 36
        },
        end: {
          line: 50,
          column: 97
        }
      },
      "51": {
        start: {
          line: 50,
          column: 42
        },
        end: {
          line: 50,
          column: 70
        }
      },
      "52": {
        start: {
          line: 50,
          column: 85
        },
        end: {
          line: 50,
          column: 95
        }
      },
      "53": {
        start: {
          line: 51,
          column: 35
        },
        end: {
          line: 51,
          column: 100
        }
      },
      "54": {
        start: {
          line: 51,
          column: 41
        },
        end: {
          line: 51,
          column: 73
        }
      },
      "55": {
        start: {
          line: 51,
          column: 88
        },
        end: {
          line: 51,
          column: 98
        }
      },
      "56": {
        start: {
          line: 52,
          column: 32
        },
        end: {
          line: 52,
          column: 116
        }
      },
      "57": {
        start: {
          line: 53,
          column: 8
        },
        end: {
          line: 53,
          column: 78
        }
      },
      "58": {
        start: {
          line: 56,
          column: 18
        },
        end: {
          line: 82,
          column: 1
        }
      },
      "59": {
        start: {
          line: 57,
          column: 12
        },
        end: {
          line: 57,
          column: 104
        }
      },
      "60": {
        start: {
          line: 57,
          column: 43
        },
        end: {
          line: 57,
          column: 68
        }
      },
      "61": {
        start: {
          line: 57,
          column: 57
        },
        end: {
          line: 57,
          column: 68
        }
      },
      "62": {
        start: {
          line: 57,
          column: 69
        },
        end: {
          line: 57,
          column: 81
        }
      },
      "63": {
        start: {
          line: 57,
          column: 119
        },
        end: {
          line: 57,
          column: 196
        }
      },
      "64": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 58,
          column: 160
        }
      },
      "65": {
        start: {
          line: 58,
          column: 141
        },
        end: {
          line: 58,
          column: 153
        }
      },
      "66": {
        start: {
          line: 59,
          column: 23
        },
        end: {
          line: 59,
          column: 68
        }
      },
      "67": {
        start: {
          line: 59,
          column: 45
        },
        end: {
          line: 59,
          column: 65
        }
      },
      "68": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 61,
          column: 70
        }
      },
      "69": {
        start: {
          line: 61,
          column: 15
        },
        end: {
          line: 61,
          column: 70
        }
      },
      "70": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 79,
          column: 66
        }
      },
      "71": {
        start: {
          line: 62,
          column: 50
        },
        end: {
          line: 79,
          column: 66
        }
      },
      "72": {
        start: {
          line: 63,
          column: 12
        },
        end: {
          line: 63,
          column: 169
        }
      },
      "73": {
        start: {
          line: 63,
          column: 160
        },
        end: {
          line: 63,
          column: 169
        }
      },
      "74": {
        start: {
          line: 64,
          column: 12
        },
        end: {
          line: 64,
          column: 52
        }
      },
      "75": {
        start: {
          line: 64,
          column: 26
        },
        end: {
          line: 64,
          column: 52
        }
      },
      "76": {
        start: {
          line: 65,
          column: 12
        },
        end: {
          line: 77,
          column: 13
        }
      },
      "77": {
        start: {
          line: 66,
          column: 32
        },
        end: {
          line: 66,
          column: 39
        }
      },
      "78": {
        start: {
          line: 66,
          column: 40
        },
        end: {
          line: 66,
          column: 46
        }
      },
      "79": {
        start: {
          line: 67,
          column: 24
        },
        end: {
          line: 67,
          column: 34
        }
      },
      "80": {
        start: {
          line: 67,
          column: 35
        },
        end: {
          line: 67,
          column: 72
        }
      },
      "81": {
        start: {
          line: 68,
          column: 24
        },
        end: {
          line: 68,
          column: 34
        }
      },
      "82": {
        start: {
          line: 68,
          column: 35
        },
        end: {
          line: 68,
          column: 45
        }
      },
      "83": {
        start: {
          line: 68,
          column: 46
        },
        end: {
          line: 68,
          column: 55
        }
      },
      "84": {
        start: {
          line: 68,
          column: 56
        },
        end: {
          line: 68,
          column: 65
        }
      },
      "85": {
        start: {
          line: 69,
          column: 24
        },
        end: {
          line: 69,
          column: 41
        }
      },
      "86": {
        start: {
          line: 69,
          column: 42
        },
        end: {
          line: 69,
          column: 55
        }
      },
      "87": {
        start: {
          line: 69,
          column: 56
        },
        end: {
          line: 69,
          column: 65
        }
      },
      "88": {
        start: {
          line: 71,
          column: 20
        },
        end: {
          line: 71,
          column: 128
        }
      },
      "89": {
        start: {
          line: 71,
          column: 110
        },
        end: {
          line: 71,
          column: 116
        }
      },
      "90": {
        start: {
          line: 71,
          column: 117
        },
        end: {
          line: 71,
          column: 126
        }
      },
      "91": {
        start: {
          line: 72,
          column: 20
        },
        end: {
          line: 72,
          column: 106
        }
      },
      "92": {
        start: {
          line: 72,
          column: 81
        },
        end: {
          line: 72,
          column: 97
        }
      },
      "93": {
        start: {
          line: 72,
          column: 98
        },
        end: {
          line: 72,
          column: 104
        }
      },
      "94": {
        start: {
          line: 73,
          column: 20
        },
        end: {
          line: 73,
          column: 89
        }
      },
      "95": {
        start: {
          line: 73,
          column: 57
        },
        end: {
          line: 73,
          column: 72
        }
      },
      "96": {
        start: {
          line: 73,
          column: 73
        },
        end: {
          line: 73,
          column: 80
        }
      },
      "97": {
        start: {
          line: 73,
          column: 81
        },
        end: {
          line: 73,
          column: 87
        }
      },
      "98": {
        start: {
          line: 74,
          column: 20
        },
        end: {
          line: 74,
          column: 87
        }
      },
      "99": {
        start: {
          line: 74,
          column: 47
        },
        end: {
          line: 74,
          column: 62
        }
      },
      "100": {
        start: {
          line: 74,
          column: 63
        },
        end: {
          line: 74,
          column: 78
        }
      },
      "101": {
        start: {
          line: 74,
          column: 79
        },
        end: {
          line: 74,
          column: 85
        }
      },
      "102": {
        start: {
          line: 75,
          column: 20
        },
        end: {
          line: 75,
          column: 42
        }
      },
      "103": {
        start: {
          line: 75,
          column: 30
        },
        end: {
          line: 75,
          column: 42
        }
      },
      "104": {
        start: {
          line: 76,
          column: 20
        },
        end: {
          line: 76,
          column: 33
        }
      },
      "105": {
        start: {
          line: 76,
          column: 34
        },
        end: {
          line: 76,
          column: 43
        }
      },
      "106": {
        start: {
          line: 78,
          column: 12
        },
        end: {
          line: 78,
          column: 39
        }
      },
      "107": {
        start: {
          line: 79,
          column: 22
        },
        end: {
          line: 79,
          column: 34
        }
      },
      "108": {
        start: {
          line: 79,
          column: 35
        },
        end: {
          line: 79,
          column: 41
        }
      },
      "109": {
        start: {
          line: 79,
          column: 54
        },
        end: {
          line: 79,
          column: 64
        }
      },
      "110": {
        start: {
          line: 80,
          column: 8
        },
        end: {
          line: 80,
          column: 35
        }
      },
      "111": {
        start: {
          line: 80,
          column: 23
        },
        end: {
          line: 80,
          column: 35
        }
      },
      "112": {
        start: {
          line: 80,
          column: 36
        },
        end: {
          line: 80,
          column: 89
        }
      },
      "113": {
        start: {
          line: 83,
          column: 20
        },
        end: {
          line: 91,
          column: 1
        }
      },
      "114": {
        start: {
          line: 84,
          column: 4
        },
        end: {
          line: 89,
          column: 5
        }
      },
      "115": {
        start: {
          line: 84,
          column: 40
        },
        end: {
          line: 89,
          column: 5
        }
      },
      "116": {
        start: {
          line: 84,
          column: 53
        },
        end: {
          line: 84,
          column: 54
        }
      },
      "117": {
        start: {
          line: 84,
          column: 60
        },
        end: {
          line: 84,
          column: 71
        }
      },
      "118": {
        start: {
          line: 85,
          column: 8
        },
        end: {
          line: 88,
          column: 9
        }
      },
      "119": {
        start: {
          line: 86,
          column: 12
        },
        end: {
          line: 86,
          column: 65
        }
      },
      "120": {
        start: {
          line: 86,
          column: 21
        },
        end: {
          line: 86,
          column: 65
        }
      },
      "121": {
        start: {
          line: 87,
          column: 12
        },
        end: {
          line: 87,
          column: 28
        }
      },
      "122": {
        start: {
          line: 90,
          column: 4
        },
        end: {
          line: 90,
          column: 61
        }
      },
      "123": {
        start: {
          line: 92,
          column: 22
        },
        end: {
          line: 94,
          column: 1
        }
      },
      "124": {
        start: {
          line: 93,
          column: 4
        },
        end: {
          line: 93,
          column: 62
        }
      },
      "125": {
        start: {
          line: 95,
          column: 0
        },
        end: {
          line: 95,
          column: 62
        }
      },
      "126": {
        start: {
          line: 96,
          column: 0
        },
        end: {
          line: 96,
          column: 32
        }
      },
      "127": {
        start: {
          line: 97,
          column: 20
        },
        end: {
          line: 97,
          column: 48
        }
      },
      "128": {
        start: {
          line: 98,
          column: 14
        },
        end: {
          line: 98,
          column: 44
        }
      },
      "129": {
        start: {
          line: 99,
          column: 14
        },
        end: {
          line: 99,
          column: 40
        }
      },
      "130": {
        start: {
          line: 100,
          column: 19
        },
        end: {
          line: 100,
          column: 45
        }
      },
      "131": {
        start: {
          line: 101,
          column: 13
        },
        end: {
          line: 101,
          column: 50
        }
      },
      "132": {
        start: {
          line: 102,
          column: 21
        },
        end: {
          line: 102,
          column: 44
        }
      },
      "133": {
        start: {
          line: 103,
          column: 15
        },
        end: {
          line: 103,
          column: 48
        }
      },
      "134": {
        start: {
          line: 104,
          column: 24
        },
        end: {
          line: 104,
          column: 50
        }
      },
      "135": {
        start: {
          line: 105,
          column: 24
        },
        end: {
          line: 105,
          column: 86
        }
      },
      "136": {
        start: {
          line: 106,
          column: 23
        },
        end: {
          line: 106,
          column: 84
        }
      },
      "137": {
        start: {
          line: 107,
          column: 16
        },
        end: {
          line: 107,
          column: 42
        }
      },
      "138": {
        start: {
          line: 108,
          column: 28
        },
        end: {
          line: 108,
          column: 74
        }
      },
      "139": {
        start: {
          line: 110,
          column: 16
        },
        end: {
          line: 110,
          column: 20
        }
      },
      "140": {
        start: {
          line: 111,
          column: 17
        },
        end: {
          line: 111,
          column: 26
        }
      },
      "141": {
        start: {
          line: 112,
          column: 17
        },
        end: {
          line: 112,
          column: 49
        }
      },
      "142": {
        start: {
          line: 113,
          column: 17
        },
        end: {
          line: 113,
          column: 46
        }
      },
      "143": {
        start: {
          line: 114,
          column: 13
        },
        end: {
          line: 114,
          column: 40
        }
      },
      "144": {
        start: {
          line: 114,
          column: 49
        },
        end: {
          line: 114,
          column: 54
        }
      },
      "145": {
        start: {
          line: 114,
          column: 66
        },
        end: {
          line: 114,
          column: 71
        }
      },
      "146": {
        start: {
          line: 115,
          column: 13
        },
        end: {
          line: 115,
          column: 40
        }
      },
      "147": {
        start: {
          line: 115,
          column: 52
        },
        end: {
          line: 115,
          column: 57
        }
      },
      "148": {
        start: {
          line: 115,
          column: 72
        },
        end: {
          line: 115,
          column: 77
        }
      },
      "149": {
        start: {
          line: 116,
          column: 13
        },
        end: {
          line: 116,
          column: 40
        }
      },
      "150": {
        start: {
          line: 116,
          column: 50
        },
        end: {
          line: 116,
          column: 55
        }
      },
      "151": {
        start: {
          line: 116,
          column: 68
        },
        end: {
          line: 116,
          column: 73
        }
      },
      "152": {
        start: {
          line: 117,
          column: 13
        },
        end: {
          line: 117,
          column: 41
        }
      },
      "153": {
        start: {
          line: 117,
          column: 63
        },
        end: {
          line: 117,
          column: 68
        }
      },
      "154": {
        start: {
          line: 117,
          column: 93
        },
        end: {
          line: 117,
          column: 98
        }
      },
      "155": {
        start: {
          line: 118,
          column: 13
        },
        end: {
          line: 118,
          column: 37
        }
      },
      "156": {
        start: {
          line: 118,
          column: 52
        },
        end: {
          line: 118,
          column: 65
        }
      },
      "157": {
        start: {
          line: 118,
          column: 81
        },
        end: {
          line: 118,
          column: 93
        }
      },
      "158": {
        start: {
          line: 119,
          column: 13
        },
        end: {
          line: 119,
          column: 45
        }
      },
      "159": {
        start: {
          line: 119,
          column: 58
        },
        end: {
          line: 119,
          column: 69
        }
      },
      "160": {
        start: {
          line: 119,
          column: 86
        },
        end: {
          line: 119,
          column: 101
        }
      },
      "161": {
        start: {
          line: 119,
          column: 112
        },
        end: {
          line: 119,
          column: 131
        }
      },
      "162": {
        start: {
          line: 119,
          column: 141
        },
        end: {
          line: 119,
          column: 149
        }
      },
      "163": {
        start: {
          line: 119,
          column: 159
        },
        end: {
          line: 119,
          column: 167
        }
      },
      "164": {
        start: {
          line: 120,
          column: 23
        },
        end: {
          line: 120,
          column: 43
        }
      },
      "165": {
        start: {
          line: 121,
          column: 20
        },
        end: {
          line: 161,
          column: 20
        }
      },
      "166": {
        start: {
          line: 121,
          column: 59
        },
        end: {
          line: 161,
          column: 7
        }
      },
      "167": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 160,
          column: 11
        }
      },
      "168": {
        start: {
          line: 124,
          column: 12
        },
        end: {
          line: 159,
          column: 13
        }
      },
      "169": {
        start: {
          line: 126,
          column: 20
        },
        end: {
          line: 126,
          column: 47
        }
      },
      "170": {
        start: {
          line: 127,
          column: 20
        },
        end: {
          line: 127,
          column: 37
        }
      },
      "171": {
        start: {
          line: 128,
          column: 20
        },
        end: {
          line: 128,
          column: 49
        }
      },
      "172": {
        start: {
          line: 130,
          column: 20
        },
        end: {
          line: 130,
          column: 47
        }
      },
      "173": {
        start: {
          line: 131,
          column: 20
        },
        end: {
          line: 131,
          column: 99
        }
      },
      "174": {
        start: {
          line: 133,
          column: 20
        },
        end: {
          line: 133,
          column: 41
        }
      },
      "175": {
        start: {
          line: 134,
          column: 20
        },
        end: {
          line: 140,
          column: 21
        }
      },
      "176": {
        start: {
          line: 135,
          column: 24
        },
        end: {
          line: 138,
          column: 25
        }
      },
      "177": {
        start: {
          line: 136,
          column: 28
        },
        end: {
          line: 136,
          column: 55
        }
      },
      "178": {
        start: {
          line: 137,
          column: 28
        },
        end: {
          line: 137,
          column: 50
        }
      },
      "179": {
        start: {
          line: 139,
          column: 24
        },
        end: {
          line: 139,
          column: 64
        }
      },
      "180": {
        start: {
          line: 141,
          column: 20
        },
        end: {
          line: 141,
          column: 58
        }
      },
      "181": {
        start: {
          line: 143,
          column: 20
        },
        end: {
          line: 143,
          column: 39
        }
      },
      "182": {
        start: {
          line: 144,
          column: 20
        },
        end: {
          line: 147,
          column: 21
        }
      },
      "183": {
        start: {
          line: 145,
          column: 24
        },
        end: {
          line: 145,
          column: 51
        }
      },
      "184": {
        start: {
          line: 146,
          column: 24
        },
        end: {
          line: 146,
          column: 46
        }
      },
      "185": {
        start: {
          line: 148,
          column: 20
        },
        end: {
          line: 148,
          column: 41
        }
      },
      "186": {
        start: {
          line: 149,
          column: 20
        },
        end: {
          line: 149,
          column: 44
        }
      },
      "187": {
        start: {
          line: 151,
          column: 20
        },
        end: {
          line: 151,
          column: 38
        }
      },
      "188": {
        start: {
          line: 152,
          column: 20
        },
        end: {
          line: 152,
          column: 65
        }
      },
      "189": {
        start: {
          line: 153,
          column: 20
        },
        end: {
          line: 153,
          column: 52
        }
      },
      "190": {
        start: {
          line: 154,
          column: 20
        },
        end: {
          line: 154,
          column: 44
        }
      },
      "191": {
        start: {
          line: 156,
          column: 20
        },
        end: {
          line: 156,
          column: 38
        }
      },
      "192": {
        start: {
          line: 157,
          column: 20
        },
        end: {
          line: 157,
          column: 46
        }
      },
      "193": {
        start: {
          line: 158,
          column: 24
        },
        end: {
          line: 158,
          column: 46
        }
      },
      "194": {
        start: {
          line: 162,
          column: 4
        },
        end: {
          line: 170,
          column: 36
        }
      },
      "195": {
        start: {
          line: 163,
          column: 8
        },
        end: {
          line: 166,
          column: 9
        }
      },
      "196": {
        start: {
          line: 164,
          column: 12
        },
        end: {
          line: 164,
          column: 34
        }
      },
      "197": {
        start: {
          line: 165,
          column: 12
        },
        end: {
          line: 165,
          column: 19
        }
      },
      "198": {
        start: {
          line: 167,
          column: 8
        },
        end: {
          line: 169,
          column: 9
        }
      },
      "199": {
        start: {
          line: 168,
          column: 12
        },
        end: {
          line: 168,
          column: 24
        }
      },
      "200": {
        start: {
          line: 171,
          column: 24
        },
        end: {
          line: 222,
          column: 9
        }
      },
      "201": {
        start: {
          line: 171,
          column: 42
        },
        end: {
          line: 222,
          column: 7
        }
      },
      "202": {
        start: {
          line: 173,
          column: 8
        },
        end: {
          line: 221,
          column: 11
        }
      },
      "203": {
        start: {
          line: 174,
          column: 12
        },
        end: {
          line: 220,
          column: 13
        }
      },
      "204": {
        start: {
          line: 176,
          column: 20
        },
        end: {
          line: 179,
          column: 21
        }
      },
      "205": {
        start: {
          line: 177,
          column: 24
        },
        end: {
          line: 177,
          column: 67
        }
      },
      "206": {
        start: {
          line: 178,
          column: 24
        },
        end: {
          line: 178,
          column: 46
        }
      },
      "207": {
        start: {
          line: 180,
          column: 20
        },
        end: {
          line: 180,
          column: 47
        }
      },
      "208": {
        start: {
          line: 181,
          column: 20
        },
        end: {
          line: 181,
          column: 35
        }
      },
      "209": {
        start: {
          line: 182,
          column: 20
        },
        end: {
          line: 182,
          column: 33
        }
      },
      "210": {
        start: {
          line: 184,
          column: 20
        },
        end: {
          line: 184,
          column: 47
        }
      },
      "211": {
        start: {
          line: 185,
          column: 20
        },
        end: {
          line: 185,
          column: 49
        }
      },
      "212": {
        start: {
          line: 187,
          column: 20
        },
        end: {
          line: 187,
          column: 47
        }
      },
      "213": {
        start: {
          line: 188,
          column: 20
        },
        end: {
          line: 194,
          column: 28
        }
      },
      "214": {
        start: {
          line: 196,
          column: 20
        },
        end: {
          line: 196,
          column: 41
        }
      },
      "215": {
        start: {
          line: 197,
          column: 20
        },
        end: {
          line: 197,
          column: 63
        }
      },
      "216": {
        start: {
          line: 197,
          column: 39
        },
        end: {
          line: 197,
          column: 63
        }
      },
      "217": {
        start: {
          line: 198,
          column: 20
        },
        end: {
          line: 198,
          column: 58
        }
      },
      "218": {
        start: {
          line: 200,
          column: 20
        },
        end: {
          line: 200,
          column: 42
        }
      },
      "219": {
        start: {
          line: 201,
          column: 20
        },
        end: {
          line: 201,
          column: 81
        }
      },
      "220": {
        start: {
          line: 202,
          column: 24
        },
        end: {
          line: 202,
          column: 62
        }
      },
      "221": {
        start: {
          line: 204,
          column: 20
        },
        end: {
          line: 204,
          column: 41
        }
      },
      "222": {
        start: {
          line: 206,
          column: 20
        },
        end: {
          line: 208,
          column: 21
        }
      },
      "223": {
        start: {
          line: 207,
          column: 24
        },
        end: {
          line: 207,
          column: 148
        }
      },
      "224": {
        start: {
          line: 209,
          column: 20
        },
        end: {
          line: 209,
          column: 28
        }
      },
      "225": {
        start: {
          line: 210,
          column: 20
        },
        end: {
          line: 210,
          column: 44
        }
      },
      "226": {
        start: {
          line: 212,
          column: 20
        },
        end: {
          line: 212,
          column: 38
        }
      },
      "227": {
        start: {
          line: 213,
          column: 20
        },
        end: {
          line: 213,
          column: 66
        }
      },
      "228": {
        start: {
          line: 214,
          column: 20
        },
        end: {
          line: 214,
          column: 96
        }
      },
      "229": {
        start: {
          line: 215,
          column: 20
        },
        end: {
          line: 215,
          column: 44
        }
      },
      "230": {
        start: {
          line: 217,
          column: 20
        },
        end: {
          line: 217,
          column: 48
        }
      },
      "231": {
        start: {
          line: 218,
          column: 20
        },
        end: {
          line: 218,
          column: 46
        }
      },
      "232": {
        start: {
          line: 219,
          column: 24
        },
        end: {
          line: 219,
          column: 46
        }
      },
      "233": {
        start: {
          line: 223,
          column: 21
        },
        end: {
          line: 231,
          column: 5
        }
      },
      "234": {
        start: {
          line: 224,
          column: 8
        },
        end: {
          line: 230,
          column: 11
        }
      },
      "235": {
        start: {
          line: 232,
          column: 25
        },
        end: {
          line: 234,
          column: 5
        }
      },
      "236": {
        start: {
          line: 233,
          column: 8
        },
        end: {
          line: 233,
          column: 57
        }
      },
      "237": {
        start: {
          line: 235,
          column: 4
        },
        end: {
          line: 237,
          column: 5
        }
      },
      "238": {
        start: {
          line: 236,
          column: 8
        },
        end: {
          line: 236,
          column: 205
        }
      },
      "239": {
        start: {
          line: 238,
          column: 4
        },
        end: {
          line: 240,
          column: 5
        }
      },
      "240": {
        start: {
          line: 239,
          column: 8
        },
        end: {
          line: 239,
          column: 378
        }
      },
      "241": {
        start: {
          line: 241,
          column: 4
        },
        end: {
          line: 243,
          column: 5
        }
      },
      "242": {
        start: {
          line: 242,
          column: 8
        },
        end: {
          line: 242,
          column: 441
        }
      },
      "243": {
        start: {
          line: 244,
          column: 4
        },
        end: {
          line: 250,
          column: 1234
        }
      },
      "244": {
        start: {
          line: 244,
          column: 3064
        },
        end: {
          line: 244,
          column: 4078
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 3,
            column: 42
          },
          end: {
            line: 3,
            column: 43
          }
        },
        loc: {
          start: {
            line: 3,
            column: 54
          },
          end: {
            line: 13,
            column: 1
          }
        },
        line: 3
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 4,
            column: 32
          },
          end: {
            line: 4,
            column: 33
          }
        },
        loc: {
          start: {
            line: 4,
            column: 44
          },
          end: {
            line: 11,
            column: 5
          }
        },
        line: 4
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 14,
            column: 74
          },
          end: {
            line: 14,
            column: 75
          }
        },
        loc: {
          start: {
            line: 14,
            column: 96
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 14
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 18,
            column: 38
          },
          end: {
            line: 18,
            column: 39
          }
        },
        loc: {
          start: {
            line: 18,
            column: 49
          },
          end: {
            line: 18,
            column: 65
          }
        },
        line: 18
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 21,
            column: 6
          },
          end: {
            line: 21,
            column: 7
          }
        },
        loc: {
          start: {
            line: 21,
            column: 28
          },
          end: {
            line: 24,
            column: 1
          }
        },
        line: 21
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 25,
            column: 80
          },
          end: {
            line: 25,
            column: 81
          }
        },
        loc: {
          start: {
            line: 25,
            column: 95
          },
          end: {
            line: 27,
            column: 1
          }
        },
        line: 25
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 27,
            column: 5
          },
          end: {
            line: 27,
            column: 6
          }
        },
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 29,
            column: 1
          }
        },
        line: 27
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 30,
            column: 51
          },
          end: {
            line: 30,
            column: 52
          }
        },
        loc: {
          start: {
            line: 30,
            column: 63
          },
          end: {
            line: 46,
            column: 1
          }
        },
        line: 30
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 31,
            column: 18
          },
          end: {
            line: 31,
            column: 19
          }
        },
        loc: {
          start: {
            line: 31,
            column: 30
          },
          end: {
            line: 38,
            column: 5
          }
        },
        line: 31
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 32,
            column: 48
          },
          end: {
            line: 32,
            column: 49
          }
        },
        loc: {
          start: {
            line: 32,
            column: 61
          },
          end: {
            line: 36,
            column: 9
          }
        },
        line: 32
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 39,
            column: 11
          },
          end: {
            line: 39,
            column: 12
          }
        },
        loc: {
          start: {
            line: 39,
            column: 26
          },
          end: {
            line: 45,
            column: 5
          }
        },
        line: 39
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 47,
            column: 44
          },
          end: {
            line: 47,
            column: 45
          }
        },
        loc: {
          start: {
            line: 47,
            column: 89
          },
          end: {
            line: 55,
            column: 1
          }
        },
        line: 47
      },
      "12": {
        name: "adopt",
        decl: {
          start: {
            line: 48,
            column: 13
          },
          end: {
            line: 48,
            column: 18
          }
        },
        loc: {
          start: {
            line: 48,
            column: 26
          },
          end: {
            line: 48,
            column: 112
          }
        },
        line: 48
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 48,
            column: 70
          },
          end: {
            line: 48,
            column: 71
          }
        },
        loc: {
          start: {
            line: 48,
            column: 89
          },
          end: {
            line: 48,
            column: 108
          }
        },
        line: 48
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 49,
            column: 36
          },
          end: {
            line: 49,
            column: 37
          }
        },
        loc: {
          start: {
            line: 49,
            column: 63
          },
          end: {
            line: 54,
            column: 5
          }
        },
        line: 49
      },
      "15": {
        name: "fulfilled",
        decl: {
          start: {
            line: 50,
            column: 17
          },
          end: {
            line: 50,
            column: 26
          }
        },
        loc: {
          start: {
            line: 50,
            column: 34
          },
          end: {
            line: 50,
            column: 99
          }
        },
        line: 50
      },
      "16": {
        name: "rejected",
        decl: {
          start: {
            line: 51,
            column: 17
          },
          end: {
            line: 51,
            column: 25
          }
        },
        loc: {
          start: {
            line: 51,
            column: 33
          },
          end: {
            line: 51,
            column: 102
          }
        },
        line: 51
      },
      "17": {
        name: "step",
        decl: {
          start: {
            line: 52,
            column: 17
          },
          end: {
            line: 52,
            column: 21
          }
        },
        loc: {
          start: {
            line: 52,
            column: 30
          },
          end: {
            line: 52,
            column: 118
          }
        },
        line: 52
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 56,
            column: 48
          },
          end: {
            line: 56,
            column: 49
          }
        },
        loc: {
          start: {
            line: 56,
            column: 73
          },
          end: {
            line: 82,
            column: 1
          }
        },
        line: 56
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 57,
            column: 30
          },
          end: {
            line: 57,
            column: 31
          }
        },
        loc: {
          start: {
            line: 57,
            column: 41
          },
          end: {
            line: 57,
            column: 83
          }
        },
        line: 57
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 58,
            column: 128
          },
          end: {
            line: 58,
            column: 129
          }
        },
        loc: {
          start: {
            line: 58,
            column: 139
          },
          end: {
            line: 58,
            column: 155
          }
        },
        line: 58
      },
      "21": {
        name: "verb",
        decl: {
          start: {
            line: 59,
            column: 13
          },
          end: {
            line: 59,
            column: 17
          }
        },
        loc: {
          start: {
            line: 59,
            column: 21
          },
          end: {
            line: 59,
            column: 70
          }
        },
        line: 59
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 59,
            column: 30
          },
          end: {
            line: 59,
            column: 31
          }
        },
        loc: {
          start: {
            line: 59,
            column: 43
          },
          end: {
            line: 59,
            column: 67
          }
        },
        line: 59
      },
      "23": {
        name: "step",
        decl: {
          start: {
            line: 60,
            column: 13
          },
          end: {
            line: 60,
            column: 17
          }
        },
        loc: {
          start: {
            line: 60,
            column: 22
          },
          end: {
            line: 81,
            column: 5
          }
        },
        line: 60
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 83,
            column: 52
          },
          end: {
            line: 83,
            column: 53
          }
        },
        loc: {
          start: {
            line: 83,
            column: 78
          },
          end: {
            line: 91,
            column: 1
          }
        },
        line: 83
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 92,
            column: 56
          },
          end: {
            line: 92,
            column: 57
          }
        },
        loc: {
          start: {
            line: 92,
            column: 71
          },
          end: {
            line: 94,
            column: 1
          }
        },
        line: 92
      },
      "26": {
        name: "ForumPostPage",
        decl: {
          start: {
            line: 109,
            column: 9
          },
          end: {
            line: 109,
            column: 22
          }
        },
        loc: {
          start: {
            line: 109,
            column: 27
          },
          end: {
            line: 251,
            column: 1
          }
        },
        line: 109
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 121,
            column: 45
          },
          end: {
            line: 121,
            column: 46
          }
        },
        loc: {
          start: {
            line: 121,
            column: 57
          },
          end: {
            line: 161,
            column: 9
          }
        },
        line: 121
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 121,
            column: 99
          },
          end: {
            line: 121,
            column: 100
          }
        },
        loc: {
          start: {
            line: 121,
            column: 111
          },
          end: {
            line: 161,
            column: 5
          }
        },
        line: 121
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 123,
            column: 33
          },
          end: {
            line: 123,
            column: 34
          }
        },
        loc: {
          start: {
            line: 123,
            column: 47
          },
          end: {
            line: 160,
            column: 9
          }
        },
        line: 123
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 162,
            column: 27
          },
          end: {
            line: 162,
            column: 28
          }
        },
        loc: {
          start: {
            line: 162,
            column: 39
          },
          end: {
            line: 170,
            column: 5
          }
        },
        line: 162
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 171,
            column: 24
          },
          end: {
            line: 171,
            column: 25
          }
        },
        loc: {
          start: {
            line: 171,
            column: 40
          },
          end: {
            line: 222,
            column: 9
          }
        },
        line: 171
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 171,
            column: 82
          },
          end: {
            line: 171,
            column: 83
          }
        },
        loc: {
          start: {
            line: 171,
            column: 94
          },
          end: {
            line: 222,
            column: 5
          }
        },
        line: 171
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 173,
            column: 33
          },
          end: {
            line: 173,
            column: 34
          }
        },
        loc: {
          start: {
            line: 173,
            column: 47
          },
          end: {
            line: 221,
            column: 9
          }
        },
        line: 173
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 223,
            column: 21
          },
          end: {
            line: 223,
            column: 22
          }
        },
        loc: {
          start: {
            line: 223,
            column: 43
          },
          end: {
            line: 231,
            column: 5
          }
        },
        line: 223
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 232,
            column: 25
          },
          end: {
            line: 232,
            column: 26
          }
        },
        loc: {
          start: {
            line: 232,
            column: 43
          },
          end: {
            line: 234,
            column: 5
          }
        },
        line: 232
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 244,
            column: 3045
          },
          end: {
            line: 244,
            column: 3046
          }
        },
        loc: {
          start: {
            line: 244,
            column: 3062
          },
          end: {
            line: 244,
            column: 4080
          }
        },
        line: 244
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 13,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 16
          },
          end: {
            line: 3,
            column: 20
          }
        }, {
          start: {
            line: 3,
            column: 24
          },
          end: {
            line: 3,
            column: 37
          }
        }, {
          start: {
            line: 3,
            column: 42
          },
          end: {
            line: 13,
            column: 1
          }
        }],
        line: 3
      },
      "1": {
        loc: {
          start: {
            line: 4,
            column: 15
          },
          end: {
            line: 11,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 15
          },
          end: {
            line: 4,
            column: 28
          }
        }, {
          start: {
            line: 4,
            column: 32
          },
          end: {
            line: 11,
            column: 5
          }
        }],
        line: 4
      },
      "2": {
        loc: {
          start: {
            line: 7,
            column: 29
          },
          end: {
            line: 8,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 7,
            column: 29
          },
          end: {
            line: 8,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 7
      },
      "3": {
        loc: {
          start: {
            line: 14,
            column: 22
          },
          end: {
            line: 24,
            column: 3
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 14,
            column: 23
          },
          end: {
            line: 14,
            column: 27
          }
        }, {
          start: {
            line: 14,
            column: 31
          },
          end: {
            line: 14,
            column: 51
          }
        }, {
          start: {
            line: 14,
            column: 57
          },
          end: {
            line: 24,
            column: 2
          }
        }],
        line: 14
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 57
          },
          end: {
            line: 24,
            column: 2
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 74
          },
          end: {
            line: 21,
            column: 1
          }
        }, {
          start: {
            line: 21,
            column: 6
          },
          end: {
            line: 24,
            column: 1
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 4
          },
          end: {
            line: 15,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 15,
            column: 4
          },
          end: {
            line: 15,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 17,
            column: 4
          },
          end: {
            line: 19,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 17,
            column: 4
          },
          end: {
            line: 19,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 17
      },
      "7": {
        loc: {
          start: {
            line: 17,
            column: 8
          },
          end: {
            line: 17,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 8
          },
          end: {
            line: 17,
            column: 13
          }
        }, {
          start: {
            line: 17,
            column: 18
          },
          end: {
            line: 17,
            column: 84
          }
        }],
        line: 17
      },
      "8": {
        loc: {
          start: {
            line: 17,
            column: 18
          },
          end: {
            line: 17,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 17,
            column: 34
          },
          end: {
            line: 17,
            column: 47
          }
        }, {
          start: {
            line: 17,
            column: 50
          },
          end: {
            line: 17,
            column: 84
          }
        }],
        line: 17
      },
      "9": {
        loc: {
          start: {
            line: 17,
            column: 50
          },
          end: {
            line: 17,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 50
          },
          end: {
            line: 17,
            column: 63
          }
        }, {
          start: {
            line: 17,
            column: 67
          },
          end: {
            line: 17,
            column: 84
          }
        }],
        line: 17
      },
      "10": {
        loc: {
          start: {
            line: 22,
            column: 4
          },
          end: {
            line: 22,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 22,
            column: 4
          },
          end: {
            line: 22,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 22
      },
      "11": {
        loc: {
          start: {
            line: 25,
            column: 25
          },
          end: {
            line: 29,
            column: 2
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 25,
            column: 26
          },
          end: {
            line: 25,
            column: 30
          }
        }, {
          start: {
            line: 25,
            column: 34
          },
          end: {
            line: 25,
            column: 57
          }
        }, {
          start: {
            line: 25,
            column: 63
          },
          end: {
            line: 29,
            column: 1
          }
        }],
        line: 25
      },
      "12": {
        loc: {
          start: {
            line: 25,
            column: 63
          },
          end: {
            line: 29,
            column: 1
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 25,
            column: 80
          },
          end: {
            line: 27,
            column: 1
          }
        }, {
          start: {
            line: 27,
            column: 5
          },
          end: {
            line: 29,
            column: 1
          }
        }],
        line: 25
      },
      "13": {
        loc: {
          start: {
            line: 30,
            column: 19
          },
          end: {
            line: 46,
            column: 4
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 24
          }
        }, {
          start: {
            line: 30,
            column: 28
          },
          end: {
            line: 30,
            column: 45
          }
        }, {
          start: {
            line: 30,
            column: 50
          },
          end: {
            line: 46,
            column: 4
          }
        }],
        line: 30
      },
      "14": {
        loc: {
          start: {
            line: 32,
            column: 18
          },
          end: {
            line: 36,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 18
          },
          end: {
            line: 32,
            column: 44
          }
        }, {
          start: {
            line: 32,
            column: 48
          },
          end: {
            line: 36,
            column: 9
          }
        }],
        line: 32
      },
      "15": {
        loc: {
          start: {
            line: 34,
            column: 29
          },
          end: {
            line: 34,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 29
          },
          end: {
            line: 34,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "16": {
        loc: {
          start: {
            line: 40,
            column: 8
          },
          end: {
            line: 40,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 8
          },
          end: {
            line: 40,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "17": {
        loc: {
          start: {
            line: 40,
            column: 12
          },
          end: {
            line: 40,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 12
          },
          end: {
            line: 40,
            column: 15
          }
        }, {
          start: {
            line: 40,
            column: 19
          },
          end: {
            line: 40,
            column: 33
          }
        }],
        line: 40
      },
      "18": {
        loc: {
          start: {
            line: 42,
            column: 8
          },
          end: {
            line: 42,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 8
          },
          end: {
            line: 42,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "19": {
        loc: {
          start: {
            line: 42,
            column: 78
          },
          end: {
            line: 42,
            column: 137
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 78
          },
          end: {
            line: 42,
            column: 137
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "20": {
        loc: {
          start: {
            line: 47,
            column: 16
          },
          end: {
            line: 55,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 47,
            column: 17
          },
          end: {
            line: 47,
            column: 21
          }
        }, {
          start: {
            line: 47,
            column: 25
          },
          end: {
            line: 47,
            column: 39
          }
        }, {
          start: {
            line: 47,
            column: 44
          },
          end: {
            line: 55,
            column: 1
          }
        }],
        line: 47
      },
      "21": {
        loc: {
          start: {
            line: 48,
            column: 35
          },
          end: {
            line: 48,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 48,
            column: 56
          },
          end: {
            line: 48,
            column: 61
          }
        }, {
          start: {
            line: 48,
            column: 64
          },
          end: {
            line: 48,
            column: 109
          }
        }],
        line: 48
      },
      "22": {
        loc: {
          start: {
            line: 49,
            column: 16
          },
          end: {
            line: 49,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 49,
            column: 16
          },
          end: {
            line: 49,
            column: 17
          }
        }, {
          start: {
            line: 49,
            column: 22
          },
          end: {
            line: 49,
            column: 33
          }
        }],
        line: 49
      },
      "23": {
        loc: {
          start: {
            line: 52,
            column: 32
          },
          end: {
            line: 52,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 52,
            column: 46
          },
          end: {
            line: 52,
            column: 67
          }
        }, {
          start: {
            line: 52,
            column: 70
          },
          end: {
            line: 52,
            column: 115
          }
        }],
        line: 52
      },
      "24": {
        loc: {
          start: {
            line: 53,
            column: 51
          },
          end: {
            line: 53,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 53,
            column: 51
          },
          end: {
            line: 53,
            column: 61
          }
        }, {
          start: {
            line: 53,
            column: 65
          },
          end: {
            line: 53,
            column: 67
          }
        }],
        line: 53
      },
      "25": {
        loc: {
          start: {
            line: 56,
            column: 18
          },
          end: {
            line: 82,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 56,
            column: 19
          },
          end: {
            line: 56,
            column: 23
          }
        }, {
          start: {
            line: 56,
            column: 27
          },
          end: {
            line: 56,
            column: 43
          }
        }, {
          start: {
            line: 56,
            column: 48
          },
          end: {
            line: 82,
            column: 1
          }
        }],
        line: 56
      },
      "26": {
        loc: {
          start: {
            line: 57,
            column: 43
          },
          end: {
            line: 57,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 57,
            column: 43
          },
          end: {
            line: 57,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 57
      },
      "27": {
        loc: {
          start: {
            line: 57,
            column: 134
          },
          end: {
            line: 57,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 57,
            column: 167
          },
          end: {
            line: 57,
            column: 175
          }
        }, {
          start: {
            line: 57,
            column: 178
          },
          end: {
            line: 57,
            column: 184
          }
        }],
        line: 57
      },
      "28": {
        loc: {
          start: {
            line: 58,
            column: 74
          },
          end: {
            line: 58,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 58,
            column: 74
          },
          end: {
            line: 58,
            column: 102
          }
        }, {
          start: {
            line: 58,
            column: 107
          },
          end: {
            line: 58,
            column: 155
          }
        }],
        line: 58
      },
      "29": {
        loc: {
          start: {
            line: 61,
            column: 8
          },
          end: {
            line: 61,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 61,
            column: 8
          },
          end: {
            line: 61,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 61
      },
      "30": {
        loc: {
          start: {
            line: 62,
            column: 15
          },
          end: {
            line: 62,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 62,
            column: 15
          },
          end: {
            line: 62,
            column: 16
          }
        }, {
          start: {
            line: 62,
            column: 21
          },
          end: {
            line: 62,
            column: 44
          }
        }],
        line: 62
      },
      "31": {
        loc: {
          start: {
            line: 62,
            column: 28
          },
          end: {
            line: 62,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 62,
            column: 28
          },
          end: {
            line: 62,
            column: 33
          }
        }, {
          start: {
            line: 62,
            column: 38
          },
          end: {
            line: 62,
            column: 43
          }
        }],
        line: 62
      },
      "32": {
        loc: {
          start: {
            line: 63,
            column: 12
          },
          end: {
            line: 63,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 63,
            column: 12
          },
          end: {
            line: 63,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 63
      },
      "33": {
        loc: {
          start: {
            line: 63,
            column: 23
          },
          end: {
            line: 63,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 63,
            column: 23
          },
          end: {
            line: 63,
            column: 24
          }
        }, {
          start: {
            line: 63,
            column: 29
          },
          end: {
            line: 63,
            column: 125
          }
        }, {
          start: {
            line: 63,
            column: 130
          },
          end: {
            line: 63,
            column: 158
          }
        }],
        line: 63
      },
      "34": {
        loc: {
          start: {
            line: 63,
            column: 33
          },
          end: {
            line: 63,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 63,
            column: 45
          },
          end: {
            line: 63,
            column: 56
          }
        }, {
          start: {
            line: 63,
            column: 59
          },
          end: {
            line: 63,
            column: 125
          }
        }],
        line: 63
      },
      "35": {
        loc: {
          start: {
            line: 63,
            column: 59
          },
          end: {
            line: 63,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 63,
            column: 67
          },
          end: {
            line: 63,
            column: 116
          }
        }, {
          start: {
            line: 63,
            column: 119
          },
          end: {
            line: 63,
            column: 125
          }
        }],
        line: 63
      },
      "36": {
        loc: {
          start: {
            line: 63,
            column: 67
          },
          end: {
            line: 63,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 63,
            column: 67
          },
          end: {
            line: 63,
            column: 77
          }
        }, {
          start: {
            line: 63,
            column: 82
          },
          end: {
            line: 63,
            column: 115
          }
        }],
        line: 63
      },
      "37": {
        loc: {
          start: {
            line: 63,
            column: 82
          },
          end: {
            line: 63,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 63,
            column: 83
          },
          end: {
            line: 63,
            column: 98
          }
        }, {
          start: {
            line: 63,
            column: 103
          },
          end: {
            line: 63,
            column: 112
          }
        }],
        line: 63
      },
      "38": {
        loc: {
          start: {
            line: 64,
            column: 12
          },
          end: {
            line: 64,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 64,
            column: 12
          },
          end: {
            line: 64,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 64
      },
      "39": {
        loc: {
          start: {
            line: 65,
            column: 12
          },
          end: {
            line: 77,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 66,
            column: 16
          },
          end: {
            line: 66,
            column: 23
          }
        }, {
          start: {
            line: 66,
            column: 24
          },
          end: {
            line: 66,
            column: 46
          }
        }, {
          start: {
            line: 67,
            column: 16
          },
          end: {
            line: 67,
            column: 72
          }
        }, {
          start: {
            line: 68,
            column: 16
          },
          end: {
            line: 68,
            column: 65
          }
        }, {
          start: {
            line: 69,
            column: 16
          },
          end: {
            line: 69,
            column: 65
          }
        }, {
          start: {
            line: 70,
            column: 16
          },
          end: {
            line: 76,
            column: 43
          }
        }],
        line: 65
      },
      "40": {
        loc: {
          start: {
            line: 71,
            column: 20
          },
          end: {
            line: 71,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 71,
            column: 20
          },
          end: {
            line: 71,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 71
      },
      "41": {
        loc: {
          start: {
            line: 71,
            column: 24
          },
          end: {
            line: 71,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 71,
            column: 24
          },
          end: {
            line: 71,
            column: 74
          }
        }, {
          start: {
            line: 71,
            column: 79
          },
          end: {
            line: 71,
            column: 90
          }
        }, {
          start: {
            line: 71,
            column: 94
          },
          end: {
            line: 71,
            column: 105
          }
        }],
        line: 71
      },
      "42": {
        loc: {
          start: {
            line: 71,
            column: 42
          },
          end: {
            line: 71,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 71,
            column: 42
          },
          end: {
            line: 71,
            column: 54
          }
        }, {
          start: {
            line: 71,
            column: 58
          },
          end: {
            line: 71,
            column: 73
          }
        }],
        line: 71
      },
      "43": {
        loc: {
          start: {
            line: 72,
            column: 20
          },
          end: {
            line: 72,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 20
          },
          end: {
            line: 72,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "44": {
        loc: {
          start: {
            line: 72,
            column: 24
          },
          end: {
            line: 72,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 72,
            column: 24
          },
          end: {
            line: 72,
            column: 35
          }
        }, {
          start: {
            line: 72,
            column: 40
          },
          end: {
            line: 72,
            column: 42
          }
        }, {
          start: {
            line: 72,
            column: 47
          },
          end: {
            line: 72,
            column: 59
          }
        }, {
          start: {
            line: 72,
            column: 63
          },
          end: {
            line: 72,
            column: 75
          }
        }],
        line: 72
      },
      "45": {
        loc: {
          start: {
            line: 73,
            column: 20
          },
          end: {
            line: 73,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 73,
            column: 20
          },
          end: {
            line: 73,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 73
      },
      "46": {
        loc: {
          start: {
            line: 73,
            column: 24
          },
          end: {
            line: 73,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 73,
            column: 24
          },
          end: {
            line: 73,
            column: 35
          }
        }, {
          start: {
            line: 73,
            column: 39
          },
          end: {
            line: 73,
            column: 53
          }
        }],
        line: 73
      },
      "47": {
        loc: {
          start: {
            line: 74,
            column: 20
          },
          end: {
            line: 74,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 20
          },
          end: {
            line: 74,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "48": {
        loc: {
          start: {
            line: 74,
            column: 24
          },
          end: {
            line: 74,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 74,
            column: 24
          },
          end: {
            line: 74,
            column: 25
          }
        }, {
          start: {
            line: 74,
            column: 29
          },
          end: {
            line: 74,
            column: 43
          }
        }],
        line: 74
      },
      "49": {
        loc: {
          start: {
            line: 75,
            column: 20
          },
          end: {
            line: 75,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 75,
            column: 20
          },
          end: {
            line: 75,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 75
      },
      "50": {
        loc: {
          start: {
            line: 80,
            column: 8
          },
          end: {
            line: 80,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 80,
            column: 8
          },
          end: {
            line: 80,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 80
      },
      "51": {
        loc: {
          start: {
            line: 80,
            column: 52
          },
          end: {
            line: 80,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 80,
            column: 60
          },
          end: {
            line: 80,
            column: 65
          }
        }, {
          start: {
            line: 80,
            column: 68
          },
          end: {
            line: 80,
            column: 74
          }
        }],
        line: 80
      },
      "52": {
        loc: {
          start: {
            line: 83,
            column: 20
          },
          end: {
            line: 91,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 83,
            column: 21
          },
          end: {
            line: 83,
            column: 25
          }
        }, {
          start: {
            line: 83,
            column: 29
          },
          end: {
            line: 83,
            column: 47
          }
        }, {
          start: {
            line: 83,
            column: 52
          },
          end: {
            line: 91,
            column: 1
          }
        }],
        line: 83
      },
      "53": {
        loc: {
          start: {
            line: 84,
            column: 4
          },
          end: {
            line: 89,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 84,
            column: 4
          },
          end: {
            line: 89,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 84
      },
      "54": {
        loc: {
          start: {
            line: 84,
            column: 8
          },
          end: {
            line: 84,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 84,
            column: 8
          },
          end: {
            line: 84,
            column: 12
          }
        }, {
          start: {
            line: 84,
            column: 16
          },
          end: {
            line: 84,
            column: 38
          }
        }],
        line: 84
      },
      "55": {
        loc: {
          start: {
            line: 85,
            column: 8
          },
          end: {
            line: 88,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 85,
            column: 8
          },
          end: {
            line: 88,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 85
      },
      "56": {
        loc: {
          start: {
            line: 85,
            column: 12
          },
          end: {
            line: 85,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 85,
            column: 12
          },
          end: {
            line: 85,
            column: 14
          }
        }, {
          start: {
            line: 85,
            column: 18
          },
          end: {
            line: 85,
            column: 30
          }
        }],
        line: 85
      },
      "57": {
        loc: {
          start: {
            line: 86,
            column: 12
          },
          end: {
            line: 86,
            column: 65
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 86,
            column: 12
          },
          end: {
            line: 86,
            column: 65
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 86
      },
      "58": {
        loc: {
          start: {
            line: 90,
            column: 21
          },
          end: {
            line: 90,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 90,
            column: 21
          },
          end: {
            line: 90,
            column: 23
          }
        }, {
          start: {
            line: 90,
            column: 27
          },
          end: {
            line: 90,
            column: 59
          }
        }],
        line: 90
      },
      "59": {
        loc: {
          start: {
            line: 92,
            column: 22
          },
          end: {
            line: 94,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 92,
            column: 23
          },
          end: {
            line: 92,
            column: 27
          }
        }, {
          start: {
            line: 92,
            column: 31
          },
          end: {
            line: 92,
            column: 51
          }
        }, {
          start: {
            line: 92,
            column: 56
          },
          end: {
            line: 94,
            column: 1
          }
        }],
        line: 92
      },
      "60": {
        loc: {
          start: {
            line: 93,
            column: 11
          },
          end: {
            line: 93,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 93,
            column: 37
          },
          end: {
            line: 93,
            column: 40
          }
        }, {
          start: {
            line: 93,
            column: 43
          },
          end: {
            line: 93,
            column: 61
          }
        }],
        line: 93
      },
      "61": {
        loc: {
          start: {
            line: 93,
            column: 12
          },
          end: {
            line: 93,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 93,
            column: 12
          },
          end: {
            line: 93,
            column: 15
          }
        }, {
          start: {
            line: 93,
            column: 19
          },
          end: {
            line: 93,
            column: 33
          }
        }],
        line: 93
      },
      "62": {
        loc: {
          start: {
            line: 124,
            column: 12
          },
          end: {
            line: 159,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 125,
            column: 16
          },
          end: {
            line: 128,
            column: 49
          }
        }, {
          start: {
            line: 129,
            column: 16
          },
          end: {
            line: 131,
            column: 99
          }
        }, {
          start: {
            line: 132,
            column: 16
          },
          end: {
            line: 141,
            column: 58
          }
        }, {
          start: {
            line: 142,
            column: 16
          },
          end: {
            line: 149,
            column: 44
          }
        }, {
          start: {
            line: 150,
            column: 16
          },
          end: {
            line: 154,
            column: 44
          }
        }, {
          start: {
            line: 155,
            column: 16
          },
          end: {
            line: 157,
            column: 46
          }
        }, {
          start: {
            line: 158,
            column: 16
          },
          end: {
            line: 158,
            column: 46
          }
        }],
        line: 124
      },
      "63": {
        loc: {
          start: {
            line: 134,
            column: 20
          },
          end: {
            line: 140,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 134,
            column: 20
          },
          end: {
            line: 140,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 134
      },
      "64": {
        loc: {
          start: {
            line: 135,
            column: 24
          },
          end: {
            line: 138,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 135,
            column: 24
          },
          end: {
            line: 138,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 135
      },
      "65": {
        loc: {
          start: {
            line: 144,
            column: 20
          },
          end: {
            line: 147,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 144,
            column: 20
          },
          end: {
            line: 147,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 144
      },
      "66": {
        loc: {
          start: {
            line: 144,
            column: 24
          },
          end: {
            line: 144,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 144,
            column: 24
          },
          end: {
            line: 144,
            column: 39
          }
        }, {
          start: {
            line: 144,
            column: 43
          },
          end: {
            line: 144,
            column: 55
          }
        }],
        line: 144
      },
      "67": {
        loc: {
          start: {
            line: 163,
            column: 8
          },
          end: {
            line: 166,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 163,
            column: 8
          },
          end: {
            line: 166,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 163
      },
      "68": {
        loc: {
          start: {
            line: 167,
            column: 8
          },
          end: {
            line: 169,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 167,
            column: 8
          },
          end: {
            line: 169,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 167
      },
      "69": {
        loc: {
          start: {
            line: 174,
            column: 12
          },
          end: {
            line: 220,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 175,
            column: 16
          },
          end: {
            line: 182,
            column: 33
          }
        }, {
          start: {
            line: 183,
            column: 16
          },
          end: {
            line: 185,
            column: 49
          }
        }, {
          start: {
            line: 186,
            column: 16
          },
          end: {
            line: 194,
            column: 28
          }
        }, {
          start: {
            line: 195,
            column: 16
          },
          end: {
            line: 198,
            column: 58
          }
        }, {
          start: {
            line: 199,
            column: 16
          },
          end: {
            line: 201,
            column: 81
          }
        }, {
          start: {
            line: 202,
            column: 16
          },
          end: {
            line: 202,
            column: 62
          }
        }, {
          start: {
            line: 203,
            column: 16
          },
          end: {
            line: 210,
            column: 44
          }
        }, {
          start: {
            line: 211,
            column: 16
          },
          end: {
            line: 215,
            column: 44
          }
        }, {
          start: {
            line: 216,
            column: 16
          },
          end: {
            line: 218,
            column: 46
          }
        }, {
          start: {
            line: 219,
            column: 16
          },
          end: {
            line: 219,
            column: 46
          }
        }],
        line: 174
      },
      "70": {
        loc: {
          start: {
            line: 176,
            column: 20
          },
          end: {
            line: 179,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 176,
            column: 20
          },
          end: {
            line: 179,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 176
      },
      "71": {
        loc: {
          start: {
            line: 197,
            column: 20
          },
          end: {
            line: 197,
            column: 63
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 197,
            column: 20
          },
          end: {
            line: 197,
            column: 63
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 197
      },
      "72": {
        loc: {
          start: {
            line: 201,
            column: 36
          },
          end: {
            line: 201,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 201,
            column: 36
          },
          end: {
            line: 201,
            column: 51
          }
        }, {
          start: {
            line: 201,
            column: 55
          },
          end: {
            line: 201,
            column: 79
          }
        }],
        line: 201
      },
      "73": {
        loc: {
          start: {
            line: 206,
            column: 20
          },
          end: {
            line: 208,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 206,
            column: 20
          },
          end: {
            line: 208,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 206
      },
      "74": {
        loc: {
          start: {
            line: 214,
            column: 29
          },
          end: {
            line: 214,
            column: 94
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 214,
            column: 54
          },
          end: {
            line: 214,
            column: 67
          }
        }, {
          start: {
            line: 214,
            column: 70
          },
          end: {
            line: 214,
            column: 94
          }
        }],
        line: 214
      },
      "75": {
        loc: {
          start: {
            line: 233,
            column: 15
          },
          end: {
            line: 233,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 233,
            column: 15
          },
          end: {
            line: 233,
            column: 26
          }
        }, {
          start: {
            line: 233,
            column: 30
          },
          end: {
            line: 233,
            column: 56
          }
        }],
        line: 233
      },
      "76": {
        loc: {
          start: {
            line: 235,
            column: 4
          },
          end: {
            line: 237,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 235,
            column: 4
          },
          end: {
            line: 237,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 235
      },
      "77": {
        loc: {
          start: {
            line: 235,
            column: 8
          },
          end: {
            line: 235,
            column: 39
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 235,
            column: 8
          },
          end: {
            line: 235,
            column: 28
          }
        }, {
          start: {
            line: 235,
            column: 32
          },
          end: {
            line: 235,
            column: 39
          }
        }],
        line: 235
      },
      "78": {
        loc: {
          start: {
            line: 238,
            column: 4
          },
          end: {
            line: 240,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 238,
            column: 4
          },
          end: {
            line: 240,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 238
      },
      "79": {
        loc: {
          start: {
            line: 241,
            column: 4
          },
          end: {
            line: 243,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 241,
            column: 4
          },
          end: {
            line: 243,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 241
      },
      "80": {
        loc: {
          start: {
            line: 241,
            column: 8
          },
          end: {
            line: 241,
            column: 22
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 241,
            column: 8
          },
          end: {
            line: 241,
            column: 13
          }
        }, {
          start: {
            line: 241,
            column: 17
          },
          end: {
            line: 241,
            column: 22
          }
        }],
        line: 241
      },
      "81": {
        loc: {
          start: {
            line: 242,
            column: 255
          },
          end: {
            line: 242,
            column: 280
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 242,
            column: 255
          },
          end: {
            line: 242,
            column: 260
          }
        }, {
          start: {
            line: 242,
            column: 264
          },
          end: {
            line: 242,
            column: 280
          }
        }],
        line: 242
      },
      "82": {
        loc: {
          start: {
            line: 244,
            column: 924
          },
          end: {
            line: 244,
            column: 1214
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 244,
            column: 924
          },
          end: {
            line: 244,
            column: 937
          }
        }, {
          start: {
            line: 244,
            column: 942
          },
          end: {
            line: 244,
            column: 1213
          }
        }],
        line: 244
      },
      "83": {
        loc: {
          start: {
            line: 244,
            column: 1220
          },
          end: {
            line: 244,
            column: 1523
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 244,
            column: 1220
          },
          end: {
            line: 244,
            column: 1248
          }
        }, {
          start: {
            line: 244,
            column: 1253
          },
          end: {
            line: 244,
            column: 1522
          }
        }],
        line: 244
      },
      "84": {
        loc: {
          start: {
            line: 244,
            column: 2645
          },
          end: {
            line: 244,
            column: 2692
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 244,
            column: 2673
          },
          end: {
            line: 244,
            column: 2680
          }
        }, {
          start: {
            line: 244,
            column: 2683
          },
          end: {
            line: 244,
            column: 2692
          }
        }],
        line: 244
      },
      "85": {
        loc: {
          start: {
            line: 244,
            column: 4323
          },
          end: {
            line: 244,
            column: 4476
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 244,
            column: 4323
          },
          end: {
            line: 244,
            column: 4328
          }
        }, {
          start: {
            line: 244,
            column: 4333
          },
          end: {
            line: 244,
            column: 4475
          }
        }],
        line: 244
      },
      "86": {
        loc: {
          start: {
            line: 250,
            column: 406
          },
          end: {
            line: 250,
            column: 544
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 250,
            column: 406
          },
          end: {
            line: 250,
            column: 420
          }
        }, {
          start: {
            line: 250,
            column: 425
          },
          end: {
            line: 250,
            column: 543
          }
        }],
        line: 250
      },
      "87": {
        loc: {
          start: {
            line: 250,
            column: 772
          },
          end: {
            line: 250,
            column: 804
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 250,
            column: 772
          },
          end: {
            line: 250,
            column: 789
          }
        }, {
          start: {
            line: 250,
            column: 793
          },
          end: {
            line: 250,
            column: 804
          }
        }],
        line: 250
      },
      "88": {
        loc: {
          start: {
            line: 250,
            column: 854
          },
          end: {
            line: 250,
            column: 1213
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 250,
            column: 875
          },
          end: {
            line: 250,
            column: 1059
          }
        }, {
          start: {
            line: 250,
            column: 1064
          },
          end: {
            line: 250,
            column: 1212
          }
        }],
        line: 250
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0, 0, 0, 0, 0],
      "40": [0, 0],
      "41": [0, 0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0, 0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0, 0, 0, 0, 0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0],
      "81": [0, 0],
      "82": [0, 0],
      "83": [0, 0],
      "84": [0, 0],
      "85": [0, 0],
      "86": [0, 0],
      "87": [0, 0],
      "88": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/forum/posts/[postId]/page.tsx",
      mappings: ";AAAA,YAAY,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEb,gCAoUC;;AAtYD,6CAAgE;AAChE,yCAA6C;AAC7C,8CAA4C;AAC5C,mDAA6B;AAC7B,6CAAmF;AACnF,iDAAgD;AAChD,mDAA0C;AAC1C,uFAAiE;AACjE,qFAA+D;AAC/D,2CAA0C;AAC1C,2EAAuF;AAwDvF,SAAwB,aAAa,CAAC,EAAmD;IAAzF,iBAoUC;QApUuC,MAAM,YAAA;IACpC,IAAA,MAAM,GAAK,IAAA,kBAAU,GAAE,OAAjB,CAAkB;IAChC,IAAM,MAAM,GAAG,IAAA,sBAAS,GAAE,CAAC;IACrB,IAAA,KAAkB,IAAA,gBAAQ,EAAmB,IAAI,CAAC,EAAjD,IAAI,QAAA,EAAE,OAAO,QAAoC,CAAC;IACnD,IAAA,KAAwB,IAAA,gBAAQ,EAAC,IAAI,CAAC,EAArC,OAAO,QAAA,EAAE,UAAU,QAAkB,CAAC;IACvC,IAAA,KAAoB,IAAA,gBAAQ,EAAgB,IAAI,CAAC,EAAhD,KAAK,QAAA,EAAE,QAAQ,QAAiC,CAAC;IAClD,IAAA,KAA4C,IAAA,gBAAQ,EAAC,KAAK,CAAC,EAA1D,iBAAiB,QAAA,EAAE,oBAAoB,QAAmB,CAAC;IAC5D,IAAA,KAAyC,IAAA,iBAAO,GAAE,EAAhD,UAAU,gBAAA,EAAa,WAAW,eAAc,CAAC;IAEnD,IAAA,KAMF,IAAA,yBAAO,GAAa,EALtB,QAAQ,cAAA,EACR,YAAY,kBAAA,EACC,MAAM,sBAAA,EACnB,KAAK,WAAA,EACL,KAAK,WACiB,CAAC;IAEzB,IAAM,YAAY,GAAG,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;IAE1C,IAAM,SAAS,GAAG,IAAA,mBAAW,EAAC;;;;;;oBAE1B,UAAU,CAAC,IAAI,CAAC,CAAC;oBACM,qBAAM,MAAM,EAAA;;oBAA7B,cAAc,GAAG,SAAY;oBAGlB,qBAAM,KAAK,CAAC,2BAAoB,cAAc,CAAC,MAAM,CAAE,CAAC,EAAA;;oBAAnE,QAAQ,GAAG,SAAwD;oBAEzE,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;wBACjB,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;4BAC5B,QAAQ,CAAC,gBAAgB,CAAC,CAAC;4BAC3B,sBAAO;wBACT,CAAC;wBACD,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;oBAC1C,CAAC;oBAEc,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;oBAA9B,MAAM,GAAG,SAAqB;oBAEpC,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;wBACpC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;wBAC3B,sBAAO;oBACT,CAAC;oBAED,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;;;;oBAErB,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAG,CAAC,CAAC;oBAC3C,QAAQ,CAAC,qBAAqB,CAAC,CAAC;;;oBAEhC,UAAU,CAAC,KAAK,CAAC,CAAC;;;;;SAErB,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,IAAA,iBAAS,EAAC;QACR,IAAI,MAAM,KAAK,iBAAiB,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACtB,OAAO;QACT,CAAC;QAED,IAAI,MAAM,KAAK,eAAe,EAAE,CAAC;YAC/B,SAAS,EAAE,CAAC;QACd,CAAC;IACH,CAAC,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;IAEhC,IAAM,aAAa,GAAG,UAAO,IAAe;;;;;oBAC1C,IAAI,MAAM,KAAK,eAAe,EAAE,CAAC;wBAC/B,QAAQ,CAAC,gCAAgC,CAAC,CAAC;wBAC3C,sBAAO;oBACT,CAAC;oBAED,oBAAoB,CAAC,IAAI,CAAC,CAAC;oBAC3B,QAAQ,CAAC,IAAI,CAAC,CAAC;;;;oBAGU,qBAAM,MAAM,EAAA;;oBAA7B,cAAc,GAAG,SAAY;oBAClB,qBAAM,KAAK,CAAC,2BAAoB,cAAc,CAAC,MAAM,aAAU,EAAE;4BAChF,MAAM,EAAE,MAAM;4BACd,OAAO,EAAE,UAAU,EAAE;4BACrB,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gCACnB,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;6BAC7B,CAAC;yBACH,CAAC,EAAA;;oBANI,QAAQ,GAAG,SAMf;yBAEE,CAAC,QAAQ,CAAC,EAAE,EAAZ,wBAAY;oBACI,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;oBAAjC,SAAS,GAAG,SAAqB;oBACvC,MAAM,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,IAAI,wBAAwB,CAAC,CAAC;wBAG9C,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;oBAAhC,QAAQ,GAAG,SAAqB;oBAEtC,gCAAgC;oBAChC,IAAI,IAAI,EAAE,CAAC;wBACT,OAAO,uBACF,IAAI,KACP,OAAO,kCAAM,IAAI,CAAC,OAAO,UAAE,QAAQ,aACnC,CAAC;oBACL,CAAC;oBAED,KAAK,EAAE,CAAC,CAAC,iBAAiB;;;;oBAE1B,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAG,CAAC,CAAC;oBAC5C,QAAQ,CAAC,KAAG,YAAY,KAAK,CAAC,CAAC,CAAC,KAAG,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC,CAAC;;;oBAExE,oBAAoB,CAAC,KAAK,CAAC,CAAC;;;;;SAE/B,CAAC;IAEF,IAAM,UAAU,GAAG,UAAC,UAAkB;QACpC,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,kBAAkB,CAAC,OAAO,EAAE;YACtD,IAAI,EAAE,SAAS;YACf,KAAK,EAAE,OAAO;YACd,GAAG,EAAE,SAAS;YACd,IAAI,EAAE,SAAS;YACf,MAAM,EAAE,SAAS;SAClB,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,IAAM,cAAc,GAAG,UAAC,MAAwC;QAC9D,OAAO,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC;IAEF,IAAI,MAAM,KAAK,SAAS,IAAI,OAAO,EAAE,CAAC;QACpC,OAAO,CACL,gCAAK,SAAS,EAAC,6BAA6B,YAC1C,uBAAC,sCAAgB,IAAC,OAAO,EAAC,uBAAuB,GAAG,GAChD,CACP,CAAC;IACJ,CAAC;IAED,IAAI,MAAM,KAAK,iBAAiB,EAAE,CAAC;QACjC,OAAO,CACL,gCAAK,SAAS,EAAC,6BAA6B,YAC1C,gCAAK,SAAS,EAAC,aAAa,YAC1B,qDAAU,uBAAC,cAAI,IAAC,IAAI,EAAC,QAAQ,EAAC,SAAS,EAAC,+BAA+B,uBAAc,2BAAuB,GACxG,GACF,CACP,CAAC;IACJ,CAAC;IAED,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC;QACnB,OAAO,CACL,gCAAK,SAAS,EAAC,6BAA6B,YAC1C,iCAAK,SAAS,EAAC,aAAa,aAC1B,8BAAG,SAAS,EAAC,gCAAgC,YAAE,KAAK,IAAI,gBAAgB,GAAK,EAC7E,uBAAC,cAAI,IAAC,IAAI,EAAC,QAAQ,EAAC,SAAS,EAAC,iDAAiD,8BAExE,IACH,GACF,CACP,CAAC;IACJ,CAAC;IAED,OAAO,CACL,iCAAK,SAAS,EAAC,uCAAuC,aACpD,gCAAK,SAAS,EAAC,MAAM,YACnB,wBAAC,cAAI,IACH,IAAI,EAAC,QAAQ,EACb,SAAS,EAAC,8GAA8G,aAExH,uBAAC,wBAAS,IAAC,SAAS,EAAC,SAAS,GAAG,qBAE5B,GACH,EAGN,gCAAK,SAAS,EAAC,yDAAyD,YACtE,iCAAK,SAAS,EAAC,WAAW,aAExB,iCAAK,SAAS,EAAC,kCAAkC,aAC/C,iCAAK,SAAS,EAAC,QAAQ,aACrB,+BAAI,SAAS,EAAC,0DAA0D,YACrE,IAAI,CAAC,KAAK,GACR,EACJ,IAAI,CAAC,QAAQ,IAAI,CAChB,gCAAK,SAAS,EAAC,MAAM,YACnB,iCAAM,SAAS,EAAC,uIAAuI,YACpJ,IAAI,CAAC,QAAQ,GACT,GACH,CACP,IACG,EACL,IAAI,CAAC,SAAS,KAAK,SAAS,IAAI,CAC/B,iCAAK,SAAS,EAAC,4DAA4D,aACzE,uBAAC,kBAAG,IAAC,SAAS,EAAC,cAAc,GAAG,EAChC,6CAAO,IAAI,CAAC,SAAS,cAAc,IAC/B,CACP,IACG,EAGN,gCAAK,SAAS,EAAC,oCAAoC,YACjD,8BAAG,SAAS,EAAC,sDAAsD,YAChE,IAAI,CAAC,OAAO,GACX,GACA,EAGN,uBAAC,yBAAe,IACd,IAAI,wBACC,IAAI,CAAC,MAAM,KACd,QAAQ,EAAE,IAAI,CAAC,SAAS,KAE1B,OAAO,EAAC,MAAM,EACd,SAAS,EAAE,IAAI,GACf,EAGF,iCAAK,SAAS,EAAC,sFAAsF,aACnG,iCAAK,SAAS,EAAC,sEAAsE,aACnF,iCAAK,SAAS,EAAC,mBAAmB,aAChC,uBAAC,uBAAQ,IAAC,SAAS,EAAC,cAAc,GAAG,EACrC,2CAAO,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,GAAQ,IACrC,EACN,iCAAK,SAAS,EAAC,mBAAmB,aAChC,uBAAC,4BAAa,IAAC,SAAS,EAAC,cAAc,GAAG,EAC1C,6CAAO,IAAI,CAAC,OAAO,CAAC,MAAM,OAAG,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,IAAQ,IAChF,IACF,EAGN,uBAAC,wBAAc,IAAC,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,OAAO,EAAC,MAAM,GAAG,IAC9C,IACF,GACF,EAGN,iCAAK,SAAS,EAAC,WAAW,aACxB,gCAAI,SAAS,EAAC,wDAAwD,0BAC1D,IAAI,CAAC,OAAO,CAAC,MAAM,SAC1B,EAEJ,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,CAC3B,gCAEE,SAAS,EAAC,yDAAyD,YAEnE,iCAAK,SAAS,EAAC,WAAW,aAExB,8BAAG,SAAS,EAAC,sDAAsD,YAChE,KAAK,CAAC,OAAO,GACZ,EAGJ,uBAAC,yBAAe,IACd,IAAI,wBACC,KAAK,CAAC,MAAM,KACf,QAAQ,EAAE,KAAK,CAAC,SAAS,KAE3B,OAAO,EAAC,SAAS,EACjB,SAAS,EAAE,KAAK,GAChB,EAGF,iCAAK,SAAS,EAAC,sFAAsF,aACnG,iCAAK,SAAS,EAAC,4DAA4D,aACzE,uBAAC,uBAAQ,IAAC,SAAS,EAAC,cAAc,GAAG,EACrC,2CAAO,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,GAAQ,IACtC,EAGN,uBAAC,wBAAc,IAAC,OAAO,EAAE,KAAK,CAAC,EAAE,EAAE,OAAO,EAAC,SAAS,GAAG,IACnD,IACF,IA7BD,KAAK,CAAC,EAAE,CA8BT,CACP,EAjC4B,CAiC5B,CAAC,EAGF,iCAAK,SAAS,EAAC,oDAAoD,aACjE,+BAAI,SAAS,EAAC,2DAA2D,4BAEpE,EAEJ,KAAK,IAAI,CACR,gCAAK,SAAS,EAAC,+EAA+E,YAC3F,KAAK,GACF,CACP,EAED,kCAAM,QAAQ,EAAE,YAAY,CAAC,aAAa,CAAC,EAAE,SAAS,EAAC,WAAW,aAChE,4CACE,8CACE,EAAE,EAAC,SAAS,EACZ,IAAI,EAAE,CAAC,IACH,QAAQ,CAAC,SAAS,EAAE;gDACtB,QAAQ,EAAE,2BAA2B;gDACrC,SAAS,EAAE;oDACT,KAAK,EAAE,IAAI;oDACX,OAAO,EAAE,uCAAuC;iDACjD;6CACF,CAAC,IACF,SAAS,EAAC,8LAA8L,EACxM,WAAW,EAAC,qBAAqB,EACjC,QAAQ,EAAE,iBAAiB,IAC3B,EACF,iCAAK,SAAS,EAAC,2BAA2B,aACvC,MAAM,CAAC,OAAO,IAAI,CACjB,8BAAG,SAAS,EAAC,wCAAwC,YAAE,MAAM,CAAC,OAAO,CAAC,OAAO,GAAK,CACnF,EACD,+BAAG,SAAS,EAAC,kDAAkD,aAC5D,YAAY,CAAC,MAAM,wBAClB,IACA,IACF,EAEN,uBAAC,eAAM,IACL,IAAI,EAAC,QAAQ,EACb,QAAQ,EAAE,iBAAiB,IAAI,WAAW,EAC1C,SAAS,EAAC,yBAAyB,YAElC,iBAAiB,CAAC,CAAC,CAAC,CACnB,6DACE,gCAAK,SAAS,EAAC,2DAA2D,GAAO,kBAEhF,CACJ,CAAC,CAAC,CAAC,CACF,6DACE,uBAAC,mBAAI,IAAC,SAAS,EAAC,SAAS,GAAG,kBAE3B,CACJ,GACM,IACJ,IACH,IACF,IACF,CACP,CAAC;AACJ,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/forum/posts/[postId]/page.tsx"],
      sourcesContent: ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useSession } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { ArrowLeft, User, Calendar, MessageSquare, Send, Eye } from 'lucide-react';\nimport { Button } from '@/components/ui/button';\nimport { useForm } from 'react-hook-form';\nimport UserProfileCard from '@/components/forum/UserProfileCard';\nimport ReactionButton from '@/components/forum/ReactionButton';\nimport { useCSRF } from '@/hooks/useCSRF';\nimport { FormLoadingState, PageErrorState } from '@/components/ui/page-loading-states';\n\ninterface ForumPost {\n  id: string;\n  title: string;\n  content: string;\n  createdAt: string;\n  updatedAt?: string;\n  category?: string;\n  tags?: string[];\n  viewCount?: number;\n  author: {\n    id: string;\n    email: string;\n    name?: string;\n    profile?: {\n      firstName?: string;\n      lastName?: string;\n      jobTitle?: string;\n      company?: string;\n      location?: string;\n      profilePictureUrl?: string;\n      experienceLevel?: string;\n      bio?: string;\n      profileCompletionScore?: number;\n    };\n    _count?: {\n      forumPosts?: number;\n      forumReplies?: number;\n      achievements?: number;\n    };\n  };\n  replies: Array<{\n    id: string;\n    content: string;\n    createdAt: string;\n    author: {\n      id: string;\n      email: string;\n      name?: string;\n      profile?: {\n        firstName?: string;\n        lastName?: string;\n        jobTitle?: string;\n        company?: string;\n        profilePictureUrl?: string;\n        experienceLevel?: string;\n      };\n    };\n  }>;\n}\n\ninterface ReplyForm {\n  content: string;\n}\n\nexport default function ForumPostPage({ params }: { params: Promise<{ postId: string }> }) {\n  const { status } = useSession();\n  const router = useRouter();\n  const [post, setPost] = useState<ForumPost | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [isSubmittingReply, setIsSubmittingReply] = useState(false);\n  const { getHeaders, isLoading: csrfLoading } = useCSRF();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n    reset,\n    watch,\n  } = useForm<ReplyForm>();\n\n  const contentValue = watch('content', '');\n\n  const fetchPost = useCallback(async () => {\n    try {\n      setLoading(true);\n      const resolvedParams = await params;\n\n      // Fetch individual post instead of all posts (fixes N+1 query)\n      const response = await fetch(`/api/forum/posts/${resolvedParams.postId}`);\n\n      if (!response.ok) {\n        if (response.status === 404) {\n          setError('Post not found');\n          return;\n        }\n        throw new Error('Failed to fetch post');\n      }\n\n      const result = await response.json();\n\n      if (!result.success || !result.data) {\n        setError('Post not found');\n        return;\n      }\n\n      setPost(result.data);\n    } catch (err) {\n      console.error('Error fetching post:', err);\n      setError('Failed to load post');\n    } finally {\n      setLoading(false);\n    }\n  }, [params]);\n\n  useEffect(() => {\n    if (status === 'unauthenticated') {\n      router.push('/login');\n      return;\n    }\n\n    if (status === 'authenticated') {\n      fetchPost();\n    }\n  }, [status, fetchPost, router]);\n\n  const onSubmitReply = async (data: ReplyForm) => {\n    if (status !== 'authenticated') {\n      setError('You must be logged in to reply');\n      return;\n    }\n\n    setIsSubmittingReply(true);\n    setError(null);\n\n    try {\n      const resolvedParams = await params;\n      const response = await fetch(`/api/forum/posts/${resolvedParams.postId}/replies`, {\n        method: 'POST',\n        headers: getHeaders(),\n        body: JSON.stringify({\n          content: data.content.trim(),\n        }),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to create reply');\n      }\n\n      const newReply = await response.json();\n      \n      // Add the new reply to the post\n      if (post) {\n        setPost({\n          ...post,\n          replies: [...post.replies, newReply],\n        });\n      }\n      \n      reset(); // Clear the form\n    } catch (err) {\n      console.error('Error creating reply:', err);\n      setError(err instanceof Error ? err.message : 'Failed to create reply');\n    } finally {\n      setIsSubmittingReply(false);\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit',\n    });\n  };\n\n  const getDisplayName = (author: { name?: string; email: string }) => {\n    return author.name || author.email.split('@')[0];\n  };\n\n  if (status === 'loading' || loading) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <FormLoadingState message=\"Loading forum post...\" />\n      </div>\n    );\n  }\n\n  if (status === 'unauthenticated') {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <p>Please <Link href=\"/login\" className=\"text-blue-600 hover:underline\">log in</Link> to view this post.</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !post) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <div className=\"text-center\">\n          <p className=\"text-red-600 dark:text-red-400\">{error || 'Post not found'}</p>\n          <Link href=\"/forum\" className=\"text-blue-600 hover:underline mt-4 inline-block\">\n            Back to Forum\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"container mx-auto px-4 py-8 max-w-4xl\">\n      <div className=\"mb-6\">\n        <Link\n          href=\"/forum\"\n          className=\"inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300\"\n        >\n          <ArrowLeft className=\"h-4 w-4\" />\n          Back to Forum\n        </Link>\n      </div>\n\n      {/* Main Post */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8\">\n        <div className=\"space-y-6\">\n          {/* Post Header */}\n          <div className=\"flex justify-between items-start\">\n            <div className=\"flex-1\">\n              <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\">\n                {post.title}\n              </h1>\n              {post.category && (\n                <div className=\"mb-4\">\n                  <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\">\n                    {post.category}\n                  </span>\n                </div>\n              )}\n            </div>\n            {post.viewCount !== undefined && (\n              <div className=\"flex items-center text-sm text-gray-500 dark:text-gray-400\">\n                <Eye className=\"h-4 w-4 mr-1\" />\n                <span>{post.viewCount} views</span>\n              </div>\n            )}\n          </div>\n\n          {/* Post Content */}\n          <div className=\"prose dark:prose-invert max-w-none\">\n            <p className=\"text-gray-700 dark:text-gray-300 whitespace-pre-wrap\">\n              {post.content}\n            </p>\n          </div>\n\n          {/* Author Profile */}\n          <UserProfileCard\n            user={{\n              ...post.author,\n              joinedAt: post.createdAt,\n            }}\n            variant=\"full\"\n            showStats={true}\n          />\n\n          {/* Post Footer */}\n          <div className=\"flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700\">\n            <div className=\"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400\">\n              <div className=\"flex items-center\">\n                <Calendar className=\"h-4 w-4 mr-1\" />\n                <span>{formatDate(post.createdAt)}</span>\n              </div>\n              <div className=\"flex items-center\">\n                <MessageSquare className=\"h-4 w-4 mr-1\" />\n                <span>{post.replies.length} {post.replies.length === 1 ? 'reply' : 'replies'}</span>\n              </div>\n            </div>\n\n            {/* Reactions */}\n            <ReactionButton postId={post.id} variant=\"full\" />\n          </div>\n        </div>\n      </div>\n\n      {/* Replies Section */}\n      <div className=\"space-y-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\n          Replies ({post.replies.length})\n        </h2>\n\n        {post.replies.map((reply) => (\n          <div\n            key={reply.id}\n            className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 ml-8\"\n          >\n            <div className=\"space-y-4\">\n              {/* Reply Content */}\n              <p className=\"text-gray-700 dark:text-gray-300 whitespace-pre-wrap\">\n                {reply.content}\n              </p>\n\n              {/* Reply Author */}\n              <UserProfileCard\n                user={{\n                  ...reply.author,\n                  joinedAt: reply.createdAt,\n                }}\n                variant=\"compact\"\n                showStats={false}\n              />\n\n              {/* Reply Footer */}\n              <div className=\"flex items-center justify-between pt-2 border-t border-gray-100 dark:border-gray-700\">\n                <div className=\"flex items-center text-sm text-gray-500 dark:text-gray-400\">\n                  <Calendar className=\"h-4 w-4 mr-1\" />\n                  <span>{formatDate(reply.createdAt)}</span>\n                </div>\n\n                {/* Reply Reactions */}\n                <ReactionButton replyId={reply.id} variant=\"compact\" />\n              </div>\n            </div>\n          </div>\n        ))}\n\n        {/* Reply Form */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6\">\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">\n            Add a Reply\n          </h3>\n\n          {error && (\n            <div className=\"bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 p-4 rounded-md mb-4\">\n              {error}\n            </div>\n          )}\n\n          <form onSubmit={handleSubmit(onSubmitReply)} className=\"space-y-4\">\n            <div>\n              <textarea\n                id=\"content\"\n                rows={4}\n                {...register('content', {\n                  required: 'Reply content is required',\n                  maxLength: {\n                    value: 2000,\n                    message: 'Reply must be 2000 characters or less',\n                  },\n                })}\n                className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white\"\n                placeholder=\"Write your reply...\"\n                disabled={isSubmittingReply}\n              />\n              <div className=\"flex justify-between mt-1\">\n                {errors.content && (\n                  <p className=\"text-sm text-red-600 dark:text-red-400\">{errors.content.message}</p>\n                )}\n                <p className=\"text-sm text-gray-500 dark:text-gray-400 ml-auto\">\n                  {contentValue.length}/2000 characters\n                </p>\n              </div>\n            </div>\n\n            <Button\n              type=\"submit\"\n              disabled={isSubmittingReply || csrfLoading}\n              className=\"flex items-center gap-2\"\n            >\n              {isSubmittingReply ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                  Posting...\n                </>\n              ) : (\n                <>\n                  <Send className=\"h-4 w-4\" />\n                  Post Reply\n                </>\n              )}\n            </Button>\n          </form>\n        </div>\n      </div>\n    </div>\n  );\n}\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "36d39a854befeaacb4b91d8f48425413a9d07d10"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_28vvjgow87 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_28vvjgow87();
var __assign =
/* istanbul ignore next */
(cov_28vvjgow87().s[0]++,
/* istanbul ignore next */
(cov_28vvjgow87().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_28vvjgow87().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_28vvjgow87().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_28vvjgow87().f[0]++;
  cov_28vvjgow87().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_28vvjgow87().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_28vvjgow87().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_28vvjgow87().f[1]++;
    cov_28vvjgow87().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_28vvjgow87().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_28vvjgow87().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_28vvjgow87().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_28vvjgow87().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_28vvjgow87().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_28vvjgow87().b[2][0]++;
          cov_28vvjgow87().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_28vvjgow87().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_28vvjgow87().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_28vvjgow87().s[10]++;
  return __assign.apply(this, arguments);
}));
var __createBinding =
/* istanbul ignore next */
(cov_28vvjgow87().s[11]++,
/* istanbul ignore next */
(cov_28vvjgow87().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_28vvjgow87().b[3][1]++, this.__createBinding) ||
/* istanbul ignore next */
(cov_28vvjgow87().b[3][2]++, Object.create ?
/* istanbul ignore next */
(cov_28vvjgow87().b[4][0]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_28vvjgow87().f[2]++;
  cov_28vvjgow87().s[12]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_28vvjgow87().b[5][0]++;
    cov_28vvjgow87().s[13]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_28vvjgow87().b[5][1]++;
  }
  var desc =
  /* istanbul ignore next */
  (cov_28vvjgow87().s[14]++, Object.getOwnPropertyDescriptor(m, k));
  /* istanbul ignore next */
  cov_28vvjgow87().s[15]++;
  if (
  /* istanbul ignore next */
  (cov_28vvjgow87().b[7][0]++, !desc) ||
  /* istanbul ignore next */
  (cov_28vvjgow87().b[7][1]++, "get" in desc ?
  /* istanbul ignore next */
  (cov_28vvjgow87().b[8][0]++, !m.__esModule) :
  /* istanbul ignore next */
  (cov_28vvjgow87().b[8][1]++,
  /* istanbul ignore next */
  (cov_28vvjgow87().b[9][0]++, desc.writable) ||
  /* istanbul ignore next */
  (cov_28vvjgow87().b[9][1]++, desc.configurable)))) {
    /* istanbul ignore next */
    cov_28vvjgow87().b[6][0]++;
    cov_28vvjgow87().s[16]++;
    desc = {
      enumerable: true,
      get: function () {
        /* istanbul ignore next */
        cov_28vvjgow87().f[3]++;
        cov_28vvjgow87().s[17]++;
        return m[k];
      }
    };
  } else
  /* istanbul ignore next */
  {
    cov_28vvjgow87().b[6][1]++;
  }
  cov_28vvjgow87().s[18]++;
  Object.defineProperty(o, k2, desc);
}) :
/* istanbul ignore next */
(cov_28vvjgow87().b[4][1]++, function (o, m, k, k2) {
  /* istanbul ignore next */
  cov_28vvjgow87().f[4]++;
  cov_28vvjgow87().s[19]++;
  if (k2 === undefined) {
    /* istanbul ignore next */
    cov_28vvjgow87().b[10][0]++;
    cov_28vvjgow87().s[20]++;
    k2 = k;
  } else
  /* istanbul ignore next */
  {
    cov_28vvjgow87().b[10][1]++;
  }
  cov_28vvjgow87().s[21]++;
  o[k2] = m[k];
})));
var __setModuleDefault =
/* istanbul ignore next */
(cov_28vvjgow87().s[22]++,
/* istanbul ignore next */
(cov_28vvjgow87().b[11][0]++, this) &&
/* istanbul ignore next */
(cov_28vvjgow87().b[11][1]++, this.__setModuleDefault) ||
/* istanbul ignore next */
(cov_28vvjgow87().b[11][2]++, Object.create ?
/* istanbul ignore next */
(cov_28vvjgow87().b[12][0]++, function (o, v) {
  /* istanbul ignore next */
  cov_28vvjgow87().f[5]++;
  cov_28vvjgow87().s[23]++;
  Object.defineProperty(o, "default", {
    enumerable: true,
    value: v
  });
}) :
/* istanbul ignore next */
(cov_28vvjgow87().b[12][1]++, function (o, v) {
  /* istanbul ignore next */
  cov_28vvjgow87().f[6]++;
  cov_28vvjgow87().s[24]++;
  o["default"] = v;
})));
var __importStar =
/* istanbul ignore next */
(cov_28vvjgow87().s[25]++,
/* istanbul ignore next */
(cov_28vvjgow87().b[13][0]++, this) &&
/* istanbul ignore next */
(cov_28vvjgow87().b[13][1]++, this.__importStar) ||
/* istanbul ignore next */
(cov_28vvjgow87().b[13][2]++, function () {
  /* istanbul ignore next */
  cov_28vvjgow87().f[7]++;
  cov_28vvjgow87().s[26]++;
  var ownKeys = function (o) {
    /* istanbul ignore next */
    cov_28vvjgow87().f[8]++;
    cov_28vvjgow87().s[27]++;
    ownKeys =
    /* istanbul ignore next */
    (cov_28vvjgow87().b[14][0]++, Object.getOwnPropertyNames) ||
    /* istanbul ignore next */
    (cov_28vvjgow87().b[14][1]++, function (o) {
      /* istanbul ignore next */
      cov_28vvjgow87().f[9]++;
      var ar =
      /* istanbul ignore next */
      (cov_28vvjgow87().s[28]++, []);
      /* istanbul ignore next */
      cov_28vvjgow87().s[29]++;
      for (var k in o) {
        /* istanbul ignore next */
        cov_28vvjgow87().s[30]++;
        if (Object.prototype.hasOwnProperty.call(o, k)) {
          /* istanbul ignore next */
          cov_28vvjgow87().b[15][0]++;
          cov_28vvjgow87().s[31]++;
          ar[ar.length] = k;
        } else
        /* istanbul ignore next */
        {
          cov_28vvjgow87().b[15][1]++;
        }
      }
      /* istanbul ignore next */
      cov_28vvjgow87().s[32]++;
      return ar;
    });
    /* istanbul ignore next */
    cov_28vvjgow87().s[33]++;
    return ownKeys(o);
  };
  /* istanbul ignore next */
  cov_28vvjgow87().s[34]++;
  return function (mod) {
    /* istanbul ignore next */
    cov_28vvjgow87().f[10]++;
    cov_28vvjgow87().s[35]++;
    if (
    /* istanbul ignore next */
    (cov_28vvjgow87().b[17][0]++, mod) &&
    /* istanbul ignore next */
    (cov_28vvjgow87().b[17][1]++, mod.__esModule)) {
      /* istanbul ignore next */
      cov_28vvjgow87().b[16][0]++;
      cov_28vvjgow87().s[36]++;
      return mod;
    } else
    /* istanbul ignore next */
    {
      cov_28vvjgow87().b[16][1]++;
    }
    var result =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[37]++, {});
    /* istanbul ignore next */
    cov_28vvjgow87().s[38]++;
    if (mod != null) {
      /* istanbul ignore next */
      cov_28vvjgow87().b[18][0]++;
      cov_28vvjgow87().s[39]++;
      for (var k =
        /* istanbul ignore next */
        (cov_28vvjgow87().s[40]++, ownKeys(mod)), i =
        /* istanbul ignore next */
        (cov_28vvjgow87().s[41]++, 0); i < k.length; i++) {
        /* istanbul ignore next */
        cov_28vvjgow87().s[42]++;
        if (k[i] !== "default") {
          /* istanbul ignore next */
          cov_28vvjgow87().b[19][0]++;
          cov_28vvjgow87().s[43]++;
          __createBinding(result, mod, k[i]);
        } else
        /* istanbul ignore next */
        {
          cov_28vvjgow87().b[19][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_28vvjgow87().b[18][1]++;
    }
    cov_28vvjgow87().s[44]++;
    __setModuleDefault(result, mod);
    /* istanbul ignore next */
    cov_28vvjgow87().s[45]++;
    return result;
  };
}()));
var __awaiter =
/* istanbul ignore next */
(cov_28vvjgow87().s[46]++,
/* istanbul ignore next */
(cov_28vvjgow87().b[20][0]++, this) &&
/* istanbul ignore next */
(cov_28vvjgow87().b[20][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_28vvjgow87().b[20][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_28vvjgow87().f[11]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_28vvjgow87().f[12]++;
    cov_28vvjgow87().s[47]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_28vvjgow87().b[21][0]++, value) :
    /* istanbul ignore next */
    (cov_28vvjgow87().b[21][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_28vvjgow87().f[13]++;
      cov_28vvjgow87().s[48]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_28vvjgow87().s[49]++;
  return new (
  /* istanbul ignore next */
  (cov_28vvjgow87().b[22][0]++, P) ||
  /* istanbul ignore next */
  (cov_28vvjgow87().b[22][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_28vvjgow87().f[14]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_28vvjgow87().f[15]++;
      cov_28vvjgow87().s[50]++;
      try {
        /* istanbul ignore next */
        cov_28vvjgow87().s[51]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_28vvjgow87().s[52]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_28vvjgow87().f[16]++;
      cov_28vvjgow87().s[53]++;
      try {
        /* istanbul ignore next */
        cov_28vvjgow87().s[54]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_28vvjgow87().s[55]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_28vvjgow87().f[17]++;
      cov_28vvjgow87().s[56]++;
      result.done ?
      /* istanbul ignore next */
      (cov_28vvjgow87().b[23][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_28vvjgow87().b[23][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_28vvjgow87().s[57]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_28vvjgow87().b[24][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_28vvjgow87().b[24][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_28vvjgow87().s[58]++,
/* istanbul ignore next */
(cov_28vvjgow87().b[25][0]++, this) &&
/* istanbul ignore next */
(cov_28vvjgow87().b[25][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_28vvjgow87().b[25][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_28vvjgow87().f[18]++;
  var _ =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[59]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_28vvjgow87().f[19]++;
        cov_28vvjgow87().s[60]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_28vvjgow87().b[26][0]++;
          cov_28vvjgow87().s[61]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_28vvjgow87().b[26][1]++;
        }
        cov_28vvjgow87().s[62]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[63]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_28vvjgow87().b[27][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_28vvjgow87().b[27][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_28vvjgow87().s[64]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_28vvjgow87().b[28][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_28vvjgow87().b[28][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_28vvjgow87().f[20]++;
    cov_28vvjgow87().s[65]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_28vvjgow87().f[21]++;
    cov_28vvjgow87().s[66]++;
    return function (v) {
      /* istanbul ignore next */
      cov_28vvjgow87().f[22]++;
      cov_28vvjgow87().s[67]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_28vvjgow87().f[23]++;
    cov_28vvjgow87().s[68]++;
    if (f) {
      /* istanbul ignore next */
      cov_28vvjgow87().b[29][0]++;
      cov_28vvjgow87().s[69]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_28vvjgow87().b[29][1]++;
    }
    cov_28vvjgow87().s[70]++;
    while (
    /* istanbul ignore next */
    (cov_28vvjgow87().b[30][0]++, g) &&
    /* istanbul ignore next */
    (cov_28vvjgow87().b[30][1]++, g = 0,
    /* istanbul ignore next */
    (cov_28vvjgow87().b[31][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_28vvjgow87().b[31][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_28vvjgow87().s[71]++;
      try {
        /* istanbul ignore next */
        cov_28vvjgow87().s[72]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_28vvjgow87().b[33][0]++, y) &&
        /* istanbul ignore next */
        (cov_28vvjgow87().b[33][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_28vvjgow87().b[34][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_28vvjgow87().b[34][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_28vvjgow87().b[35][0]++,
        /* istanbul ignore next */
        (cov_28vvjgow87().b[36][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_28vvjgow87().b[36][1]++,
        /* istanbul ignore next */
        (cov_28vvjgow87().b[37][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_28vvjgow87().b[37][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_28vvjgow87().b[35][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_28vvjgow87().b[33][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_28vvjgow87().b[32][0]++;
          cov_28vvjgow87().s[73]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_28vvjgow87().b[32][1]++;
        }
        cov_28vvjgow87().s[74]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_28vvjgow87().b[38][0]++;
          cov_28vvjgow87().s[75]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_28vvjgow87().b[38][1]++;
        }
        cov_28vvjgow87().s[76]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_28vvjgow87().b[39][0]++;
          case 1:
            /* istanbul ignore next */
            cov_28vvjgow87().b[39][1]++;
            cov_28vvjgow87().s[77]++;
            t = op;
            /* istanbul ignore next */
            cov_28vvjgow87().s[78]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_28vvjgow87().b[39][2]++;
            cov_28vvjgow87().s[79]++;
            _.label++;
            /* istanbul ignore next */
            cov_28vvjgow87().s[80]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_28vvjgow87().b[39][3]++;
            cov_28vvjgow87().s[81]++;
            _.label++;
            /* istanbul ignore next */
            cov_28vvjgow87().s[82]++;
            y = op[1];
            /* istanbul ignore next */
            cov_28vvjgow87().s[83]++;
            op = [0];
            /* istanbul ignore next */
            cov_28vvjgow87().s[84]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_28vvjgow87().b[39][4]++;
            cov_28vvjgow87().s[85]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_28vvjgow87().s[86]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_28vvjgow87().s[87]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_28vvjgow87().b[39][5]++;
            cov_28vvjgow87().s[88]++;
            if (
            /* istanbul ignore next */
            (cov_28vvjgow87().b[41][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_28vvjgow87().b[42][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_28vvjgow87().b[42][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_28vvjgow87().b[41][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_28vvjgow87().b[41][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_28vvjgow87().b[40][0]++;
              cov_28vvjgow87().s[89]++;
              _ = 0;
              /* istanbul ignore next */
              cov_28vvjgow87().s[90]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_28vvjgow87().b[40][1]++;
            }
            cov_28vvjgow87().s[91]++;
            if (
            /* istanbul ignore next */
            (cov_28vvjgow87().b[44][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_28vvjgow87().b[44][1]++, !t) ||
            /* istanbul ignore next */
            (cov_28vvjgow87().b[44][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_28vvjgow87().b[44][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_28vvjgow87().b[43][0]++;
              cov_28vvjgow87().s[92]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_28vvjgow87().s[93]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_28vvjgow87().b[43][1]++;
            }
            cov_28vvjgow87().s[94]++;
            if (
            /* istanbul ignore next */
            (cov_28vvjgow87().b[46][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_28vvjgow87().b[46][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_28vvjgow87().b[45][0]++;
              cov_28vvjgow87().s[95]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_28vvjgow87().s[96]++;
              t = op;
              /* istanbul ignore next */
              cov_28vvjgow87().s[97]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_28vvjgow87().b[45][1]++;
            }
            cov_28vvjgow87().s[98]++;
            if (
            /* istanbul ignore next */
            (cov_28vvjgow87().b[48][0]++, t) &&
            /* istanbul ignore next */
            (cov_28vvjgow87().b[48][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_28vvjgow87().b[47][0]++;
              cov_28vvjgow87().s[99]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_28vvjgow87().s[100]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_28vvjgow87().s[101]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_28vvjgow87().b[47][1]++;
            }
            cov_28vvjgow87().s[102]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_28vvjgow87().b[49][0]++;
              cov_28vvjgow87().s[103]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_28vvjgow87().b[49][1]++;
            }
            cov_28vvjgow87().s[104]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_28vvjgow87().s[105]++;
            continue;
        }
        /* istanbul ignore next */
        cov_28vvjgow87().s[106]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_28vvjgow87().s[107]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_28vvjgow87().s[108]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_28vvjgow87().s[109]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_28vvjgow87().s[110]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_28vvjgow87().b[50][0]++;
      cov_28vvjgow87().s[111]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_28vvjgow87().b[50][1]++;
    }
    cov_28vvjgow87().s[112]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_28vvjgow87().b[51][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_28vvjgow87().b[51][1]++, void 0),
      done: true
    };
  }
}));
var __spreadArray =
/* istanbul ignore next */
(cov_28vvjgow87().s[113]++,
/* istanbul ignore next */
(cov_28vvjgow87().b[52][0]++, this) &&
/* istanbul ignore next */
(cov_28vvjgow87().b[52][1]++, this.__spreadArray) ||
/* istanbul ignore next */
(cov_28vvjgow87().b[52][2]++, function (to, from, pack) {
  /* istanbul ignore next */
  cov_28vvjgow87().f[24]++;
  cov_28vvjgow87().s[114]++;
  if (
  /* istanbul ignore next */
  (cov_28vvjgow87().b[54][0]++, pack) ||
  /* istanbul ignore next */
  (cov_28vvjgow87().b[54][1]++, arguments.length === 2)) {
    /* istanbul ignore next */
    cov_28vvjgow87().b[53][0]++;
    cov_28vvjgow87().s[115]++;
    for (var i =
      /* istanbul ignore next */
      (cov_28vvjgow87().s[116]++, 0), l =
      /* istanbul ignore next */
      (cov_28vvjgow87().s[117]++, from.length), ar; i < l; i++) {
      /* istanbul ignore next */
      cov_28vvjgow87().s[118]++;
      if (
      /* istanbul ignore next */
      (cov_28vvjgow87().b[56][0]++, ar) ||
      /* istanbul ignore next */
      (cov_28vvjgow87().b[56][1]++, !(i in from))) {
        /* istanbul ignore next */
        cov_28vvjgow87().b[55][0]++;
        cov_28vvjgow87().s[119]++;
        if (!ar) {
          /* istanbul ignore next */
          cov_28vvjgow87().b[57][0]++;
          cov_28vvjgow87().s[120]++;
          ar = Array.prototype.slice.call(from, 0, i);
        } else
        /* istanbul ignore next */
        {
          cov_28vvjgow87().b[57][1]++;
        }
        cov_28vvjgow87().s[121]++;
        ar[i] = from[i];
      } else
      /* istanbul ignore next */
      {
        cov_28vvjgow87().b[55][1]++;
      }
    }
  } else
  /* istanbul ignore next */
  {
    cov_28vvjgow87().b[53][1]++;
  }
  cov_28vvjgow87().s[122]++;
  return to.concat(
  /* istanbul ignore next */
  (cov_28vvjgow87().b[58][0]++, ar) ||
  /* istanbul ignore next */
  (cov_28vvjgow87().b[58][1]++, Array.prototype.slice.call(from)));
}));
var __importDefault =
/* istanbul ignore next */
(cov_28vvjgow87().s[123]++,
/* istanbul ignore next */
(cov_28vvjgow87().b[59][0]++, this) &&
/* istanbul ignore next */
(cov_28vvjgow87().b[59][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_28vvjgow87().b[59][2]++, function (mod) {
  /* istanbul ignore next */
  cov_28vvjgow87().f[25]++;
  cov_28vvjgow87().s[124]++;
  return /* istanbul ignore next */(cov_28vvjgow87().b[61][0]++, mod) &&
  /* istanbul ignore next */
  (cov_28vvjgow87().b[61][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_28vvjgow87().b[60][0]++, mod) :
  /* istanbul ignore next */
  (cov_28vvjgow87().b[60][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_28vvjgow87().s[125]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_28vvjgow87().s[126]++;
exports.default = ForumPostPage;
var jsx_runtime_1 =
/* istanbul ignore next */
(cov_28vvjgow87().s[127]++, require("react/jsx-runtime"));
var react_1 =
/* istanbul ignore next */
(cov_28vvjgow87().s[128]++, __importStar(require("react")));
var react_2 =
/* istanbul ignore next */
(cov_28vvjgow87().s[129]++, require("next-auth/react"));
var navigation_1 =
/* istanbul ignore next */
(cov_28vvjgow87().s[130]++, require("next/navigation"));
var link_1 =
/* istanbul ignore next */
(cov_28vvjgow87().s[131]++, __importDefault(require("next/link")));
var lucide_react_1 =
/* istanbul ignore next */
(cov_28vvjgow87().s[132]++, require("lucide-react"));
var button_1 =
/* istanbul ignore next */
(cov_28vvjgow87().s[133]++, require("@/components/ui/button"));
var react_hook_form_1 =
/* istanbul ignore next */
(cov_28vvjgow87().s[134]++, require("react-hook-form"));
var UserProfileCard_1 =
/* istanbul ignore next */
(cov_28vvjgow87().s[135]++, __importDefault(require("@/components/forum/UserProfileCard")));
var ReactionButton_1 =
/* istanbul ignore next */
(cov_28vvjgow87().s[136]++, __importDefault(require("@/components/forum/ReactionButton")));
var useCSRF_1 =
/* istanbul ignore next */
(cov_28vvjgow87().s[137]++, require("@/hooks/useCSRF"));
var page_loading_states_1 =
/* istanbul ignore next */
(cov_28vvjgow87().s[138]++, require("@/components/ui/page-loading-states"));
function ForumPostPage(_a) {
  /* istanbul ignore next */
  cov_28vvjgow87().f[26]++;
  var _this =
  /* istanbul ignore next */
  (cov_28vvjgow87().s[139]++, this);
  var params =
  /* istanbul ignore next */
  (cov_28vvjgow87().s[140]++, _a.params);
  var status =
  /* istanbul ignore next */
  (cov_28vvjgow87().s[141]++, (0, react_2.useSession)().status);
  var router =
  /* istanbul ignore next */
  (cov_28vvjgow87().s[142]++, (0, navigation_1.useRouter)());
  var _b =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[143]++, (0, react_1.useState)(null)),
    post =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[144]++, _b[0]),
    setPost =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[145]++, _b[1]);
  var _c =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[146]++, (0, react_1.useState)(true)),
    loading =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[147]++, _c[0]),
    setLoading =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[148]++, _c[1]);
  var _d =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[149]++, (0, react_1.useState)(null)),
    error =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[150]++, _d[0]),
    setError =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[151]++, _d[1]);
  var _e =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[152]++, (0, react_1.useState)(false)),
    isSubmittingReply =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[153]++, _e[0]),
    setIsSubmittingReply =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[154]++, _e[1]);
  var _f =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[155]++, (0, useCSRF_1.useCSRF)()),
    getHeaders =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[156]++, _f.getHeaders),
    csrfLoading =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[157]++, _f.isLoading);
  var _g =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[158]++, (0, react_hook_form_1.useForm)()),
    register =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[159]++, _g.register),
    handleSubmit =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[160]++, _g.handleSubmit),
    errors =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[161]++, _g.formState.errors),
    reset =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[162]++, _g.reset),
    watch =
    /* istanbul ignore next */
    (cov_28vvjgow87().s[163]++, _g.watch);
  var contentValue =
  /* istanbul ignore next */
  (cov_28vvjgow87().s[164]++, watch('content', ''));
  var fetchPost =
  /* istanbul ignore next */
  (cov_28vvjgow87().s[165]++, (0, react_1.useCallback)(function () {
    /* istanbul ignore next */
    cov_28vvjgow87().f[27]++;
    cov_28vvjgow87().s[166]++;
    return __awaiter(_this, void 0, void 0, function () {
      /* istanbul ignore next */
      cov_28vvjgow87().f[28]++;
      var resolvedParams, response, result, err_1;
      /* istanbul ignore next */
      cov_28vvjgow87().s[167]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_28vvjgow87().f[29]++;
        cov_28vvjgow87().s[168]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_28vvjgow87().b[62][0]++;
            cov_28vvjgow87().s[169]++;
            _a.trys.push([0, 4, 5, 6]);
            /* istanbul ignore next */
            cov_28vvjgow87().s[170]++;
            setLoading(true);
            /* istanbul ignore next */
            cov_28vvjgow87().s[171]++;
            return [4 /*yield*/, params];
          case 1:
            /* istanbul ignore next */
            cov_28vvjgow87().b[62][1]++;
            cov_28vvjgow87().s[172]++;
            resolvedParams = _a.sent();
            /* istanbul ignore next */
            cov_28vvjgow87().s[173]++;
            return [4 /*yield*/, fetch("/api/forum/posts/".concat(resolvedParams.postId))];
          case 2:
            /* istanbul ignore next */
            cov_28vvjgow87().b[62][2]++;
            cov_28vvjgow87().s[174]++;
            response = _a.sent();
            /* istanbul ignore next */
            cov_28vvjgow87().s[175]++;
            if (!response.ok) {
              /* istanbul ignore next */
              cov_28vvjgow87().b[63][0]++;
              cov_28vvjgow87().s[176]++;
              if (response.status === 404) {
                /* istanbul ignore next */
                cov_28vvjgow87().b[64][0]++;
                cov_28vvjgow87().s[177]++;
                setError('Post not found');
                /* istanbul ignore next */
                cov_28vvjgow87().s[178]++;
                return [2 /*return*/];
              } else
              /* istanbul ignore next */
              {
                cov_28vvjgow87().b[64][1]++;
              }
              cov_28vvjgow87().s[179]++;
              throw new Error('Failed to fetch post');
            } else
            /* istanbul ignore next */
            {
              cov_28vvjgow87().b[63][1]++;
            }
            cov_28vvjgow87().s[180]++;
            return [4 /*yield*/, response.json()];
          case 3:
            /* istanbul ignore next */
            cov_28vvjgow87().b[62][3]++;
            cov_28vvjgow87().s[181]++;
            result = _a.sent();
            /* istanbul ignore next */
            cov_28vvjgow87().s[182]++;
            if (
            /* istanbul ignore next */
            (cov_28vvjgow87().b[66][0]++, !result.success) ||
            /* istanbul ignore next */
            (cov_28vvjgow87().b[66][1]++, !result.data)) {
              /* istanbul ignore next */
              cov_28vvjgow87().b[65][0]++;
              cov_28vvjgow87().s[183]++;
              setError('Post not found');
              /* istanbul ignore next */
              cov_28vvjgow87().s[184]++;
              return [2 /*return*/];
            } else
            /* istanbul ignore next */
            {
              cov_28vvjgow87().b[65][1]++;
            }
            cov_28vvjgow87().s[185]++;
            setPost(result.data);
            /* istanbul ignore next */
            cov_28vvjgow87().s[186]++;
            return [3 /*break*/, 6];
          case 4:
            /* istanbul ignore next */
            cov_28vvjgow87().b[62][4]++;
            cov_28vvjgow87().s[187]++;
            err_1 = _a.sent();
            /* istanbul ignore next */
            cov_28vvjgow87().s[188]++;
            console.error('Error fetching post:', err_1);
            /* istanbul ignore next */
            cov_28vvjgow87().s[189]++;
            setError('Failed to load post');
            /* istanbul ignore next */
            cov_28vvjgow87().s[190]++;
            return [3 /*break*/, 6];
          case 5:
            /* istanbul ignore next */
            cov_28vvjgow87().b[62][5]++;
            cov_28vvjgow87().s[191]++;
            setLoading(false);
            /* istanbul ignore next */
            cov_28vvjgow87().s[192]++;
            return [7 /*endfinally*/];
          case 6:
            /* istanbul ignore next */
            cov_28vvjgow87().b[62][6]++;
            cov_28vvjgow87().s[193]++;
            return [2 /*return*/];
        }
      });
    });
  }, [params]));
  /* istanbul ignore next */
  cov_28vvjgow87().s[194]++;
  (0, react_1.useEffect)(function () {
    /* istanbul ignore next */
    cov_28vvjgow87().f[30]++;
    cov_28vvjgow87().s[195]++;
    if (status === 'unauthenticated') {
      /* istanbul ignore next */
      cov_28vvjgow87().b[67][0]++;
      cov_28vvjgow87().s[196]++;
      router.push('/login');
      /* istanbul ignore next */
      cov_28vvjgow87().s[197]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_28vvjgow87().b[67][1]++;
    }
    cov_28vvjgow87().s[198]++;
    if (status === 'authenticated') {
      /* istanbul ignore next */
      cov_28vvjgow87().b[68][0]++;
      cov_28vvjgow87().s[199]++;
      fetchPost();
    } else
    /* istanbul ignore next */
    {
      cov_28vvjgow87().b[68][1]++;
    }
  }, [status, fetchPost, router]);
  /* istanbul ignore next */
  cov_28vvjgow87().s[200]++;
  var onSubmitReply = function (data) {
    /* istanbul ignore next */
    cov_28vvjgow87().f[31]++;
    cov_28vvjgow87().s[201]++;
    return __awaiter(_this, void 0, void 0, function () {
      /* istanbul ignore next */
      cov_28vvjgow87().f[32]++;
      var resolvedParams, response, errorData, newReply, err_2;
      /* istanbul ignore next */
      cov_28vvjgow87().s[202]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_28vvjgow87().f[33]++;
        cov_28vvjgow87().s[203]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_28vvjgow87().b[69][0]++;
            cov_28vvjgow87().s[204]++;
            if (status !== 'authenticated') {
              /* istanbul ignore next */
              cov_28vvjgow87().b[70][0]++;
              cov_28vvjgow87().s[205]++;
              setError('You must be logged in to reply');
              /* istanbul ignore next */
              cov_28vvjgow87().s[206]++;
              return [2 /*return*/];
            } else
            /* istanbul ignore next */
            {
              cov_28vvjgow87().b[70][1]++;
            }
            cov_28vvjgow87().s[207]++;
            setIsSubmittingReply(true);
            /* istanbul ignore next */
            cov_28vvjgow87().s[208]++;
            setError(null);
            /* istanbul ignore next */
            cov_28vvjgow87().s[209]++;
            _a.label = 1;
          case 1:
            /* istanbul ignore next */
            cov_28vvjgow87().b[69][1]++;
            cov_28vvjgow87().s[210]++;
            _a.trys.push([1, 7, 8, 9]);
            /* istanbul ignore next */
            cov_28vvjgow87().s[211]++;
            return [4 /*yield*/, params];
          case 2:
            /* istanbul ignore next */
            cov_28vvjgow87().b[69][2]++;
            cov_28vvjgow87().s[212]++;
            resolvedParams = _a.sent();
            /* istanbul ignore next */
            cov_28vvjgow87().s[213]++;
            return [4 /*yield*/, fetch("/api/forum/posts/".concat(resolvedParams.postId, "/replies"), {
              method: 'POST',
              headers: getHeaders(),
              body: JSON.stringify({
                content: data.content.trim()
              })
            })];
          case 3:
            /* istanbul ignore next */
            cov_28vvjgow87().b[69][3]++;
            cov_28vvjgow87().s[214]++;
            response = _a.sent();
            /* istanbul ignore next */
            cov_28vvjgow87().s[215]++;
            if (!!response.ok) {
              /* istanbul ignore next */
              cov_28vvjgow87().b[71][0]++;
              cov_28vvjgow87().s[216]++;
              return [3 /*break*/, 5];
            } else
            /* istanbul ignore next */
            {
              cov_28vvjgow87().b[71][1]++;
            }
            cov_28vvjgow87().s[217]++;
            return [4 /*yield*/, response.json()];
          case 4:
            /* istanbul ignore next */
            cov_28vvjgow87().b[69][4]++;
            cov_28vvjgow87().s[218]++;
            errorData = _a.sent();
            /* istanbul ignore next */
            cov_28vvjgow87().s[219]++;
            throw new Error(
            /* istanbul ignore next */
            (cov_28vvjgow87().b[72][0]++, errorData.error) ||
            /* istanbul ignore next */
            (cov_28vvjgow87().b[72][1]++, 'Failed to create reply'));
          case 5:
            /* istanbul ignore next */
            cov_28vvjgow87().b[69][5]++;
            cov_28vvjgow87().s[220]++;
            return [4 /*yield*/, response.json()];
          case 6:
            /* istanbul ignore next */
            cov_28vvjgow87().b[69][6]++;
            cov_28vvjgow87().s[221]++;
            newReply = _a.sent();
            // Add the new reply to the post
            /* istanbul ignore next */
            cov_28vvjgow87().s[222]++;
            if (post) {
              /* istanbul ignore next */
              cov_28vvjgow87().b[73][0]++;
              cov_28vvjgow87().s[223]++;
              setPost(__assign(__assign({}, post), {
                replies: __spreadArray(__spreadArray([], post.replies, true), [newReply], false)
              }));
            } else
            /* istanbul ignore next */
            {
              cov_28vvjgow87().b[73][1]++;
            }
            cov_28vvjgow87().s[224]++;
            reset(); // Clear the form
            /* istanbul ignore next */
            cov_28vvjgow87().s[225]++;
            return [3 /*break*/, 9];
          case 7:
            /* istanbul ignore next */
            cov_28vvjgow87().b[69][7]++;
            cov_28vvjgow87().s[226]++;
            err_2 = _a.sent();
            /* istanbul ignore next */
            cov_28vvjgow87().s[227]++;
            console.error('Error creating reply:', err_2);
            /* istanbul ignore next */
            cov_28vvjgow87().s[228]++;
            setError(err_2 instanceof Error ?
            /* istanbul ignore next */
            (cov_28vvjgow87().b[74][0]++, err_2.message) :
            /* istanbul ignore next */
            (cov_28vvjgow87().b[74][1]++, 'Failed to create reply'));
            /* istanbul ignore next */
            cov_28vvjgow87().s[229]++;
            return [3 /*break*/, 9];
          case 8:
            /* istanbul ignore next */
            cov_28vvjgow87().b[69][8]++;
            cov_28vvjgow87().s[230]++;
            setIsSubmittingReply(false);
            /* istanbul ignore next */
            cov_28vvjgow87().s[231]++;
            return [7 /*endfinally*/];
          case 9:
            /* istanbul ignore next */
            cov_28vvjgow87().b[69][9]++;
            cov_28vvjgow87().s[232]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /* istanbul ignore next */
  cov_28vvjgow87().s[233]++;
  var formatDate = function (dateString) {
    /* istanbul ignore next */
    cov_28vvjgow87().f[34]++;
    cov_28vvjgow87().s[234]++;
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  /* istanbul ignore next */
  cov_28vvjgow87().s[235]++;
  var getDisplayName = function (author) {
    /* istanbul ignore next */
    cov_28vvjgow87().f[35]++;
    cov_28vvjgow87().s[236]++;
    return /* istanbul ignore next */(cov_28vvjgow87().b[75][0]++, author.name) ||
    /* istanbul ignore next */
    (cov_28vvjgow87().b[75][1]++, author.email.split('@')[0]);
  };
  /* istanbul ignore next */
  cov_28vvjgow87().s[237]++;
  if (
  /* istanbul ignore next */
  (cov_28vvjgow87().b[77][0]++, status === 'loading') ||
  /* istanbul ignore next */
  (cov_28vvjgow87().b[77][1]++, loading)) {
    /* istanbul ignore next */
    cov_28vvjgow87().b[76][0]++;
    cov_28vvjgow87().s[238]++;
    return (0, jsx_runtime_1.jsx)("div", {
      className: "container mx-auto px-4 py-8",
      children: (0, jsx_runtime_1.jsx)(page_loading_states_1.FormLoadingState, {
        message: "Loading forum post..."
      })
    });
  } else
  /* istanbul ignore next */
  {
    cov_28vvjgow87().b[76][1]++;
  }
  cov_28vvjgow87().s[239]++;
  if (status === 'unauthenticated') {
    /* istanbul ignore next */
    cov_28vvjgow87().b[78][0]++;
    cov_28vvjgow87().s[240]++;
    return (0, jsx_runtime_1.jsx)("div", {
      className: "container mx-auto px-4 py-8",
      children: (0, jsx_runtime_1.jsx)("div", {
        className: "text-center",
        children: (0, jsx_runtime_1.jsxs)("p", {
          children: ["Please ", (0, jsx_runtime_1.jsx)(link_1.default, {
            href: "/login",
            className: "text-blue-600 hover:underline",
            children: "log in"
          }), " to view this post."]
        })
      })
    });
  } else
  /* istanbul ignore next */
  {
    cov_28vvjgow87().b[78][1]++;
  }
  cov_28vvjgow87().s[241]++;
  if (
  /* istanbul ignore next */
  (cov_28vvjgow87().b[80][0]++, error) ||
  /* istanbul ignore next */
  (cov_28vvjgow87().b[80][1]++, !post)) {
    /* istanbul ignore next */
    cov_28vvjgow87().b[79][0]++;
    cov_28vvjgow87().s[242]++;
    return (0, jsx_runtime_1.jsx)("div", {
      className: "container mx-auto px-4 py-8",
      children: (0, jsx_runtime_1.jsxs)("div", {
        className: "text-center",
        children: [(0, jsx_runtime_1.jsx)("p", {
          className: "text-red-600 dark:text-red-400",
          children:
          /* istanbul ignore next */
          (cov_28vvjgow87().b[81][0]++, error) ||
          /* istanbul ignore next */
          (cov_28vvjgow87().b[81][1]++, 'Post not found')
        }), (0, jsx_runtime_1.jsx)(link_1.default, {
          href: "/forum",
          className: "text-blue-600 hover:underline mt-4 inline-block",
          children: "Back to Forum"
        })]
      })
    });
  } else
  /* istanbul ignore next */
  {
    cov_28vvjgow87().b[79][1]++;
  }
  cov_28vvjgow87().s[243]++;
  return (0, jsx_runtime_1.jsxs)("div", {
    className: "container mx-auto px-4 py-8 max-w-4xl",
    children: [(0, jsx_runtime_1.jsx)("div", {
      className: "mb-6",
      children: (0, jsx_runtime_1.jsxs)(link_1.default, {
        href: "/forum",
        className: "inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300",
        children: [(0, jsx_runtime_1.jsx)(lucide_react_1.ArrowLeft, {
          className: "h-4 w-4"
        }), "Back to Forum"]
      })
    }), (0, jsx_runtime_1.jsx)("div", {
      className: "bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8",
      children: (0, jsx_runtime_1.jsxs)("div", {
        className: "space-y-6",
        children: [(0, jsx_runtime_1.jsxs)("div", {
          className: "flex justify-between items-start",
          children: [(0, jsx_runtime_1.jsxs)("div", {
            className: "flex-1",
            children: [(0, jsx_runtime_1.jsx)("h1", {
              className: "text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2",
              children: post.title
            }),
            /* istanbul ignore next */
            (cov_28vvjgow87().b[82][0]++, post.category) &&
            /* istanbul ignore next */
            (cov_28vvjgow87().b[82][1]++, (0, jsx_runtime_1.jsx)("div", {
              className: "mb-4",
              children: (0, jsx_runtime_1.jsx)("span", {
                className: "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
                children: post.category
              })
            }))]
          }),
          /* istanbul ignore next */
          (cov_28vvjgow87().b[83][0]++, post.viewCount !== undefined) &&
          /* istanbul ignore next */
          (cov_28vvjgow87().b[83][1]++, (0, jsx_runtime_1.jsxs)("div", {
            className: "flex items-center text-sm text-gray-500 dark:text-gray-400",
            children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Eye, {
              className: "h-4 w-4 mr-1"
            }), (0, jsx_runtime_1.jsxs)("span", {
              children: [post.viewCount, " views"]
            })]
          }))]
        }), (0, jsx_runtime_1.jsx)("div", {
          className: "prose dark:prose-invert max-w-none",
          children: (0, jsx_runtime_1.jsx)("p", {
            className: "text-gray-700 dark:text-gray-300 whitespace-pre-wrap",
            children: post.content
          })
        }), (0, jsx_runtime_1.jsx)(UserProfileCard_1.default, {
          user: __assign(__assign({}, post.author), {
            joinedAt: post.createdAt
          }),
          variant: "full",
          showStats: true
        }), (0, jsx_runtime_1.jsxs)("div", {
          className: "flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700",
          children: [(0, jsx_runtime_1.jsxs)("div", {
            className: "flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400",
            children: [(0, jsx_runtime_1.jsxs)("div", {
              className: "flex items-center",
              children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Calendar, {
                className: "h-4 w-4 mr-1"
              }), (0, jsx_runtime_1.jsx)("span", {
                children: formatDate(post.createdAt)
              })]
            }), (0, jsx_runtime_1.jsxs)("div", {
              className: "flex items-center",
              children: [(0, jsx_runtime_1.jsx)(lucide_react_1.MessageSquare, {
                className: "h-4 w-4 mr-1"
              }), (0, jsx_runtime_1.jsxs)("span", {
                children: [post.replies.length, " ", post.replies.length === 1 ?
                /* istanbul ignore next */
                (cov_28vvjgow87().b[84][0]++, 'reply') :
                /* istanbul ignore next */
                (cov_28vvjgow87().b[84][1]++, 'replies')]
              })]
            })]
          }), (0, jsx_runtime_1.jsx)(ReactionButton_1.default, {
            postId: post.id,
            variant: "full"
          })]
        })]
      })
    }), (0, jsx_runtime_1.jsxs)("div", {
      className: "space-y-6",
      children: [(0, jsx_runtime_1.jsxs)("h2", {
        className: "text-xl font-semibold text-gray-900 dark:text-gray-100",
        children: ["Replies (", post.replies.length, ")"]
      }), post.replies.map(function (reply) {
        /* istanbul ignore next */
        cov_28vvjgow87().f[36]++;
        cov_28vvjgow87().s[244]++;
        return (0, jsx_runtime_1.jsx)("div", {
          className: "bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 ml-8",
          children: (0, jsx_runtime_1.jsxs)("div", {
            className: "space-y-4",
            children: [(0, jsx_runtime_1.jsx)("p", {
              className: "text-gray-700 dark:text-gray-300 whitespace-pre-wrap",
              children: reply.content
            }), (0, jsx_runtime_1.jsx)(UserProfileCard_1.default, {
              user: __assign(__assign({}, reply.author), {
                joinedAt: reply.createdAt
              }),
              variant: "compact",
              showStats: false
            }), (0, jsx_runtime_1.jsxs)("div", {
              className: "flex items-center justify-between pt-2 border-t border-gray-100 dark:border-gray-700",
              children: [(0, jsx_runtime_1.jsxs)("div", {
                className: "flex items-center text-sm text-gray-500 dark:text-gray-400",
                children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Calendar, {
                  className: "h-4 w-4 mr-1"
                }), (0, jsx_runtime_1.jsx)("span", {
                  children: formatDate(reply.createdAt)
                })]
              }), (0, jsx_runtime_1.jsx)(ReactionButton_1.default, {
                replyId: reply.id,
                variant: "compact"
              })]
            })]
          })
        }, reply.id);
      }), (0, jsx_runtime_1.jsxs)("div", {
        className: "bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6",
        children: [(0, jsx_runtime_1.jsx)("h3", {
          className: "text-lg font-medium text-gray-900 dark:text-gray-100 mb-4",
          children: "Add a Reply"
        }),
        /* istanbul ignore next */
        (cov_28vvjgow87().b[85][0]++, error) &&
        /* istanbul ignore next */
        (cov_28vvjgow87().b[85][1]++, (0, jsx_runtime_1.jsx)("div", {
          className: "bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 p-4 rounded-md mb-4",
          children: error
        })), (0, jsx_runtime_1.jsxs)("form", {
          onSubmit: handleSubmit(onSubmitReply),
          className: "space-y-4",
          children: [(0, jsx_runtime_1.jsxs)("div", {
            children: [(0, jsx_runtime_1.jsx)("textarea", __assign({
              id: "content",
              rows: 4
            }, register('content', {
              required: 'Reply content is required',
              maxLength: {
                value: 2000,
                message: 'Reply must be 2000 characters or less'
              }
            }), {
              className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white",
              placeholder: "Write your reply...",
              disabled: isSubmittingReply
            })), (0, jsx_runtime_1.jsxs)("div", {
              className: "flex justify-between mt-1",
              children: [
              /* istanbul ignore next */
              (cov_28vvjgow87().b[86][0]++, errors.content) &&
              /* istanbul ignore next */
              (cov_28vvjgow87().b[86][1]++, (0, jsx_runtime_1.jsx)("p", {
                className: "text-sm text-red-600 dark:text-red-400",
                children: errors.content.message
              })), (0, jsx_runtime_1.jsxs)("p", {
                className: "text-sm text-gray-500 dark:text-gray-400 ml-auto",
                children: [contentValue.length, "/2000 characters"]
              })]
            })]
          }), (0, jsx_runtime_1.jsx)(button_1.Button, {
            type: "submit",
            disabled:
            /* istanbul ignore next */
            (cov_28vvjgow87().b[87][0]++, isSubmittingReply) ||
            /* istanbul ignore next */
            (cov_28vvjgow87().b[87][1]++, csrfLoading),
            className: "flex items-center gap-2",
            children: isSubmittingReply ?
            /* istanbul ignore next */
            (cov_28vvjgow87().b[88][0]++, (0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, {
              children: [(0, jsx_runtime_1.jsx)("div", {
                className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white"
              }), "Posting..."]
            })) :
            /* istanbul ignore next */
            (cov_28vvjgow87().b[88][1]++, (0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, {
              children: [(0, jsx_runtime_1.jsx)(lucide_react_1.Send, {
                className: "h-4 w-4"
              }), "Post Reply"]
            }))
          })]
        })]
      })]
    })]
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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