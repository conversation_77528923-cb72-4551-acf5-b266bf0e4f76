3cceadfe84cd22c2c7c0b4a4cc6aa45c
"use strict";

/* istanbul ignore next */
function cov_1u8lalrqaz() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/profile/route.ts";
  var hash = "8862b66f15c8c9302f43e32e9ee37f295a3fc551";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/profile/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 22
        },
        end: {
          line: 40,
          column: 1
        }
      },
      "68": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 39,
          column: 62
        }
      },
      "69": {
        start: {
          line: 41,
          column: 0
        },
        end: {
          line: 41,
          column: 62
        }
      },
      "70": {
        start: {
          line: 42,
          column: 0
        },
        end: {
          line: 42,
          column: 35
        }
      },
      "71": {
        start: {
          line: 43,
          column: 15
        },
        end: {
          line: 43,
          column: 37
        }
      },
      "72": {
        start: {
          line: 44,
          column: 13
        },
        end: {
          line: 44,
          column: 38
        }
      },
      "73": {
        start: {
          line: 45,
          column: 13
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "74": {
        start: {
          line: 46,
          column: 15
        },
        end: {
          line: 46,
          column: 55
        }
      },
      "75": {
        start: {
          line: 47,
          column: 15
        },
        end: {
          line: 47,
          column: 38
        }
      },
      "76": {
        start: {
          line: 48,
          column: 34
        },
        end: {
          line: 48,
          column: 76
        }
      },
      "77": {
        start: {
          line: 49,
          column: 13
        },
        end: {
          line: 49,
          column: 34
        }
      },
      "78": {
        start: {
          line: 50,
          column: 18
        },
        end: {
          line: 50,
          column: 44
        }
      },
      "79": {
        start: {
          line: 51,
          column: 35
        },
        end: {
          line: 51,
          column: 87
        }
      },
      "80": {
        start: {
          line: 52,
          column: 35
        },
        end: {
          line: 52,
          column: 87
        }
      },
      "81": {
        start: {
          line: 53,
          column: 25
        },
        end: {
          line: 53,
          column: 58
        }
      },
      "82": {
        start: {
          line: 56,
          column: 25
        },
        end: {
          line: 56,
          column: 27
        }
      },
      "83": {
        start: {
          line: 57,
          column: 24
        },
        end: {
          line: 57,
          column: 26
        }
      },
      "84": {
        start: {
          line: 59,
          column: 21
        },
        end: {
          line: 64,
          column: 5
        }
      },
      "85": {
        start: {
          line: 66,
          column: 4
        },
        end: {
          line: 104,
          column: 7
        }
      },
      "86": {
        start: {
          line: 67,
          column: 8
        },
        end: {
          line: 103,
          column: 9
        }
      },
      "87": {
        start: {
          line: 68,
          column: 29
        },
        end: {
          line: 68,
          column: 48
        }
      },
      "88": {
        start: {
          line: 69,
          column: 29
        },
        end: {
          line: 69,
          column: 81
        }
      },
      "89": {
        start: {
          line: 70,
          column: 12
        },
        end: {
          line: 72,
          column: 13
        }
      },
      "90": {
        start: {
          line: 71,
          column: 16
        },
        end: {
          line: 71,
          column: 98
        }
      },
      "91": {
        start: {
          line: 74,
          column: 12
        },
        end: {
          line: 102,
          column: 13
        }
      },
      "92": {
        start: {
          line: 76,
          column: 16
        },
        end: {
          line: 80,
          column: 19
        }
      },
      "93": {
        start: {
          line: 82,
          column: 17
        },
        end: {
          line: 102,
          column: 13
        }
      },
      "94": {
        start: {
          line: 84,
          column: 33
        },
        end: {
          line: 84,
          column: 138
        }
      },
      "95": {
        start: {
          line: 85,
          column: 16
        },
        end: {
          line: 94,
          column: 17
        }
      },
      "96": {
        start: {
          line: 86,
          column: 20
        },
        end: {
          line: 86,
          column: 82
        }
      },
      "97": {
        start: {
          line: 87,
          column: 20
        },
        end: {
          line: 87,
          column: 46
        }
      },
      "98": {
        start: {
          line: 90,
          column: 20
        },
        end: {
          line: 93,
          column: 23
        }
      },
      "99": {
        start: {
          line: 98,
          column: 16
        },
        end: {
          line: 101,
          column: 19
        }
      },
      "100": {
        start: {
          line: 106,
          column: 22
        },
        end: {
          line: 106,
          column: 58
        }
      },
      "101": {
        start: {
          line: 107,
          column: 4
        },
        end: {
          line: 137,
          column: 7
        }
      },
      "102": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 136,
          column: 9
        }
      },
      "103": {
        start: {
          line: 109,
          column: 12
        },
        end: {
          line: 135,
          column: 13
        }
      },
      "104": {
        start: {
          line: 110,
          column: 16
        },
        end: {
          line: 123,
          column: 73
        }
      },
      "105": {
        start: {
          line: 113,
          column: 34
        },
        end: {
          line: 113,
          column: 46
        }
      },
      "106": {
        start: {
          line: 114,
          column: 37
        },
        end: {
          line: 114,
          column: 86
        }
      },
      "107": {
        start: {
          line: 115,
          column: 20
        },
        end: {
          line: 117,
          column: 21
        }
      },
      "108": {
        start: {
          line: 116,
          column: 24
        },
        end: {
          line: 116,
          column: 111
        }
      },
      "109": {
        start: {
          line: 118,
          column: 20
        },
        end: {
          line: 121,
          column: 23
        }
      },
      "110": {
        start: {
          line: 123,
          column: 46
        },
        end: {
          line: 123,
          column: 69
        }
      },
      "111": {
        start: {
          line: 127,
          column: 33
        },
        end: {
          line: 127,
          column: 94
        }
      },
      "112": {
        start: {
          line: 128,
          column: 16
        },
        end: {
          line: 130,
          column: 17
        }
      },
      "113": {
        start: {
          line: 129,
          column: 20
        },
        end: {
          line: 129,
          column: 102
        }
      },
      "114": {
        start: {
          line: 131,
          column: 16
        },
        end: {
          line: 134,
          column: 19
        }
      },
      "115": {
        start: {
          line: 139,
          column: 24
        },
        end: {
          line: 139,
          column: 117
        }
      },
      "116": {
        start: {
          line: 140,
          column: 4
        },
        end: {
          line: 144,
          column: 7
        }
      },
      "117": {
        start: {
          line: 141,
          column: 8
        },
        end: {
          line: 143,
          column: 9
        }
      },
      "118": {
        start: {
          line: 142,
          column: 12
        },
        end: {
          line: 142,
          column: 56
        }
      },
      "119": {
        start: {
          line: 146,
          column: 4
        },
        end: {
          line: 155,
          column: 5
        }
      },
      "120": {
        start: {
          line: 147,
          column: 19
        },
        end: {
          line: 147,
          column: 50
        }
      },
      "121": {
        start: {
          line: 148,
          column: 8
        },
        end: {
          line: 154,
          column: 9
        }
      },
      "122": {
        start: {
          line: 149,
          column: 12
        },
        end: {
          line: 149,
          column: 69
        }
      },
      "123": {
        start: {
          line: 150,
          column: 12
        },
        end: {
          line: 150,
          column: 52
        }
      },
      "124": {
        start: {
          line: 153,
          column: 12
        },
        end: {
          line: 153,
          column: 52
        }
      },
      "125": {
        start: {
          line: 157,
          column: 32
        },
        end: {
          line: 157,
          column: 82
        }
      },
      "126": {
        start: {
          line: 158,
          column: 4
        },
        end: {
          line: 164,
          column: 5
        }
      },
      "127": {
        start: {
          line: 159,
          column: 8
        },
        end: {
          line: 159,
          column: 62
        }
      },
      "128": {
        start: {
          line: 160,
          column: 8
        },
        end: {
          line: 160,
          column: 45
        }
      },
      "129": {
        start: {
          line: 163,
          column: 8
        },
        end: {
          line: 163,
          column: 61
        }
      },
      "130": {
        start: {
          line: 165,
          column: 33
        },
        end: {
          line: 165,
          column: 72
        }
      },
      "131": {
        start: {
          line: 166,
          column: 4
        },
        end: {
          line: 172,
          column: 5
        }
      },
      "132": {
        start: {
          line: 167,
          column: 8
        },
        end: {
          line: 167,
          column: 64
        }
      },
      "133": {
        start: {
          line: 168,
          column: 8
        },
        end: {
          line: 168,
          column: 59
        }
      },
      "134": {
        start: {
          line: 171,
          column: 8
        },
        end: {
          line: 171,
          column: 85
        }
      },
      "135": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 192,
          column: 5
        }
      },
      "136": {
        start: {
          line: 175,
          column: 18
        },
        end: {
          line: 175,
          column: 48
        }
      },
      "137": {
        start: {
          line: 176,
          column: 25
        },
        end: {
          line: 176,
          column: 70
        }
      },
      "138": {
        start: {
          line: 177,
          column: 8
        },
        end: {
          line: 179,
          column: 9
        }
      },
      "139": {
        start: {
          line: 178,
          column: 12
        },
        end: {
          line: 178,
          column: 93
        }
      },
      "140": {
        start: {
          line: 181,
          column: 30
        },
        end: {
          line: 181,
          column: 80
        }
      },
      "141": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 191,
          column: 9
        }
      },
      "142": {
        start: {
          line: 183,
          column: 12
        },
        end: {
          line: 183,
          column: 79
        }
      },
      "143": {
        start: {
          line: 184,
          column: 12
        },
        end: {
          line: 184,
          column: 51
        }
      },
      "144": {
        start: {
          line: 187,
          column: 12
        },
        end: {
          line: 190,
          column: 15
        }
      },
      "145": {
        start: {
          line: 194,
          column: 4
        },
        end: {
          line: 219,
          column: 5
        }
      },
      "146": {
        start: {
          line: 195,
          column: 8
        },
        end: {
          line: 218,
          column: 9
        }
      },
      "147": {
        start: {
          line: 196,
          column: 35
        },
        end: {
          line: 196,
          column: 37
        }
      },
      "148": {
        start: {
          line: 197,
          column: 37
        },
        end: {
          line: 197,
          column: 94
        }
      },
      "149": {
        start: {
          line: 198,
          column: 12
        },
        end: {
          line: 216,
          column: 15
        }
      },
      "150": {
        start: {
          line: 199,
          column: 16
        },
        end: {
          line: 215,
          column: 17
        }
      },
      "151": {
        start: {
          line: 200,
          column: 30
        },
        end: {
          line: 200,
          column: 69
        }
      },
      "152": {
        start: {
          line: 201,
          column: 37
        },
        end: {
          line: 201,
          column: 82
        }
      },
      "153": {
        start: {
          line: 202,
          column: 20
        },
        end: {
          line: 204,
          column: 21
        }
      },
      "154": {
        start: {
          line: 203,
          column: 24
        },
        end: {
          line: 203,
          column: 126
        }
      },
      "155": {
        start: {
          line: 205,
          column: 37
        },
        end: {
          line: 205,
          column: 53
        }
      },
      "156": {
        start: {
          line: 206,
          column: 20
        },
        end: {
          line: 214,
          column: 21
        }
      },
      "157": {
        start: {
          line: 207,
          column: 24
        },
        end: {
          line: 207,
          column: 106
        }
      },
      "158": {
        start: {
          line: 210,
          column: 24
        },
        end: {
          line: 213,
          column: 27
        }
      },
      "159": {
        start: {
          line: 217,
          column: 12
        },
        end: {
          line: 217,
          column: 62
        }
      },
      "160": {
        start: {
          line: 220,
          column: 4
        },
        end: {
          line: 220,
          column: 76
        }
      },
      "161": {
        start: {
          line: 224,
          column: 17
        },
        end: {
          line: 240,
          column: 5
        }
      },
      "162": {
        start: {
          line: 241,
          column: 26
        },
        end: {
          line: 241,
          column: 27
        }
      },
      "163": {
        start: {
          line: 242,
          column: 4
        },
        end: {
          line: 258,
          column: 7
        }
      },
      "164": {
        start: {
          line: 243,
          column: 20
        },
        end: {
          line: 243,
          column: 38
        }
      },
      "165": {
        start: {
          line: 244,
          column: 8
        },
        end: {
          line: 257,
          column: 9
        }
      },
      "166": {
        start: {
          line: 245,
          column: 12
        },
        end: {
          line: 256,
          column: 13
        }
      },
      "167": {
        start: {
          line: 246,
          column: 16
        },
        end: {
          line: 246,
          column: 34
        }
      },
      "168": {
        start: {
          line: 248,
          column: 17
        },
        end: {
          line: 256,
          column: 13
        }
      },
      "169": {
        start: {
          line: 249,
          column: 16
        },
        end: {
          line: 249,
          column: 34
        }
      },
      "170": {
        start: {
          line: 251,
          column: 17
        },
        end: {
          line: 256,
          column: 13
        }
      },
      "171": {
        start: {
          line: 252,
          column: 16
        },
        end: {
          line: 252,
          column: 34
        }
      },
      "172": {
        start: {
          line: 254,
          column: 17
        },
        end: {
          line: 256,
          column: 13
        }
      },
      "173": {
        start: {
          line: 255,
          column: 16
        },
        end: {
          line: 255,
          column: 34
        }
      },
      "174": {
        start: {
          line: 259,
          column: 4
        },
        end: {
          line: 259,
          column: 63
        }
      },
      "175": {
        start: {
          line: 261,
          column: 0
        },
        end: {
          line: 333,
          column: 7
        }
      },
      "176": {
        start: {
          line: 261,
          column: 93
        },
        end: {
          line: 333,
          column: 3
        }
      },
      "177": {
        start: {
          line: 264,
          column: 4
        },
        end: {
          line: 332,
          column: 7
        }
      },
      "178": {
        start: {
          line: 265,
          column: 8
        },
        end: {
          line: 331,
          column: 9
        }
      },
      "179": {
        start: {
          line: 267,
          column: 16
        },
        end: {
          line: 267,
          column: 39
        }
      },
      "180": {
        start: {
          line: 268,
          column: 16
        },
        end: {
          line: 268,
          column: 87
        }
      },
      "181": {
        start: {
          line: 270,
          column: 16
        },
        end: {
          line: 270,
          column: 36
        }
      },
      "182": {
        start: {
          line: 271,
          column: 16
        },
        end: {
          line: 279,
          column: 17
        }
      },
      "183": {
        start: {
          line: 272,
          column: 20
        },
        end: {
          line: 275,
          column: 23
        }
      },
      "184": {
        start: {
          line: 276,
          column: 20
        },
        end: {
          line: 276,
          column: 59
        }
      },
      "185": {
        start: {
          line: 277,
          column: 20
        },
        end: {
          line: 277,
          column: 43
        }
      },
      "186": {
        start: {
          line: 278,
          column: 20
        },
        end: {
          line: 278,
          column: 32
        }
      },
      "187": {
        start: {
          line: 280,
          column: 16
        },
        end: {
          line: 284,
          column: 19
        }
      },
      "188": {
        start: {
          line: 285,
          column: 16
        },
        end: {
          line: 285,
          column: 41
        }
      },
      "189": {
        start: {
          line: 286,
          column: 16
        },
        end: {
          line: 289,
          column: 24
        }
      },
      "190": {
        start: {
          line: 291,
          column: 16
        },
        end: {
          line: 291,
          column: 33
        }
      },
      "191": {
        start: {
          line: 292,
          column: 16
        },
        end: {
          line: 292,
          column: 54
        }
      },
      "192": {
        start: {
          line: 293,
          column: 16
        },
        end: {
          line: 295,
          column: 19
        }
      },
      "193": {
        start: {
          line: 296,
          column: 16
        },
        end: {
          line: 304,
          column: 17
        }
      },
      "194": {
        start: {
          line: 297,
          column: 20
        },
        end: {
          line: 300,
          column: 23
        }
      },
      "195": {
        start: {
          line: 301,
          column: 20
        },
        end: {
          line: 301,
          column: 56
        }
      },
      "196": {
        start: {
          line: 302,
          column: 20
        },
        end: {
          line: 302,
          column: 43
        }
      },
      "197": {
        start: {
          line: 303,
          column: 20
        },
        end: {
          line: 303,
          column: 32
        }
      },
      "198": {
        start: {
          line: 305,
          column: 16
        },
        end: {
          line: 305,
          column: 39
        }
      },
      "199": {
        start: {
          line: 306,
          column: 16
        },
        end: {
          line: 306,
          column: 55
        }
      },
      "200": {
        start: {
          line: 306,
          column: 31
        },
        end: {
          line: 306,
          column: 55
        }
      },
      "201": {
        start: {
          line: 307,
          column: 16
        },
        end: {
          line: 310,
          column: 19
        }
      },
      "202": {
        start: {
          line: 311,
          column: 16
        },
        end: {
          line: 311,
          column: 45
        }
      },
      "203": {
        start: {
          line: 312,
          column: 16
        },
        end: {
          line: 316,
          column: 24
        }
      },
      "204": {
        start: {
          line: 318,
          column: 16
        },
        end: {
          line: 318,
          column: 36
        }
      },
      "205": {
        start: {
          line: 319,
          column: 16
        },
        end: {
          line: 319,
          column: 62
        }
      },
      "206": {
        start: {
          line: 320,
          column: 16
        },
        end: {
          line: 322,
          column: 19
        }
      },
      "207": {
        start: {
          line: 323,
          column: 16
        },
        end: {
          line: 323,
          column: 29
        }
      },
      "208": {
        start: {
          line: 325,
          column: 16
        },
        end: {
          line: 325,
          column: 55
        }
      },
      "209": {
        start: {
          line: 326,
          column: 16
        },
        end: {
          line: 329,
          column: 19
        }
      },
      "210": {
        start: {
          line: 330,
          column: 16
        },
        end: {
          line: 330,
          column: 100
        }
      },
      "211": {
        start: {
          line: 334,
          column: 0
        },
        end: {
          line: 483,
          column: 7
        }
      },
      "212": {
        start: {
          line: 334,
          column: 93
        },
        end: {
          line: 483,
          column: 3
        }
      },
      "213": {
        start: {
          line: 335,
          column: 4
        },
        end: {
          line: 482,
          column: 7
        }
      },
      "214": {
        start: {
          line: 336,
          column: 8
        },
        end: {
          line: 481,
          column: 20
        }
      },
      "215": {
        start: {
          line: 336,
          column: 84
        },
        end: {
          line: 481,
          column: 15
        }
      },
      "216": {
        start: {
          line: 337,
          column: 16
        },
        end: {
          line: 480,
          column: 19
        }
      },
      "217": {
        start: {
          line: 338,
          column: 20
        },
        end: {
          line: 479,
          column: 32
        }
      },
      "218": {
        start: {
          line: 338,
          column: 143
        },
        end: {
          line: 479,
          column: 27
        }
      },
      "219": {
        start: {
          line: 341,
          column: 28
        },
        end: {
          line: 478,
          column: 31
        }
      },
      "220": {
        start: {
          line: 342,
          column: 32
        },
        end: {
          line: 477,
          column: 33
        }
      },
      "221": {
        start: {
          line: 343,
          column: 44
        },
        end: {
          line: 343,
          column: 115
        }
      },
      "222": {
        start: {
          line: 345,
          column: 40
        },
        end: {
          line: 345,
          column: 60
        }
      },
      "223": {
        start: {
          line: 346,
          column: 40
        },
        end: {
          line: 350,
          column: 41
        }
      },
      "224": {
        start: {
          line: 347,
          column: 44
        },
        end: {
          line: 347,
          column: 83
        }
      },
      "225": {
        start: {
          line: 348,
          column: 44
        },
        end: {
          line: 348,
          column: 67
        }
      },
      "226": {
        start: {
          line: 349,
          column: 44
        },
        end: {
          line: 349,
          column: 56
        }
      },
      "227": {
        start: {
          line: 351,
          column: 40
        },
        end: {
          line: 353,
          column: 48
        }
      },
      "228": {
        start: {
          line: 355,
          column: 40
        },
        end: {
          line: 355,
          column: 57
        }
      },
      "229": {
        start: {
          line: 356,
          column: 40
        },
        end: {
          line: 360,
          column: 41
        }
      },
      "230": {
        start: {
          line: 357,
          column: 44
        },
        end: {
          line: 357,
          column: 80
        }
      },
      "231": {
        start: {
          line: 358,
          column: 44
        },
        end: {
          line: 358,
          column: 67
        }
      },
      "232": {
        start: {
          line: 359,
          column: 44
        },
        end: {
          line: 359,
          column: 56
        }
      },
      "233": {
        start: {
          line: 361,
          column: 40
        },
        end: {
          line: 361,
          column: 77
        }
      },
      "234": {
        start: {
          line: 363,
          column: 40
        },
        end: {
          line: 363,
          column: 60
        }
      },
      "235": {
        start: {
          line: 364,
          column: 40
        },
        end: {
          line: 364,
          column: 144
        }
      },
      "236": {
        start: {
          line: 366,
          column: 40
        },
        end: {
          line: 378,
          column: 41
        }
      },
      "237": {
        start: {
          line: 367,
          column: 44
        },
        end: {
          line: 371,
          column: 47
        }
      },
      "238": {
        start: {
          line: 379,
          column: 40
        },
        end: {
          line: 379,
          column: 938
        }
      },
      "239": {
        start: {
          line: 380,
          column: 40
        },
        end: {
          line: 396,
          column: 43
        }
      },
      "240": {
        start: {
          line: 397,
          column: 40
        },
        end: {
          line: 455,
          column: 48
        }
      },
      "241": {
        start: {
          line: 457,
          column: 40
        },
        end: {
          line: 457,
          column: 67
        }
      },
      "242": {
        start: {
          line: 458,
          column: 40
        },
        end: {
          line: 458,
          column: 115
        }
      },
      "243": {
        start: {
          line: 459,
          column: 40
        },
        end: {
          line: 459,
          column: 139
        }
      },
      "244": {
        start: {
          line: 460,
          column: 40
        },
        end: {
          line: 460,
          column: 53
        }
      },
      "245": {
        start: {
          line: 462,
          column: 40
        },
        end: {
          line: 462,
          column: 66
        }
      },
      "246": {
        start: {
          line: 463,
          column: 40
        },
        end: {
          line: 463,
          column: 120
        }
      },
      "247": {
        start: {
          line: 465,
          column: 40
        },
        end: {
          line: 465,
          column: 50
        }
      },
      "248": {
        start: {
          line: 466,
          column: 40
        },
        end: {
          line: 466,
          column: 64
        }
      },
      "249": {
        start: {
          line: 468,
          column: 40
        },
        end: {
          line: 468,
          column: 65
        }
      },
      "250": {
        start: {
          line: 470,
          column: 40
        },
        end: {
          line: 474,
          column: 43
        }
      },
      "251": {
        start: {
          line: 475,
          column: 40
        },
        end: {
          line: 475,
          column: 64
        }
      },
      "252": {
        start: {
          line: 476,
          column: 44
        },
        end: {
          line: 476,
          column: 135
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 38,
            column: 57
          }
        },
        loc: {
          start: {
            line: 38,
            column: 71
          },
          end: {
            line: 40,
            column: 1
          }
        },
        line: 38
      },
      "14": {
        name: "sanitizeProfileData",
        decl: {
          start: {
            line: 55,
            column: 9
          },
          end: {
            line: 55,
            column: 28
          }
        },
        loc: {
          start: {
            line: 55,
            column: 35
          },
          end: {
            line: 221,
            column: 1
          }
        },
        line: 55
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 66,
            column: 23
          },
          end: {
            line: 66,
            column: 24
          }
        },
        loc: {
          start: {
            line: 66,
            column: 40
          },
          end: {
            line: 104,
            column: 5
          }
        },
        line: 66
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 107,
            column: 24
          },
          end: {
            line: 107,
            column: 25
          }
        },
        loc: {
          start: {
            line: 107,
            column: 41
          },
          end: {
            line: 137,
            column: 5
          }
        },
        line: 107
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 112,
            column: 25
          },
          end: {
            line: 112,
            column: 26
          }
        },
        loc: {
          start: {
            line: 112,
            column: 41
          },
          end: {
            line: 122,
            column: 17
          }
        },
        line: 112
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 123,
            column: 28
          },
          end: {
            line: 123,
            column: 29
          }
        },
        loc: {
          start: {
            line: 123,
            column: 44
          },
          end: {
            line: 123,
            column: 71
          }
        },
        line: 123
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 140,
            column: 26
          },
          end: {
            line: 140,
            column: 27
          }
        },
        loc: {
          start: {
            line: 140,
            column: 43
          },
          end: {
            line: 144,
            column: 5
          }
        },
        line: 140
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 198,
            column: 55
          },
          end: {
            line: 198,
            column: 56
          }
        },
        loc: {
          start: {
            line: 198,
            column: 75
          },
          end: {
            line: 216,
            column: 13
          }
        },
        line: 198
      },
      "21": {
        name: "calculateProfileCompletionScore",
        decl: {
          start: {
            line: 223,
            column: 9
          },
          end: {
            line: 223,
            column: 40
          }
        },
        loc: {
          start: {
            line: 223,
            column: 54
          },
          end: {
            line: 260,
            column: 1
          }
        },
        line: 223
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 242,
            column: 19
          },
          end: {
            line: 242,
            column: 20
          }
        },
        loc: {
          start: {
            line: 242,
            column: 36
          },
          end: {
            line: 258,
            column: 5
          }
        },
        line: 242
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 261,
            column: 72
          },
          end: {
            line: 261,
            column: 73
          }
        },
        loc: {
          start: {
            line: 261,
            column: 91
          },
          end: {
            line: 333,
            column: 5
          }
        },
        line: 261
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 261,
            column: 135
          },
          end: {
            line: 261,
            column: 136
          }
        },
        loc: {
          start: {
            line: 261,
            column: 147
          },
          end: {
            line: 333,
            column: 1
          }
        },
        line: 261
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 264,
            column: 29
          },
          end: {
            line: 264,
            column: 30
          }
        },
        loc: {
          start: {
            line: 264,
            column: 43
          },
          end: {
            line: 332,
            column: 5
          }
        },
        line: 264
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 334,
            column: 72
          },
          end: {
            line: 334,
            column: 73
          }
        },
        loc: {
          start: {
            line: 334,
            column: 91
          },
          end: {
            line: 483,
            column: 5
          }
        },
        line: 334
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 334,
            column: 134
          },
          end: {
            line: 334,
            column: 135
          }
        },
        loc: {
          start: {
            line: 334,
            column: 146
          },
          end: {
            line: 483,
            column: 1
          }
        },
        line: 334
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 335,
            column: 29
          },
          end: {
            line: 335,
            column: 30
          }
        },
        loc: {
          start: {
            line: 335,
            column: 43
          },
          end: {
            line: 482,
            column: 5
          }
        },
        line: 335
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 336,
            column: 70
          },
          end: {
            line: 336,
            column: 71
          }
        },
        loc: {
          start: {
            line: 336,
            column: 82
          },
          end: {
            line: 481,
            column: 17
          }
        },
        line: 336
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 336,
            column: 125
          },
          end: {
            line: 336,
            column: 126
          }
        },
        loc: {
          start: {
            line: 336,
            column: 137
          },
          end: {
            line: 481,
            column: 13
          }
        },
        line: 336
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 337,
            column: 41
          },
          end: {
            line: 337,
            column: 42
          }
        },
        loc: {
          start: {
            line: 337,
            column: 55
          },
          end: {
            line: 480,
            column: 17
          }
        },
        line: 337
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 338,
            column: 129
          },
          end: {
            line: 338,
            column: 130
          }
        },
        loc: {
          start: {
            line: 338,
            column: 141
          },
          end: {
            line: 479,
            column: 29
          }
        },
        line: 338
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 338,
            column: 184
          },
          end: {
            line: 338,
            column: 185
          }
        },
        loc: {
          start: {
            line: 338,
            column: 196
          },
          end: {
            line: 479,
            column: 25
          }
        },
        line: 338
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 341,
            column: 53
          },
          end: {
            line: 341,
            column: 54
          }
        },
        loc: {
          start: {
            line: 341,
            column: 67
          },
          end: {
            line: 478,
            column: 29
          }
        },
        line: 341
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 38,
            column: 22
          },
          end: {
            line: 40,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 23
          },
          end: {
            line: 38,
            column: 27
          }
        }, {
          start: {
            line: 38,
            column: 31
          },
          end: {
            line: 38,
            column: 51
          }
        }, {
          start: {
            line: 38,
            column: 56
          },
          end: {
            line: 40,
            column: 1
          }
        }],
        line: 38
      },
      "33": {
        loc: {
          start: {
            line: 39,
            column: 11
          },
          end: {
            line: 39,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 39,
            column: 37
          },
          end: {
            line: 39,
            column: 40
          }
        }, {
          start: {
            line: 39,
            column: 43
          },
          end: {
            line: 39,
            column: 61
          }
        }],
        line: 39
      },
      "34": {
        loc: {
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 12
          },
          end: {
            line: 39,
            column: 15
          }
        }, {
          start: {
            line: 39,
            column: 19
          },
          end: {
            line: 39,
            column: 33
          }
        }],
        line: 39
      },
      "35": {
        loc: {
          start: {
            line: 67,
            column: 8
          },
          end: {
            line: 103,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 67,
            column: 8
          },
          end: {
            line: 103,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 67
      },
      "36": {
        loc: {
          start: {
            line: 67,
            column: 12
          },
          end: {
            line: 67,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 67,
            column: 12
          },
          end: {
            line: 67,
            column: 37
          }
        }, {
          start: {
            line: 67,
            column: 41
          },
          end: {
            line: 67,
            column: 61
          }
        }],
        line: 67
      },
      "37": {
        loc: {
          start: {
            line: 70,
            column: 12
          },
          end: {
            line: 72,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 70,
            column: 12
          },
          end: {
            line: 72,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 70
      },
      "38": {
        loc: {
          start: {
            line: 74,
            column: 12
          },
          end: {
            line: 102,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 12
          },
          end: {
            line: 102,
            column: 13
          }
        }, {
          start: {
            line: 82,
            column: 17
          },
          end: {
            line: 102,
            column: 13
          }
        }],
        line: 74
      },
      "39": {
        loc: {
          start: {
            line: 74,
            column: 16
          },
          end: {
            line: 74,
            column: 99
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 74,
            column: 16
          },
          end: {
            line: 74,
            column: 31
          }
        }, {
          start: {
            line: 74,
            column: 35
          },
          end: {
            line: 74,
            column: 64
          }
        }, {
          start: {
            line: 74,
            column: 68
          },
          end: {
            line: 74,
            column: 99
          }
        }],
        line: 74
      },
      "40": {
        loc: {
          start: {
            line: 82,
            column: 17
          },
          end: {
            line: 102,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 82,
            column: 17
          },
          end: {
            line: 102,
            column: 13
          }
        }, {
          start: {
            line: 96,
            column: 17
          },
          end: {
            line: 102,
            column: 13
          }
        }],
        line: 82
      },
      "41": {
        loc: {
          start: {
            line: 85,
            column: 16
          },
          end: {
            line: 94,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 85,
            column: 16
          },
          end: {
            line: 94,
            column: 17
          }
        }, {
          start: {
            line: 89,
            column: 21
          },
          end: {
            line: 94,
            column: 17
          }
        }],
        line: 85
      },
      "42": {
        loc: {
          start: {
            line: 85,
            column: 20
          },
          end: {
            line: 85,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 85,
            column: 20
          },
          end: {
            line: 85,
            column: 30
          }
        }, {
          start: {
            line: 85,
            column: 34
          },
          end: {
            line: 85,
            column: 62
          }
        }],
        line: 85
      },
      "43": {
        loc: {
          start: {
            line: 108,
            column: 8
          },
          end: {
            line: 136,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 108,
            column: 8
          },
          end: {
            line: 136,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 108
      },
      "44": {
        loc: {
          start: {
            line: 108,
            column: 12
          },
          end: {
            line: 108,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 108,
            column: 12
          },
          end: {
            line: 108,
            column: 37
          }
        }, {
          start: {
            line: 108,
            column: 41
          },
          end: {
            line: 108,
            column: 61
          }
        }],
        line: 108
      },
      "45": {
        loc: {
          start: {
            line: 109,
            column: 12
          },
          end: {
            line: 135,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 109,
            column: 12
          },
          end: {
            line: 135,
            column: 13
          }
        }, {
          start: {
            line: 125,
            column: 17
          },
          end: {
            line: 135,
            column: 13
          }
        }],
        line: 109
      },
      "46": {
        loc: {
          start: {
            line: 115,
            column: 20
          },
          end: {
            line: 117,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 115,
            column: 20
          },
          end: {
            line: 117,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 115
      },
      "47": {
        loc: {
          start: {
            line: 128,
            column: 16
          },
          end: {
            line: 130,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 128,
            column: 16
          },
          end: {
            line: 130,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 128
      },
      "48": {
        loc: {
          start: {
            line: 141,
            column: 8
          },
          end: {
            line: 143,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 141,
            column: 8
          },
          end: {
            line: 143,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 141
      },
      "49": {
        loc: {
          start: {
            line: 141,
            column: 12
          },
          end: {
            line: 141,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 141,
            column: 12
          },
          end: {
            line: 141,
            column: 37
          }
        }, {
          start: {
            line: 141,
            column: 41
          },
          end: {
            line: 141,
            column: 61
          }
        }],
        line: 141
      },
      "50": {
        loc: {
          start: {
            line: 146,
            column: 4
          },
          end: {
            line: 155,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 146,
            column: 4
          },
          end: {
            line: 155,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 146
      },
      "51": {
        loc: {
          start: {
            line: 146,
            column: 8
          },
          end: {
            line: 146,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 146,
            column: 8
          },
          end: {
            line: 146,
            column: 45
          }
        }, {
          start: {
            line: 146,
            column: 49
          },
          end: {
            line: 146,
            column: 81
          }
        }],
        line: 146
      },
      "52": {
        loc: {
          start: {
            line: 148,
            column: 8
          },
          end: {
            line: 154,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 148,
            column: 8
          },
          end: {
            line: 154,
            column: 9
          }
        }, {
          start: {
            line: 152,
            column: 13
          },
          end: {
            line: 154,
            column: 9
          }
        }],
        line: 148
      },
      "53": {
        loc: {
          start: {
            line: 148,
            column: 12
          },
          end: {
            line: 148,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 148,
            column: 12
          },
          end: {
            line: 148,
            column: 23
          }
        }, {
          start: {
            line: 148,
            column: 27
          },
          end: {
            line: 148,
            column: 35
          }
        }, {
          start: {
            line: 148,
            column: 39
          },
          end: {
            line: 148,
            column: 49
          }
        }],
        line: 148
      },
      "54": {
        loc: {
          start: {
            line: 158,
            column: 4
          },
          end: {
            line: 164,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 158,
            column: 4
          },
          end: {
            line: 164,
            column: 5
          }
        }, {
          start: {
            line: 162,
            column: 9
          },
          end: {
            line: 164,
            column: 5
          }
        }],
        line: 158
      },
      "55": {
        loc: {
          start: {
            line: 158,
            column: 8
          },
          end: {
            line: 158,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 158,
            column: 8
          },
          end: {
            line: 158,
            column: 28
          }
        }, {
          start: {
            line: 158,
            column: 32
          },
          end: {
            line: 158,
            column: 85
          }
        }],
        line: 158
      },
      "56": {
        loc: {
          start: {
            line: 166,
            column: 4
          },
          end: {
            line: 172,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 166,
            column: 4
          },
          end: {
            line: 172,
            column: 5
          }
        }, {
          start: {
            line: 170,
            column: 9
          },
          end: {
            line: 172,
            column: 5
          }
        }],
        line: 166
      },
      "57": {
        loc: {
          start: {
            line: 166,
            column: 8
          },
          end: {
            line: 166,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 166,
            column: 8
          },
          end: {
            line: 166,
            column: 30
          }
        }, {
          start: {
            line: 166,
            column: 34
          },
          end: {
            line: 166,
            column: 90
          }
        }],
        line: 166
      },
      "58": {
        loc: {
          start: {
            line: 171,
            column: 42
          },
          end: {
            line: 171,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 171,
            column: 42
          },
          end: {
            line: 171,
            column: 64
          }
        }, {
          start: {
            line: 171,
            column: 68
          },
          end: {
            line: 171,
            column: 84
          }
        }],
        line: 171
      },
      "59": {
        loc: {
          start: {
            line: 174,
            column: 4
          },
          end: {
            line: 192,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 174,
            column: 4
          },
          end: {
            line: 192,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 174
      },
      "60": {
        loc: {
          start: {
            line: 174,
            column: 8
          },
          end: {
            line: 174,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 174,
            column: 8
          },
          end: {
            line: 174,
            column: 44
          }
        }, {
          start: {
            line: 174,
            column: 48
          },
          end: {
            line: 174,
            column: 79
          }
        }],
        line: 174
      },
      "61": {
        loc: {
          start: {
            line: 177,
            column: 8
          },
          end: {
            line: 179,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 177,
            column: 8
          },
          end: {
            line: 179,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 177
      },
      "62": {
        loc: {
          start: {
            line: 182,
            column: 8
          },
          end: {
            line: 191,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 182,
            column: 8
          },
          end: {
            line: 191,
            column: 9
          }
        }, {
          start: {
            line: 186,
            column: 13
          },
          end: {
            line: 191,
            column: 9
          }
        }],
        line: 182
      },
      "63": {
        loc: {
          start: {
            line: 182,
            column: 12
          },
          end: {
            line: 182,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 182,
            column: 12
          },
          end: {
            line: 182,
            column: 15
          }
        }, {
          start: {
            line: 182,
            column: 19
          },
          end: {
            line: 182,
            column: 45
          }
        }],
        line: 182
      },
      "64": {
        loc: {
          start: {
            line: 194,
            column: 4
          },
          end: {
            line: 219,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 194,
            column: 4
          },
          end: {
            line: 219,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 194
      },
      "65": {
        loc: {
          start: {
            line: 194,
            column: 8
          },
          end: {
            line: 194,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 194,
            column: 8
          },
          end: {
            line: 194,
            column: 43
          }
        }, {
          start: {
            line: 194,
            column: 47
          },
          end: {
            line: 194,
            column: 77
          }
        }],
        line: 194
      },
      "66": {
        loc: {
          start: {
            line: 195,
            column: 8
          },
          end: {
            line: 218,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 195,
            column: 8
          },
          end: {
            line: 218,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 195
      },
      "67": {
        loc: {
          start: {
            line: 199,
            column: 16
          },
          end: {
            line: 215,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 199,
            column: 16
          },
          end: {
            line: 215,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 199
      },
      "68": {
        loc: {
          start: {
            line: 202,
            column: 20
          },
          end: {
            line: 204,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 202,
            column: 20
          },
          end: {
            line: 204,
            column: 21
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 202
      },
      "69": {
        loc: {
          start: {
            line: 206,
            column: 20
          },
          end: {
            line: 214,
            column: 21
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 206,
            column: 20
          },
          end: {
            line: 214,
            column: 21
          }
        }, {
          start: {
            line: 209,
            column: 25
          },
          end: {
            line: 214,
            column: 21
          }
        }],
        line: 206
      },
      "70": {
        loc: {
          start: {
            line: 206,
            column: 24
          },
          end: {
            line: 206,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 206,
            column: 24
          },
          end: {
            line: 206,
            column: 27
          }
        }, {
          start: {
            line: 206,
            column: 31
          },
          end: {
            line: 206,
            column: 52
          }
        }],
        line: 206
      },
      "71": {
        loc: {
          start: {
            line: 244,
            column: 8
          },
          end: {
            line: 257,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 244,
            column: 8
          },
          end: {
            line: 257,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 244
      },
      "72": {
        loc: {
          start: {
            line: 244,
            column: 12
          },
          end: {
            line: 244,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 244,
            column: 12
          },
          end: {
            line: 244,
            column: 26
          }
        }, {
          start: {
            line: 244,
            column: 30
          },
          end: {
            line: 244,
            column: 49
          }
        }, {
          start: {
            line: 244,
            column: 53
          },
          end: {
            line: 244,
            column: 65
          }
        }],
        line: 244
      },
      "73": {
        loc: {
          start: {
            line: 245,
            column: 12
          },
          end: {
            line: 256,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 12
          },
          end: {
            line: 256,
            column: 13
          }
        }, {
          start: {
            line: 248,
            column: 17
          },
          end: {
            line: 256,
            column: 13
          }
        }],
        line: 245
      },
      "74": {
        loc: {
          start: {
            line: 245,
            column: 16
          },
          end: {
            line: 245,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 245,
            column: 16
          },
          end: {
            line: 245,
            column: 36
          }
        }, {
          start: {
            line: 245,
            column: 40
          },
          end: {
            line: 245,
            column: 56
          }
        }],
        line: 245
      },
      "75": {
        loc: {
          start: {
            line: 248,
            column: 17
          },
          end: {
            line: 256,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 248,
            column: 17
          },
          end: {
            line: 256,
            column: 13
          }
        }, {
          start: {
            line: 251,
            column: 17
          },
          end: {
            line: 256,
            column: 13
          }
        }],
        line: 248
      },
      "76": {
        loc: {
          start: {
            line: 248,
            column: 21
          },
          end: {
            line: 248,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 248,
            column: 21
          },
          end: {
            line: 248,
            column: 46
          }
        }, {
          start: {
            line: 248,
            column: 50
          },
          end: {
            line: 248,
            column: 73
          }
        }],
        line: 248
      },
      "77": {
        loc: {
          start: {
            line: 251,
            column: 17
          },
          end: {
            line: 256,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 251,
            column: 17
          },
          end: {
            line: 256,
            column: 13
          }
        }, {
          start: {
            line: 254,
            column: 17
          },
          end: {
            line: 256,
            column: 13
          }
        }],
        line: 251
      },
      "78": {
        loc: {
          start: {
            line: 251,
            column: 21
          },
          end: {
            line: 251,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 251,
            column: 21
          },
          end: {
            line: 251,
            column: 46
          }
        }, {
          start: {
            line: 251,
            column: 50
          },
          end: {
            line: 251,
            column: 59
          }
        }],
        line: 251
      },
      "79": {
        loc: {
          start: {
            line: 254,
            column: 17
          },
          end: {
            line: 256,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 254,
            column: 17
          },
          end: {
            line: 256,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 254
      },
      "80": {
        loc: {
          start: {
            line: 254,
            column: 21
          },
          end: {
            line: 254,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 254,
            column: 21
          },
          end: {
            line: 254,
            column: 46
          }
        }, {
          start: {
            line: 254,
            column: 50
          },
          end: {
            line: 254,
            column: 75
          }
        }, {
          start: {
            line: 254,
            column: 79
          },
          end: {
            line: 254,
            column: 84
          }
        }],
        line: 254
      },
      "81": {
        loc: {
          start: {
            line: 265,
            column: 8
          },
          end: {
            line: 331,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 266,
            column: 12
          },
          end: {
            line: 268,
            column: 87
          }
        }, {
          start: {
            line: 269,
            column: 12
          },
          end: {
            line: 289,
            column: 24
          }
        }, {
          start: {
            line: 290,
            column: 12
          },
          end: {
            line: 316,
            column: 24
          }
        }, {
          start: {
            line: 317,
            column: 12
          },
          end: {
            line: 323,
            column: 29
          }
        }, {
          start: {
            line: 324,
            column: 12
          },
          end: {
            line: 330,
            column: 100
          }
        }],
        line: 265
      },
      "82": {
        loc: {
          start: {
            line: 271,
            column: 16
          },
          end: {
            line: 279,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 271,
            column: 16
          },
          end: {
            line: 279,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 271
      },
      "83": {
        loc: {
          start: {
            line: 271,
            column: 22
          },
          end: {
            line: 271,
            column: 137
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 271,
            column: 120
          },
          end: {
            line: 271,
            column: 126
          }
        }, {
          start: {
            line: 271,
            column: 129
          },
          end: {
            line: 271,
            column: 137
          }
        }],
        line: 271
      },
      "84": {
        loc: {
          start: {
            line: 271,
            column: 22
          },
          end: {
            line: 271,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 271,
            column: 22
          },
          end: {
            line: 271,
            column: 100
          }
        }, {
          start: {
            line: 271,
            column: 104
          },
          end: {
            line: 271,
            column: 117
          }
        }],
        line: 271
      },
      "85": {
        loc: {
          start: {
            line: 271,
            column: 28
          },
          end: {
            line: 271,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 271,
            column: 69
          },
          end: {
            line: 271,
            column: 75
          }
        }, {
          start: {
            line: 271,
            column: 78
          },
          end: {
            line: 271,
            column: 90
          }
        }],
        line: 271
      },
      "86": {
        loc: {
          start: {
            line: 271,
            column: 28
          },
          end: {
            line: 271,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 271,
            column: 28
          },
          end: {
            line: 271,
            column: 44
          }
        }, {
          start: {
            line: 271,
            column: 48
          },
          end: {
            line: 271,
            column: 66
          }
        }],
        line: 271
      },
      "87": {
        loc: {
          start: {
            line: 296,
            column: 16
          },
          end: {
            line: 304,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 296,
            column: 16
          },
          end: {
            line: 304,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 296
      },
      "88": {
        loc: {
          start: {
            line: 306,
            column: 16
          },
          end: {
            line: 306,
            column: 55
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 306,
            column: 16
          },
          end: {
            line: 306,
            column: 55
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 306
      },
      "89": {
        loc: {
          start: {
            line: 342,
            column: 32
          },
          end: {
            line: 477,
            column: 33
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 343,
            column: 36
          },
          end: {
            line: 343,
            column: 115
          }
        }, {
          start: {
            line: 344,
            column: 36
          },
          end: {
            line: 353,
            column: 48
          }
        }, {
          start: {
            line: 354,
            column: 36
          },
          end: {
            line: 361,
            column: 77
          }
        }, {
          start: {
            line: 362,
            column: 36
          },
          end: {
            line: 455,
            column: 48
          }
        }, {
          start: {
            line: 456,
            column: 36
          },
          end: {
            line: 460,
            column: 53
          }
        }, {
          start: {
            line: 461,
            column: 36
          },
          end: {
            line: 463,
            column: 120
          }
        }, {
          start: {
            line: 464,
            column: 36
          },
          end: {
            line: 466,
            column: 64
          }
        }, {
          start: {
            line: 467,
            column: 36
          },
          end: {
            line: 475,
            column: 64
          }
        }, {
          start: {
            line: 476,
            column: 36
          },
          end: {
            line: 476,
            column: 135
          }
        }],
        line: 342
      },
      "90": {
        loc: {
          start: {
            line: 346,
            column: 40
          },
          end: {
            line: 350,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 346,
            column: 40
          },
          end: {
            line: 350,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 346
      },
      "91": {
        loc: {
          start: {
            line: 346,
            column: 46
          },
          end: {
            line: 346,
            column: 161
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 346,
            column: 144
          },
          end: {
            line: 346,
            column: 150
          }
        }, {
          start: {
            line: 346,
            column: 153
          },
          end: {
            line: 346,
            column: 161
          }
        }],
        line: 346
      },
      "92": {
        loc: {
          start: {
            line: 346,
            column: 46
          },
          end: {
            line: 346,
            column: 141
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 346,
            column: 46
          },
          end: {
            line: 346,
            column: 124
          }
        }, {
          start: {
            line: 346,
            column: 128
          },
          end: {
            line: 346,
            column: 141
          }
        }],
        line: 346
      },
      "93": {
        loc: {
          start: {
            line: 346,
            column: 52
          },
          end: {
            line: 346,
            column: 114
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 346,
            column: 93
          },
          end: {
            line: 346,
            column: 99
          }
        }, {
          start: {
            line: 346,
            column: 102
          },
          end: {
            line: 346,
            column: 114
          }
        }],
        line: 346
      },
      "94": {
        loc: {
          start: {
            line: 346,
            column: 52
          },
          end: {
            line: 346,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 346,
            column: 52
          },
          end: {
            line: 346,
            column: 68
          }
        }, {
          start: {
            line: 346,
            column: 72
          },
          end: {
            line: 346,
            column: 90
          }
        }],
        line: 346
      },
      "95": {
        loc: {
          start: {
            line: 356,
            column: 40
          },
          end: {
            line: 360,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 356,
            column: 40
          },
          end: {
            line: 360,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 356
      },
      "96": {
        loc: {
          start: {
            line: 366,
            column: 40
          },
          end: {
            line: 378,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 366,
            column: 40
          },
          end: {
            line: 378,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 366
      },
      "97": {
        loc: {
          start: {
            line: 410,
            column: 69
          },
          end: {
            line: 410,
            column: 130
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 410,
            column: 87
          },
          end: {
            line: 410,
            column: 118
          }
        }, {
          start: {
            line: 410,
            column: 121
          },
          end: {
            line: 410,
            column: 130
          }
        }],
        line: 410
      },
      "98": {
        loc: {
          start: {
            line: 411,
            column: 67
          },
          end: {
            line: 411,
            column: 124
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 411,
            column: 83
          },
          end: {
            line: 411,
            column: 112
          }
        }, {
          start: {
            line: 411,
            column: 115
          },
          end: {
            line: 411,
            column: 124
          }
        }],
        line: 411
      },
      "99": {
        loc: {
          start: {
            line: 416,
            column: 72
          },
          end: {
            line: 416,
            column: 160
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 416,
            column: 135
          },
          end: {
            line: 416,
            column: 153
          }
        }, {
          start: {
            line: 416,
            column: 156
          },
          end: {
            line: 416,
            column: 160
          }
        }],
        line: 416
      },
      "100": {
        loc: {
          start: {
            line: 416,
            column: 72
          },
          end: {
            line: 416,
            column: 132
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 416,
            column: 72
          },
          end: {
            line: 416,
            column: 99
          }
        }, {
          start: {
            line: 416,
            column: 103
          },
          end: {
            line: 416,
            column: 132
          }
        }],
        line: 416
      },
      "101": {
        loc: {
          start: {
            line: 417,
            column: 71
          },
          end: {
            line: 417,
            column: 108
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 417,
            column: 71
          },
          end: {
            line: 417,
            column: 88
          }
        }, {
          start: {
            line: 417,
            column: 92
          },
          end: {
            line: 417,
            column: 108
          }
        }],
        line: 417
      },
      "102": {
        loc: {
          start: {
            line: 418,
            column: 67
          },
          end: {
            line: 418,
            column: 141
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 418,
            column: 120
          },
          end: {
            line: 418,
            column: 133
          }
        }, {
          start: {
            line: 418,
            column: 136
          },
          end: {
            line: 418,
            column: 141
          }
        }],
        line: 418
      },
      "103": {
        loc: {
          start: {
            line: 418,
            column: 67
          },
          end: {
            line: 418,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 418,
            column: 67
          },
          end: {
            line: 418,
            column: 89
          }
        }, {
          start: {
            line: 418,
            column: 93
          },
          end: {
            line: 418,
            column: 117
          }
        }],
        line: 418
      },
      "104": {
        loc: {
          start: {
            line: 419,
            column: 63
          },
          end: {
            line: 419,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 419,
            column: 108
          },
          end: {
            line: 419,
            column: 117
          }
        }, {
          start: {
            line: 419,
            column: 120
          },
          end: {
            line: 419,
            column: 125
          }
        }],
        line: 419
      },
      "105": {
        loc: {
          start: {
            line: 419,
            column: 63
          },
          end: {
            line: 419,
            column: 105
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 419,
            column: 63
          },
          end: {
            line: 419,
            column: 81
          }
        }, {
          start: {
            line: 419,
            column: 85
          },
          end: {
            line: 419,
            column: 105
          }
        }],
        line: 419
      },
      "106": {
        loc: {
          start: {
            line: 420,
            column: 63
          },
          end: {
            line: 420,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 420,
            column: 108
          },
          end: {
            line: 420,
            column: 117
          }
        }, {
          start: {
            line: 420,
            column: 120
          },
          end: {
            line: 420,
            column: 125
          }
        }],
        line: 420
      },
      "107": {
        loc: {
          start: {
            line: 420,
            column: 63
          },
          end: {
            line: 420,
            column: 105
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 420,
            column: 63
          },
          end: {
            line: 420,
            column: 81
          }
        }, {
          start: {
            line: 420,
            column: 85
          },
          end: {
            line: 420,
            column: 105
          }
        }],
        line: 420
      },
      "108": {
        loc: {
          start: {
            line: 423,
            column: 71
          },
          end: {
            line: 423,
            column: 148
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 423,
            column: 104
          },
          end: {
            line: 423,
            column: 130
          }
        }, {
          start: {
            line: 423,
            column: 133
          },
          end: {
            line: 423,
            column: 148
          }
        }],
        line: 423
      },
      "109": {
        loc: {
          start: {
            line: 439,
            column: 69
          },
          end: {
            line: 439,
            column: 130
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 439,
            column: 87
          },
          end: {
            line: 439,
            column: 118
          }
        }, {
          start: {
            line: 439,
            column: 121
          },
          end: {
            line: 439,
            column: 130
          }
        }],
        line: 439
      },
      "110": {
        loc: {
          start: {
            line: 440,
            column: 67
          },
          end: {
            line: 440,
            column: 124
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 440,
            column: 83
          },
          end: {
            line: 440,
            column: 112
          }
        }, {
          start: {
            line: 440,
            column: 115
          },
          end: {
            line: 440,
            column: 124
          }
        }],
        line: 440
      },
      "111": {
        loc: {
          start: {
            line: 445,
            column: 72
          },
          end: {
            line: 445,
            column: 160
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 445,
            column: 135
          },
          end: {
            line: 445,
            column: 153
          }
        }, {
          start: {
            line: 445,
            column: 156
          },
          end: {
            line: 445,
            column: 160
          }
        }],
        line: 445
      },
      "112": {
        loc: {
          start: {
            line: 445,
            column: 72
          },
          end: {
            line: 445,
            column: 132
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 445,
            column: 72
          },
          end: {
            line: 445,
            column: 99
          }
        }, {
          start: {
            line: 445,
            column: 103
          },
          end: {
            line: 445,
            column: 132
          }
        }],
        line: 445
      },
      "113": {
        loc: {
          start: {
            line: 446,
            column: 71
          },
          end: {
            line: 446,
            column: 108
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 446,
            column: 71
          },
          end: {
            line: 446,
            column: 88
          }
        }, {
          start: {
            line: 446,
            column: 92
          },
          end: {
            line: 446,
            column: 108
          }
        }],
        line: 446
      },
      "114": {
        loc: {
          start: {
            line: 447,
            column: 67
          },
          end: {
            line: 447,
            column: 141
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 447,
            column: 120
          },
          end: {
            line: 447,
            column: 133
          }
        }, {
          start: {
            line: 447,
            column: 136
          },
          end: {
            line: 447,
            column: 141
          }
        }],
        line: 447
      },
      "115": {
        loc: {
          start: {
            line: 447,
            column: 67
          },
          end: {
            line: 447,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 447,
            column: 67
          },
          end: {
            line: 447,
            column: 89
          }
        }, {
          start: {
            line: 447,
            column: 93
          },
          end: {
            line: 447,
            column: 117
          }
        }],
        line: 447
      },
      "116": {
        loc: {
          start: {
            line: 448,
            column: 63
          },
          end: {
            line: 448,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 448,
            column: 108
          },
          end: {
            line: 448,
            column: 117
          }
        }, {
          start: {
            line: 448,
            column: 120
          },
          end: {
            line: 448,
            column: 125
          }
        }],
        line: 448
      },
      "117": {
        loc: {
          start: {
            line: 448,
            column: 63
          },
          end: {
            line: 448,
            column: 105
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 448,
            column: 63
          },
          end: {
            line: 448,
            column: 81
          }
        }, {
          start: {
            line: 448,
            column: 85
          },
          end: {
            line: 448,
            column: 105
          }
        }],
        line: 448
      },
      "118": {
        loc: {
          start: {
            line: 449,
            column: 63
          },
          end: {
            line: 449,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 449,
            column: 108
          },
          end: {
            line: 449,
            column: 117
          }
        }, {
          start: {
            line: 449,
            column: 120
          },
          end: {
            line: 449,
            column: 125
          }
        }],
        line: 449
      },
      "119": {
        loc: {
          start: {
            line: 449,
            column: 63
          },
          end: {
            line: 449,
            column: 105
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 449,
            column: 63
          },
          end: {
            line: 449,
            column: 81
          }
        }, {
          start: {
            line: 449,
            column: 85
          },
          end: {
            line: 449,
            column: 105
          }
        }],
        line: 449
      },
      "120": {
        loc: {
          start: {
            line: 452,
            column: 71
          },
          end: {
            line: 452,
            column: 148
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 452,
            column: 104
          },
          end: {
            line: 452,
            column: 130
          }
        }, {
          start: {
            line: 452,
            column: 133
          },
          end: {
            line: 452,
            column: 148
          }
        }],
        line: 452
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0, 0],
      "81": [0, 0, 0, 0, 0],
      "82": [0, 0],
      "83": [0, 0],
      "84": [0, 0],
      "85": [0, 0],
      "86": [0, 0],
      "87": [0, 0],
      "88": [0, 0],
      "89": [0, 0, 0, 0, 0, 0, 0, 0, 0],
      "90": [0, 0],
      "91": [0, 0],
      "92": [0, 0],
      "93": [0, 0],
      "94": [0, 0],
      "95": [0, 0],
      "96": [0, 0],
      "97": [0, 0],
      "98": [0, 0],
      "99": [0, 0],
      "100": [0, 0],
      "101": [0, 0],
      "102": [0, 0],
      "103": [0, 0],
      "104": [0, 0],
      "105": [0, 0],
      "106": [0, 0],
      "107": [0, 0],
      "108": [0, 0],
      "109": [0, 0],
      "110": [0, 0],
      "111": [0, 0],
      "112": [0, 0],
      "113": [0, 0],
      "114": [0, 0],
      "115": [0, 0],
      "116": [0, 0],
      "117": [0, 0],
      "118": [0, 0],
      "119": [0, 0],
      "120": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/profile/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAAkD;AAClD,mCAAyC;AACzC,wDAAkC;AAElC,uCAAmC;AAEnC,6EAAwF;AAExF,mCAAgD;AAChD,6CAAgD;AAChD,wFAAqF;AACrF,wFAAqF;AACrF,2DAAwE;AAExE,wDAAwD;AACxD,SAAS,mBAAmB,CAAC,IAAS;IAIpC,IAAM,cAAc,GAAa,EAAE,CAAC;IACpC,IAAM,aAAa,GAAQ,EAAE,CAAC;IAE9B,qCAAqC;IACrC,IAAM,UAAU,GAAG;QACjB,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS;QACrD,UAAU,EAAE,aAAa,EAAE,SAAS,EAAE,iBAAiB;QACvD,gBAAgB,EAAE,qBAAqB,EAAE,mBAAmB;QAC5D,eAAe;KAChB,CAAC;IAEF,uBAAuB;IACvB,UAAU,CAAC,OAAO,CAAC,UAAA,KAAK;QACtB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;YACtD,IAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YACvC,IAAM,UAAU,GAAG,IAAA,mCAAgB,EAAC,UAAU,CAAC,CAAC;YAEhD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gBACxB,cAAc,CAAC,IAAI,CAAC,UAAG,KAAK,eAAK,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CAAC;YACpE,CAAC;YAED,qDAAqD;YACrD,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,mBAAmB,IAAI,KAAK,KAAK,qBAAqB,EAAE,CAAC;gBACxF,0CAA0C;gBAC1C,aAAa,CAAC,KAAK,CAAC,GAAG,IAAA,+BAAY,EAAC,UAAU,CAAC,SAAS,EAAE;oBACxD,SAAS,EAAE,IAAI;oBACf,SAAS,EAAE,IAAI;oBACf,eAAe,EAAE,IAAI;iBACtB,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC/B,4BAA4B;gBAC5B,IAAM,UAAU,GAAG,yGAAyG,CAAC;gBAC7H,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC/C,cAAc,CAAC,IAAI,CAAC,UAAG,KAAK,yBAAsB,CAAC,CAAC;oBACpD,aAAa,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;gBAC5B,CAAC;qBAAM,CAAC;oBACN,aAAa,CAAC,KAAK,CAAC,GAAG,IAAA,+BAAY,EAAC,UAAU,CAAC,SAAS,EAAE;wBACxD,SAAS,EAAE,GAAG;wBACd,eAAe,EAAE,IAAI;qBACtB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,6BAA6B;gBAC7B,aAAa,CAAC,KAAK,CAAC,GAAG,IAAA,+BAAY,EAAC,UAAU,CAAC,SAAS,EAAE;oBACxD,SAAS,EAAE,GAAG;oBACd,eAAe,EAAE,IAAI;iBACtB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,uDAAuD;IACvD,IAAM,WAAW,GAAG,CAAC,iBAAiB,EAAE,eAAe,CAAC,CAAC;IACzD,WAAW,CAAC,OAAO,CAAC,UAAA,KAAK;QACvB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;YACtD,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC/B,aAAa,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;qBAC/B,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,mBAAmB;qBAChC,GAAG,CAAC,UAAC,IAAS;oBACb,IAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;oBAC7B,IAAM,UAAU,GAAG,IAAA,mCAAgB,EAAC,OAAO,CAAC,CAAC;oBAC7C,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;wBACxB,cAAc,CAAC,IAAI,CAAC,UAAG,KAAK,oBAAU,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CAAC;oBACzE,CAAC;oBACD,OAAO,IAAA,+BAAY,EAAC,UAAU,CAAC,SAAS,EAAE;wBACxC,SAAS,EAAE,GAAG;wBACd,eAAe,EAAE,IAAI;qBACtB,CAAC,CAAC;gBACL,CAAC,CAAC;qBACD,MAAM,CAAC,UAAC,IAAY,IAAK,OAAA,IAAI,CAAC,MAAM,GAAG,CAAC,EAAf,CAAe,CAAC,CAAC;YAC/C,CAAC;iBAAM,CAAC;gBACN,uBAAuB;gBACvB,IAAM,UAAU,GAAG,IAAA,mCAAgB,EAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBACzD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;oBACxB,cAAc,CAAC,IAAI,CAAC,UAAG,KAAK,eAAK,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CAAC;gBACpE,CAAC;gBACD,aAAa,CAAC,KAAK,CAAC,GAAG,IAAA,+BAAY,EAAC,UAAU,CAAC,SAAS,EAAE;oBACxD,SAAS,EAAE,GAAG;oBACd,eAAe,EAAE,IAAI;iBACtB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,wBAAwB;IACxB,IAAM,aAAa,GAAG,CAAC,oBAAoB,EAAE,eAAe,EAAE,WAAW,EAAE,WAAW,EAAE,0BAA0B,CAAC,CAAC;IACpH,aAAa,CAAC,OAAO,CAAC,UAAA,KAAK;QACzB,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,SAAS,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;YACtD,aAAa,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,wBAAwB;IACxB,IAAI,IAAI,CAAC,kBAAkB,KAAK,SAAS,IAAI,IAAI,CAAC,kBAAkB,KAAK,IAAI,EAAE,CAAC;QAC9E,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAC7C,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,GAAG,EAAE,CAAC,CAAC,yBAAyB;YACpE,cAAc,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACzD,aAAa,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC1C,CAAC;aAAM,CAAC;YACN,aAAa,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,qBAAqB;IACrB,IAAM,qBAAqB,GAAG,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;IACjF,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;QAClF,cAAc,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;QACtD,aAAa,CAAC,eAAe,GAAG,IAAI,CAAC;IACvC,CAAC;SAAM,CAAC;QACN,aAAa,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;IACvD,CAAC;IAED,IAAM,sBAAsB,GAAG,CAAC,SAAS,EAAE,gBAAgB,EAAE,QAAQ,CAAC,CAAC;IACvE,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC;QACvF,cAAc,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QACxD,aAAa,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;IACrD,CAAC;SAAM,CAAC;QACN,aAAa,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,IAAI,gBAAgB,CAAC;IAC/E,CAAC;IAED,mDAAmD;IACnD,IAAI,IAAI,CAAC,iBAAiB,KAAK,SAAS,IAAI,IAAI,CAAC,iBAAiB,KAAK,IAAI,EAAE,CAAC;QAC5E,IAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAC3C,IAAM,UAAU,GAAG,IAAA,mCAAgB,EAAC,GAAG,CAAC,CAAC;QAEzC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YACxB,cAAc,CAAC,IAAI,CAAC,6BAAsB,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CAAC;QAC7E,CAAC;QAED,iDAAiD;QACjD,IAAM,eAAe,GAAG,kDAAkD,CAAC;QAC3E,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACtC,cAAc,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;YACnE,aAAa,CAAC,iBAAiB,GAAG,IAAI,CAAC;QACzC,CAAC;aAAM,CAAC;YACN,aAAa,CAAC,iBAAiB,GAAG,IAAA,+BAAY,EAAC,UAAU,CAAC,SAAS,EAAE;gBACnE,SAAS,EAAE,GAAG;gBACd,eAAe,EAAE,IAAI;aACtB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,mCAAmC;IACnC,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,IAAI,IAAI,CAAC,gBAAgB,KAAK,IAAI,EAAE,CAAC;QAC1E,IAAI,OAAO,IAAI,CAAC,gBAAgB,KAAK,QAAQ,EAAE,CAAC;YAC9C,IAAM,gBAAc,GAAQ,EAAE,CAAC;YAC/B,IAAM,kBAAgB,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC;YAEnF,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,OAAO,CAAC,UAAA,QAAQ;gBACjD,IAAI,kBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,EAAE,CAAC;oBACtD,IAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;oBACpD,IAAM,UAAU,GAAG,IAAA,mCAAgB,EAAC,GAAG,CAAC,CAAC;oBAEzC,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;wBACxB,cAAc,CAAC,IAAI,CAAC,2BAAoB,QAAQ,eAAK,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CAAC;oBACxF,CAAC;oBAED,IAAM,UAAU,GAAG,gBAAgB,CAAC;oBACpC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;wBACjC,cAAc,CAAC,IAAI,CAAC,2BAAoB,QAAQ,yBAAsB,CAAC,CAAC;oBAC1E,CAAC;yBAAM,CAAC;wBACN,gBAAc,CAAC,QAAQ,CAAC,GAAG,IAAA,+BAAY,EAAC,UAAU,CAAC,SAAS,EAAE;4BAC5D,SAAS,EAAE,GAAG;4BACd,eAAe,EAAE,IAAI;yBACtB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YACH,aAAa,CAAC,gBAAgB,GAAG,gBAAc,CAAC;QAClD,CAAC;IACH,CAAC;IAED,OAAO,EAAE,aAAa,eAAA,EAAE,cAAc,gBAAA,EAAE,CAAC;AAC3C,CAAC;AAED,wDAAwD;AACxD,SAAS,+BAA+B,CAAC,WAAgB;IACvD,IAAM,MAAM,GAAG;QACb,KAAK;QACL,mBAAmB;QACnB,WAAW;QACX,UAAU;QACV,UAAU;QACV,SAAS;QACT,UAAU;QACV,aAAa;QACb,SAAS;QACT,iBAAiB;QACjB,eAAe;QACf,iBAAiB;QACjB,iBAAiB;QACjB,gBAAgB;QAChB,oBAAoB;KACrB,CAAC;IAEF,IAAI,eAAe,GAAG,CAAC,CAAC;IAExB,MAAM,CAAC,OAAO,CAAC,UAAA,KAAK;QAClB,IAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;QACjC,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;YAC1D,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7C,eAAe,EAAE,CAAC;YACpB,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChE,eAAe,EAAE,CAAC;YACpB,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;gBAClD,eAAe,EAAE,CAAC;YACpB,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,EAAE,CAAC;gBAC3E,eAAe,EAAE,CAAC;YACpB,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,IAAI,CAAC,KAAK,CAAC,CAAC,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;AAC7D,CAAC;AA2CY,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB,qCAAG,OAAO;;;;;;gBACzE,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACb,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;gBAA7C,OAAO,GAAG,SAAmC;gBAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,KAAK,CAAA,EAAE,CAAC;oBAC1B,YAAG,CAAC,IAAI,CAAC,uBAAuB,EAAE,SAAS,EAAE,KAAK,EAAE;wBAClD,SAAS,EAAE,aAAa;wBACxB,MAAM,EAAE,eAAe;qBACxB,CAAC,CAAC;oBACG,KAAK,GAAG,IAAI,KAAK,CAAC,mBAAmB,CAAQ,CAAC;oBACpD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,YAAG,CAAC,IAAI,CAAC,uBAAuB,EAAE;oBAChC,SAAS,EAAE,aAAa;oBACxB,MAAM,EAAE,eAAe;oBACvB,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK;iBAC3B,CAAC,CAAC;gBAEG,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAClB,qBAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;wBACxC,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE;wBACpC,OAAO,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;qBAC3B,CAAC,EAAA;;gBAHI,IAAI,GAAG,SAGX;gBACI,UAAU,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,WAAW,CAAC;gBAE5C,YAAG,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE;oBAC7C,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK;iBAC3B,CAAC,CAAC;gBAEH,IAAI,CAAC,IAAI,EAAE,CAAC;oBACV,YAAG,CAAC,IAAI,CAAC,qCAAqC,EAAE;wBAC9C,SAAS,EAAE,aAAa;wBACxB,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK;qBAC3B,CAAC,CAAC;oBACG,KAAK,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAQ,CAAC;oBACjD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;oBACvB,MAAM,KAAK,CAAC;gBACd,CAAC;gBAGG,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;qBACvB,CAAC,OAAO,EAAR,wBAAQ;gBACV,YAAG,CAAC,IAAI,CAAC,+BAA+B,EAAE;oBACxC,SAAS,EAAE,aAAa;oBACxB,MAAM,EAAE,IAAI,CAAC,EAAE;iBAChB,CAAC,CAAC;gBAEG,eAAe,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACzB,qBAAM,gBAAM,CAAC,OAAO,CAAC,MAAM,CAAC;wBACpC,IAAI,EAAE;4BACJ,MAAM,EAAE,IAAI,CAAC,EAAE;yBAChB;qBACF,CAAC,EAAA;;gBAJF,OAAO,GAAG,SAIR,CAAC;gBACG,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,eAAe,CAAC;gBAEpD,YAAG,CAAC,QAAQ,CAAC,QAAQ,EAAE,SAAS,EAAE,cAAc,EAAE;oBAChD,MAAM,EAAE,IAAI,CAAC,EAAE;iBAChB,CAAC,CAAC;;;gBAGC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAC7C,YAAG,CAAC,GAAG,CAAC,KAAK,EAAE,cAAc,EAAE,GAAG,EAAE,aAAa,EAAE;oBACjD,SAAS,EAAE,aAAa;oBACxB,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK;iBAC3B,CAAC,CAAC;gBAEH,sBAAO,qBAAY,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,EAAC;;;KAC5D,CAAC,CAAC;AAkCU,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;QACrE,sBAAO,IAAA,yBAAkB,EAAC,OAAO,EAAE;;oBACjC,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAC7C;;;;;4CACkB,qBAAM,IAAA,uBAAgB,EAAC,kBAAW,CAAC,EAAA;;wCAA7C,OAAO,GAAG,SAAmC;wCAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,KAAK,CAAA,EAAE,CAAC;4CACpB,KAAK,GAAG,IAAI,KAAK,CAAC,mBAAmB,CAAQ,CAAC;4CACpD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAEY,qBAAM,gBAAM,CAAC,IAAI,CAAC,UAAU,CAAC;gDACxC,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE;6CACrC,CAAC,EAAA;;wCAFI,IAAI,GAAG,SAEX;wCAEF,IAAI,CAAC,IAAI,EAAE,CAAC;4CACJ,KAAK,GAAG,IAAI,KAAK,CAAC,gBAAgB,CAAQ,CAAC;4CACjD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAEe,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;wCAA9B,OAAO,GAAG,SAAoB;wCAG9B,KAAoC,mBAAmB,CAAC,OAAO,CAAC,EAA9D,aAAa,mBAAA,EAAE,cAAc,oBAAA,CAAkC;wCAEvE,4EAA4E;wCAC5E,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4CAC9B,YAAG,CAAC,IAAI,CAAC,yCAAyC,EAAE;gDAClD,SAAS,EAAE,aAAa;gDACxB,MAAM,EAAE,IAAI,CAAC,EAAE;gDACf,QAAQ,EAAE,EAAE,cAAc,gBAAA,EAAE;6CAC7B,CAAC,CAAC;4CAEH,mFAAmF;4CACnF,yEAAyE;4CACzE,+EAA+E;4CAC/E,0BAA0B;4CAC1B,mCAAmC;4CACnC,eAAe;wCACjB,CAAC;wCAGC,GAAG,GAqBD,aAAa,IArBZ,EACH,iBAAiB,GAoBf,aAAa,kBApBE,EACjB,gBAAgB,GAmBd,aAAa,iBAnBC,EAChB,SAAS,GAkBP,aAAa,UAlBN,EACT,QAAQ,GAiBN,aAAa,SAjBP,EACR,QAAQ,GAgBN,aAAa,SAhBP,EACR,OAAO,GAeL,aAAa,QAfR,EACP,QAAQ,GAcN,aAAa,SAdP,EACR,WAAW,GAaT,aAAa,YAbJ,EACX,OAAO,GAYL,aAAa,QAZR,EACP,eAAe,GAWb,aAAa,gBAXA,EACf,aAAa,GAUX,aAAa,cAVF,EACb,eAAe,GASb,aAAa,gBATA,EACf,eAAe,GAQb,aAAa,gBARA,EACf,cAAc,GAOZ,aAAa,eAPD,EACd,kBAAkB,GAMhB,aAAa,mBANG,EAClB,kBAAkB,GAKhB,aAAa,mBALG,EAClB,iBAAiB,GAIf,aAAa,kBAJE,EACjB,aAAa,GAGX,aAAa,cAHF,EACb,SAAS,GAEP,aAAa,UAFN,EACT,SAAS,GACP,aAAa,UADN,CACO;wCAGZ,sBAAsB,GAAG,+BAA+B,CAAC;4CAC7D,GAAG,KAAA;4CACH,iBAAiB,mBAAA;4CACjB,SAAS,WAAA;4CACT,QAAQ,UAAA;4CACR,QAAQ,UAAA;4CACR,OAAO,SAAA;4CACP,QAAQ,UAAA;4CACR,WAAW,aAAA;4CACX,OAAO,SAAA;4CACP,eAAe,iBAAA;4CACf,aAAa,eAAA;4CACb,eAAe,iBAAA;4CACf,eAAe,iBAAA;4CACf,cAAc,gBAAA;4CACd,kBAAkB,oBAAA;yCACnB,CAAC,CAAC;wCAEoB,qBAAM,gBAAM,CAAC,OAAO,CAAC,MAAM,CAAC;gDACjD,KAAK,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE;gDAC1B,MAAM,EAAE;oDACN,GAAG,KAAA;oDACH,iBAAiB,mBAAA;oDACjB,gBAAgB,kBAAA;oDAChB,SAAS,WAAA;oDACT,QAAQ,UAAA;oDACR,QAAQ,UAAA;oDACR,OAAO,SAAA;oDACP,QAAQ,UAAA;oDACR,WAAW,aAAA;oDACX,OAAO,SAAA;oDACP,eAAe,EAAE,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS;oDAC9E,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,SAAS;oDACxE,eAAe,iBAAA;oDACf,eAAe,iBAAA;oDACf,cAAc,gBAAA;oDACd,kBAAkB,oBAAA;oDAClB,kBAAkB,EAAE,kBAAkB,aAAlB,kBAAkB,cAAlB,kBAAkB,GAAI,IAAI;oDAC9C,iBAAiB,EAAE,iBAAiB,IAAI,gBAAgB;oDACxD,aAAa,EAAE,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,KAAK;oDACrC,SAAS,EAAE,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,KAAK;oDAC7B,SAAS,EAAE,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,KAAK;oDAC7B,sBAAsB,wBAAA;oDACtB,iBAAiB,EAAE,IAAI,IAAI,EAAE;oDAC7B,iBAAiB,EAAE,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,eAAe;oDAChG,aAAa,EAAE,eAAe;oDAC9B,YAAY,EAAE,IAAI,IAAI,EAAE;iDACzB;gDACD,MAAM,EAAE;oDACN,MAAM,EAAE,IAAI,CAAC,EAAE;oDACf,GAAG,KAAA;oDACH,iBAAiB,mBAAA;oDACjB,gBAAgB,kBAAA;oDAChB,SAAS,WAAA;oDACT,QAAQ,UAAA;oDACR,QAAQ,UAAA;oDACR,OAAO,SAAA;oDACP,QAAQ,UAAA;oDACR,WAAW,aAAA;oDACX,OAAO,SAAA;oDACP,eAAe,EAAE,eAAe,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,SAAS;oDAC9E,aAAa,EAAE,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,SAAS;oDACxE,eAAe,iBAAA;oDACf,eAAe,iBAAA;oDACf,cAAc,gBAAA;oDACd,kBAAkB,oBAAA;oDAClB,kBAAkB,EAAE,kBAAkB,aAAlB,kBAAkB,cAAlB,kBAAkB,GAAI,IAAI;oDAC9C,iBAAiB,EAAE,iBAAiB,IAAI,gBAAgB;oDACxD,aAAa,EAAE,aAAa,aAAb,aAAa,cAAb,aAAa,GAAI,KAAK;oDACrC,SAAS,EAAE,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,KAAK;oDAC7B,SAAS,EAAE,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,KAAK;oDAC7B,sBAAsB,wBAAA;oDACtB,iBAAiB,EAAE,IAAI,IAAI,EAAE;oDAC7B,iBAAiB,EAAE,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,eAAe;oDAChG,aAAa,EAAE,eAAe;iDAC/B;6CACF,CAAC,EAAA;;wCA1DI,cAAc,GAAG,SA0DrB;wCAGI,YAAY,GAAG,IAAI,qDAAwB,EAAE,CAAC;wCAC9C,wBAAwB,GAAG,IAAI,qDAAwB,CAAC,YAAY,CAAC,CAAC;;;;wCAG1E,qBAAM,wBAAwB,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAA;;wCAA/D,SAA+D,CAAC;;;;wCAEhE,2DAA2D;wCAC3D,YAAG,CAAC,IAAI,CAAC,qCAAqC,EAAE;4CAC9C,SAAS,EAAE,aAAa;4CACxB,MAAM,EAAE,IAAI,CAAC,EAAE;4CACf,QAAQ,EAAE,EAAE,KAAK,EAAE,YAAU,EAAE;yCAChC,CAAC,CAAC;;4CAGL,sBAAO,qBAAY,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC,EAAC;;;6BACnE,CACF,EAAC;;iBACH,CAAC,EAAC;;KACJ,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/profile/route.ts"],
      sourcesContent: ["import { NextResponse, NextRequest } from 'next/server';\nimport { getServerSession } from 'next-auth/next';\nimport { authOptions } from '@/lib/auth';\nimport prisma from '@/lib/prisma';\nimport { ErrorReporter } from '@/lib/errorReporting';\nimport { log } from '@/lib/logger';\nimport { trackError } from '@/lib/errorTracking';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\n\nimport { withCSRFProtection } from '@/lib/csrf';\nimport { withRateLimit } from '@/lib/rateLimit';\nimport { CacheInvalidationService } from '@/lib/services/cache-invalidation-service';\nimport { ConsolidatedCacheService } from '@/lib/services/consolidated-cache-service';\nimport { sanitizeText, validateSecurity } from '@/lib/input-validation';\n\n// SECURITY FIX: Comprehensive profile data sanitization\nfunction sanitizeProfileData(data: any): {\n  sanitizedData: any;\n  securityIssues: string[];\n} {\n  const securityIssues: string[] = [];\n  const sanitizedData: any = {};\n\n  // Text fields that need sanitization\n  const textFields = [\n    'bio', 'firstName', 'lastName', 'jobTitle', 'company',\n    'location', 'phoneNumber', 'website', 'currentIndustry',\n    'targetIndustry', 'mentorshipInterests', 'professionalGoals',\n    'skillsToTeach'\n  ];\n\n  // Sanitize text fields\n  textFields.forEach(field => {\n    if (data[field] !== undefined && data[field] !== null) {\n      const fieldValue = String(data[field]);\n      const validation = validateSecurity(fieldValue);\n\n      if (!validation.isValid) {\n        securityIssues.push(`${field}: ${validation.threats.join(', ')}`);\n      }\n\n      // Apply appropriate sanitization based on field type\n      if (field === 'bio' || field === 'professionalGoals' || field === 'mentorshipInterests') {\n        // Allow limited HTML for rich text fields\n        sanitizedData[field] = sanitizeText(validation.sanitized, {\n          allowHtml: true,\n          maxLength: 2000,\n          stripWhitespace: true\n        });\n      } else if (field === 'website') {\n        // Special handling for URLs\n        const urlPattern = /^https?:\\/\\/(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b([-a-zA-Z0-9()@:%_\\+.~#?&//=]*)$/;\n        if (fieldValue && !urlPattern.test(fieldValue)) {\n          securityIssues.push(`${field}: Invalid URL format`);\n          sanitizedData[field] = '';\n        } else {\n          sanitizedData[field] = sanitizeText(validation.sanitized, {\n            maxLength: 500,\n            stripWhitespace: true\n          });\n        }\n      } else {\n        // Standard text sanitization\n        sanitizedData[field] = sanitizeText(validation.sanitized, {\n          maxLength: 200,\n          stripWhitespace: true\n        });\n      }\n    }\n  });\n\n  // Handle array fields (careerInterests, skillsToLearn)\n  const arrayFields = ['careerInterests', 'skillsToLearn'];\n  arrayFields.forEach(field => {\n    if (data[field] !== undefined && data[field] !== null) {\n      if (Array.isArray(data[field])) {\n        sanitizedData[field] = data[field]\n          .slice(0, 20) // Limit array size\n          .map((item: any) => {\n            const itemStr = String(item);\n            const validation = validateSecurity(itemStr);\n            if (!validation.isValid) {\n              securityIssues.push(`${field} item: ${validation.threats.join(', ')}`);\n            }\n            return sanitizeText(validation.sanitized, {\n              maxLength: 100,\n              stripWhitespace: true\n            });\n          })\n          .filter((item: string) => item.length > 0);\n      } else {\n        // Handle string format\n        const validation = validateSecurity(String(data[field]));\n        if (!validation.isValid) {\n          securityIssues.push(`${field}: ${validation.threats.join(', ')}`);\n        }\n        sanitizedData[field] = sanitizeText(validation.sanitized, {\n          maxLength: 500,\n          stripWhitespace: true\n        });\n      }\n    }\n  });\n\n  // Handle boolean fields\n  const booleanFields = ['emailNotifications', 'profilePublic', 'showEmail', 'showPhone', 'availabilityForMentoring'];\n  booleanFields.forEach(field => {\n    if (data[field] !== undefined && data[field] !== null) {\n      sanitizedData[field] = Boolean(data[field]);\n    }\n  });\n\n  // Handle numeric fields\n  if (data.weeklyLearningGoal !== undefined && data.weeklyLearningGoal !== null) {\n    const goal = Number(data.weeklyLearningGoal);\n    if (isNaN(goal) || goal < 0 || goal > 168) { // Max 168 hours per week\n      securityIssues.push('weeklyLearningGoal: Invalid value');\n      sanitizedData.weeklyLearningGoal = null;\n    } else {\n      sanitizedData.weeklyLearningGoal = goal;\n    }\n  }\n\n  // Handle enum fields\n  const validExperienceLevels = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];\n  if (data.experienceLevel && !validExperienceLevels.includes(data.experienceLevel)) {\n    securityIssues.push('experienceLevel: Invalid value');\n    sanitizedData.experienceLevel = null;\n  } else {\n    sanitizedData.experienceLevel = data.experienceLevel;\n  }\n\n  const validVisibilityOptions = ['PRIVATE', 'COMMUNITY_ONLY', 'PUBLIC'];\n  if (data.profileVisibility && !validVisibilityOptions.includes(data.profileVisibility)) {\n    securityIssues.push('profileVisibility: Invalid value');\n    sanitizedData.profileVisibility = 'COMMUNITY_ONLY';\n  } else {\n    sanitizedData.profileVisibility = data.profileVisibility || 'COMMUNITY_ONLY';\n  }\n\n  // Handle profile picture URL with extra validation\n  if (data.profilePictureUrl !== undefined && data.profilePictureUrl !== null) {\n    const url = String(data.profilePictureUrl);\n    const validation = validateSecurity(url);\n\n    if (!validation.isValid) {\n      securityIssues.push(`profilePictureUrl: ${validation.threats.join(', ')}`);\n    }\n\n    // Additional URL validation for profile pictures\n    const imageUrlPattern = /^https?:\\/\\/.+\\.(jpg|jpeg|png|gif|webp)(\\?.*)?$/i;\n    if (url && !imageUrlPattern.test(url)) {\n      securityIssues.push('profilePictureUrl: Invalid image URL format');\n      sanitizedData.profilePictureUrl = null;\n    } else {\n      sanitizedData.profilePictureUrl = sanitizeText(validation.sanitized, {\n        maxLength: 500,\n        stripWhitespace: true\n      });\n    }\n  }\n\n  // Handle social media links object\n  if (data.socialMediaLinks !== undefined && data.socialMediaLinks !== null) {\n    if (typeof data.socialMediaLinks === 'object') {\n      const sanitizedLinks: any = {};\n      const allowedPlatforms = ['linkedin', 'twitter', 'github', 'portfolio', 'website'];\n\n      Object.keys(data.socialMediaLinks).forEach(platform => {\n        if (allowedPlatforms.includes(platform.toLowerCase())) {\n          const url = String(data.socialMediaLinks[platform]);\n          const validation = validateSecurity(url);\n\n          if (!validation.isValid) {\n            securityIssues.push(`socialMediaLinks.${platform}: ${validation.threats.join(', ')}`);\n          }\n\n          const urlPattern = /^https?:\\/\\/.+/;\n          if (url && !urlPattern.test(url)) {\n            securityIssues.push(`socialMediaLinks.${platform}: Invalid URL format`);\n          } else {\n            sanitizedLinks[platform] = sanitizeText(validation.sanitized, {\n              maxLength: 500,\n              stripWhitespace: true\n            });\n          }\n        }\n      });\n      sanitizedData.socialMediaLinks = sanitizedLinks;\n    }\n  }\n\n  return { sanitizedData, securityIssues };\n}\n\n// Helper function to calculate profile completion score\nfunction calculateProfileCompletionScore(profileData: any): number {\n  const fields = [\n    'bio',\n    'profilePictureUrl',\n    'firstName',\n    'lastName',\n    'jobTitle',\n    'company',\n    'location',\n    'phoneNumber',\n    'website',\n    'careerInterests',\n    'skillsToLearn',\n    'experienceLevel',\n    'currentIndustry',\n    'targetIndustry',\n    'weeklyLearningGoal'\n  ];\n\n  let completedFields = 0;\n\n  fields.forEach(field => {\n    const value = profileData[field];\n    if (value !== null && value !== undefined && value !== '') {\n      if (Array.isArray(value) && value.length > 0) {\n        completedFields++;\n      } else if (typeof value === 'string' && value.trim().length > 0) {\n        completedFields++;\n      } else if (typeof value === 'number' && value > 0) {\n        completedFields++;\n      } else if (typeof value !== 'string' && typeof value !== 'number' && value) {\n        completedFields++;\n      }\n    }\n  });\n\n  return Math.round((completedFields / fields.length) * 100);\n}\n\ninterface ProfileResponse {\n  id: string;\n  createdAt: Date;\n  updatedAt: Date;\n  userId: string;\n  bio?: string | null;\n  profilePictureUrl?: string | null;\n  socialMediaLinks?: any;\n  firstName?: string | null;\n  lastName?: string | null;\n  jobTitle?: string | null;\n  company?: string | null;\n  location?: string | null;\n  phoneNumber?: string | null;\n  website?: string | null;\n  careerInterests?: any;\n  skillsToLearn?: any;\n  experienceLevel?: any;\n  currentIndustry?: string | null;\n  targetIndustry?: string | null;\n  weeklyLearningGoal?: number | null;\n  emailNotifications?: boolean;\n  profileVisibility?: string;\n  profilePublic?: boolean;\n  showEmail?: boolean;\n  showPhone?: boolean;\n  profileCompletionScore?: number;\n  lastProfileUpdate?: Date | null;\n  currentCareerPath?: string | null;\n  progressLevel?: string | null;\n  lastActiveAt?: Date;\n  achievements?: any;\n  learningStreak?: number | null;\n  totalLearningHours?: number | null;\n  preferredLearningStyle?: string | null;\n  availabilityForMentoring?: boolean;\n  mentorshipInterests?: string | null;\n  professionalGoals?: string | null;\n  skillsToTeach?: string | null;\n}\n\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<ProfileResponse>>> => {\n  const startTime = Date.now();\n  const session = await getServerSession(authOptions);\n\n  if (!session?.user?.email) {\n    log.auth('profile_access_denied', undefined, false, {\n      component: 'profile_api',\n      action: 'fetch_profile'\n    });\n    const error = new Error('Not authenticated') as any;\n    error.statusCode = 401;\n    throw error;\n  }\n\n  log.info('Fetching user profile', {\n    component: 'profile_api',\n    action: 'fetch_profile',\n    userId: session.user.email\n  });\n\n  const dbStartTime = Date.now();\n  const user = await prisma.user.findUnique({\n    where: { email: session.user.email },\n    include: { profile: true },\n  });\n  const dbDuration = Date.now() - dbStartTime;\n\n  log.database('findUnique', 'user', dbDuration, {\n    userId: session.user.email\n  });\n\n  if (!user) {\n    log.warn('User not found during profile fetch', {\n      component: 'profile_api',\n      userId: session.user.email\n    });\n    const error = new Error('User not found') as any;\n    error.statusCode = 404;\n    throw error;\n  }\n\n  // If profile doesn't exist, create an empty one\n  let profile = user.profile;\n  if (!profile) {\n    log.info('Creating new profile for user', {\n      component: 'profile_api',\n      userId: user.id\n    });\n\n    const createStartTime = Date.now();\n    profile = await prisma.profile.create({\n      data: {\n        userId: user.id,\n      },\n    });\n    const createDuration = Date.now() - createStartTime;\n\n    log.database('create', 'profile', createDuration, {\n      userId: user.id\n    });\n  }\n\n  const totalDuration = Date.now() - startTime;\n  log.api('GET', '/api/profile', 200, totalDuration, {\n    component: 'profile_api',\n    userId: session.user.email\n  });\n\n  return NextResponse.json({ success: true, data: profile });\n});\n\ninterface ProfileUpdateResponse {\n  id: string;\n  userId: string;\n  bio?: string | null;\n  profilePictureUrl?: string | null;\n  firstName?: string | null;\n  lastName?: string | null;\n  jobTitle?: string | null;\n  company?: string | null;\n  location?: string | null;\n  phoneNumber?: string | null;\n  website?: string | null;\n  careerInterests?: string | null;\n  skillsToLearn?: string | null;\n  experienceLevel?: string | null;\n  currentIndustry?: string | null;\n  targetIndustry?: string | null;\n  weeklyLearningGoal?: number | null;\n  emailNotifications?: boolean;\n  profileVisibility?: string;\n  profilePublic?: boolean;\n  showEmail?: boolean;\n  showPhone?: boolean;\n  profileCompletionScore?: number;\n  lastProfileUpdate?: Date;\n  currentCareerPath?: string | null;\n  progressLevel?: string | null;\n  lastActiveAt?: Date;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport const PUT = withUnifiedErrorHandling(async (request: NextRequest) => {\n  return withCSRFProtection(request, async () => {\n    return withRateLimit(\n      request,\n      { windowMs: 15 * 60 * 1000, maxRequests: 30 },\n      async () => {\n        const session = await getServerSession(authOptions);\n\n        if (!session?.user?.email) {\n          const error = new Error('Not authenticated') as any;\n          error.statusCode = 401;\n          throw error;\n        }\n\n        const user = await prisma.user.findUnique({\n          where: { email: session.user.email },\n        });\n\n        if (!user) {\n          const error = new Error('User not found') as any;\n          error.statusCode = 404;\n          throw error;\n        }\n\n        const rawData = await request.json();\n\n        // SECURITY FIX: Sanitize and validate all profile data\n        const { sanitizedData, securityIssues } = sanitizeProfileData(rawData);\n\n        // If there are security issues, log them and potentially reject the request\n        if (securityIssues.length > 0) {\n          log.warn('Profile update security issues detected', {\n            component: 'profile_api',\n            userId: user.id,\n            metadata: { securityIssues }\n          });\n\n          // For now, we'll sanitize and continue, but in production you might want to reject\n          // Uncomment the following lines to reject requests with security issues:\n          // const error = new Error('Profile data contains security violations') as any;\n          // error.statusCode = 400;\n          // error.data = { securityIssues };\n          // throw error;\n        }\n\n        const {\n          bio,\n          profilePictureUrl,\n          socialMediaLinks,\n          firstName,\n          lastName,\n          jobTitle,\n          company,\n          location,\n          phoneNumber,\n          website,\n          careerInterests,\n          skillsToLearn,\n          experienceLevel,\n          currentIndustry,\n          targetIndustry,\n          weeklyLearningGoal,\n          emailNotifications,\n          profileVisibility,\n          profilePublic,\n          showEmail,\n          showPhone\n        } = sanitizedData;\n\n        // Calculate profile completion score\n        const profileCompletionScore = calculateProfileCompletionScore({\n          bio,\n          profilePictureUrl,\n          firstName,\n          lastName,\n          jobTitle,\n          company,\n          location,\n          phoneNumber,\n          website,\n          careerInterests,\n          skillsToLearn,\n          experienceLevel,\n          currentIndustry,\n          targetIndustry,\n          weeklyLearningGoal\n        });\n\n        const updatedProfile = await prisma.profile.upsert({\n          where: { userId: user.id },\n          update: {\n            bio,\n            profilePictureUrl,\n            socialMediaLinks,\n            firstName,\n            lastName,\n            jobTitle,\n            company,\n            location,\n            phoneNumber,\n            website,\n            careerInterests: careerInterests ? JSON.stringify(careerInterests) : undefined,\n            skillsToLearn: skillsToLearn ? JSON.stringify(skillsToLearn) : undefined,\n            experienceLevel,\n            currentIndustry,\n            targetIndustry,\n            weeklyLearningGoal,\n            emailNotifications: emailNotifications ?? true,\n            profileVisibility: profileVisibility || 'COMMUNITY_ONLY',\n            profilePublic: profilePublic ?? false,\n            showEmail: showEmail ?? false,\n            showPhone: showPhone ?? false,\n            profileCompletionScore,\n            lastProfileUpdate: new Date(),\n            currentCareerPath: Array.isArray(careerInterests) ? careerInterests.join(', ') : careerInterests,\n            progressLevel: experienceLevel,\n            lastActiveAt: new Date(),\n          },\n          create: {\n            userId: user.id,\n            bio,\n            profilePictureUrl,\n            socialMediaLinks,\n            firstName,\n            lastName,\n            jobTitle,\n            company,\n            location,\n            phoneNumber,\n            website,\n            careerInterests: careerInterests ? JSON.stringify(careerInterests) : undefined,\n            skillsToLearn: skillsToLearn ? JSON.stringify(skillsToLearn) : undefined,\n            experienceLevel,\n            currentIndustry,\n            targetIndustry,\n            weeklyLearningGoal,\n            emailNotifications: emailNotifications ?? true,\n            profileVisibility: profileVisibility || 'COMMUNITY_ONLY',\n            profilePublic: profilePublic ?? false,\n            showEmail: showEmail ?? false,\n            showPhone: showPhone ?? false,\n            profileCompletionScore,\n            lastProfileUpdate: new Date(),\n            currentCareerPath: Array.isArray(careerInterests) ? careerInterests.join(', ') : careerInterests,\n            progressLevel: experienceLevel,\n          },\n        });\n\n        // INTEGRATION FIX: Invalidate profile-related caches using new CacheInvalidationService\n        const cacheService = new ConsolidatedCacheService();\n        const cacheInvalidationService = new CacheInvalidationService(cacheService);\n\n        try {\n          await cacheInvalidationService.invalidateProfileCaches(user.id);\n        } catch (cacheError) {\n          // Log cache invalidation errors but don't fail the request\n          log.warn('Failed to invalidate profile caches', {\n            component: 'profile_api',\n            userId: user.id,\n            metadata: { error: cacheError }\n          });\n        }\n\n        return NextResponse.json({ success: true, data: updatedProfile });\n      }\n    );\n  });\n});"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8862b66f15c8c9302f43e32e9ee37f295a3fc551"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1u8lalrqaz = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1u8lalrqaz();
var __awaiter =
/* istanbul ignore next */
(cov_1u8lalrqaz().s[0]++,
/* istanbul ignore next */
(cov_1u8lalrqaz().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1u8lalrqaz().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1u8lalrqaz().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1u8lalrqaz().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1u8lalrqaz().f[1]++;
    cov_1u8lalrqaz().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1u8lalrqaz().f[2]++;
      cov_1u8lalrqaz().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1u8lalrqaz().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_1u8lalrqaz().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_1u8lalrqaz().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1u8lalrqaz().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1u8lalrqaz().f[4]++;
      cov_1u8lalrqaz().s[4]++;
      try {
        /* istanbul ignore next */
        cov_1u8lalrqaz().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1u8lalrqaz().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1u8lalrqaz().f[5]++;
      cov_1u8lalrqaz().s[7]++;
      try {
        /* istanbul ignore next */
        cov_1u8lalrqaz().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1u8lalrqaz().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1u8lalrqaz().f[6]++;
      cov_1u8lalrqaz().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1u8lalrqaz().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1u8lalrqaz().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1u8lalrqaz().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1u8lalrqaz().s[12]++,
/* istanbul ignore next */
(cov_1u8lalrqaz().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_1u8lalrqaz().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1u8lalrqaz().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1u8lalrqaz().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_1u8lalrqaz().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1u8lalrqaz().f[8]++;
        cov_1u8lalrqaz().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1u8lalrqaz().b[6][0]++;
          cov_1u8lalrqaz().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1u8lalrqaz().b[6][1]++;
        }
        cov_1u8lalrqaz().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1u8lalrqaz().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1u8lalrqaz().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1u8lalrqaz().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1u8lalrqaz().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1u8lalrqaz().f[9]++;
    cov_1u8lalrqaz().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1u8lalrqaz().f[10]++;
    cov_1u8lalrqaz().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1u8lalrqaz().f[11]++;
      cov_1u8lalrqaz().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1u8lalrqaz().f[12]++;
    cov_1u8lalrqaz().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_1u8lalrqaz().b[9][0]++;
      cov_1u8lalrqaz().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1u8lalrqaz().b[9][1]++;
    }
    cov_1u8lalrqaz().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1u8lalrqaz().s[25]++;
      try {
        /* istanbul ignore next */
        cov_1u8lalrqaz().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1u8lalrqaz().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_1u8lalrqaz().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1u8lalrqaz().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1u8lalrqaz().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1u8lalrqaz().b[15][0]++,
        /* istanbul ignore next */
        (cov_1u8lalrqaz().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1u8lalrqaz().b[16][1]++,
        /* istanbul ignore next */
        (cov_1u8lalrqaz().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1u8lalrqaz().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1u8lalrqaz().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1u8lalrqaz().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1u8lalrqaz().b[12][0]++;
          cov_1u8lalrqaz().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1u8lalrqaz().b[12][1]++;
        }
        cov_1u8lalrqaz().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1u8lalrqaz().b[18][0]++;
          cov_1u8lalrqaz().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1u8lalrqaz().b[18][1]++;
        }
        cov_1u8lalrqaz().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1u8lalrqaz().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1u8lalrqaz().b[19][1]++;
            cov_1u8lalrqaz().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_1u8lalrqaz().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1u8lalrqaz().b[19][2]++;
            cov_1u8lalrqaz().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_1u8lalrqaz().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1u8lalrqaz().b[19][3]++;
            cov_1u8lalrqaz().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_1u8lalrqaz().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1u8lalrqaz().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_1u8lalrqaz().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1u8lalrqaz().b[19][4]++;
            cov_1u8lalrqaz().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1u8lalrqaz().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1u8lalrqaz().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1u8lalrqaz().b[19][5]++;
            cov_1u8lalrqaz().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_1u8lalrqaz().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1u8lalrqaz().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1u8lalrqaz().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1u8lalrqaz().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1u8lalrqaz().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1u8lalrqaz().b[20][0]++;
              cov_1u8lalrqaz().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1u8lalrqaz().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1u8lalrqaz().b[20][1]++;
            }
            cov_1u8lalrqaz().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_1u8lalrqaz().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1u8lalrqaz().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1u8lalrqaz().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1u8lalrqaz().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1u8lalrqaz().b[23][0]++;
              cov_1u8lalrqaz().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1u8lalrqaz().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1u8lalrqaz().b[23][1]++;
            }
            cov_1u8lalrqaz().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_1u8lalrqaz().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1u8lalrqaz().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1u8lalrqaz().b[25][0]++;
              cov_1u8lalrqaz().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1u8lalrqaz().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_1u8lalrqaz().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1u8lalrqaz().b[25][1]++;
            }
            cov_1u8lalrqaz().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_1u8lalrqaz().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_1u8lalrqaz().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1u8lalrqaz().b[27][0]++;
              cov_1u8lalrqaz().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1u8lalrqaz().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1u8lalrqaz().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1u8lalrqaz().b[27][1]++;
            }
            cov_1u8lalrqaz().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1u8lalrqaz().b[29][0]++;
              cov_1u8lalrqaz().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1u8lalrqaz().b[29][1]++;
            }
            cov_1u8lalrqaz().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1u8lalrqaz().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1u8lalrqaz().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1u8lalrqaz().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1u8lalrqaz().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1u8lalrqaz().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1u8lalrqaz().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1u8lalrqaz().b[30][0]++;
      cov_1u8lalrqaz().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1u8lalrqaz().b[30][1]++;
    }
    cov_1u8lalrqaz().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1u8lalrqaz().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1u8lalrqaz().b[31][1]++, void 0),
      done: true
    };
  }
}));
var __importDefault =
/* istanbul ignore next */
(cov_1u8lalrqaz().s[67]++,
/* istanbul ignore next */
(cov_1u8lalrqaz().b[32][0]++, this) &&
/* istanbul ignore next */
(cov_1u8lalrqaz().b[32][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_1u8lalrqaz().b[32][2]++, function (mod) {
  /* istanbul ignore next */
  cov_1u8lalrqaz().f[13]++;
  cov_1u8lalrqaz().s[68]++;
  return /* istanbul ignore next */(cov_1u8lalrqaz().b[34][0]++, mod) &&
  /* istanbul ignore next */
  (cov_1u8lalrqaz().b[34][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_1u8lalrqaz().b[33][0]++, mod) :
  /* istanbul ignore next */
  (cov_1u8lalrqaz().b[33][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_1u8lalrqaz().s[69]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1u8lalrqaz().s[70]++;
exports.PUT = exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_1u8lalrqaz().s[71]++, require("next/server"));
var next_1 =
/* istanbul ignore next */
(cov_1u8lalrqaz().s[72]++, require("next-auth/next"));
var auth_1 =
/* istanbul ignore next */
(cov_1u8lalrqaz().s[73]++, require("@/lib/auth"));
var prisma_1 =
/* istanbul ignore next */
(cov_1u8lalrqaz().s[74]++, __importDefault(require("@/lib/prisma")));
var logger_1 =
/* istanbul ignore next */
(cov_1u8lalrqaz().s[75]++, require("@/lib/logger"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_1u8lalrqaz().s[76]++, require("@/lib/unified-api-error-handler"));
var csrf_1 =
/* istanbul ignore next */
(cov_1u8lalrqaz().s[77]++, require("@/lib/csrf"));
var rateLimit_1 =
/* istanbul ignore next */
(cov_1u8lalrqaz().s[78]++, require("@/lib/rateLimit"));
var cache_invalidation_service_1 =
/* istanbul ignore next */
(cov_1u8lalrqaz().s[79]++, require("@/lib/services/cache-invalidation-service"));
var consolidated_cache_service_1 =
/* istanbul ignore next */
(cov_1u8lalrqaz().s[80]++, require("@/lib/services/consolidated-cache-service"));
var input_validation_1 =
/* istanbul ignore next */
(cov_1u8lalrqaz().s[81]++, require("@/lib/input-validation"));
// SECURITY FIX: Comprehensive profile data sanitization
function sanitizeProfileData(data) {
  /* istanbul ignore next */
  cov_1u8lalrqaz().f[14]++;
  var securityIssues =
  /* istanbul ignore next */
  (cov_1u8lalrqaz().s[82]++, []);
  var sanitizedData =
  /* istanbul ignore next */
  (cov_1u8lalrqaz().s[83]++, {});
  // Text fields that need sanitization
  var textFields =
  /* istanbul ignore next */
  (cov_1u8lalrqaz().s[84]++, ['bio', 'firstName', 'lastName', 'jobTitle', 'company', 'location', 'phoneNumber', 'website', 'currentIndustry', 'targetIndustry', 'mentorshipInterests', 'professionalGoals', 'skillsToTeach']);
  // Sanitize text fields
  /* istanbul ignore next */
  cov_1u8lalrqaz().s[85]++;
  textFields.forEach(function (field) {
    /* istanbul ignore next */
    cov_1u8lalrqaz().f[15]++;
    cov_1u8lalrqaz().s[86]++;
    if (
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[36][0]++, data[field] !== undefined) &&
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[36][1]++, data[field] !== null)) {
      /* istanbul ignore next */
      cov_1u8lalrqaz().b[35][0]++;
      var fieldValue =
      /* istanbul ignore next */
      (cov_1u8lalrqaz().s[87]++, String(data[field]));
      var validation =
      /* istanbul ignore next */
      (cov_1u8lalrqaz().s[88]++, (0, input_validation_1.validateSecurity)(fieldValue));
      /* istanbul ignore next */
      cov_1u8lalrqaz().s[89]++;
      if (!validation.isValid) {
        /* istanbul ignore next */
        cov_1u8lalrqaz().b[37][0]++;
        cov_1u8lalrqaz().s[90]++;
        securityIssues.push("".concat(field, ": ").concat(validation.threats.join(', ')));
      } else
      /* istanbul ignore next */
      {
        cov_1u8lalrqaz().b[37][1]++;
      }
      // Apply appropriate sanitization based on field type
      cov_1u8lalrqaz().s[91]++;
      if (
      /* istanbul ignore next */
      (cov_1u8lalrqaz().b[39][0]++, field === 'bio') ||
      /* istanbul ignore next */
      (cov_1u8lalrqaz().b[39][1]++, field === 'professionalGoals') ||
      /* istanbul ignore next */
      (cov_1u8lalrqaz().b[39][2]++, field === 'mentorshipInterests')) {
        /* istanbul ignore next */
        cov_1u8lalrqaz().b[38][0]++;
        cov_1u8lalrqaz().s[92]++;
        // Allow limited HTML for rich text fields
        sanitizedData[field] = (0, input_validation_1.sanitizeText)(validation.sanitized, {
          allowHtml: true,
          maxLength: 2000,
          stripWhitespace: true
        });
      } else {
        /* istanbul ignore next */
        cov_1u8lalrqaz().b[38][1]++;
        cov_1u8lalrqaz().s[93]++;
        if (field === 'website') {
          /* istanbul ignore next */
          cov_1u8lalrqaz().b[40][0]++;
          // Special handling for URLs
          var urlPattern =
          /* istanbul ignore next */
          (cov_1u8lalrqaz().s[94]++, /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/);
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[95]++;
          if (
          /* istanbul ignore next */
          (cov_1u8lalrqaz().b[42][0]++, fieldValue) &&
          /* istanbul ignore next */
          (cov_1u8lalrqaz().b[42][1]++, !urlPattern.test(fieldValue))) {
            /* istanbul ignore next */
            cov_1u8lalrqaz().b[41][0]++;
            cov_1u8lalrqaz().s[96]++;
            securityIssues.push("".concat(field, ": Invalid URL format"));
            /* istanbul ignore next */
            cov_1u8lalrqaz().s[97]++;
            sanitizedData[field] = '';
          } else {
            /* istanbul ignore next */
            cov_1u8lalrqaz().b[41][1]++;
            cov_1u8lalrqaz().s[98]++;
            sanitizedData[field] = (0, input_validation_1.sanitizeText)(validation.sanitized, {
              maxLength: 500,
              stripWhitespace: true
            });
          }
        } else {
          /* istanbul ignore next */
          cov_1u8lalrqaz().b[40][1]++;
          cov_1u8lalrqaz().s[99]++;
          // Standard text sanitization
          sanitizedData[field] = (0, input_validation_1.sanitizeText)(validation.sanitized, {
            maxLength: 200,
            stripWhitespace: true
          });
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_1u8lalrqaz().b[35][1]++;
    }
  });
  // Handle array fields (careerInterests, skillsToLearn)
  var arrayFields =
  /* istanbul ignore next */
  (cov_1u8lalrqaz().s[100]++, ['careerInterests', 'skillsToLearn']);
  /* istanbul ignore next */
  cov_1u8lalrqaz().s[101]++;
  arrayFields.forEach(function (field) {
    /* istanbul ignore next */
    cov_1u8lalrqaz().f[16]++;
    cov_1u8lalrqaz().s[102]++;
    if (
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[44][0]++, data[field] !== undefined) &&
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[44][1]++, data[field] !== null)) {
      /* istanbul ignore next */
      cov_1u8lalrqaz().b[43][0]++;
      cov_1u8lalrqaz().s[103]++;
      if (Array.isArray(data[field])) {
        /* istanbul ignore next */
        cov_1u8lalrqaz().b[45][0]++;
        cov_1u8lalrqaz().s[104]++;
        sanitizedData[field] = data[field].slice(0, 20) // Limit array size
        .map(function (item) {
          /* istanbul ignore next */
          cov_1u8lalrqaz().f[17]++;
          var itemStr =
          /* istanbul ignore next */
          (cov_1u8lalrqaz().s[105]++, String(item));
          var validation =
          /* istanbul ignore next */
          (cov_1u8lalrqaz().s[106]++, (0, input_validation_1.validateSecurity)(itemStr));
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[107]++;
          if (!validation.isValid) {
            /* istanbul ignore next */
            cov_1u8lalrqaz().b[46][0]++;
            cov_1u8lalrqaz().s[108]++;
            securityIssues.push("".concat(field, " item: ").concat(validation.threats.join(', ')));
          } else
          /* istanbul ignore next */
          {
            cov_1u8lalrqaz().b[46][1]++;
          }
          cov_1u8lalrqaz().s[109]++;
          return (0, input_validation_1.sanitizeText)(validation.sanitized, {
            maxLength: 100,
            stripWhitespace: true
          });
        }).filter(function (item) {
          /* istanbul ignore next */
          cov_1u8lalrqaz().f[18]++;
          cov_1u8lalrqaz().s[110]++;
          return item.length > 0;
        });
      } else {
        /* istanbul ignore next */
        cov_1u8lalrqaz().b[45][1]++;
        // Handle string format
        var validation =
        /* istanbul ignore next */
        (cov_1u8lalrqaz().s[111]++, (0, input_validation_1.validateSecurity)(String(data[field])));
        /* istanbul ignore next */
        cov_1u8lalrqaz().s[112]++;
        if (!validation.isValid) {
          /* istanbul ignore next */
          cov_1u8lalrqaz().b[47][0]++;
          cov_1u8lalrqaz().s[113]++;
          securityIssues.push("".concat(field, ": ").concat(validation.threats.join(', ')));
        } else
        /* istanbul ignore next */
        {
          cov_1u8lalrqaz().b[47][1]++;
        }
        cov_1u8lalrqaz().s[114]++;
        sanitizedData[field] = (0, input_validation_1.sanitizeText)(validation.sanitized, {
          maxLength: 500,
          stripWhitespace: true
        });
      }
    } else
    /* istanbul ignore next */
    {
      cov_1u8lalrqaz().b[43][1]++;
    }
  });
  // Handle boolean fields
  var booleanFields =
  /* istanbul ignore next */
  (cov_1u8lalrqaz().s[115]++, ['emailNotifications', 'profilePublic', 'showEmail', 'showPhone', 'availabilityForMentoring']);
  /* istanbul ignore next */
  cov_1u8lalrqaz().s[116]++;
  booleanFields.forEach(function (field) {
    /* istanbul ignore next */
    cov_1u8lalrqaz().f[19]++;
    cov_1u8lalrqaz().s[117]++;
    if (
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[49][0]++, data[field] !== undefined) &&
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[49][1]++, data[field] !== null)) {
      /* istanbul ignore next */
      cov_1u8lalrqaz().b[48][0]++;
      cov_1u8lalrqaz().s[118]++;
      sanitizedData[field] = Boolean(data[field]);
    } else
    /* istanbul ignore next */
    {
      cov_1u8lalrqaz().b[48][1]++;
    }
  });
  // Handle numeric fields
  /* istanbul ignore next */
  cov_1u8lalrqaz().s[119]++;
  if (
  /* istanbul ignore next */
  (cov_1u8lalrqaz().b[51][0]++, data.weeklyLearningGoal !== undefined) &&
  /* istanbul ignore next */
  (cov_1u8lalrqaz().b[51][1]++, data.weeklyLearningGoal !== null)) {
    /* istanbul ignore next */
    cov_1u8lalrqaz().b[50][0]++;
    var goal =
    /* istanbul ignore next */
    (cov_1u8lalrqaz().s[120]++, Number(data.weeklyLearningGoal));
    /* istanbul ignore next */
    cov_1u8lalrqaz().s[121]++;
    if (
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[53][0]++, isNaN(goal)) ||
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[53][1]++, goal < 0) ||
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[53][2]++, goal > 168)) {
      /* istanbul ignore next */
      cov_1u8lalrqaz().b[52][0]++;
      cov_1u8lalrqaz().s[122]++;
      // Max 168 hours per week
      securityIssues.push('weeklyLearningGoal: Invalid value');
      /* istanbul ignore next */
      cov_1u8lalrqaz().s[123]++;
      sanitizedData.weeklyLearningGoal = null;
    } else {
      /* istanbul ignore next */
      cov_1u8lalrqaz().b[52][1]++;
      cov_1u8lalrqaz().s[124]++;
      sanitizedData.weeklyLearningGoal = goal;
    }
  } else
  /* istanbul ignore next */
  {
    cov_1u8lalrqaz().b[50][1]++;
  }
  // Handle enum fields
  var validExperienceLevels =
  /* istanbul ignore next */
  (cov_1u8lalrqaz().s[125]++, ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']);
  /* istanbul ignore next */
  cov_1u8lalrqaz().s[126]++;
  if (
  /* istanbul ignore next */
  (cov_1u8lalrqaz().b[55][0]++, data.experienceLevel) &&
  /* istanbul ignore next */
  (cov_1u8lalrqaz().b[55][1]++, !validExperienceLevels.includes(data.experienceLevel))) {
    /* istanbul ignore next */
    cov_1u8lalrqaz().b[54][0]++;
    cov_1u8lalrqaz().s[127]++;
    securityIssues.push('experienceLevel: Invalid value');
    /* istanbul ignore next */
    cov_1u8lalrqaz().s[128]++;
    sanitizedData.experienceLevel = null;
  } else {
    /* istanbul ignore next */
    cov_1u8lalrqaz().b[54][1]++;
    cov_1u8lalrqaz().s[129]++;
    sanitizedData.experienceLevel = data.experienceLevel;
  }
  var validVisibilityOptions =
  /* istanbul ignore next */
  (cov_1u8lalrqaz().s[130]++, ['PRIVATE', 'COMMUNITY_ONLY', 'PUBLIC']);
  /* istanbul ignore next */
  cov_1u8lalrqaz().s[131]++;
  if (
  /* istanbul ignore next */
  (cov_1u8lalrqaz().b[57][0]++, data.profileVisibility) &&
  /* istanbul ignore next */
  (cov_1u8lalrqaz().b[57][1]++, !validVisibilityOptions.includes(data.profileVisibility))) {
    /* istanbul ignore next */
    cov_1u8lalrqaz().b[56][0]++;
    cov_1u8lalrqaz().s[132]++;
    securityIssues.push('profileVisibility: Invalid value');
    /* istanbul ignore next */
    cov_1u8lalrqaz().s[133]++;
    sanitizedData.profileVisibility = 'COMMUNITY_ONLY';
  } else {
    /* istanbul ignore next */
    cov_1u8lalrqaz().b[56][1]++;
    cov_1u8lalrqaz().s[134]++;
    sanitizedData.profileVisibility =
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[58][0]++, data.profileVisibility) ||
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[58][1]++, 'COMMUNITY_ONLY');
  }
  // Handle profile picture URL with extra validation
  /* istanbul ignore next */
  cov_1u8lalrqaz().s[135]++;
  if (
  /* istanbul ignore next */
  (cov_1u8lalrqaz().b[60][0]++, data.profilePictureUrl !== undefined) &&
  /* istanbul ignore next */
  (cov_1u8lalrqaz().b[60][1]++, data.profilePictureUrl !== null)) {
    /* istanbul ignore next */
    cov_1u8lalrqaz().b[59][0]++;
    var url =
    /* istanbul ignore next */
    (cov_1u8lalrqaz().s[136]++, String(data.profilePictureUrl));
    var validation =
    /* istanbul ignore next */
    (cov_1u8lalrqaz().s[137]++, (0, input_validation_1.validateSecurity)(url));
    /* istanbul ignore next */
    cov_1u8lalrqaz().s[138]++;
    if (!validation.isValid) {
      /* istanbul ignore next */
      cov_1u8lalrqaz().b[61][0]++;
      cov_1u8lalrqaz().s[139]++;
      securityIssues.push("profilePictureUrl: ".concat(validation.threats.join(', ')));
    } else
    /* istanbul ignore next */
    {
      cov_1u8lalrqaz().b[61][1]++;
    }
    // Additional URL validation for profile pictures
    var imageUrlPattern =
    /* istanbul ignore next */
    (cov_1u8lalrqaz().s[140]++, /^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)(\?.*)?$/i);
    /* istanbul ignore next */
    cov_1u8lalrqaz().s[141]++;
    if (
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[63][0]++, url) &&
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[63][1]++, !imageUrlPattern.test(url))) {
      /* istanbul ignore next */
      cov_1u8lalrqaz().b[62][0]++;
      cov_1u8lalrqaz().s[142]++;
      securityIssues.push('profilePictureUrl: Invalid image URL format');
      /* istanbul ignore next */
      cov_1u8lalrqaz().s[143]++;
      sanitizedData.profilePictureUrl = null;
    } else {
      /* istanbul ignore next */
      cov_1u8lalrqaz().b[62][1]++;
      cov_1u8lalrqaz().s[144]++;
      sanitizedData.profilePictureUrl = (0, input_validation_1.sanitizeText)(validation.sanitized, {
        maxLength: 500,
        stripWhitespace: true
      });
    }
  } else
  /* istanbul ignore next */
  {
    cov_1u8lalrqaz().b[59][1]++;
  }
  // Handle social media links object
  cov_1u8lalrqaz().s[145]++;
  if (
  /* istanbul ignore next */
  (cov_1u8lalrqaz().b[65][0]++, data.socialMediaLinks !== undefined) &&
  /* istanbul ignore next */
  (cov_1u8lalrqaz().b[65][1]++, data.socialMediaLinks !== null)) {
    /* istanbul ignore next */
    cov_1u8lalrqaz().b[64][0]++;
    cov_1u8lalrqaz().s[146]++;
    if (typeof data.socialMediaLinks === 'object') {
      /* istanbul ignore next */
      cov_1u8lalrqaz().b[66][0]++;
      var sanitizedLinks_1 =
      /* istanbul ignore next */
      (cov_1u8lalrqaz().s[147]++, {});
      var allowedPlatforms_1 =
      /* istanbul ignore next */
      (cov_1u8lalrqaz().s[148]++, ['linkedin', 'twitter', 'github', 'portfolio', 'website']);
      /* istanbul ignore next */
      cov_1u8lalrqaz().s[149]++;
      Object.keys(data.socialMediaLinks).forEach(function (platform) {
        /* istanbul ignore next */
        cov_1u8lalrqaz().f[20]++;
        cov_1u8lalrqaz().s[150]++;
        if (allowedPlatforms_1.includes(platform.toLowerCase())) {
          /* istanbul ignore next */
          cov_1u8lalrqaz().b[67][0]++;
          var url =
          /* istanbul ignore next */
          (cov_1u8lalrqaz().s[151]++, String(data.socialMediaLinks[platform]));
          var validation =
          /* istanbul ignore next */
          (cov_1u8lalrqaz().s[152]++, (0, input_validation_1.validateSecurity)(url));
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[153]++;
          if (!validation.isValid) {
            /* istanbul ignore next */
            cov_1u8lalrqaz().b[68][0]++;
            cov_1u8lalrqaz().s[154]++;
            securityIssues.push("socialMediaLinks.".concat(platform, ": ").concat(validation.threats.join(', ')));
          } else
          /* istanbul ignore next */
          {
            cov_1u8lalrqaz().b[68][1]++;
          }
          var urlPattern =
          /* istanbul ignore next */
          (cov_1u8lalrqaz().s[155]++, /^https?:\/\/.+/);
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[156]++;
          if (
          /* istanbul ignore next */
          (cov_1u8lalrqaz().b[70][0]++, url) &&
          /* istanbul ignore next */
          (cov_1u8lalrqaz().b[70][1]++, !urlPattern.test(url))) {
            /* istanbul ignore next */
            cov_1u8lalrqaz().b[69][0]++;
            cov_1u8lalrqaz().s[157]++;
            securityIssues.push("socialMediaLinks.".concat(platform, ": Invalid URL format"));
          } else {
            /* istanbul ignore next */
            cov_1u8lalrqaz().b[69][1]++;
            cov_1u8lalrqaz().s[158]++;
            sanitizedLinks_1[platform] = (0, input_validation_1.sanitizeText)(validation.sanitized, {
              maxLength: 500,
              stripWhitespace: true
            });
          }
        } else
        /* istanbul ignore next */
        {
          cov_1u8lalrqaz().b[67][1]++;
        }
      });
      /* istanbul ignore next */
      cov_1u8lalrqaz().s[159]++;
      sanitizedData.socialMediaLinks = sanitizedLinks_1;
    } else
    /* istanbul ignore next */
    {
      cov_1u8lalrqaz().b[66][1]++;
    }
  } else
  /* istanbul ignore next */
  {
    cov_1u8lalrqaz().b[64][1]++;
  }
  cov_1u8lalrqaz().s[160]++;
  return {
    sanitizedData: sanitizedData,
    securityIssues: securityIssues
  };
}
// Helper function to calculate profile completion score
function calculateProfileCompletionScore(profileData) {
  /* istanbul ignore next */
  cov_1u8lalrqaz().f[21]++;
  var fields =
  /* istanbul ignore next */
  (cov_1u8lalrqaz().s[161]++, ['bio', 'profilePictureUrl', 'firstName', 'lastName', 'jobTitle', 'company', 'location', 'phoneNumber', 'website', 'careerInterests', 'skillsToLearn', 'experienceLevel', 'currentIndustry', 'targetIndustry', 'weeklyLearningGoal']);
  var completedFields =
  /* istanbul ignore next */
  (cov_1u8lalrqaz().s[162]++, 0);
  /* istanbul ignore next */
  cov_1u8lalrqaz().s[163]++;
  fields.forEach(function (field) {
    /* istanbul ignore next */
    cov_1u8lalrqaz().f[22]++;
    var value =
    /* istanbul ignore next */
    (cov_1u8lalrqaz().s[164]++, profileData[field]);
    /* istanbul ignore next */
    cov_1u8lalrqaz().s[165]++;
    if (
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[72][0]++, value !== null) &&
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[72][1]++, value !== undefined) &&
    /* istanbul ignore next */
    (cov_1u8lalrqaz().b[72][2]++, value !== '')) {
      /* istanbul ignore next */
      cov_1u8lalrqaz().b[71][0]++;
      cov_1u8lalrqaz().s[166]++;
      if (
      /* istanbul ignore next */
      (cov_1u8lalrqaz().b[74][0]++, Array.isArray(value)) &&
      /* istanbul ignore next */
      (cov_1u8lalrqaz().b[74][1]++, value.length > 0)) {
        /* istanbul ignore next */
        cov_1u8lalrqaz().b[73][0]++;
        cov_1u8lalrqaz().s[167]++;
        completedFields++;
      } else {
        /* istanbul ignore next */
        cov_1u8lalrqaz().b[73][1]++;
        cov_1u8lalrqaz().s[168]++;
        if (
        /* istanbul ignore next */
        (cov_1u8lalrqaz().b[76][0]++, typeof value === 'string') &&
        /* istanbul ignore next */
        (cov_1u8lalrqaz().b[76][1]++, value.trim().length > 0)) {
          /* istanbul ignore next */
          cov_1u8lalrqaz().b[75][0]++;
          cov_1u8lalrqaz().s[169]++;
          completedFields++;
        } else {
          /* istanbul ignore next */
          cov_1u8lalrqaz().b[75][1]++;
          cov_1u8lalrqaz().s[170]++;
          if (
          /* istanbul ignore next */
          (cov_1u8lalrqaz().b[78][0]++, typeof value === 'number') &&
          /* istanbul ignore next */
          (cov_1u8lalrqaz().b[78][1]++, value > 0)) {
            /* istanbul ignore next */
            cov_1u8lalrqaz().b[77][0]++;
            cov_1u8lalrqaz().s[171]++;
            completedFields++;
          } else {
            /* istanbul ignore next */
            cov_1u8lalrqaz().b[77][1]++;
            cov_1u8lalrqaz().s[172]++;
            if (
            /* istanbul ignore next */
            (cov_1u8lalrqaz().b[80][0]++, typeof value !== 'string') &&
            /* istanbul ignore next */
            (cov_1u8lalrqaz().b[80][1]++, typeof value !== 'number') &&
            /* istanbul ignore next */
            (cov_1u8lalrqaz().b[80][2]++, value)) {
              /* istanbul ignore next */
              cov_1u8lalrqaz().b[79][0]++;
              cov_1u8lalrqaz().s[173]++;
              completedFields++;
            } else
            /* istanbul ignore next */
            {
              cov_1u8lalrqaz().b[79][1]++;
            }
          }
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_1u8lalrqaz().b[71][1]++;
    }
  });
  /* istanbul ignore next */
  cov_1u8lalrqaz().s[174]++;
  return Math.round(completedFields / fields.length * 100);
}
/* istanbul ignore next */
cov_1u8lalrqaz().s[175]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_1u8lalrqaz().f[23]++;
  cov_1u8lalrqaz().s[176]++;
  return __awaiter(void 0, void 0, Promise, function () {
    /* istanbul ignore next */
    cov_1u8lalrqaz().f[24]++;
    var startTime, session, error, dbStartTime, user, dbDuration, error, profile, createStartTime, createDuration, totalDuration;
    var _a;
    /* istanbul ignore next */
    cov_1u8lalrqaz().s[177]++;
    return __generator(this, function (_b) {
      /* istanbul ignore next */
      cov_1u8lalrqaz().f[25]++;
      cov_1u8lalrqaz().s[178]++;
      switch (_b.label) {
        case 0:
          /* istanbul ignore next */
          cov_1u8lalrqaz().b[81][0]++;
          cov_1u8lalrqaz().s[179]++;
          startTime = Date.now();
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[180]++;
          return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
        case 1:
          /* istanbul ignore next */
          cov_1u8lalrqaz().b[81][1]++;
          cov_1u8lalrqaz().s[181]++;
          session = _b.sent();
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[182]++;
          if (!(
          /* istanbul ignore next */
          (cov_1u8lalrqaz().b[84][0]++, (_a =
          /* istanbul ignore next */
          (cov_1u8lalrqaz().b[86][0]++, session === null) ||
          /* istanbul ignore next */
          (cov_1u8lalrqaz().b[86][1]++, session === void 0) ?
          /* istanbul ignore next */
          (cov_1u8lalrqaz().b[85][0]++, void 0) :
          /* istanbul ignore next */
          (cov_1u8lalrqaz().b[85][1]++, session.user)) === null) ||
          /* istanbul ignore next */
          (cov_1u8lalrqaz().b[84][1]++, _a === void 0) ?
          /* istanbul ignore next */
          (cov_1u8lalrqaz().b[83][0]++, void 0) :
          /* istanbul ignore next */
          (cov_1u8lalrqaz().b[83][1]++, _a.email))) {
            /* istanbul ignore next */
            cov_1u8lalrqaz().b[82][0]++;
            cov_1u8lalrqaz().s[183]++;
            logger_1.log.auth('profile_access_denied', undefined, false, {
              component: 'profile_api',
              action: 'fetch_profile'
            });
            /* istanbul ignore next */
            cov_1u8lalrqaz().s[184]++;
            error = new Error('Not authenticated');
            /* istanbul ignore next */
            cov_1u8lalrqaz().s[185]++;
            error.statusCode = 401;
            /* istanbul ignore next */
            cov_1u8lalrqaz().s[186]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_1u8lalrqaz().b[82][1]++;
          }
          cov_1u8lalrqaz().s[187]++;
          logger_1.log.info('Fetching user profile', {
            component: 'profile_api',
            action: 'fetch_profile',
            userId: session.user.email
          });
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[188]++;
          dbStartTime = Date.now();
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[189]++;
          return [4 /*yield*/, prisma_1.default.user.findUnique({
            where: {
              email: session.user.email
            },
            include: {
              profile: true
            }
          })];
        case 2:
          /* istanbul ignore next */
          cov_1u8lalrqaz().b[81][2]++;
          cov_1u8lalrqaz().s[190]++;
          user = _b.sent();
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[191]++;
          dbDuration = Date.now() - dbStartTime;
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[192]++;
          logger_1.log.database('findUnique', 'user', dbDuration, {
            userId: session.user.email
          });
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[193]++;
          if (!user) {
            /* istanbul ignore next */
            cov_1u8lalrqaz().b[87][0]++;
            cov_1u8lalrqaz().s[194]++;
            logger_1.log.warn('User not found during profile fetch', {
              component: 'profile_api',
              userId: session.user.email
            });
            /* istanbul ignore next */
            cov_1u8lalrqaz().s[195]++;
            error = new Error('User not found');
            /* istanbul ignore next */
            cov_1u8lalrqaz().s[196]++;
            error.statusCode = 404;
            /* istanbul ignore next */
            cov_1u8lalrqaz().s[197]++;
            throw error;
          } else
          /* istanbul ignore next */
          {
            cov_1u8lalrqaz().b[87][1]++;
          }
          cov_1u8lalrqaz().s[198]++;
          profile = user.profile;
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[199]++;
          if (!!profile) {
            /* istanbul ignore next */
            cov_1u8lalrqaz().b[88][0]++;
            cov_1u8lalrqaz().s[200]++;
            return [3 /*break*/, 4];
          } else
          /* istanbul ignore next */
          {
            cov_1u8lalrqaz().b[88][1]++;
          }
          cov_1u8lalrqaz().s[201]++;
          logger_1.log.info('Creating new profile for user', {
            component: 'profile_api',
            userId: user.id
          });
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[202]++;
          createStartTime = Date.now();
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[203]++;
          return [4 /*yield*/, prisma_1.default.profile.create({
            data: {
              userId: user.id
            }
          })];
        case 3:
          /* istanbul ignore next */
          cov_1u8lalrqaz().b[81][3]++;
          cov_1u8lalrqaz().s[204]++;
          profile = _b.sent();
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[205]++;
          createDuration = Date.now() - createStartTime;
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[206]++;
          logger_1.log.database('create', 'profile', createDuration, {
            userId: user.id
          });
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[207]++;
          _b.label = 4;
        case 4:
          /* istanbul ignore next */
          cov_1u8lalrqaz().b[81][4]++;
          cov_1u8lalrqaz().s[208]++;
          totalDuration = Date.now() - startTime;
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[209]++;
          logger_1.log.api('GET', '/api/profile', 200, totalDuration, {
            component: 'profile_api',
            userId: session.user.email
          });
          /* istanbul ignore next */
          cov_1u8lalrqaz().s[210]++;
          return [2 /*return*/, server_1.NextResponse.json({
            success: true,
            data: profile
          })];
      }
    });
  });
});
/* istanbul ignore next */
cov_1u8lalrqaz().s[211]++;
exports.PUT = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_1u8lalrqaz().f[26]++;
  cov_1u8lalrqaz().s[212]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_1u8lalrqaz().f[27]++;
    cov_1u8lalrqaz().s[213]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_1u8lalrqaz().f[28]++;
      cov_1u8lalrqaz().s[214]++;
      return [2 /*return*/, (0, csrf_1.withCSRFProtection)(request, function () {
        /* istanbul ignore next */
        cov_1u8lalrqaz().f[29]++;
        cov_1u8lalrqaz().s[215]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_1u8lalrqaz().f[30]++;
          cov_1u8lalrqaz().s[216]++;
          return __generator(this, function (_a) {
            /* istanbul ignore next */
            cov_1u8lalrqaz().f[31]++;
            cov_1u8lalrqaz().s[217]++;
            return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
              windowMs: 15 * 60 * 1000,
              maxRequests: 30
            }, function () {
              /* istanbul ignore next */
              cov_1u8lalrqaz().f[32]++;
              cov_1u8lalrqaz().s[218]++;
              return __awaiter(void 0, void 0, void 0, function () {
                /* istanbul ignore next */
                cov_1u8lalrqaz().f[33]++;
                var session, error, user, error, rawData, _a, sanitizedData, securityIssues, bio, profilePictureUrl, socialMediaLinks, firstName, lastName, jobTitle, company, location, phoneNumber, website, careerInterests, skillsToLearn, experienceLevel, currentIndustry, targetIndustry, weeklyLearningGoal, emailNotifications, profileVisibility, profilePublic, showEmail, showPhone, profileCompletionScore, updatedProfile, cacheService, cacheInvalidationService, cacheError_1;
                var _b;
                /* istanbul ignore next */
                cov_1u8lalrqaz().s[219]++;
                return __generator(this, function (_c) {
                  /* istanbul ignore next */
                  cov_1u8lalrqaz().f[34]++;
                  cov_1u8lalrqaz().s[220]++;
                  switch (_c.label) {
                    case 0:
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().b[89][0]++;
                      cov_1u8lalrqaz().s[221]++;
                      return [4 /*yield*/, (0, next_1.getServerSession)(auth_1.authOptions)];
                    case 1:
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().b[89][1]++;
                      cov_1u8lalrqaz().s[222]++;
                      session = _c.sent();
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().s[223]++;
                      if (!(
                      /* istanbul ignore next */
                      (cov_1u8lalrqaz().b[92][0]++, (_b =
                      /* istanbul ignore next */
                      (cov_1u8lalrqaz().b[94][0]++, session === null) ||
                      /* istanbul ignore next */
                      (cov_1u8lalrqaz().b[94][1]++, session === void 0) ?
                      /* istanbul ignore next */
                      (cov_1u8lalrqaz().b[93][0]++, void 0) :
                      /* istanbul ignore next */
                      (cov_1u8lalrqaz().b[93][1]++, session.user)) === null) ||
                      /* istanbul ignore next */
                      (cov_1u8lalrqaz().b[92][1]++, _b === void 0) ?
                      /* istanbul ignore next */
                      (cov_1u8lalrqaz().b[91][0]++, void 0) :
                      /* istanbul ignore next */
                      (cov_1u8lalrqaz().b[91][1]++, _b.email))) {
                        /* istanbul ignore next */
                        cov_1u8lalrqaz().b[90][0]++;
                        cov_1u8lalrqaz().s[224]++;
                        error = new Error('Not authenticated');
                        /* istanbul ignore next */
                        cov_1u8lalrqaz().s[225]++;
                        error.statusCode = 401;
                        /* istanbul ignore next */
                        cov_1u8lalrqaz().s[226]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_1u8lalrqaz().b[90][1]++;
                      }
                      cov_1u8lalrqaz().s[227]++;
                      return [4 /*yield*/, prisma_1.default.user.findUnique({
                        where: {
                          email: session.user.email
                        }
                      })];
                    case 2:
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().b[89][2]++;
                      cov_1u8lalrqaz().s[228]++;
                      user = _c.sent();
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().s[229]++;
                      if (!user) {
                        /* istanbul ignore next */
                        cov_1u8lalrqaz().b[95][0]++;
                        cov_1u8lalrqaz().s[230]++;
                        error = new Error('User not found');
                        /* istanbul ignore next */
                        cov_1u8lalrqaz().s[231]++;
                        error.statusCode = 404;
                        /* istanbul ignore next */
                        cov_1u8lalrqaz().s[232]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_1u8lalrqaz().b[95][1]++;
                      }
                      cov_1u8lalrqaz().s[233]++;
                      return [4 /*yield*/, request.json()];
                    case 3:
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().b[89][3]++;
                      cov_1u8lalrqaz().s[234]++;
                      rawData = _c.sent();
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().s[235]++;
                      _a = sanitizeProfileData(rawData), sanitizedData = _a.sanitizedData, securityIssues = _a.securityIssues;
                      // If there are security issues, log them and potentially reject the request
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().s[236]++;
                      if (securityIssues.length > 0) {
                        /* istanbul ignore next */
                        cov_1u8lalrqaz().b[96][0]++;
                        cov_1u8lalrqaz().s[237]++;
                        logger_1.log.warn('Profile update security issues detected', {
                          component: 'profile_api',
                          userId: user.id,
                          metadata: {
                            securityIssues: securityIssues
                          }
                        });
                        // For now, we'll sanitize and continue, but in production you might want to reject
                        // Uncomment the following lines to reject requests with security issues:
                        // const error = new Error('Profile data contains security violations') as any;
                        // error.statusCode = 400;
                        // error.data = { securityIssues };
                        // throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_1u8lalrqaz().b[96][1]++;
                      }
                      cov_1u8lalrqaz().s[238]++;
                      bio = sanitizedData.bio, profilePictureUrl = sanitizedData.profilePictureUrl, socialMediaLinks = sanitizedData.socialMediaLinks, firstName = sanitizedData.firstName, lastName = sanitizedData.lastName, jobTitle = sanitizedData.jobTitle, company = sanitizedData.company, location = sanitizedData.location, phoneNumber = sanitizedData.phoneNumber, website = sanitizedData.website, careerInterests = sanitizedData.careerInterests, skillsToLearn = sanitizedData.skillsToLearn, experienceLevel = sanitizedData.experienceLevel, currentIndustry = sanitizedData.currentIndustry, targetIndustry = sanitizedData.targetIndustry, weeklyLearningGoal = sanitizedData.weeklyLearningGoal, emailNotifications = sanitizedData.emailNotifications, profileVisibility = sanitizedData.profileVisibility, profilePublic = sanitizedData.profilePublic, showEmail = sanitizedData.showEmail, showPhone = sanitizedData.showPhone;
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().s[239]++;
                      profileCompletionScore = calculateProfileCompletionScore({
                        bio: bio,
                        profilePictureUrl: profilePictureUrl,
                        firstName: firstName,
                        lastName: lastName,
                        jobTitle: jobTitle,
                        company: company,
                        location: location,
                        phoneNumber: phoneNumber,
                        website: website,
                        careerInterests: careerInterests,
                        skillsToLearn: skillsToLearn,
                        experienceLevel: experienceLevel,
                        currentIndustry: currentIndustry,
                        targetIndustry: targetIndustry,
                        weeklyLearningGoal: weeklyLearningGoal
                      });
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().s[240]++;
                      return [4 /*yield*/, prisma_1.default.profile.upsert({
                        where: {
                          userId: user.id
                        },
                        update: {
                          bio: bio,
                          profilePictureUrl: profilePictureUrl,
                          socialMediaLinks: socialMediaLinks,
                          firstName: firstName,
                          lastName: lastName,
                          jobTitle: jobTitle,
                          company: company,
                          location: location,
                          phoneNumber: phoneNumber,
                          website: website,
                          careerInterests: careerInterests ?
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[97][0]++, JSON.stringify(careerInterests)) :
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[97][1]++, undefined),
                          skillsToLearn: skillsToLearn ?
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[98][0]++, JSON.stringify(skillsToLearn)) :
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[98][1]++, undefined),
                          experienceLevel: experienceLevel,
                          currentIndustry: currentIndustry,
                          targetIndustry: targetIndustry,
                          weeklyLearningGoal: weeklyLearningGoal,
                          emailNotifications:
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[100][0]++, emailNotifications !== null) &&
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[100][1]++, emailNotifications !== void 0) ?
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[99][0]++, emailNotifications) :
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[99][1]++, true),
                          profileVisibility:
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[101][0]++, profileVisibility) ||
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[101][1]++, 'COMMUNITY_ONLY'),
                          profilePublic:
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[103][0]++, profilePublic !== null) &&
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[103][1]++, profilePublic !== void 0) ?
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[102][0]++, profilePublic) :
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[102][1]++, false),
                          showEmail:
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[105][0]++, showEmail !== null) &&
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[105][1]++, showEmail !== void 0) ?
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[104][0]++, showEmail) :
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[104][1]++, false),
                          showPhone:
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[107][0]++, showPhone !== null) &&
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[107][1]++, showPhone !== void 0) ?
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[106][0]++, showPhone) :
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[106][1]++, false),
                          profileCompletionScore: profileCompletionScore,
                          lastProfileUpdate: new Date(),
                          currentCareerPath: Array.isArray(careerInterests) ?
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[108][0]++, careerInterests.join(', ')) :
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[108][1]++, careerInterests),
                          progressLevel: experienceLevel,
                          lastActiveAt: new Date()
                        },
                        create: {
                          userId: user.id,
                          bio: bio,
                          profilePictureUrl: profilePictureUrl,
                          socialMediaLinks: socialMediaLinks,
                          firstName: firstName,
                          lastName: lastName,
                          jobTitle: jobTitle,
                          company: company,
                          location: location,
                          phoneNumber: phoneNumber,
                          website: website,
                          careerInterests: careerInterests ?
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[109][0]++, JSON.stringify(careerInterests)) :
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[109][1]++, undefined),
                          skillsToLearn: skillsToLearn ?
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[110][0]++, JSON.stringify(skillsToLearn)) :
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[110][1]++, undefined),
                          experienceLevel: experienceLevel,
                          currentIndustry: currentIndustry,
                          targetIndustry: targetIndustry,
                          weeklyLearningGoal: weeklyLearningGoal,
                          emailNotifications:
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[112][0]++, emailNotifications !== null) &&
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[112][1]++, emailNotifications !== void 0) ?
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[111][0]++, emailNotifications) :
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[111][1]++, true),
                          profileVisibility:
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[113][0]++, profileVisibility) ||
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[113][1]++, 'COMMUNITY_ONLY'),
                          profilePublic:
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[115][0]++, profilePublic !== null) &&
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[115][1]++, profilePublic !== void 0) ?
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[114][0]++, profilePublic) :
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[114][1]++, false),
                          showEmail:
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[117][0]++, showEmail !== null) &&
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[117][1]++, showEmail !== void 0) ?
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[116][0]++, showEmail) :
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[116][1]++, false),
                          showPhone:
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[119][0]++, showPhone !== null) &&
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[119][1]++, showPhone !== void 0) ?
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[118][0]++, showPhone) :
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[118][1]++, false),
                          profileCompletionScore: profileCompletionScore,
                          lastProfileUpdate: new Date(),
                          currentCareerPath: Array.isArray(careerInterests) ?
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[120][0]++, careerInterests.join(', ')) :
                          /* istanbul ignore next */
                          (cov_1u8lalrqaz().b[120][1]++, careerInterests),
                          progressLevel: experienceLevel
                        }
                      })];
                    case 4:
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().b[89][4]++;
                      cov_1u8lalrqaz().s[241]++;
                      updatedProfile = _c.sent();
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().s[242]++;
                      cacheService = new consolidated_cache_service_1.ConsolidatedCacheService();
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().s[243]++;
                      cacheInvalidationService = new cache_invalidation_service_1.CacheInvalidationService(cacheService);
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().s[244]++;
                      _c.label = 5;
                    case 5:
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().b[89][5]++;
                      cov_1u8lalrqaz().s[245]++;
                      _c.trys.push([5, 7,, 8]);
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().s[246]++;
                      return [4 /*yield*/, cacheInvalidationService.invalidateProfileCaches(user.id)];
                    case 6:
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().b[89][6]++;
                      cov_1u8lalrqaz().s[247]++;
                      _c.sent();
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().s[248]++;
                      return [3 /*break*/, 8];
                    case 7:
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().b[89][7]++;
                      cov_1u8lalrqaz().s[249]++;
                      cacheError_1 = _c.sent();
                      // Log cache invalidation errors but don't fail the request
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().s[250]++;
                      logger_1.log.warn('Failed to invalidate profile caches', {
                        component: 'profile_api',
                        userId: user.id,
                        metadata: {
                          error: cacheError_1
                        }
                      });
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().s[251]++;
                      return [3 /*break*/, 8];
                    case 8:
                      /* istanbul ignore next */
                      cov_1u8lalrqaz().b[89][8]++;
                      cov_1u8lalrqaz().s[252]++;
                      return [2 /*return*/, server_1.NextResponse.json({
                        success: true,
                        data: updatedProfile
                      })];
                  }
                });
              });
            })];
          });
        });
      })];
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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