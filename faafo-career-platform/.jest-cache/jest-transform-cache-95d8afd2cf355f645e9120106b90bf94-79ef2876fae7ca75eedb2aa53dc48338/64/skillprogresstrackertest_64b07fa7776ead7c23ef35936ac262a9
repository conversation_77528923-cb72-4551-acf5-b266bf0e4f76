266d05cd805506e5bb0a896ed15b888d
"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
// Mock the Progress component
jest.mock('@/components/ui/progress', function () { return ({
    Progress: function (_a) {
        var value = _a.value, className = _a.className;
        return ((0, jsx_runtime_1.jsx)("div", { "data-testid": "progress-bar", "data-value": value, className: className }));
    },
}); });
/**
 * Skill Progress Tracker Tests
 *
 * Tests Skill Progress Tracker component functionality, rendering, user interactions, and edge cases.
 *
 * @category unit
 * @requires React Testing Library, component mocking
 */
var react_1 = __importDefault(require("react"));
var react_2 = require("@testing-library/react");
require("@testing-library/jest-dom");
var SkillProgressTracker_1 = __importDefault(require("@/components/skills/visualizations/SkillProgressTracker"));
var mockSkillProgressData = [
    {
        id: 'skill-1',
        name: 'JavaScript',
        category: 'Programming',
        currentLevel: 7,
        targetLevel: 9,
        progress: 75,
        status: 'in_progress',
        estimatedTimeToComplete: 4,
        priority: 'high',
        lastUpdated: '2025-06-21T10:00:00Z',
        milestones: [
            {
                id: 'milestone-1',
                title: 'Complete ES6 modules',
                completed: true,
                dueDate: '2025-06-15T00:00:00Z',
            },
            {
                id: 'milestone-2',
                title: 'Build React project',
                completed: false,
                dueDate: '2025-07-01T00:00:00Z',
            },
        ],
    },
    {
        id: 'skill-2',
        name: 'React',
        category: 'Frontend',
        currentLevel: 6,
        targetLevel: 8,
        progress: 50,
        status: 'at_risk',
        estimatedTimeToComplete: 6,
        priority: 'critical',
        lastUpdated: '2025-06-20T15:30:00Z',
        milestones: [
            {
                id: 'milestone-3',
                title: 'Learn hooks',
                completed: true,
            },
            {
                id: 'milestone-4',
                title: 'State management',
                completed: false,
            },
        ],
    },
    {
        id: 'skill-3',
        name: 'Node.js',
        category: 'Backend',
        currentLevel: 8,
        targetLevel: 8,
        progress: 100,
        status: 'completed',
        estimatedTimeToComplete: 0,
        priority: 'medium',
        lastUpdated: '2025-06-19T12:00:00Z',
        milestones: [
            {
                id: 'milestone-5',
                title: 'Express.js mastery',
                completed: true,
            },
        ],
    },
];
describe('SkillProgressTracker', function () {
    describe('Component Rendering', function () {
        it('should render with default props', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            expect(react_2.screen.getByText('Skill Development Progress')).toBeInTheDocument();
            expect(react_2.screen.getByText('Track your progress towards your skill development goals')).toBeInTheDocument();
        });
        it('should render with custom title and description', function () {
            var customTitle = 'Custom Progress Tracker';
            var customDescription = 'Custom description for testing';
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData, title: customTitle, description: customDescription }));
            expect(react_2.screen.getByText(customTitle)).toBeInTheDocument();
            expect(react_2.screen.getByText(customDescription)).toBeInTheDocument();
        });
        it('should render without description when not provided', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData, title: "Test Title", description: "" }));
            expect(react_2.screen.getByText('Test Title')).toBeInTheDocument();
            expect(react_2.screen.queryByText('Track your progress')).not.toBeInTheDocument();
        });
    });
    describe('Overall Progress Summary', function () {
        it('should calculate and display overall progress correctly', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            // Overall progress: (75 + 50 + 100) / 3 = 75.0%
            expect(react_2.screen.getByText('75.0%')).toBeInTheDocument();
            expect(react_2.screen.getByText('Overall Progress')).toBeInTheDocument();
        });
        it('should count completed skills correctly', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            // Only Node.js is completed
            expect(react_2.screen.getByText('1/3')).toBeInTheDocument();
            expect(react_2.screen.getByText('Completed Skills')).toBeInTheDocument();
        });
        it('should count at-risk skills correctly', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            // Only React is at risk
            expect(react_2.screen.getByText('1')).toBeInTheDocument();
            expect(react_2.screen.getByText('At Risk')).toBeInTheDocument();
        });
        it('should handle empty skills array', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: [] }));
            expect(react_2.screen.getByText('0.0%')).toBeInTheDocument(); // Overall progress
            expect(react_2.screen.getByText('0/0')).toBeInTheDocument(); // Completed skills
            expect(react_2.screen.getByText('0')).toBeInTheDocument(); // At risk skills
        });
    });
    describe('Skill Items Display', function () {
        it('should display all skills with correct information', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            // Check skill names
            expect(react_2.screen.getByText('JavaScript')).toBeInTheDocument();
            expect(react_2.screen.getByText('React')).toBeInTheDocument();
            expect(react_2.screen.getByText('Node.js')).toBeInTheDocument();
            // Check level progression
            expect(react_2.screen.getByText('Level 7 → 9')).toBeInTheDocument();
            expect(react_2.screen.getByText('Level 6 → 8')).toBeInTheDocument();
            expect(react_2.screen.getByText('Level 8 → 8')).toBeInTheDocument();
        });
        it('should display progress bars for each skill', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            var progressBars = react_2.screen.getAllByTestId('progress-bar');
            expect(progressBars).toHaveLength(3);
            expect(progressBars[0]).toHaveAttribute('data-value', '75');
            expect(progressBars[1]).toHaveAttribute('data-value', '50');
            expect(progressBars[2]).toHaveAttribute('data-value', '100');
        });
        it('should display correct status badges', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            expect(react_2.screen.getByText('in progress')).toBeInTheDocument();
            expect(react_2.screen.getByText('at risk')).toBeInTheDocument();
            expect(react_2.screen.getByText('completed')).toBeInTheDocument();
        });
        it('should display correct priority badges', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            expect(react_2.screen.getByText('high')).toBeInTheDocument();
            expect(react_2.screen.getByText('critical')).toBeInTheDocument();
            expect(react_2.screen.getByText('medium')).toBeInTheDocument();
        });
        it('should display estimated time to complete', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            expect(react_2.screen.getByText('Est. 4 weeks')).toBeInTheDocument();
            expect(react_2.screen.getByText('Est. 6 weeks')).toBeInTheDocument();
            expect(react_2.screen.getByText('Est. 0 weeks')).toBeInTheDocument();
        });
    });
    describe('Milestones Display', function () {
        it('should display milestones when showMilestones is true', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData, showMilestones: true }));
            expect(react_2.screen.getByText('Complete ES6 modules')).toBeInTheDocument();
            expect(react_2.screen.getByText('Build React project')).toBeInTheDocument();
            expect(react_2.screen.getByText('Learn hooks')).toBeInTheDocument();
            expect(react_2.screen.getByText('State management')).toBeInTheDocument();
            expect(react_2.screen.getByText('Express.js mastery')).toBeInTheDocument();
        });
        it('should not display milestones when showMilestones is false', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData, showMilestones: false }));
            expect(react_2.screen.queryByText('Complete ES6 modules')).not.toBeInTheDocument();
            expect(react_2.screen.queryByText('Milestones')).not.toBeInTheDocument();
        });
        it('should display milestone due dates when available', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData, showMilestones: true }));
            // Check for formatted dates
            expect(react_2.screen.getByText('6/15/2025')).toBeInTheDocument();
            expect(react_2.screen.getByText('7/1/2025')).toBeInTheDocument();
        });
        it('should handle milestones without due dates', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData, showMilestones: true }));
            // Milestones without due dates should still be displayed
            expect(react_2.screen.getByText('Learn hooks')).toBeInTheDocument();
            expect(react_2.screen.getByText('State management')).toBeInTheDocument();
        });
    });
    describe('Category Grouping', function () {
        it('should group skills by category when groupByCategory is true', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData, groupByCategory: true }));
            expect(react_2.screen.getByText('Programming')).toBeInTheDocument();
            expect(react_2.screen.getByText('Frontend')).toBeInTheDocument();
            expect(react_2.screen.getByText('Backend')).toBeInTheDocument();
        });
        it('should not show category headers when groupByCategory is false', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData, groupByCategory: false }));
            expect(react_2.screen.queryByText('Programming')).not.toBeInTheDocument();
            expect(react_2.screen.queryByText('Frontend')).not.toBeInTheDocument();
            expect(react_2.screen.queryByText('Backend')).not.toBeInTheDocument();
        });
    });
    describe('Status Icons', function () {
        it('should display correct status icons', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            // We can't easily test the actual icons, but we can verify the component renders
            // In a real test, you might check for specific icon classes or data attributes
            expect(react_2.screen.getByText('JavaScript')).toBeInTheDocument();
            expect(react_2.screen.getByText('React')).toBeInTheDocument();
            expect(react_2.screen.getByText('Node.js')).toBeInTheDocument();
        });
    });
    describe('Last Updated Display', function () {
        it('should display last updated dates', function () {
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: mockSkillProgressData }));
            expect(react_2.screen.getByText('Last updated: 6/21/2025')).toBeInTheDocument();
            expect(react_2.screen.getByText('Last updated: 6/20/2025')).toBeInTheDocument();
            expect(react_2.screen.getByText('Last updated: 6/19/2025')).toBeInTheDocument();
        });
    });
    describe('Edge Cases', function () {
        it('should handle skills with no milestones', function () {
            var skillsWithoutMilestones = [
                __assign(__assign({}, mockSkillProgressData[0]), { milestones: [] }),
            ];
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: skillsWithoutMilestones, showMilestones: true }));
            expect(react_2.screen.getByText('JavaScript')).toBeInTheDocument();
            // Should not show milestones section if no milestones exist
        });
        it('should handle skills with not_started status', function () {
            var notStartedSkill = [
                __assign(__assign({}, mockSkillProgressData[0]), { status: 'not_started', progress: 0 }),
            ];
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: notStartedSkill }));
            expect(react_2.screen.getByText('not started')).toBeInTheDocument();
        });
        it('should handle skills with low priority', function () {
            var lowPrioritySkill = [
                __assign(__assign({}, mockSkillProgressData[0]), { priority: 'low' }),
            ];
            (0, react_2.render)((0, jsx_runtime_1.jsx)(SkillProgressTracker_1.default, { skills: lowPrioritySkill }));
            expect(react_2.screen.getByText('low')).toBeInTheDocument();
        });
    });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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