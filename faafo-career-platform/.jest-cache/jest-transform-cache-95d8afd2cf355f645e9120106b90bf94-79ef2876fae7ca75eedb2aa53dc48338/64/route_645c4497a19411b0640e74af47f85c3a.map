{"version": 3, "names": ["next_auth_1", "cov_zlbm2yvqc", "s", "__importDefault", "require", "auth_1", "handler", "default", "authOptions", "exports", "GET", "POST"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/[...nextauth]/route.tsx"], "sourcesContent": ["import NextAuth from \"next-auth\";\nimport { authOptions } from \"@/lib/auth\"; // Import authOptions from the auth configuration file\n\nconst handler = NextAuth(authOptions);\n\n// Export handlers directly without wrapping to avoid conflicts with NextAuth's internal error handling\nexport const GET = handler;\nexport const POST = handler;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,WAAA;AAAA;AAAA,CAAAC,aAAA,GAAAC,CAAA,OAAAC,eAAA,CAAAC,OAAA;AACA,IAAAC,MAAA;AAAA;AAAA,CAAAJ,aAAA,GAAAC,CAAA,OAAAE,OAAA,gBAAyC,CAAC;AAE1C,IAAME,OAAO;AAAA;AAAA,CAAAL,aAAA,GAAAC,CAAA,OAAG,IAAAF,WAAA,CAAAO,OAAQ,EAACF,MAAA,CAAAG,WAAW,CAAC;AAErC;AAAA;AAAAP,aAAA,GAAAC,CAAA;AACaO,OAAA,CAAAC,GAAG,GAAGJ,OAAO;AAAC;AAAAL,aAAA,GAAAC,CAAA;AACdO,OAAA,CAAAE,IAAI,GAAGL,OAAO", "ignoreList": []}