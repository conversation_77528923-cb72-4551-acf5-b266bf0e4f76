7f26cc12917afd9549e699cf647e2a85
"use strict";

/* istanbul ignore next */
function cov_zlbm2yvqc() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/[...nextauth]/route.tsx";
  var hash = "8ff3836f5131ce7f23701626603b5f9dc3aa1088";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/[...nextauth]/route.tsx",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 22
        },
        end: {
          line: 4,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 3,
          column: 62
        }
      },
      "2": {
        start: {
          line: 5,
          column: 0
        },
        end: {
          line: 5,
          column: 62
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 6,
          column: 36
        }
      },
      "4": {
        start: {
          line: 7,
          column: 18
        },
        end: {
          line: 7,
          column: 55
        }
      },
      "5": {
        start: {
          line: 8,
          column: 13
        },
        end: {
          line: 8,
          column: 34
        }
      },
      "6": {
        start: {
          line: 9,
          column: 14
        },
        end: {
          line: 9,
          column: 58
        }
      },
      "7": {
        start: {
          line: 11,
          column: 0
        },
        end: {
          line: 11,
          column: 22
        }
      },
      "8": {
        start: {
          line: 12,
          column: 0
        },
        end: {
          line: 12,
          column: 23
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 2,
            column: 57
          }
        },
        loc: {
          start: {
            line: 2,
            column: 71
          },
          end: {
            line: 4,
            column: 1
          }
        },
        line: 2
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 22
          },
          end: {
            line: 4,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 23
          },
          end: {
            line: 2,
            column: 27
          }
        }, {
          start: {
            line: 2,
            column: 31
          },
          end: {
            line: 2,
            column: 51
          }
        }, {
          start: {
            line: 2,
            column: 56
          },
          end: {
            line: 4,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 11
          },
          end: {
            line: 3,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 37
          },
          end: {
            line: 3,
            column: 40
          }
        }, {
          start: {
            line: 3,
            column: 43
          },
          end: {
            line: 3,
            column: 61
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 12
          },
          end: {
            line: 3,
            column: 15
          }
        }, {
          start: {
            line: 3,
            column: 19
          },
          end: {
            line: 3,
            column: 33
          }
        }],
        line: 3
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/[...nextauth]/route.tsx",
      mappings: ";;;;;;AAAA,wDAAiC;AACjC,mCAAyC,CAAC,sDAAsD;AAEhG,IAAM,OAAO,GAAG,IAAA,mBAAQ,EAAC,kBAAW,CAAC,CAAC;AAEtC,uGAAuG;AAC1F,QAAA,GAAG,GAAG,OAAO,CAAC;AACd,QAAA,IAAI,GAAG,OAAO,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/auth/[...nextauth]/route.tsx"],
      sourcesContent: ["import NextAuth from \"next-auth\";\nimport { authOptions } from \"@/lib/auth\"; // Import authOptions from the auth configuration file\n\nconst handler = NextAuth(authOptions);\n\n// Export handlers directly without wrapping to avoid conflicts with NextAuth's internal error handling\nexport const GET = handler;\nexport const POST = handler;"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8ff3836f5131ce7f23701626603b5f9dc3aa1088"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_zlbm2yvqc = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_zlbm2yvqc();
var __importDefault =
/* istanbul ignore next */
(cov_zlbm2yvqc().s[0]++,
/* istanbul ignore next */
(cov_zlbm2yvqc().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_zlbm2yvqc().b[0][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_zlbm2yvqc().b[0][2]++, function (mod) {
  /* istanbul ignore next */
  cov_zlbm2yvqc().f[0]++;
  cov_zlbm2yvqc().s[1]++;
  return /* istanbul ignore next */(cov_zlbm2yvqc().b[2][0]++, mod) &&
  /* istanbul ignore next */
  (cov_zlbm2yvqc().b[2][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_zlbm2yvqc().b[1][0]++, mod) :
  /* istanbul ignore next */
  (cov_zlbm2yvqc().b[1][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_zlbm2yvqc().s[2]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_zlbm2yvqc().s[3]++;
exports.POST = exports.GET = void 0;
var next_auth_1 =
/* istanbul ignore next */
(cov_zlbm2yvqc().s[4]++, __importDefault(require("next-auth")));
var auth_1 =
/* istanbul ignore next */
(cov_zlbm2yvqc().s[5]++, require("@/lib/auth")); // Import authOptions from the auth configuration file
var handler =
/* istanbul ignore next */
(cov_zlbm2yvqc().s[6]++, (0, next_auth_1.default)(auth_1.authOptions));
// Export handlers directly without wrapping to avoid conflicts with NextAuth's internal error handling
/* istanbul ignore next */
cov_zlbm2yvqc().s[7]++;
exports.GET = handler;
/* istanbul ignore next */
cov_zlbm2yvqc().s[8]++;
exports.POST = handler;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJuZXh0X2F1dGhfMSIsImNvdl96bGJtMnl2cWMiLCJzIiwiX19pbXBvcnREZWZhdWx0IiwicmVxdWlyZSIsImF1dGhfMSIsImhhbmRsZXIiLCJkZWZhdWx0IiwiYXV0aE9wdGlvbnMiLCJleHBvcnRzIiwiR0VUIiwiUE9TVCJdLCJzb3VyY2VzIjpbIi9Vc2Vycy9kZDYwL2ZhYWZvL2ZhYWZvL2ZhYWZvLWNhcmVlci1wbGF0Zm9ybS9zcmMvYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBOZXh0QXV0aCBmcm9tIFwibmV4dC1hdXRoXCI7XG5pbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gXCJAL2xpYi9hdXRoXCI7IC8vIEltcG9ydCBhdXRoT3B0aW9ucyBmcm9tIHRoZSBhdXRoIGNvbmZpZ3VyYXRpb24gZmlsZVxuXG5jb25zdCBoYW5kbGVyID0gTmV4dEF1dGgoYXV0aE9wdGlvbnMpO1xuXG4vLyBFeHBvcnQgaGFuZGxlcnMgZGlyZWN0bHkgd2l0aG91dCB3cmFwcGluZyB0byBhdm9pZCBjb25mbGljdHMgd2l0aCBOZXh0QXV0aCdzIGludGVybmFsIGVycm9yIGhhbmRsaW5nXG5leHBvcnQgY29uc3QgR0VUID0gaGFuZGxlcjtcbmV4cG9ydCBjb25zdCBQT1NUID0gaGFuZGxlcjsiXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLElBQUFBLFdBQUE7QUFBQTtBQUFBLENBQUFDLGFBQUEsR0FBQUMsQ0FBQSxPQUFBQyxlQUFBLENBQUFDLE9BQUE7QUFDQSxJQUFBQyxNQUFBO0FBQUE7QUFBQSxDQUFBSixhQUFBLEdBQUFDLENBQUEsT0FBQUUsT0FBQSxnQkFBeUMsQ0FBQztBQUUxQyxJQUFNRSxPQUFPO0FBQUE7QUFBQSxDQUFBTCxhQUFBLEdBQUFDLENBQUEsT0FBRyxJQUFBRixXQUFBLENBQUFPLE9BQVEsRUFBQ0YsTUFBQSxDQUFBRyxXQUFXLENBQUM7QUFFckM7QUFBQTtBQUFBUCxhQUFBLEdBQUFDLENBQUE7QUFDYU8sT0FBQSxDQUFBQyxHQUFHLEdBQUdKLE9BQU87QUFBQztBQUFBTCxhQUFBLEdBQUFDLENBQUE7QUFDZE8sT0FBQSxDQUFBRSxJQUFJLEdBQUdMLE9BQU8iLCJpZ25vcmVMaXN0IjpbXX0=