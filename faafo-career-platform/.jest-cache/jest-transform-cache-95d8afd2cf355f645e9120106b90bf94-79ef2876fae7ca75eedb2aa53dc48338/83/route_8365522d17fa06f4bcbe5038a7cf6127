c11172b16fd9510f1685375a3855fa0e
"use strict";

/* istanbul ignore next */
function cov_2mjl24kbm9() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/interview-practice/[sessionId]/responses/route.ts";
  var hash = "e3e01080e6a7031bb1713a7da56f8320a4bb2f0b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/interview-practice/[sessionId]/responses/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 49,
          column: 62
        }
      },
      "79": {
        start: {
          line: 50,
          column: 0
        },
        end: {
          line: 50,
          column: 36
        }
      },
      "80": {
        start: {
          line: 51,
          column: 15
        },
        end: {
          line: 51,
          column: 37
        }
      },
      "81": {
        start: {
          line: 52,
          column: 18
        },
        end: {
          line: 52,
          column: 38
        }
      },
      "82": {
        start: {
          line: 53,
          column: 13
        },
        end: {
          line: 53,
          column: 34
        }
      },
      "83": {
        start: {
          line: 54,
          column: 15
        },
        end: {
          line: 54,
          column: 38
        }
      },
      "84": {
        start: {
          line: 55,
          column: 34
        },
        end: {
          line: 55,
          column: 76
        }
      },
      "85": {
        start: {
          line: 56,
          column: 18
        },
        end: {
          line: 56,
          column: 44
        }
      },
      "86": {
        start: {
          line: 57,
          column: 32
        },
        end: {
          line: 57,
          column: 72
        }
      },
      "87": {
        start: {
          line: 58,
          column: 35
        },
        end: {
          line: 58,
          column: 78
        }
      },
      "88": {
        start: {
          line: 59,
          column: 12
        },
        end: {
          line: 59,
          column: 26
        }
      },
      "89": {
        start: {
          line: 61,
          column: 27
        },
        end: {
          line: 70,
          column: 2
        }
      },
      "90": {
        start: {
          line: 64,
          column: 86
        },
        end: {
          line: 64,
          column: 110
        }
      },
      "91": {
        start: {
          line: 65,
          column: 86
        },
        end: {
          line: 65,
          column: 110
        }
      },
      "92": {
        start: {
          line: 68,
          column: 91
        },
        end: {
          line: 68,
          column: 115
        }
      },
      "93": {
        start: {
          line: 72,
          column: 27
        },
        end: {
          line: 80,
          column: 2
        }
      },
      "94": {
        start: {
          line: 74,
          column: 86
        },
        end: {
          line: 74,
          column: 110
        }
      },
      "95": {
        start: {
          line: 75,
          column: 86
        },
        end: {
          line: 75,
          column: 110
        }
      },
      "96": {
        start: {
          line: 78,
          column: 91
        },
        end: {
          line: 78,
          column: 115
        }
      },
      "97": {
        start: {
          line: 82,
          column: 0
        },
        end: {
          line: 150,
          column: 7
        }
      },
      "98": {
        start: {
          line: 82,
          column: 99
        },
        end: {
          line: 150,
          column: 3
        }
      },
      "99": {
        start: {
          line: 83,
          column: 17
        },
        end: {
          line: 83,
          column: 26
        }
      },
      "100": {
        start: {
          line: 84,
          column: 4
        },
        end: {
          line: 149,
          column: 7
        }
      },
      "101": {
        start: {
          line: 85,
          column: 8
        },
        end: {
          line: 148,
          column: 20
        }
      },
      "102": {
        start: {
          line: 88,
          column: 29
        },
        end: {
          line: 148,
          column: 15
        }
      },
      "103": {
        start: {
          line: 91,
          column: 16
        },
        end: {
          line: 147,
          column: 19
        }
      },
      "104": {
        start: {
          line: 92,
          column: 20
        },
        end: {
          line: 146,
          column: 21
        }
      },
      "105": {
        start: {
          line: 93,
          column: 32
        },
        end: {
          line: 93,
          column: 108
        }
      },
      "106": {
        start: {
          line: 95,
          column: 28
        },
        end: {
          line: 95,
          column: 48
        }
      },
      "107": {
        start: {
          line: 96,
          column: 28
        },
        end: {
          line: 98,
          column: 29
        }
      },
      "108": {
        start: {
          line: 97,
          column: 32
        },
        end: {
          line: 97,
          column: 153
        }
      },
      "109": {
        start: {
          line: 99,
          column: 28
        },
        end: {
          line: 99,
          column: 53
        }
      },
      "110": {
        start: {
          line: 100,
          column: 28
        },
        end: {
          line: 100,
          column: 57
        }
      },
      "111": {
        start: {
          line: 102,
          column: 28
        },
        end: {
          line: 102,
          column: 62
        }
      },
      "112": {
        start: {
          line: 103,
          column: 28
        },
        end: {
          line: 108,
          column: 36
        }
      },
      "113": {
        start: {
          line: 110,
          column: 28
        },
        end: {
          line: 110,
          column: 57
        }
      },
      "114": {
        start: {
          line: 111,
          column: 28
        },
        end: {
          line: 113,
          column: 29
        }
      },
      "115": {
        start: {
          line: 112,
          column: 32
        },
        end: {
          line: 112,
          column: 157
        }
      },
      "116": {
        start: {
          line: 114,
          column: 28
        },
        end: {
          line: 139,
          column: 36
        }
      },
      "117": {
        start: {
          line: 141,
          column: 28
        },
        end: {
          line: 141,
          column: 50
        }
      },
      "118": {
        start: {
          line: 142,
          column: 28
        },
        end: {
          line: 145,
          column: 36
        }
      },
      "119": {
        start: {
          line: 152,
          column: 0
        },
        end: {
          line: 365,
          column: 7
        }
      },
      "120": {
        start: {
          line: 152,
          column: 100
        },
        end: {
          line: 365,
          column: 3
        }
      },
      "121": {
        start: {
          line: 153,
          column: 17
        },
        end: {
          line: 153,
          column: 26
        }
      },
      "122": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 364,
          column: 7
        }
      },
      "123": {
        start: {
          line: 155,
          column: 8
        },
        end: {
          line: 363,
          column: 20
        }
      },
      "124": {
        start: {
          line: 158,
          column: 29
        },
        end: {
          line: 363,
          column: 15
        }
      },
      "125": {
        start: {
          line: 161,
          column: 16
        },
        end: {
          line: 362,
          column: 19
        }
      },
      "126": {
        start: {
          line: 162,
          column: 20
        },
        end: {
          line: 361,
          column: 21
        }
      },
      "127": {
        start: {
          line: 163,
          column: 32
        },
        end: {
          line: 163,
          column: 108
        }
      },
      "128": {
        start: {
          line: 165,
          column: 28
        },
        end: {
          line: 165,
          column: 48
        }
      },
      "129": {
        start: {
          line: 166,
          column: 28
        },
        end: {
          line: 168,
          column: 29
        }
      },
      "130": {
        start: {
          line: 167,
          column: 32
        },
        end: {
          line: 167,
          column: 153
        }
      },
      "131": {
        start: {
          line: 169,
          column: 28
        },
        end: {
          line: 169,
          column: 53
        }
      },
      "132": {
        start: {
          line: 170,
          column: 28
        },
        end: {
          line: 170,
          column: 57
        }
      },
      "133": {
        start: {
          line: 172,
          column: 28
        },
        end: {
          line: 172,
          column: 62
        }
      },
      "134": {
        start: {
          line: 173,
          column: 28
        },
        end: {
          line: 173,
          column: 65
        }
      },
      "135": {
        start: {
          line: 175,
          column: 28
        },
        end: {
          line: 175,
          column: 45
        }
      },
      "136": {
        start: {
          line: 176,
          column: 28
        },
        end: {
          line: 176,
          column: 78
        }
      },
      "137": {
        start: {
          line: 177,
          column: 28
        },
        end: {
          line: 183,
          column: 29
        }
      },
      "138": {
        start: {
          line: 178,
          column: 32
        },
        end: {
          line: 182,
          column: 57
        }
      },
      "139": {
        start: {
          line: 184,
          column: 28
        },
        end: {
          line: 184,
          column: 59
        }
      },
      "140": {
        start: {
          line: 185,
          column: 28
        },
        end: {
          line: 190,
          column: 31
        }
      },
      "141": {
        start: {
          line: 191,
          column: 28
        },
        end: {
          line: 198,
          column: 29
        }
      },
      "142": {
        start: {
          line: 192,
          column: 32
        },
        end: {
          line: 197,
          column: 57
        }
      },
      "143": {
        start: {
          line: 200,
          column: 28
        },
        end: {
          line: 207,
          column: 29
        }
      },
      "144": {
        start: {
          line: 201,
          column: 32
        },
        end: {
          line: 206,
          column: 35
        }
      },
      "145": {
        start: {
          line: 208,
          column: 28
        },
        end: {
          line: 209,
          column: 32
        }
      },
      "146": {
        start: {
          line: 210,
          column: 28
        },
        end: {
          line: 223,
          column: 36
        }
      },
      "147": {
        start: {
          line: 225,
          column: 28
        },
        end: {
          line: 225,
          column: 87
        }
      },
      "148": {
        start: {
          line: 226,
          column: 28
        },
        end: {
          line: 228,
          column: 29
        }
      },
      "149": {
        start: {
          line: 227,
          column: 32
        },
        end: {
          line: 227,
          column: 157
        }
      },
      "150": {
        start: {
          line: 229,
          column: 28
        },
        end: {
          line: 231,
          column: 29
        }
      },
      "151": {
        start: {
          line: 230,
          column: 32
        },
        end: {
          line: 230,
          column: 148
        }
      },
      "152": {
        start: {
          line: 232,
          column: 28
        },
        end: {
          line: 237,
          column: 36
        }
      },
      "153": {
        start: {
          line: 239,
          column: 28
        },
        end: {
          line: 239,
          column: 57
        }
      },
      "154": {
        start: {
          line: 240,
          column: 28
        },
        end: {
          line: 242,
          column: 29
        }
      },
      "155": {
        start: {
          line: 241,
          column: 32
        },
        end: {
          line: 241,
          column: 171
        }
      },
      "156": {
        start: {
          line: 243,
          column: 28
        },
        end: {
          line: 268,
          column: 36
        }
      },
      "157": {
        start: {
          line: 270,
          column: 28
        },
        end: {
          line: 270,
          column: 56
        }
      },
      "158": {
        start: {
          line: 271,
          column: 28
        },
        end: {
          line: 271,
          column: 88
        }
      },
      "159": {
        start: {
          line: 271,
          column: 63
        },
        end: {
          line: 271,
          column: 88
        }
      },
      "160": {
        start: {
          line: 272,
          column: 28
        },
        end: {
          line: 272,
          column: 41
        }
      },
      "161": {
        start: {
          line: 274,
          column: 28
        },
        end: {
          line: 274,
          column: 56
        }
      },
      "162": {
        start: {
          line: 275,
          column: 28
        },
        end: {
          line: 279,
          column: 36
        }
      },
      "163": {
        start: {
          line: 281,
          column: 28
        },
        end: {
          line: 281,
          column: 49
        }
      },
      "164": {
        start: {
          line: 282,
          column: 28
        },
        end: {
          line: 282,
          column: 95
        }
      },
      "165": {
        start: {
          line: 282,
          column: 70
        },
        end: {
          line: 282,
          column: 95
        }
      },
      "166": {
        start: {
          line: 283,
          column: 28
        },
        end: {
          line: 287,
          column: 31
        }
      },
      "167": {
        start: {
          line: 288,
          column: 28
        },
        end: {
          line: 298,
          column: 29
        }
      },
      "168": {
        start: {
          line: 289,
          column: 32
        },
        end: {
          line: 297,
          column: 35
        }
      },
      "169": {
        start: {
          line: 299,
          column: 28
        },
        end: {
          line: 324,
          column: 36
        }
      },
      "170": {
        start: {
          line: 327,
          column: 28
        },
        end: {
          line: 327,
          column: 56
        }
      },
      "171": {
        start: {
          line: 328,
          column: 28
        },
        end: {
          line: 328,
          column: 181
        }
      },
      "172": {
        start: {
          line: 329,
          column: 28
        },
        end: {
          line: 329,
          column: 53
        }
      },
      "173": {
        start: {
          line: 331,
          column: 28
        },
        end: {
          line: 331,
          column: 80
        }
      },
      "174": {
        start: {
          line: 332,
          column: 28
        },
        end: {
          line: 332,
          column: 42
        }
      },
      "175": {
        start: {
          line: 333,
          column: 33
        },
        end: {
          line: 333,
          column: 58
        }
      },
      "176": {
        start: {
          line: 335,
          column: 28
        },
        end: {
          line: 335,
          column: 56
        }
      },
      "177": {
        start: {
          line: 336,
          column: 28
        },
        end: {
          line: 336,
          column: 92
        }
      },
      "178": {
        start: {
          line: 337,
          column: 28
        },
        end: {
          line: 337,
          column: 53
        }
      },
      "179": {
        start: {
          line: 338,
          column: 33
        },
        end: {
          line: 344,
          column: 32
        }
      },
      "180": {
        start: {
          line: 346,
          column: 28
        },
        end: {
          line: 346,
          column: 59
        }
      },
      "181": {
        start: {
          line: 347,
          column: 28
        },
        end: {
          line: 353,
          column: 36
        }
      },
      "182": {
        start: {
          line: 355,
          column: 28
        },
        end: {
          line: 355,
          column: 38
        }
      },
      "183": {
        start: {
          line: 356,
          column: 28
        },
        end: {
          line: 360,
          column: 36
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 64,
            column: 69
          },
          end: {
            line: 64,
            column: 70
          }
        },
        loc: {
          start: {
            line: 64,
            column: 84
          },
          end: {
            line: 64,
            column: 112
          }
        },
        line: 64
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 65,
            column: 69
          },
          end: {
            line: 65,
            column: 70
          }
        },
        loc: {
          start: {
            line: 65,
            column: 84
          },
          end: {
            line: 65,
            column: 112
          }
        },
        line: 65
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 68,
            column: 74
          },
          end: {
            line: 68,
            column: 75
          }
        },
        loc: {
          start: {
            line: 68,
            column: 89
          },
          end: {
            line: 68,
            column: 117
          }
        },
        line: 68
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 74,
            column: 69
          },
          end: {
            line: 74,
            column: 70
          }
        },
        loc: {
          start: {
            line: 74,
            column: 84
          },
          end: {
            line: 74,
            column: 112
          }
        },
        line: 74
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 75,
            column: 69
          },
          end: {
            line: 75,
            column: 70
          }
        },
        loc: {
          start: {
            line: 75,
            column: 84
          },
          end: {
            line: 75,
            column: 112
          }
        },
        line: 75
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 78,
            column: 74
          },
          end: {
            line: 78,
            column: 75
          }
        },
        loc: {
          start: {
            line: 78,
            column: 89
          },
          end: {
            line: 78,
            column: 117
          }
        },
        line: 78
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 82,
            column: 72
          },
          end: {
            line: 82,
            column: 73
          }
        },
        loc: {
          start: {
            line: 82,
            column: 97
          },
          end: {
            line: 150,
            column: 5
          }
        },
        line: 82
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 82,
            column: 149
          },
          end: {
            line: 82,
            column: 150
          }
        },
        loc: {
          start: {
            line: 82,
            column: 172
          },
          end: {
            line: 150,
            column: 1
          }
        },
        line: 82
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 84,
            column: 29
          },
          end: {
            line: 84,
            column: 30
          }
        },
        loc: {
          start: {
            line: 84,
            column: 43
          },
          end: {
            line: 149,
            column: 5
          }
        },
        line: 84
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 88,
            column: 15
          },
          end: {
            line: 88,
            column: 16
          }
        },
        loc: {
          start: {
            line: 88,
            column: 27
          },
          end: {
            line: 148,
            column: 17
          }
        },
        line: 88
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 88,
            column: 70
          },
          end: {
            line: 88,
            column: 71
          }
        },
        loc: {
          start: {
            line: 88,
            column: 82
          },
          end: {
            line: 148,
            column: 13
          }
        },
        line: 88
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 91,
            column: 41
          },
          end: {
            line: 91,
            column: 42
          }
        },
        loc: {
          start: {
            line: 91,
            column: 55
          },
          end: {
            line: 147,
            column: 17
          }
        },
        line: 91
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 152,
            column: 73
          },
          end: {
            line: 152,
            column: 74
          }
        },
        loc: {
          start: {
            line: 152,
            column: 98
          },
          end: {
            line: 365,
            column: 5
          }
        },
        line: 152
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 152,
            column: 150
          },
          end: {
            line: 152,
            column: 151
          }
        },
        loc: {
          start: {
            line: 152,
            column: 173
          },
          end: {
            line: 365,
            column: 1
          }
        },
        line: 152
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 154,
            column: 29
          },
          end: {
            line: 154,
            column: 30
          }
        },
        loc: {
          start: {
            line: 154,
            column: 43
          },
          end: {
            line: 364,
            column: 5
          }
        },
        line: 154
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 158,
            column: 15
          },
          end: {
            line: 158,
            column: 16
          }
        },
        loc: {
          start: {
            line: 158,
            column: 27
          },
          end: {
            line: 363,
            column: 17
          }
        },
        line: 158
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 158,
            column: 70
          },
          end: {
            line: 158,
            column: 71
          }
        },
        loc: {
          start: {
            line: 158,
            column: 82
          },
          end: {
            line: 363,
            column: 13
          }
        },
        line: 158
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 161,
            column: 41
          },
          end: {
            line: 161,
            column: 42
          }
        },
        loc: {
          start: {
            line: 161,
            column: 55
          },
          end: {
            line: 362,
            column: 17
          }
        },
        line: 161
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 64,
            column: 93
          },
          end: {
            line: 64,
            column: 109
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 64,
            column: 93
          },
          end: {
            line: 64,
            column: 96
          }
        }, {
          start: {
            line: 64,
            column: 100
          },
          end: {
            line: 64,
            column: 109
          }
        }],
        line: 64
      },
      "36": {
        loc: {
          start: {
            line: 65,
            column: 93
          },
          end: {
            line: 65,
            column: 109
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 65,
            column: 93
          },
          end: {
            line: 65,
            column: 96
          }
        }, {
          start: {
            line: 65,
            column: 100
          },
          end: {
            line: 65,
            column: 109
          }
        }],
        line: 65
      },
      "37": {
        loc: {
          start: {
            line: 68,
            column: 98
          },
          end: {
            line: 68,
            column: 114
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 68,
            column: 98
          },
          end: {
            line: 68,
            column: 101
          }
        }, {
          start: {
            line: 68,
            column: 105
          },
          end: {
            line: 68,
            column: 114
          }
        }],
        line: 68
      },
      "38": {
        loc: {
          start: {
            line: 74,
            column: 93
          },
          end: {
            line: 74,
            column: 109
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 74,
            column: 93
          },
          end: {
            line: 74,
            column: 96
          }
        }, {
          start: {
            line: 74,
            column: 100
          },
          end: {
            line: 74,
            column: 109
          }
        }],
        line: 74
      },
      "39": {
        loc: {
          start: {
            line: 75,
            column: 93
          },
          end: {
            line: 75,
            column: 109
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 75,
            column: 93
          },
          end: {
            line: 75,
            column: 96
          }
        }, {
          start: {
            line: 75,
            column: 100
          },
          end: {
            line: 75,
            column: 109
          }
        }],
        line: 75
      },
      "40": {
        loc: {
          start: {
            line: 78,
            column: 98
          },
          end: {
            line: 78,
            column: 114
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 78,
            column: 98
          },
          end: {
            line: 78,
            column: 101
          }
        }, {
          start: {
            line: 78,
            column: 105
          },
          end: {
            line: 78,
            column: 114
          }
        }],
        line: 78
      },
      "41": {
        loc: {
          start: {
            line: 87,
            column: 29
          },
          end: {
            line: 87,
            column: 78
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 87,
            column: 70
          },
          end: {
            line: 87,
            column: 73
          }
        }, {
          start: {
            line: 87,
            column: 76
          },
          end: {
            line: 87,
            column: 78
          }
        }],
        line: 87
      },
      "42": {
        loc: {
          start: {
            line: 92,
            column: 20
          },
          end: {
            line: 146,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 93,
            column: 24
          },
          end: {
            line: 93,
            column: 108
          }
        }, {
          start: {
            line: 94,
            column: 24
          },
          end: {
            line: 100,
            column: 57
          }
        }, {
          start: {
            line: 101,
            column: 24
          },
          end: {
            line: 108,
            column: 36
          }
        }, {
          start: {
            line: 109,
            column: 24
          },
          end: {
            line: 139,
            column: 36
          }
        }, {
          start: {
            line: 140,
            column: 24
          },
          end: {
            line: 145,
            column: 36
          }
        }],
        line: 92
      },
      "43": {
        loc: {
          start: {
            line: 96,
            column: 28
          },
          end: {
            line: 98,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 96,
            column: 28
          },
          end: {
            line: 98,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 96
      },
      "44": {
        loc: {
          start: {
            line: 96,
            column: 34
          },
          end: {
            line: 96,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 96,
            column: 132
          },
          end: {
            line: 96,
            column: 138
          }
        }, {
          start: {
            line: 96,
            column: 141
          },
          end: {
            line: 96,
            column: 146
          }
        }],
        line: 96
      },
      "45": {
        loc: {
          start: {
            line: 96,
            column: 34
          },
          end: {
            line: 96,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 96,
            column: 34
          },
          end: {
            line: 96,
            column: 112
          }
        }, {
          start: {
            line: 96,
            column: 116
          },
          end: {
            line: 96,
            column: 129
          }
        }],
        line: 96
      },
      "46": {
        loc: {
          start: {
            line: 96,
            column: 40
          },
          end: {
            line: 96,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 96,
            column: 81
          },
          end: {
            line: 96,
            column: 87
          }
        }, {
          start: {
            line: 96,
            column: 90
          },
          end: {
            line: 96,
            column: 102
          }
        }],
        line: 96
      },
      "47": {
        loc: {
          start: {
            line: 96,
            column: 40
          },
          end: {
            line: 96,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 96,
            column: 40
          },
          end: {
            line: 96,
            column: 56
          }
        }, {
          start: {
            line: 96,
            column: 60
          },
          end: {
            line: 96,
            column: 78
          }
        }],
        line: 96
      },
      "48": {
        loc: {
          start: {
            line: 111,
            column: 28
          },
          end: {
            line: 113,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 111,
            column: 28
          },
          end: {
            line: 113,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 111
      },
      "49": {
        loc: {
          start: {
            line: 157,
            column: 29
          },
          end: {
            line: 157,
            column: 78
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 157,
            column: 70
          },
          end: {
            line: 157,
            column: 73
          }
        }, {
          start: {
            line: 157,
            column: 76
          },
          end: {
            line: 157,
            column: 78
          }
        }],
        line: 157
      },
      "50": {
        loc: {
          start: {
            line: 162,
            column: 20
          },
          end: {
            line: 361,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 163,
            column: 24
          },
          end: {
            line: 163,
            column: 108
          }
        }, {
          start: {
            line: 164,
            column: 24
          },
          end: {
            line: 170,
            column: 57
          }
        }, {
          start: {
            line: 171,
            column: 24
          },
          end: {
            line: 173,
            column: 65
          }
        }, {
          start: {
            line: 174,
            column: 24
          },
          end: {
            line: 223,
            column: 36
          }
        }, {
          start: {
            line: 224,
            column: 24
          },
          end: {
            line: 237,
            column: 36
          }
        }, {
          start: {
            line: 238,
            column: 24
          },
          end: {
            line: 268,
            column: 36
          }
        }, {
          start: {
            line: 269,
            column: 24
          },
          end: {
            line: 272,
            column: 41
          }
        }, {
          start: {
            line: 273,
            column: 24
          },
          end: {
            line: 279,
            column: 36
          }
        }, {
          start: {
            line: 280,
            column: 24
          },
          end: {
            line: 324,
            column: 36
          }
        }, {
          start: {
            line: 325,
            column: 24
          },
          end: {
            line: 329,
            column: 53
          }
        }, {
          start: {
            line: 330,
            column: 24
          },
          end: {
            line: 332,
            column: 42
          }
        }, {
          start: {
            line: 333,
            column: 24
          },
          end: {
            line: 333,
            column: 58
          }
        }, {
          start: {
            line: 334,
            column: 24
          },
          end: {
            line: 337,
            column: 53
          }
        }, {
          start: {
            line: 338,
            column: 24
          },
          end: {
            line: 344,
            column: 32
          }
        }, {
          start: {
            line: 345,
            column: 24
          },
          end: {
            line: 353,
            column: 36
          }
        }, {
          start: {
            line: 354,
            column: 24
          },
          end: {
            line: 360,
            column: 36
          }
        }],
        line: 162
      },
      "51": {
        loc: {
          start: {
            line: 166,
            column: 28
          },
          end: {
            line: 168,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 166,
            column: 28
          },
          end: {
            line: 168,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 166
      },
      "52": {
        loc: {
          start: {
            line: 166,
            column: 34
          },
          end: {
            line: 166,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 166,
            column: 132
          },
          end: {
            line: 166,
            column: 138
          }
        }, {
          start: {
            line: 166,
            column: 141
          },
          end: {
            line: 166,
            column: 146
          }
        }],
        line: 166
      },
      "53": {
        loc: {
          start: {
            line: 166,
            column: 34
          },
          end: {
            line: 166,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 166,
            column: 34
          },
          end: {
            line: 166,
            column: 112
          }
        }, {
          start: {
            line: 166,
            column: 116
          },
          end: {
            line: 166,
            column: 129
          }
        }],
        line: 166
      },
      "54": {
        loc: {
          start: {
            line: 166,
            column: 40
          },
          end: {
            line: 166,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 166,
            column: 81
          },
          end: {
            line: 166,
            column: 87
          }
        }, {
          start: {
            line: 166,
            column: 90
          },
          end: {
            line: 166,
            column: 102
          }
        }],
        line: 166
      },
      "55": {
        loc: {
          start: {
            line: 166,
            column: 40
          },
          end: {
            line: 166,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 166,
            column: 40
          },
          end: {
            line: 166,
            column: 56
          }
        }, {
          start: {
            line: 166,
            column: 60
          },
          end: {
            line: 166,
            column: 78
          }
        }],
        line: 166
      },
      "56": {
        loc: {
          start: {
            line: 177,
            column: 28
          },
          end: {
            line: 183,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 177,
            column: 28
          },
          end: {
            line: 183,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 177
      },
      "57": {
        loc: {
          start: {
            line: 186,
            column: 46
          },
          end: {
            line: 186,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 186,
            column: 46
          },
          end: {
            line: 186,
            column: 71
          }
        }, {
          start: {
            line: 186,
            column: 75
          },
          end: {
            line: 186,
            column: 77
          }
        }],
        line: 186
      },
      "58": {
        loc: {
          start: {
            line: 191,
            column: 28
          },
          end: {
            line: 198,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 191,
            column: 28
          },
          end: {
            line: 198,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 191
      },
      "59": {
        loc: {
          start: {
            line: 200,
            column: 28
          },
          end: {
            line: 207,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 200,
            column: 28
          },
          end: {
            line: 207,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 200
      },
      "60": {
        loc: {
          start: {
            line: 200,
            column: 32
          },
          end: {
            line: 200,
            column: 109
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 200,
            column: 32
          },
          end: {
            line: 200,
            column: 63
          }
        }, {
          start: {
            line: 200,
            column: 67
          },
          end: {
            line: 200,
            column: 109
          }
        }],
        line: 200
      },
      "61": {
        loc: {
          start: {
            line: 226,
            column: 28
          },
          end: {
            line: 228,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 226,
            column: 28
          },
          end: {
            line: 228,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 226
      },
      "62": {
        loc: {
          start: {
            line: 229,
            column: 28
          },
          end: {
            line: 231,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 229,
            column: 28
          },
          end: {
            line: 231,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 229
      },
      "63": {
        loc: {
          start: {
            line: 240,
            column: 28
          },
          end: {
            line: 242,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 240,
            column: 28
          },
          end: {
            line: 242,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 240
      },
      "64": {
        loc: {
          start: {
            line: 271,
            column: 28
          },
          end: {
            line: 271,
            column: 88
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 271,
            column: 28
          },
          end: {
            line: 271,
            column: 88
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 271
      },
      "65": {
        loc: {
          start: {
            line: 282,
            column: 28
          },
          end: {
            line: 282,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 282,
            column: 28
          },
          end: {
            line: 282,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 282
      },
      "66": {
        loc: {
          start: {
            line: 282,
            column: 34
          },
          end: {
            line: 282,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 282,
            column: 34
          },
          end: {
            line: 282,
            column: 50
          }
        }, {
          start: {
            line: 282,
            column: 54
          },
          end: {
            line: 282,
            column: 67
          }
        }],
        line: 282
      },
      "67": {
        loc: {
          start: {
            line: 288,
            column: 28
          },
          end: {
            line: 298,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 288,
            column: 28
          },
          end: {
            line: 298,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 288
      },
      "68": {
        loc: {
          start: {
            line: 302,
            column: 49
          },
          end: {
            line: 302,
            column: 111
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 302,
            column: 75
          },
          end: {
            line: 302,
            column: 104
          }
        }, {
          start: {
            line: 302,
            column: 107
          },
          end: {
            line: 302,
            column: 111
          }
        }],
        line: 302
      },
      "69": {
        loc: {
          start: {
            line: 307,
            column: 57
          },
          end: {
            line: 307,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 307,
            column: 57
          },
          end: {
            line: 307,
            column: 86
          }
        }, {
          start: {
            line: 307,
            column: 90
          },
          end: {
            line: 307,
            column: 94
          }
        }],
        line: 307
      },
      "70": {
        loc: {
          start: {
            line: 308,
            column: 57
          },
          end: {
            line: 308,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 308,
            column: 57
          },
          end: {
            line: 308,
            column: 86
          }
        }, {
          start: {
            line: 308,
            column: 90
          },
          end: {
            line: 308,
            column: 94
          }
        }],
        line: 308
      },
      "71": {
        loc: {
          start: {
            line: 309,
            column: 60
          },
          end: {
            line: 309,
            column: 100
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 309,
            column: 60
          },
          end: {
            line: 309,
            column: 92
          }
        }, {
          start: {
            line: 309,
            column: 96
          },
          end: {
            line: 309,
            column: 100
          }
        }],
        line: 309
      },
      "72": {
        loc: {
          start: {
            line: 310,
            column: 56
          },
          end: {
            line: 310,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 310,
            column: 56
          },
          end: {
            line: 310,
            column: 84
          }
        }, {
          start: {
            line: 310,
            column: 88
          },
          end: {
            line: 310,
            column: 92
          }
        }],
        line: 310
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0, 0, 0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/interview-practice/[sessionId]/responses/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAA6C;AAC7C,mCAAyC;AACzC,uCAAsC;AACtC,6EAA2E;AAC3E,6CAAgD;AAGhD,yEAAqE;AACrE,+EAA4E;AAC5E,2BAAwB;AAGxB,6CAA6C;AAC7C,IAAM,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IACpC,UAAU,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE;IAC7B,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,yCAAyC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,mBAAmB,CAAC;IAC1G,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,IAAI,SAAS,EAAhB,CAAgB,CAAC;IACnF,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,IAAI,SAAS,EAAhB,CAAgB,CAAC;IACnF,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,aAAa;IACxD,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,iBAAiB;IAC1E,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,IAAI,SAAS,EAAhB,CAAgB,CAAC;IACxF,eAAe,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;CAC3C,CAAC,CAAC;AAEH,2CAA2C;AAC3C,IAAM,oBAAoB,GAAG,OAAC,CAAC,MAAM,CAAC;IACpC,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;IACrD,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,IAAI,SAAS,EAAhB,CAAgB,CAAC;IACnF,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,IAAI,SAAS,EAAhB,CAAgB,CAAC;IACnF,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;IACpD,eAAe,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;IACvD,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,IAAI,SAAS,EAAhB,CAAgB,CAAC;IACxF,WAAW,EAAE,OAAC,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;CACpC,CAAC,CAAC;AAEH,yCAAyC;AAC5B,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,uFAC1C,OAAoB,EACpB,EAAsD;QAApD,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP;gBACE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;gBACxB,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,+BAA+B;aAC/F,EACD;;;;;gCACkB,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BACnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;gCACvB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,EACpD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAEK,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;4BACT,qBAAM,MAAM,EAAA;;4BAA1B,SAAS,GAAK,CAAA,SAAY,CAAA,UAAjB;4BAGQ,qBAAM,eAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC;oCAC/D,KAAK,EAAE;wCACL,EAAE,EAAE,SAAS;wCACb,MAAM,QAAA;qCACP;iCACF,CAAC,EAAA;;4BALI,gBAAgB,GAAG,SAKvB;4BAEF,IAAI,CAAC,gBAAgB,EAAE,CAAC;gCACtB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,6BAA6B,EAAE,EACxD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAGiB,qBAAM,eAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;oCACxD,KAAK,EAAE;wCACL,SAAS,WAAA;wCACT,MAAM,QAAA;qCACP;oCACD,OAAO,EAAE;wCACP,QAAQ,EAAE;4CACR,MAAM,EAAE;gDACN,EAAE,EAAE,IAAI;gDACR,YAAY,EAAE,IAAI;gDAClB,YAAY,EAAE,IAAI;gDAClB,QAAQ,EAAE,IAAI;gDACd,UAAU,EAAE,IAAI;gDAChB,gBAAgB,EAAE,IAAI;gDACtB,OAAO,EAAE,IAAI;gDACb,KAAK,EAAE,IAAI;gDACX,aAAa,EAAE,IAAI;6CACpB;yCACF;qCACF;oCACD,OAAO,EAAE;wCACP,QAAQ,EAAE;4CACR,aAAa,EAAE,KAAK;yCACrB;qCACF;iCACF,CAAC,EAAA;;4BAzBI,SAAS,GAAG,SAyBhB;4BAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,SAAS;iCAChB,CAAC,EAAC;;;iBACJ,CACF,EAAC;;KACH,CAAC,CAAC;AAEH,+BAA+B;AAClB,QAAA,IAAI,GAAG,IAAA,oDAAwB,EAAC,uFAC3C,OAAoB,EACpB,EAAsD;QAApD,MAAM,YAAA;;QAER,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP;gBACE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;gBACxB,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,+BAA+B;aAC/F,EACD;;;;;gCACkB,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BACnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;gCACvB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,yBAAyB,EAAE,EACpD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAEK,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;4BACT,qBAAM,MAAM,EAAA;;4BAA1B,SAAS,GAAK,CAAA,SAAY,CAAA,UAAjB;4BAEJ,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;4BAA3B,IAAI,GAAG,SAAoB;4BACzB,UAAU,GAAG,oBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;4BAExD,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;gCACxB,sBAAO,qBAAY,CAAC,IAAI,CACtB;wCACE,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,sBAAsB;wCAC7B,OAAO,EAAE,UAAU,CAAC,KAAK,CAAC,MAAM;qCACjC,EACD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAEK,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC;4BAG/B,iBAAiB,GAAG,qDAAwB,CAAC,yBAAyB,CAAC;gCAC3E,YAAY,EAAE,YAAY,CAAC,YAAY,IAAI,EAAE;gCAC7C,SAAS,EAAE,YAAY,CAAC,SAAS;gCACjC,YAAY,EAAE,YAAY,CAAC,YAAY;gCACvC,eAAe,EAAE,YAAY,CAAC,eAAe;6BAC9C,CAAC,CAAC;4BAEH,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAC;gCAC/B,sBAAO,qBAAY,CAAC,IAAI,CACtB;wCACE,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,4BAA4B;wCACnC,OAAO,EAAE,iBAAiB,CAAC,MAAM;wCACjC,aAAa,EAAE,iBAAiB,CAAC,aAAa;qCAC/C,EACD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAED,oCAAoC;4BACpC,IAAI,iBAAiB,CAAC,aAAa,IAAI,iBAAiB,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gCAClF,OAAO,CAAC,IAAI,CAAC,iDAAiD,EAAE;oCAC9D,MAAM,QAAA;oCACN,SAAS,WAAA;oCACT,UAAU,EAAE,YAAY,CAAC,UAAU;oCACnC,KAAK,EAAE,iBAAiB,CAAC,aAAa;iCACvC,CAAC,CAAC;4BACL,CAAC;4BAGK,aAAa,yBACd,iBAAiB,CAAC,aAAc,KACnC,UAAU,EAAE,YAAY,CAAC,UAAU,CAAC,oCAAoC;+BACzE,CAAC;4BAGmC,qBAAM,OAAO,CAAC,GAAG,CAAC;oCACrD,eAAM,CAAC,gBAAgB,CAAC,SAAS,CAAC;wCAChC,KAAK,EAAE;4CACL,EAAE,EAAE,SAAS;4CACb,MAAM,QAAA;yCACP;qCACF,CAAC;oCACF,eAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC;wCACjC,KAAK,EAAE;4CACL,EAAE,EAAE,aAAa,CAAC,UAAU;4CAC5B,SAAS,WAAA;yCACV;qCACF,CAAC;iCACH,CAAC,EAAA;;4BAbI,KAA+B,SAanC,EAbK,gBAAgB,QAAA,EAAE,QAAQ,QAAA;4BAejC,IAAI,CAAC,gBAAgB,EAAE,CAAC;gCACtB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,6BAA6B,EAAE,EACxD,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAED,IAAI,CAAC,QAAQ,EAAE,CAAC;gCACd,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,oBAAoB,EAAE,EAC/C,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAGwB,qBAAM,eAAM,CAAC,iBAAiB,CAAC,SAAS,CAAC;oCAChE,KAAK,EAAE;wCACL,MAAM,QAAA;wCACN,UAAU,EAAE,aAAa,CAAC,UAAU;qCACrC;iCACF,CAAC,EAAA;;4BALI,gBAAgB,GAAG,SAKvB;4BAEF,IAAI,gBAAgB,EAAE,CAAC;gCACrB,sBAAO,qBAAY,CAAC,IAAI,CACtB,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2CAA2C,EAAE,EACtE,EAAE,MAAM,EAAE,GAAG,EAAE,CAChB,EAAC;4BACJ,CAAC;4BAGqB,qBAAM,eAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;oCAC1D,IAAI,EAAE;wCACJ,MAAM,QAAA;wCACN,SAAS,WAAA;wCACT,UAAU,EAAE,aAAa,CAAC,UAAU;wCACpC,YAAY,EAAE,aAAa,CAAC,YAAY;wCACxC,QAAQ,EAAE,YAAY,CAAC,QAAQ,EAAE,mCAAmC;wCACpE,QAAQ,EAAE,YAAY,CAAC,QAAQ;wCAC/B,YAAY,EAAE,aAAa,CAAC,YAAY;wCACxC,eAAe,EAAE,aAAa,CAAC,eAAe;wCAC9C,SAAS,EAAE,aAAa,CAAC,SAAS;wCAClC,WAAW,EAAE,IAAI;qCAClB;oCACD,OAAO,EAAE;wCACP,QAAQ,EAAE;4CACR,MAAM,EAAE;gDACN,YAAY,EAAE,IAAI;gDAClB,YAAY,EAAE,IAAI;gDAClB,QAAQ,EAAE,IAAI;gDACd,UAAU,EAAE,IAAI;gDAChB,OAAO,EAAE,IAAI;gDACb,KAAK,EAAE,IAAI;6CACZ;yCACF;qCACF;iCACF,CAAC,EAAA;;4BAzBE,eAAe,GAAG,SAyBpB;iCAGE,YAAY,CAAC,eAAe,EAA5B,yBAA4B;;;;4BAEX,qBAAM,8CAAoB,CAAC,wBAAwB,CAClE,QAAQ,CAAC,YAAY,EACrB,aAAa,CAAC,YAAY,EAC1B;oCACE,OAAO,EAAE,KAAK,EAAE,mCAAmC;oCACnD,UAAU,EAAE,CAAC;oCACb,gBAAgB,EAAE,IAAI;iCACvB,CACF,EAAA;;4BARK,QAAQ,GAAG,SAQhB;iCAEG,CAAA,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAA,EAAjC,yBAAiC;4BAE7B,eAAe,GAAG,qDAAwB,CAAC,eAAe,CAC9D,QAAQ,CAAC,IAAI,CAAC,OAAO,EACrB;gCACE,cAAc,EAAE,aAAa,CAAC,YAAY,CAAC,MAAM;gCACjD,YAAY,EAAE,aAAa,CAAC,YAAY;gCACxC,YAAY,EAAE,QAAQ,CAAC,YAAY;6BACpC,CACF,CAAC;4BAEF,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;gCAC7B,OAAO,CAAC,IAAI,CAAC,6BAA6B,EAAE;oCAC1C,MAAM,QAAA;oCACN,SAAS,WAAA;oCACT,UAAU,EAAE,aAAa,CAAC,UAAU;oCACpC,KAAK,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO;oCAC5B,MAAM,EAAE,eAAe,CAAC,MAAM;oCAC9B,KAAK,EAAE,eAAe,CAAC,aAAa;oCACpC,MAAM,EAAE,QAAQ,CAAC,MAAM;iCACxB,CAAC,CAAC;4BACL,CAAC;4BAGiB,qBAAM,eAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;oCACtD,KAAK,EAAE,EAAE,EAAE,EAAE,eAAe,CAAC,EAAE,EAAE;oCACjC,IAAI,EAAE;wCACJ,OAAO,EAAE,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI;wCACvE,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,UAAU;wCACpC,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;wCAChC,SAAS,EAAE,QAAQ,CAAC,IAAI,CAAC,SAAS;wCAClC,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,YAAY;wCACxC,eAAe,EAAE,QAAQ,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI;wCACtD,eAAe,EAAE,QAAQ,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI;wCACtD,kBAAkB,EAAE,QAAQ,CAAC,IAAI,CAAC,kBAAkB,IAAI,IAAI;wCAC5D,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI;qCACrD;oCACD,OAAO,EAAE;wCACP,QAAQ,EAAE;4CACR,MAAM,EAAE;gDACN,YAAY,EAAE,IAAI;gDAClB,YAAY,EAAE,IAAI;gDAClB,QAAQ,EAAE,IAAI;gDACd,UAAU,EAAE,IAAI;gDAChB,OAAO,EAAE,IAAI;gDACb,KAAK,EAAE,IAAI;6CACZ;yCACF;qCACF;iCACF,CAAC,EAAA;;4BA1BF,mCAAmC;4BACnC,eAAe,GAAG,SAyBhB,CAAC;4BAEH,OAAO,CAAC,GAAG,CAAC,oCAA6B,QAAQ,CAAC,MAAM,iBAAO,QAAQ,CAAC,YAAY,iBAAO,QAAQ,CAAC,UAAU,cAAW,CAAC,CAAC;;;4BAE3H,OAAO,CAAC,IAAI,CAAC,qBAAqB,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;;;;;4BAGtD,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,eAAa,CAAC,CAAC;;iCAMvC,qBAAM,eAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC;gCAC9D,KAAK,EAAE;oCACL,SAAS,WAAA;oCACT,MAAM,QAAA;oCACN,WAAW,EAAE,IAAI;iCAClB;6BACF,CAAC,EAAA;;4BANI,kBAAkB,GAAG,SAMzB;4BAEF,qBAAM,eAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC;oCACnC,KAAK,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE;oCACxB,IAAI,EAAE;wCACJ,kBAAkB,EAAE,kBAAkB;wCACtC,YAAY,EAAE,IAAI,IAAI,EAAE;qCACzB;iCACF,CAAC,EAAA;;4BANF,SAME,CAAC;4BAEH,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,eAAe;oCACrB,OAAO,EAAE,iCAAiC;iCAC3C,CAAC,EAAC;;;iBACJ,CACF,EAAC;;KACH,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/interview-practice/[sessionId]/responses/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { prisma } from '@/lib/prisma';\nimport { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';\nimport { withRateLimit } from '@/lib/rateLimit';\nimport BusinessLogicSecurity from '@/lib/business-logic-security';\nimport { UserValidationService } from '@/lib/user-validation-service';\nimport { SelfHealingAIService } from '@/lib/self-healing-ai-service';\nimport { UnifiedValidationService } from '@/lib/unified-validation-service';\nimport { z } from 'zod';\nimport { withCSRFProtection } from '@/lib/csrf';\n\n// Validation schema for submitting responses\nconst submitResponseSchema = z.object({\n  questionId: z.string().uuid(),\n  responseText: z.string().min(10, 'Response must be at least 10 characters').max(5000, 'Response too long'),\n  audioUrl: z.string().url().optional().nullable().transform(val => val || undefined),\n  videoUrl: z.string().url().optional().nullable().transform(val => val || undefined),\n  responseTime: z.number().min(0).max(3600), // Max 1 hour\n  preparationTime: z.number().min(0).max(1800).default(0), // Max 30 minutes\n  userNotes: z.string().max(1000).optional().nullable().transform(val => val || undefined),\n  requestFeedback: z.boolean().default(true),\n});\n\n// Validation schema for updating responses\nconst updateResponseSchema = z.object({\n  responseText: z.string().min(10).max(5000).optional(),\n  audioUrl: z.string().url().optional().nullable().transform(val => val || undefined),\n  videoUrl: z.string().url().optional().nullable().transform(val => val || undefined),\n  responseTime: z.number().min(0).max(3600).optional(),\n  preparationTime: z.number().min(0).max(1800).optional(),\n  userNotes: z.string().max(1000).optional().nullable().transform(val => val || undefined),\n  needsReview: z.boolean().optional(),\n});\n\n// GET - Retrieve responses for a session\nexport const GET = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ sessionId: string }> }\n) => {\n  return withRateLimit(\n    request,\n    {\n      windowMs: 15 * 60 * 1000,\n      maxRequests: process.env.NODE_ENV === 'development' ? 200 : 50 // Higher limit for development\n    },\n    async () => {\n      const session = await getServerSession(authOptions);\n      if (!session?.user?.id) {\n        return NextResponse.json(\n          { success: false, error: 'Authentication required' },\n          { status: 401 }\n        );\n      }\n\n      const userId = session.user.id;\n      const { sessionId } = await params;\n\n      // Verify session ownership\n      const interviewSession = await prisma.interviewSession.findFirst({\n        where: {\n          id: sessionId,\n          userId,\n        },\n      });\n\n      if (!interviewSession) {\n        return NextResponse.json(\n          { success: false, error: 'Interview session not found' },\n          { status: 404 }\n        );\n      }\n\n      // Get responses with question details\n      const responses = await prisma.interviewResponse.findMany({\n        where: {\n          sessionId,\n          userId,\n        },\n        include: {\n          question: {\n            select: {\n              id: true,\n              questionText: true,\n              questionType: true,\n              category: true,\n              difficulty: true,\n              expectedDuration: true,\n              context: true,\n              hints: true,\n              questionOrder: true,\n            },\n          },\n        },\n        orderBy: {\n          question: {\n            questionOrder: 'asc',\n          },\n        },\n      });\n\n      return NextResponse.json({\n        success: true,\n        data: responses,\n      });\n    }\n  );\n});\n\n// POST - Submit a new response\nexport const POST = withUnifiedErrorHandling(async (\n  request: NextRequest,\n  { params }: { params: Promise<{ sessionId: string }> }\n) => {\n  return withRateLimit(\n    request,\n    {\n      windowMs: 15 * 60 * 1000,\n      maxRequests: process.env.NODE_ENV === 'development' ? 150 : 30 // Higher limit for development\n    },\n    async () => {\n      const session = await getServerSession(authOptions);\n      if (!session?.user?.id) {\n        return NextResponse.json(\n          { success: false, error: 'Authentication required' },\n          { status: 401 }\n        );\n      }\n\n      const userId = session.user.id;\n      const { sessionId } = await params;\n\n      const body = await request.json();\n        const validation = submitResponseSchema.safeParse(body);\n        \n        if (!validation.success) {\n          return NextResponse.json(\n            { \n              success: false, \n              error: 'Invalid request data',\n              details: validation.error.errors \n            },\n            { status: 400 }\n          );\n        }\n\n        const responseData = validation.data;\n\n        // Apply unified validation service for comprehensive validation\n        const unifiedValidation = UnifiedValidationService.validateInterviewResponse({\n          responseText: responseData.responseText || '',\n          userNotes: responseData.userNotes,\n          responseTime: responseData.responseTime,\n          preparationTime: responseData.preparationTime,\n        });\n\n        if (!unifiedValidation.isValid) {\n          return NextResponse.json(\n            {\n              success: false,\n              error: 'Response validation failed',\n              details: unifiedValidation.errors,\n              securityFlags: unifiedValidation.securityFlags\n            },\n            { status: 400 }\n          );\n        }\n\n        // Log security flags for monitoring\n        if (unifiedValidation.securityFlags && unifiedValidation.securityFlags.length > 0) {\n          console.warn('Security flags detected in response submission:', {\n            userId,\n            sessionId,\n            questionId: responseData.questionId,\n            flags: unifiedValidation.securityFlags\n          });\n        }\n\n        // Use sanitized data\n        const sanitizedData = {\n          ...unifiedValidation.sanitizedData!,\n          questionId: responseData.questionId // Add questionId from original data\n        };\n\n        // Verify session ownership and question exists\n        const [interviewSession, question] = await Promise.all([\n          prisma.interviewSession.findFirst({\n            where: {\n              id: sessionId,\n              userId,\n            },\n          }),\n          prisma.interviewQuestion.findFirst({\n            where: {\n              id: sanitizedData.questionId,\n              sessionId,\n            },\n          }),\n        ]);\n\n        if (!interviewSession) {\n          return NextResponse.json(\n            { success: false, error: 'Interview session not found' },\n            { status: 404 }\n          );\n        }\n\n        if (!question) {\n          return NextResponse.json(\n            { success: false, error: 'Question not found' },\n            { status: 404 }\n          );\n        }\n\n        // Check if response already exists\n        const existingResponse = await prisma.interviewResponse.findFirst({\n          where: {\n            userId,\n            questionId: sanitizedData.questionId,\n          },\n        });\n\n        if (existingResponse) {\n          return NextResponse.json(\n            { success: false, error: 'Response already exists for this question' },\n            { status: 400 }\n          );\n        }\n\n        // Create the response using sanitized data\n        let createdResponse = await prisma.interviewResponse.create({\n          data: {\n            userId,\n            sessionId,\n            questionId: sanitizedData.questionId,\n            responseText: sanitizedData.responseText,\n            audioUrl: responseData.audioUrl, // URLs are validated by Zod schema\n            videoUrl: responseData.videoUrl,\n            responseTime: sanitizedData.responseTime,\n            preparationTime: sanitizedData.preparationTime,\n            userNotes: sanitizedData.userNotes,\n            isCompleted: true,\n          },\n          include: {\n            question: {\n              select: {\n                questionText: true,\n                questionType: true,\n                category: true,\n                difficulty: true,\n                context: true,\n                hints: true,\n              },\n            },\n          },\n        });\n\n        // Generate AI feedback if requested using self-healing service\n        if (responseData.requestFeedback) {\n          try {\n            const aiResult = await SelfHealingAIService.analyzeInterviewResponse(\n              question.questionText,\n              sanitizedData.responseText,\n              {\n                timeout: 20000, // 20 seconds for response analysis\n                maxRetries: 2,\n                fallbackToStatic: true\n              }\n            );\n\n            if (aiResult.success && aiResult.data) {\n              // Validate AI score before storing using unified validation service\n              const scoreValidation = UnifiedValidationService.validateAIScore(\n                aiResult.data.aiScore,\n                {\n                  responseLength: sanitizedData.responseText.length,\n                  responseTime: sanitizedData.responseTime,\n                  questionType: question.questionType,\n                }\n              );\n\n              if (!scoreValidation.isValid) {\n                console.warn('AI score validation failed:', {\n                  userId,\n                  sessionId,\n                  questionId: sanitizedData.questionId,\n                  score: aiResult.data.aiScore,\n                  errors: scoreValidation.errors,\n                  flags: scoreValidation.securityFlags,\n                  source: aiResult.source\n                });\n              }\n\n              // Update response with AI analysis\n              createdResponse = await prisma.interviewResponse.update({\n                where: { id: createdResponse.id },\n                data: {\n                  aiScore: scoreValidation.isValid ? scoreValidation.sanitizedData : null,\n                  aiAnalysis: aiResult.data.aiAnalysis,\n                  feedback: aiResult.data.feedback,\n                  strengths: aiResult.data.strengths,\n                  improvements: aiResult.data.improvements,\n                  starMethodScore: aiResult.data.starMethodScore || null,\n                  confidenceLevel: aiResult.data.confidenceLevel || null,\n                  communicationScore: aiResult.data.communicationScore || null,\n                  technicalScore: aiResult.data.technicalScore || null,\n                },\n                include: {\n                  question: {\n                    select: {\n                      questionText: true,\n                      questionType: true,\n                      category: true,\n                      difficulty: true,\n                      context: true,\n                      hints: true,\n                    },\n                  },\n                },\n              });\n\n              console.log(`AI analysis completed via ${aiResult.source} in ${aiResult.responseTime}ms (${aiResult.retryCount} retries)`);\n            } else {\n              console.warn('AI analysis failed:', aiResult.error);\n            }\n          } catch (feedbackError) {\n            console.error('Error generating AI feedback:', feedbackError);\n            // Continue without feedback - don't fail the response submission\n          }\n        }\n\n        // Update session progress\n        const completedResponses = await prisma.interviewResponse.count({\n          where: {\n            sessionId,\n            userId,\n            isCompleted: true,\n          },\n        });\n\n        await prisma.interviewSession.update({\n          where: { id: sessionId },\n          data: {\n            completedQuestions: completedResponses,\n            lastActiveAt: new Date(),\n          },\n        });\n\n        return NextResponse.json({\n          success: true,\n          data: createdResponse,\n          message: 'Response submitted successfully',\n        });\n      }\n    );\n  });\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e3e01080e6a7031bb1713a7da56f8320a4bb2f0b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2mjl24kbm9 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2mjl24kbm9();
var __assign =
/* istanbul ignore next */
(cov_2mjl24kbm9().s[0]++,
/* istanbul ignore next */
(cov_2mjl24kbm9().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2mjl24kbm9().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_2mjl24kbm9().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_2mjl24kbm9().f[0]++;
  cov_2mjl24kbm9().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_2mjl24kbm9().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_2mjl24kbm9().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_2mjl24kbm9().f[1]++;
    cov_2mjl24kbm9().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_2mjl24kbm9().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_2mjl24kbm9().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_2mjl24kbm9().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_2mjl24kbm9().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_2mjl24kbm9().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_2mjl24kbm9().b[2][0]++;
          cov_2mjl24kbm9().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_2mjl24kbm9().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_2mjl24kbm9().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_2mjl24kbm9().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_2mjl24kbm9().s[11]++,
/* istanbul ignore next */
(cov_2mjl24kbm9().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_2mjl24kbm9().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_2mjl24kbm9().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_2mjl24kbm9().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_2mjl24kbm9().f[3]++;
    cov_2mjl24kbm9().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_2mjl24kbm9().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_2mjl24kbm9().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_2mjl24kbm9().f[4]++;
      cov_2mjl24kbm9().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_2mjl24kbm9().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_2mjl24kbm9().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_2mjl24kbm9().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_2mjl24kbm9().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_2mjl24kbm9().f[6]++;
      cov_2mjl24kbm9().s[15]++;
      try {
        /* istanbul ignore next */
        cov_2mjl24kbm9().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2mjl24kbm9().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_2mjl24kbm9().f[7]++;
      cov_2mjl24kbm9().s[18]++;
      try {
        /* istanbul ignore next */
        cov_2mjl24kbm9().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2mjl24kbm9().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_2mjl24kbm9().f[8]++;
      cov_2mjl24kbm9().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_2mjl24kbm9().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_2mjl24kbm9().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_2mjl24kbm9().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_2mjl24kbm9().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_2mjl24kbm9().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_2mjl24kbm9().s[23]++,
/* istanbul ignore next */
(cov_2mjl24kbm9().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_2mjl24kbm9().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_2mjl24kbm9().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_2mjl24kbm9().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_2mjl24kbm9().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_2mjl24kbm9().f[10]++;
        cov_2mjl24kbm9().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_2mjl24kbm9().b[9][0]++;
          cov_2mjl24kbm9().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_2mjl24kbm9().b[9][1]++;
        }
        cov_2mjl24kbm9().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_2mjl24kbm9().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_2mjl24kbm9().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_2mjl24kbm9().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_2mjl24kbm9().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_2mjl24kbm9().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_2mjl24kbm9().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_2mjl24kbm9().f[11]++;
    cov_2mjl24kbm9().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_2mjl24kbm9().f[12]++;
    cov_2mjl24kbm9().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_2mjl24kbm9().f[13]++;
      cov_2mjl24kbm9().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_2mjl24kbm9().f[14]++;
    cov_2mjl24kbm9().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_2mjl24kbm9().b[12][0]++;
      cov_2mjl24kbm9().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_2mjl24kbm9().b[12][1]++;
    }
    cov_2mjl24kbm9().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_2mjl24kbm9().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_2mjl24kbm9().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_2mjl24kbm9().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_2mjl24kbm9().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_2mjl24kbm9().s[36]++;
      try {
        /* istanbul ignore next */
        cov_2mjl24kbm9().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_2mjl24kbm9().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_2mjl24kbm9().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_2mjl24kbm9().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_2mjl24kbm9().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_2mjl24kbm9().b[18][0]++,
        /* istanbul ignore next */
        (cov_2mjl24kbm9().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_2mjl24kbm9().b[19][1]++,
        /* istanbul ignore next */
        (cov_2mjl24kbm9().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_2mjl24kbm9().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_2mjl24kbm9().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_2mjl24kbm9().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_2mjl24kbm9().b[15][0]++;
          cov_2mjl24kbm9().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_2mjl24kbm9().b[15][1]++;
        }
        cov_2mjl24kbm9().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_2mjl24kbm9().b[21][0]++;
          cov_2mjl24kbm9().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_2mjl24kbm9().b[21][1]++;
        }
        cov_2mjl24kbm9().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_2mjl24kbm9().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_2mjl24kbm9().b[22][1]++;
            cov_2mjl24kbm9().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_2mjl24kbm9().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_2mjl24kbm9().b[22][2]++;
            cov_2mjl24kbm9().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_2mjl24kbm9().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_2mjl24kbm9().b[22][3]++;
            cov_2mjl24kbm9().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_2mjl24kbm9().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_2mjl24kbm9().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_2mjl24kbm9().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_2mjl24kbm9().b[22][4]++;
            cov_2mjl24kbm9().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_2mjl24kbm9().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2mjl24kbm9().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_2mjl24kbm9().b[22][5]++;
            cov_2mjl24kbm9().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_2mjl24kbm9().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_2mjl24kbm9().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_2mjl24kbm9().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_2mjl24kbm9().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_2mjl24kbm9().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_2mjl24kbm9().b[23][0]++;
              cov_2mjl24kbm9().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_2mjl24kbm9().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_2mjl24kbm9().b[23][1]++;
            }
            cov_2mjl24kbm9().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_2mjl24kbm9().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_2mjl24kbm9().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_2mjl24kbm9().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_2mjl24kbm9().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_2mjl24kbm9().b[26][0]++;
              cov_2mjl24kbm9().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_2mjl24kbm9().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2mjl24kbm9().b[26][1]++;
            }
            cov_2mjl24kbm9().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_2mjl24kbm9().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_2mjl24kbm9().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_2mjl24kbm9().b[28][0]++;
              cov_2mjl24kbm9().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_2mjl24kbm9().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_2mjl24kbm9().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2mjl24kbm9().b[28][1]++;
            }
            cov_2mjl24kbm9().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_2mjl24kbm9().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_2mjl24kbm9().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_2mjl24kbm9().b[30][0]++;
              cov_2mjl24kbm9().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_2mjl24kbm9().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_2mjl24kbm9().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2mjl24kbm9().b[30][1]++;
            }
            cov_2mjl24kbm9().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_2mjl24kbm9().b[32][0]++;
              cov_2mjl24kbm9().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_2mjl24kbm9().b[32][1]++;
            }
            cov_2mjl24kbm9().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2mjl24kbm9().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_2mjl24kbm9().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_2mjl24kbm9().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_2mjl24kbm9().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_2mjl24kbm9().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_2mjl24kbm9().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_2mjl24kbm9().b[33][0]++;
      cov_2mjl24kbm9().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_2mjl24kbm9().b[33][1]++;
    }
    cov_2mjl24kbm9().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_2mjl24kbm9().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_2mjl24kbm9().b[34][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_2mjl24kbm9().s[78]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2mjl24kbm9().s[79]++;
exports.POST = exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_2mjl24kbm9().s[80]++, require("next/server"));
var next_auth_1 =
/* istanbul ignore next */
(cov_2mjl24kbm9().s[81]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_2mjl24kbm9().s[82]++, require("@/lib/auth"));
var prisma_1 =
/* istanbul ignore next */
(cov_2mjl24kbm9().s[83]++, require("@/lib/prisma"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_2mjl24kbm9().s[84]++, require("@/lib/unified-api-error-handler"));
var rateLimit_1 =
/* istanbul ignore next */
(cov_2mjl24kbm9().s[85]++, require("@/lib/rateLimit"));
var self_healing_ai_service_1 =
/* istanbul ignore next */
(cov_2mjl24kbm9().s[86]++, require("@/lib/self-healing-ai-service"));
var unified_validation_service_1 =
/* istanbul ignore next */
(cov_2mjl24kbm9().s[87]++, require("@/lib/unified-validation-service"));
var zod_1 =
/* istanbul ignore next */
(cov_2mjl24kbm9().s[88]++, require("zod"));
// Validation schema for submitting responses
var submitResponseSchema =
/* istanbul ignore next */
(cov_2mjl24kbm9().s[89]++, zod_1.z.object({
  questionId: zod_1.z.string().uuid(),
  responseText: zod_1.z.string().min(10, 'Response must be at least 10 characters').max(5000, 'Response too long'),
  audioUrl: zod_1.z.string().url().optional().nullable().transform(function (val) {
    /* istanbul ignore next */
    cov_2mjl24kbm9().f[15]++;
    cov_2mjl24kbm9().s[90]++;
    return /* istanbul ignore next */(cov_2mjl24kbm9().b[35][0]++, val) ||
    /* istanbul ignore next */
    (cov_2mjl24kbm9().b[35][1]++, undefined);
  }),
  videoUrl: zod_1.z.string().url().optional().nullable().transform(function (val) {
    /* istanbul ignore next */
    cov_2mjl24kbm9().f[16]++;
    cov_2mjl24kbm9().s[91]++;
    return /* istanbul ignore next */(cov_2mjl24kbm9().b[36][0]++, val) ||
    /* istanbul ignore next */
    (cov_2mjl24kbm9().b[36][1]++, undefined);
  }),
  responseTime: zod_1.z.number().min(0).max(3600),
  // Max 1 hour
  preparationTime: zod_1.z.number().min(0).max(1800).default(0),
  // Max 30 minutes
  userNotes: zod_1.z.string().max(1000).optional().nullable().transform(function (val) {
    /* istanbul ignore next */
    cov_2mjl24kbm9().f[17]++;
    cov_2mjl24kbm9().s[92]++;
    return /* istanbul ignore next */(cov_2mjl24kbm9().b[37][0]++, val) ||
    /* istanbul ignore next */
    (cov_2mjl24kbm9().b[37][1]++, undefined);
  }),
  requestFeedback: zod_1.z.boolean().default(true)
}));
// Validation schema for updating responses
var updateResponseSchema =
/* istanbul ignore next */
(cov_2mjl24kbm9().s[93]++, zod_1.z.object({
  responseText: zod_1.z.string().min(10).max(5000).optional(),
  audioUrl: zod_1.z.string().url().optional().nullable().transform(function (val) {
    /* istanbul ignore next */
    cov_2mjl24kbm9().f[18]++;
    cov_2mjl24kbm9().s[94]++;
    return /* istanbul ignore next */(cov_2mjl24kbm9().b[38][0]++, val) ||
    /* istanbul ignore next */
    (cov_2mjl24kbm9().b[38][1]++, undefined);
  }),
  videoUrl: zod_1.z.string().url().optional().nullable().transform(function (val) {
    /* istanbul ignore next */
    cov_2mjl24kbm9().f[19]++;
    cov_2mjl24kbm9().s[95]++;
    return /* istanbul ignore next */(cov_2mjl24kbm9().b[39][0]++, val) ||
    /* istanbul ignore next */
    (cov_2mjl24kbm9().b[39][1]++, undefined);
  }),
  responseTime: zod_1.z.number().min(0).max(3600).optional(),
  preparationTime: zod_1.z.number().min(0).max(1800).optional(),
  userNotes: zod_1.z.string().max(1000).optional().nullable().transform(function (val) {
    /* istanbul ignore next */
    cov_2mjl24kbm9().f[20]++;
    cov_2mjl24kbm9().s[96]++;
    return /* istanbul ignore next */(cov_2mjl24kbm9().b[40][0]++, val) ||
    /* istanbul ignore next */
    (cov_2mjl24kbm9().b[40][1]++, undefined);
  }),
  needsReview: zod_1.z.boolean().optional()
}));
// GET - Retrieve responses for a session
/* istanbul ignore next */
cov_2mjl24kbm9().s[97]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_2mjl24kbm9().f[21]++;
  cov_2mjl24kbm9().s[98]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_2mjl24kbm9().f[22]++;
    var params =
    /* istanbul ignore next */
    (cov_2mjl24kbm9().s[99]++, _b.params);
    /* istanbul ignore next */
    cov_2mjl24kbm9().s[100]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_2mjl24kbm9().f[23]++;
      cov_2mjl24kbm9().s[101]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: process.env.NODE_ENV === 'development' ?
        /* istanbul ignore next */
        (cov_2mjl24kbm9().b[41][0]++, 200) :
        /* istanbul ignore next */
        (cov_2mjl24kbm9().b[41][1]++, 50) // Higher limit for development
      }, function () {
        /* istanbul ignore next */
        cov_2mjl24kbm9().f[24]++;
        cov_2mjl24kbm9().s[102]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_2mjl24kbm9().f[25]++;
          var session, userId, sessionId, interviewSession, responses;
          var _a;
          /* istanbul ignore next */
          cov_2mjl24kbm9().s[103]++;
          return __generator(this, function (_b) {
            /* istanbul ignore next */
            cov_2mjl24kbm9().f[26]++;
            cov_2mjl24kbm9().s[104]++;
            switch (_b.label) {
              case 0:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[42][0]++;
                cov_2mjl24kbm9().s[105]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[42][1]++;
                cov_2mjl24kbm9().s[106]++;
                session = _b.sent();
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[107]++;
                if (!(
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[45][0]++, (_a =
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[47][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[47][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[46][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[46][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[45][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[44][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[44][1]++, _a.id))) {
                  /* istanbul ignore next */
                  cov_2mjl24kbm9().b[43][0]++;
                  cov_2mjl24kbm9().s[108]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Authentication required'
                  }, {
                    status: 401
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_2mjl24kbm9().b[43][1]++;
                }
                cov_2mjl24kbm9().s[109]++;
                userId = session.user.id;
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[110]++;
                return [4 /*yield*/, params];
              case 2:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[42][2]++;
                cov_2mjl24kbm9().s[111]++;
                sessionId = _b.sent().sessionId;
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[112]++;
                return [4 /*yield*/, prisma_1.prisma.interviewSession.findFirst({
                  where: {
                    id: sessionId,
                    userId: userId
                  }
                })];
              case 3:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[42][3]++;
                cov_2mjl24kbm9().s[113]++;
                interviewSession = _b.sent();
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[114]++;
                if (!interviewSession) {
                  /* istanbul ignore next */
                  cov_2mjl24kbm9().b[48][0]++;
                  cov_2mjl24kbm9().s[115]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Interview session not found'
                  }, {
                    status: 404
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_2mjl24kbm9().b[48][1]++;
                }
                cov_2mjl24kbm9().s[116]++;
                return [4 /*yield*/, prisma_1.prisma.interviewResponse.findMany({
                  where: {
                    sessionId: sessionId,
                    userId: userId
                  },
                  include: {
                    question: {
                      select: {
                        id: true,
                        questionText: true,
                        questionType: true,
                        category: true,
                        difficulty: true,
                        expectedDuration: true,
                        context: true,
                        hints: true,
                        questionOrder: true
                      }
                    }
                  },
                  orderBy: {
                    question: {
                      questionOrder: 'asc'
                    }
                  }
                })];
              case 4:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[42][4]++;
                cov_2mjl24kbm9().s[117]++;
                responses = _b.sent();
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[118]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: responses
                })];
            }
          });
        });
      })];
    });
  });
});
// POST - Submit a new response
/* istanbul ignore next */
cov_2mjl24kbm9().s[119]++;
exports.POST = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request_1, _a) {
  /* istanbul ignore next */
  cov_2mjl24kbm9().f[27]++;
  cov_2mjl24kbm9().s[120]++;
  return __awaiter(void 0, [request_1, _a], void 0, function (request, _b) {
    /* istanbul ignore next */
    cov_2mjl24kbm9().f[28]++;
    var params =
    /* istanbul ignore next */
    (cov_2mjl24kbm9().s[121]++, _b.params);
    /* istanbul ignore next */
    cov_2mjl24kbm9().s[122]++;
    return __generator(this, function (_c) {
      /* istanbul ignore next */
      cov_2mjl24kbm9().f[29]++;
      cov_2mjl24kbm9().s[123]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: process.env.NODE_ENV === 'development' ?
        /* istanbul ignore next */
        (cov_2mjl24kbm9().b[49][0]++, 150) :
        /* istanbul ignore next */
        (cov_2mjl24kbm9().b[49][1]++, 30) // Higher limit for development
      }, function () {
        /* istanbul ignore next */
        cov_2mjl24kbm9().f[30]++;
        cov_2mjl24kbm9().s[124]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_2mjl24kbm9().f[31]++;
          var session, userId, sessionId, body, validation, responseData, unifiedValidation, sanitizedData, _a, interviewSession, question, existingResponse, createdResponse, aiResult, scoreValidation, feedbackError_1, completedResponses;
          var _b;
          /* istanbul ignore next */
          cov_2mjl24kbm9().s[125]++;
          return __generator(this, function (_c) {
            /* istanbul ignore next */
            cov_2mjl24kbm9().f[32]++;
            cov_2mjl24kbm9().s[126]++;
            switch (_c.label) {
              case 0:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[50][0]++;
                cov_2mjl24kbm9().s[127]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[50][1]++;
                cov_2mjl24kbm9().s[128]++;
                session = _c.sent();
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[129]++;
                if (!(
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[53][0]++, (_b =
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[55][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[55][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[54][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[54][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[53][1]++, _b === void 0) ?
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[52][0]++, void 0) :
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[52][1]++, _b.id))) {
                  /* istanbul ignore next */
                  cov_2mjl24kbm9().b[51][0]++;
                  cov_2mjl24kbm9().s[130]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Authentication required'
                  }, {
                    status: 401
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_2mjl24kbm9().b[51][1]++;
                }
                cov_2mjl24kbm9().s[131]++;
                userId = session.user.id;
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[132]++;
                return [4 /*yield*/, params];
              case 2:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[50][2]++;
                cov_2mjl24kbm9().s[133]++;
                sessionId = _c.sent().sessionId;
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[134]++;
                return [4 /*yield*/, request.json()];
              case 3:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[50][3]++;
                cov_2mjl24kbm9().s[135]++;
                body = _c.sent();
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[136]++;
                validation = submitResponseSchema.safeParse(body);
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[137]++;
                if (!validation.success) {
                  /* istanbul ignore next */
                  cov_2mjl24kbm9().b[56][0]++;
                  cov_2mjl24kbm9().s[138]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Invalid request data',
                    details: validation.error.errors
                  }, {
                    status: 400
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_2mjl24kbm9().b[56][1]++;
                }
                cov_2mjl24kbm9().s[139]++;
                responseData = validation.data;
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[140]++;
                unifiedValidation = unified_validation_service_1.UnifiedValidationService.validateInterviewResponse({
                  responseText:
                  /* istanbul ignore next */
                  (cov_2mjl24kbm9().b[57][0]++, responseData.responseText) ||
                  /* istanbul ignore next */
                  (cov_2mjl24kbm9().b[57][1]++, ''),
                  userNotes: responseData.userNotes,
                  responseTime: responseData.responseTime,
                  preparationTime: responseData.preparationTime
                });
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[141]++;
                if (!unifiedValidation.isValid) {
                  /* istanbul ignore next */
                  cov_2mjl24kbm9().b[58][0]++;
                  cov_2mjl24kbm9().s[142]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Response validation failed',
                    details: unifiedValidation.errors,
                    securityFlags: unifiedValidation.securityFlags
                  }, {
                    status: 400
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_2mjl24kbm9().b[58][1]++;
                }
                // Log security flags for monitoring
                cov_2mjl24kbm9().s[143]++;
                if (
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[60][0]++, unifiedValidation.securityFlags) &&
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[60][1]++, unifiedValidation.securityFlags.length > 0)) {
                  /* istanbul ignore next */
                  cov_2mjl24kbm9().b[59][0]++;
                  cov_2mjl24kbm9().s[144]++;
                  console.warn('Security flags detected in response submission:', {
                    userId: userId,
                    sessionId: sessionId,
                    questionId: responseData.questionId,
                    flags: unifiedValidation.securityFlags
                  });
                } else
                /* istanbul ignore next */
                {
                  cov_2mjl24kbm9().b[59][1]++;
                }
                cov_2mjl24kbm9().s[145]++;
                sanitizedData = __assign(__assign({}, unifiedValidation.sanitizedData), {
                  questionId: responseData.questionId // Add questionId from original data
                });
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[146]++;
                return [4 /*yield*/, Promise.all([prisma_1.prisma.interviewSession.findFirst({
                  where: {
                    id: sessionId,
                    userId: userId
                  }
                }), prisma_1.prisma.interviewQuestion.findFirst({
                  where: {
                    id: sanitizedData.questionId,
                    sessionId: sessionId
                  }
                })])];
              case 4:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[50][4]++;
                cov_2mjl24kbm9().s[147]++;
                _a = _c.sent(), interviewSession = _a[0], question = _a[1];
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[148]++;
                if (!interviewSession) {
                  /* istanbul ignore next */
                  cov_2mjl24kbm9().b[61][0]++;
                  cov_2mjl24kbm9().s[149]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Interview session not found'
                  }, {
                    status: 404
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_2mjl24kbm9().b[61][1]++;
                }
                cov_2mjl24kbm9().s[150]++;
                if (!question) {
                  /* istanbul ignore next */
                  cov_2mjl24kbm9().b[62][0]++;
                  cov_2mjl24kbm9().s[151]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Question not found'
                  }, {
                    status: 404
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_2mjl24kbm9().b[62][1]++;
                }
                cov_2mjl24kbm9().s[152]++;
                return [4 /*yield*/, prisma_1.prisma.interviewResponse.findFirst({
                  where: {
                    userId: userId,
                    questionId: sanitizedData.questionId
                  }
                })];
              case 5:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[50][5]++;
                cov_2mjl24kbm9().s[153]++;
                existingResponse = _c.sent();
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[154]++;
                if (existingResponse) {
                  /* istanbul ignore next */
                  cov_2mjl24kbm9().b[63][0]++;
                  cov_2mjl24kbm9().s[155]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Response already exists for this question'
                  }, {
                    status: 400
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_2mjl24kbm9().b[63][1]++;
                }
                cov_2mjl24kbm9().s[156]++;
                return [4 /*yield*/, prisma_1.prisma.interviewResponse.create({
                  data: {
                    userId: userId,
                    sessionId: sessionId,
                    questionId: sanitizedData.questionId,
                    responseText: sanitizedData.responseText,
                    audioUrl: responseData.audioUrl,
                    // URLs are validated by Zod schema
                    videoUrl: responseData.videoUrl,
                    responseTime: sanitizedData.responseTime,
                    preparationTime: sanitizedData.preparationTime,
                    userNotes: sanitizedData.userNotes,
                    isCompleted: true
                  },
                  include: {
                    question: {
                      select: {
                        questionText: true,
                        questionType: true,
                        category: true,
                        difficulty: true,
                        context: true,
                        hints: true
                      }
                    }
                  }
                })];
              case 6:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[50][6]++;
                cov_2mjl24kbm9().s[157]++;
                createdResponse = _c.sent();
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[158]++;
                if (!responseData.requestFeedback) {
                  /* istanbul ignore next */
                  cov_2mjl24kbm9().b[64][0]++;
                  cov_2mjl24kbm9().s[159]++;
                  return [3 /*break*/, 13];
                } else
                /* istanbul ignore next */
                {
                  cov_2mjl24kbm9().b[64][1]++;
                }
                cov_2mjl24kbm9().s[160]++;
                _c.label = 7;
              case 7:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[50][7]++;
                cov_2mjl24kbm9().s[161]++;
                _c.trys.push([7, 12,, 13]);
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[162]++;
                return [4 /*yield*/, self_healing_ai_service_1.SelfHealingAIService.analyzeInterviewResponse(question.questionText, sanitizedData.responseText, {
                  timeout: 20000,
                  // 20 seconds for response analysis
                  maxRetries: 2,
                  fallbackToStatic: true
                })];
              case 8:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[50][8]++;
                cov_2mjl24kbm9().s[163]++;
                aiResult = _c.sent();
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[164]++;
                if (!(
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[66][0]++, aiResult.success) &&
                /* istanbul ignore next */
                (cov_2mjl24kbm9().b[66][1]++, aiResult.data))) {
                  /* istanbul ignore next */
                  cov_2mjl24kbm9().b[65][0]++;
                  cov_2mjl24kbm9().s[165]++;
                  return [3 /*break*/, 10];
                } else
                /* istanbul ignore next */
                {
                  cov_2mjl24kbm9().b[65][1]++;
                }
                cov_2mjl24kbm9().s[166]++;
                scoreValidation = unified_validation_service_1.UnifiedValidationService.validateAIScore(aiResult.data.aiScore, {
                  responseLength: sanitizedData.responseText.length,
                  responseTime: sanitizedData.responseTime,
                  questionType: question.questionType
                });
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[167]++;
                if (!scoreValidation.isValid) {
                  /* istanbul ignore next */
                  cov_2mjl24kbm9().b[67][0]++;
                  cov_2mjl24kbm9().s[168]++;
                  console.warn('AI score validation failed:', {
                    userId: userId,
                    sessionId: sessionId,
                    questionId: sanitizedData.questionId,
                    score: aiResult.data.aiScore,
                    errors: scoreValidation.errors,
                    flags: scoreValidation.securityFlags,
                    source: aiResult.source
                  });
                } else
                /* istanbul ignore next */
                {
                  cov_2mjl24kbm9().b[67][1]++;
                }
                cov_2mjl24kbm9().s[169]++;
                return [4 /*yield*/, prisma_1.prisma.interviewResponse.update({
                  where: {
                    id: createdResponse.id
                  },
                  data: {
                    aiScore: scoreValidation.isValid ?
                    /* istanbul ignore next */
                    (cov_2mjl24kbm9().b[68][0]++, scoreValidation.sanitizedData) :
                    /* istanbul ignore next */
                    (cov_2mjl24kbm9().b[68][1]++, null),
                    aiAnalysis: aiResult.data.aiAnalysis,
                    feedback: aiResult.data.feedback,
                    strengths: aiResult.data.strengths,
                    improvements: aiResult.data.improvements,
                    starMethodScore:
                    /* istanbul ignore next */
                    (cov_2mjl24kbm9().b[69][0]++, aiResult.data.starMethodScore) ||
                    /* istanbul ignore next */
                    (cov_2mjl24kbm9().b[69][1]++, null),
                    confidenceLevel:
                    /* istanbul ignore next */
                    (cov_2mjl24kbm9().b[70][0]++, aiResult.data.confidenceLevel) ||
                    /* istanbul ignore next */
                    (cov_2mjl24kbm9().b[70][1]++, null),
                    communicationScore:
                    /* istanbul ignore next */
                    (cov_2mjl24kbm9().b[71][0]++, aiResult.data.communicationScore) ||
                    /* istanbul ignore next */
                    (cov_2mjl24kbm9().b[71][1]++, null),
                    technicalScore:
                    /* istanbul ignore next */
                    (cov_2mjl24kbm9().b[72][0]++, aiResult.data.technicalScore) ||
                    /* istanbul ignore next */
                    (cov_2mjl24kbm9().b[72][1]++, null)
                  },
                  include: {
                    question: {
                      select: {
                        questionText: true,
                        questionType: true,
                        category: true,
                        difficulty: true,
                        context: true,
                        hints: true
                      }
                    }
                  }
                })];
              case 9:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[50][9]++;
                cov_2mjl24kbm9().s[170]++;
                // Update response with AI analysis
                createdResponse = _c.sent();
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[171]++;
                console.log("AI analysis completed via ".concat(aiResult.source, " in ").concat(aiResult.responseTime, "ms (").concat(aiResult.retryCount, " retries)"));
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[172]++;
                return [3 /*break*/, 11];
              case 10:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[50][10]++;
                cov_2mjl24kbm9().s[173]++;
                console.warn('AI analysis failed:', aiResult.error);
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[174]++;
                _c.label = 11;
              case 11:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[50][11]++;
                cov_2mjl24kbm9().s[175]++;
                return [3 /*break*/, 13];
              case 12:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[50][12]++;
                cov_2mjl24kbm9().s[176]++;
                feedbackError_1 = _c.sent();
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[177]++;
                console.error('Error generating AI feedback:', feedbackError_1);
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[178]++;
                return [3 /*break*/, 13];
              case 13:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[50][13]++;
                cov_2mjl24kbm9().s[179]++;
                return [4 /*yield*/, prisma_1.prisma.interviewResponse.count({
                  where: {
                    sessionId: sessionId,
                    userId: userId,
                    isCompleted: true
                  }
                })];
              case 14:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[50][14]++;
                cov_2mjl24kbm9().s[180]++;
                completedResponses = _c.sent();
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[181]++;
                return [4 /*yield*/, prisma_1.prisma.interviewSession.update({
                  where: {
                    id: sessionId
                  },
                  data: {
                    completedQuestions: completedResponses,
                    lastActiveAt: new Date()
                  }
                })];
              case 15:
                /* istanbul ignore next */
                cov_2mjl24kbm9().b[50][15]++;
                cov_2mjl24kbm9().s[182]++;
                _c.sent();
                /* istanbul ignore next */
                cov_2mjl24kbm9().s[183]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: createdResponse,
                  message: 'Response submitted successfully'
                })];
            }
          });
        });
      })];
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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