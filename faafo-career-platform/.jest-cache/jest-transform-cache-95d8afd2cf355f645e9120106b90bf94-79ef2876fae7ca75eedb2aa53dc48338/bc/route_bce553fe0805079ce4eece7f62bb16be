1c9a193abc59d0a2020f2073e746b9d7
"use strict";

/* istanbul ignore next */
function cov_bhy9hk6ya() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/admin/database/route.ts";
  var hash = "42f57ec0bd924d8cf524aae191dfcc83d21822ff";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/admin/database/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 4
        },
        end: {
          line: 10,
          column: 6
        }
      },
      "2": {
        start: {
          line: 4,
          column: 8
        },
        end: {
          line: 8,
          column: 9
        }
      },
      "3": {
        start: {
          line: 4,
          column: 24
        },
        end: {
          line: 4,
          column: 25
        }
      },
      "4": {
        start: {
          line: 4,
          column: 31
        },
        end: {
          line: 4,
          column: 47
        }
      },
      "5": {
        start: {
          line: 5,
          column: 12
        },
        end: {
          line: 5,
          column: 29
        }
      },
      "6": {
        start: {
          line: 6,
          column: 12
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "7": {
        start: {
          line: 6,
          column: 29
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "8": {
        start: {
          line: 7,
          column: 16
        },
        end: {
          line: 7,
          column: 28
        }
      },
      "9": {
        start: {
          line: 9,
          column: 8
        },
        end: {
          line: 9,
          column: 17
        }
      },
      "10": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 43
        }
      },
      "11": {
        start: {
          line: 13,
          column: 16
        },
        end: {
          line: 21,
          column: 1
        }
      },
      "12": {
        start: {
          line: 14,
          column: 28
        },
        end: {
          line: 14,
          column: 110
        }
      },
      "13": {
        start: {
          line: 14,
          column: 91
        },
        end: {
          line: 14,
          column: 106
        }
      },
      "14": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 20,
          column: 7
        }
      },
      "15": {
        start: {
          line: 16,
          column: 36
        },
        end: {
          line: 16,
          column: 97
        }
      },
      "16": {
        start: {
          line: 16,
          column: 42
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "17": {
        start: {
          line: 16,
          column: 85
        },
        end: {
          line: 16,
          column: 95
        }
      },
      "18": {
        start: {
          line: 17,
          column: 35
        },
        end: {
          line: 17,
          column: 100
        }
      },
      "19": {
        start: {
          line: 17,
          column: 41
        },
        end: {
          line: 17,
          column: 73
        }
      },
      "20": {
        start: {
          line: 17,
          column: 88
        },
        end: {
          line: 17,
          column: 98
        }
      },
      "21": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 116
        }
      },
      "22": {
        start: {
          line: 19,
          column: 8
        },
        end: {
          line: 19,
          column: 78
        }
      },
      "23": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "24": {
        start: {
          line: 23,
          column: 12
        },
        end: {
          line: 23,
          column: 104
        }
      },
      "25": {
        start: {
          line: 23,
          column: 43
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "26": {
        start: {
          line: 23,
          column: 57
        },
        end: {
          line: 23,
          column: 68
        }
      },
      "27": {
        start: {
          line: 23,
          column: 69
        },
        end: {
          line: 23,
          column: 81
        }
      },
      "28": {
        start: {
          line: 23,
          column: 119
        },
        end: {
          line: 23,
          column: 196
        }
      },
      "29": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 24,
          column: 160
        }
      },
      "30": {
        start: {
          line: 24,
          column: 141
        },
        end: {
          line: 24,
          column: 153
        }
      },
      "31": {
        start: {
          line: 25,
          column: 23
        },
        end: {
          line: 25,
          column: 68
        }
      },
      "32": {
        start: {
          line: 25,
          column: 45
        },
        end: {
          line: 25,
          column: 65
        }
      },
      "33": {
        start: {
          line: 27,
          column: 8
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "34": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 70
        }
      },
      "35": {
        start: {
          line: 28,
          column: 8
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "36": {
        start: {
          line: 28,
          column: 50
        },
        end: {
          line: 45,
          column: 66
        }
      },
      "37": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "38": {
        start: {
          line: 29,
          column: 160
        },
        end: {
          line: 29,
          column: 169
        }
      },
      "39": {
        start: {
          line: 30,
          column: 12
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "40": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "41": {
        start: {
          line: 31,
          column: 12
        },
        end: {
          line: 43,
          column: 13
        }
      },
      "42": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 39
        }
      },
      "43": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "44": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 34
        }
      },
      "45": {
        start: {
          line: 33,
          column: 35
        },
        end: {
          line: 33,
          column: 72
        }
      },
      "46": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "47": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 45
        }
      },
      "48": {
        start: {
          line: 34,
          column: 46
        },
        end: {
          line: 34,
          column: 55
        }
      },
      "49": {
        start: {
          line: 34,
          column: 56
        },
        end: {
          line: 34,
          column: 65
        }
      },
      "50": {
        start: {
          line: 35,
          column: 24
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "51": {
        start: {
          line: 35,
          column: 42
        },
        end: {
          line: 35,
          column: 55
        }
      },
      "52": {
        start: {
          line: 35,
          column: 56
        },
        end: {
          line: 35,
          column: 65
        }
      },
      "53": {
        start: {
          line: 37,
          column: 20
        },
        end: {
          line: 37,
          column: 128
        }
      },
      "54": {
        start: {
          line: 37,
          column: 110
        },
        end: {
          line: 37,
          column: 116
        }
      },
      "55": {
        start: {
          line: 37,
          column: 117
        },
        end: {
          line: 37,
          column: 126
        }
      },
      "56": {
        start: {
          line: 38,
          column: 20
        },
        end: {
          line: 38,
          column: 106
        }
      },
      "57": {
        start: {
          line: 38,
          column: 81
        },
        end: {
          line: 38,
          column: 97
        }
      },
      "58": {
        start: {
          line: 38,
          column: 98
        },
        end: {
          line: 38,
          column: 104
        }
      },
      "59": {
        start: {
          line: 39,
          column: 20
        },
        end: {
          line: 39,
          column: 89
        }
      },
      "60": {
        start: {
          line: 39,
          column: 57
        },
        end: {
          line: 39,
          column: 72
        }
      },
      "61": {
        start: {
          line: 39,
          column: 73
        },
        end: {
          line: 39,
          column: 80
        }
      },
      "62": {
        start: {
          line: 39,
          column: 81
        },
        end: {
          line: 39,
          column: 87
        }
      },
      "63": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 40,
          column: 87
        }
      },
      "64": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 62
        }
      },
      "65": {
        start: {
          line: 40,
          column: 63
        },
        end: {
          line: 40,
          column: 78
        }
      },
      "66": {
        start: {
          line: 40,
          column: 79
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "67": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "68": {
        start: {
          line: 41,
          column: 30
        },
        end: {
          line: 41,
          column: 42
        }
      },
      "69": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 33
        }
      },
      "70": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 43
        }
      },
      "71": {
        start: {
          line: 44,
          column: 12
        },
        end: {
          line: 44,
          column: 39
        }
      },
      "72": {
        start: {
          line: 45,
          column: 22
        },
        end: {
          line: 45,
          column: 34
        }
      },
      "73": {
        start: {
          line: 45,
          column: 35
        },
        end: {
          line: 45,
          column: 41
        }
      },
      "74": {
        start: {
          line: 45,
          column: 54
        },
        end: {
          line: 45,
          column: 64
        }
      },
      "75": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "76": {
        start: {
          line: 46,
          column: 23
        },
        end: {
          line: 46,
          column: 35
        }
      },
      "77": {
        start: {
          line: 46,
          column: 36
        },
        end: {
          line: 46,
          column: 89
        }
      },
      "78": {
        start: {
          line: 49,
          column: 0
        },
        end: {
          line: 49,
          column: 62
        }
      },
      "79": {
        start: {
          line: 50,
          column: 0
        },
        end: {
          line: 50,
          column: 50
        }
      },
      "80": {
        start: {
          line: 51,
          column: 15
        },
        end: {
          line: 51,
          column: 37
        }
      },
      "81": {
        start: {
          line: 52,
          column: 18
        },
        end: {
          line: 52,
          column: 38
        }
      },
      "82": {
        start: {
          line: 53,
          column: 13
        },
        end: {
          line: 53,
          column: 34
        }
      },
      "83": {
        start: {
          line: 54,
          column: 29
        },
        end: {
          line: 54,
          column: 75
        }
      },
      "84": {
        start: {
          line: 55,
          column: 18
        },
        end: {
          line: 55,
          column: 44
        }
      },
      "85": {
        start: {
          line: 56,
          column: 13
        },
        end: {
          line: 56,
          column: 34
        }
      },
      "86": {
        start: {
          line: 57,
          column: 19
        },
        end: {
          line: 57,
          column: 46
        }
      },
      "87": {
        start: {
          line: 58,
          column: 34
        },
        end: {
          line: 58,
          column: 76
        }
      },
      "88": {
        start: {
          line: 60,
          column: 0
        },
        end: {
          line: 160,
          column: 7
        }
      },
      "89": {
        start: {
          line: 60,
          column: 93
        },
        end: {
          line: 160,
          column: 3
        }
      },
      "90": {
        start: {
          line: 61,
          column: 4
        },
        end: {
          line: 159,
          column: 7
        }
      },
      "91": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 158,
          column: 20
        }
      },
      "92": {
        start: {
          line: 63,
          column: 26
        },
        end: {
          line: 158,
          column: 15
        }
      },
      "93": {
        start: {
          line: 66,
          column: 16
        },
        end: {
          line: 157,
          column: 19
        }
      },
      "94": {
        start: {
          line: 67,
          column: 20
        },
        end: {
          line: 156,
          column: 21
        }
      },
      "95": {
        start: {
          line: 68,
          column: 32
        },
        end: {
          line: 68,
          column: 108
        }
      },
      "96": {
        start: {
          line: 70,
          column: 28
        },
        end: {
          line: 70,
          column: 48
        }
      },
      "97": {
        start: {
          line: 71,
          column: 28
        },
        end: {
          line: 75,
          column: 29
        }
      },
      "98": {
        start: {
          line: 72,
          column: 32
        },
        end: {
          line: 72,
          column: 77
        }
      },
      "99": {
        start: {
          line: 73,
          column: 32
        },
        end: {
          line: 73,
          column: 55
        }
      },
      "100": {
        start: {
          line: 74,
          column: 32
        },
        end: {
          line: 74,
          column: 44
        }
      },
      "101": {
        start: {
          line: 76,
          column: 28
        },
        end: {
          line: 76,
          column: 97
        }
      },
      "102": {
        start: {
          line: 78,
          column: 28
        },
        end: {
          line: 78,
          column: 48
        }
      },
      "103": {
        start: {
          line: 79,
          column: 28
        },
        end: {
          line: 83,
          column: 29
        }
      },
      "104": {
        start: {
          line: 80,
          column: 32
        },
        end: {
          line: 80,
          column: 75
        }
      },
      "105": {
        start: {
          line: 81,
          column: 32
        },
        end: {
          line: 81,
          column: 55
        }
      },
      "106": {
        start: {
          line: 82,
          column: 32
        },
        end: {
          line: 82,
          column: 44
        }
      },
      "107": {
        start: {
          line: 84,
          column: 28
        },
        end: {
          line: 84,
          column: 77
        }
      },
      "108": {
        start: {
          line: 85,
          column: 28
        },
        end: {
          line: 85,
          column: 64
        }
      },
      "109": {
        start: {
          line: 86,
          column: 28
        },
        end: {
          line: 86,
          column: 40
        }
      },
      "110": {
        start: {
          line: 87,
          column: 28
        },
        end: {
          line: 94,
          column: 29
        }
      },
      "111": {
        start: {
          line: 88,
          column: 46
        },
        end: {
          line: 88,
          column: 70
        }
      },
      "112": {
        start: {
          line: 89,
          column: 48
        },
        end: {
          line: 89,
          column: 72
        }
      },
      "113": {
        start: {
          line: 90,
          column: 47
        },
        end: {
          line: 90,
          column: 71
        }
      },
      "114": {
        start: {
          line: 91,
          column: 52
        },
        end: {
          line: 91,
          column: 76
        }
      },
      "115": {
        start: {
          line: 92,
          column: 56
        },
        end: {
          line: 92,
          column: 81
        }
      },
      "116": {
        start: {
          line: 93,
          column: 48
        },
        end: {
          line: 93,
          column: 73
        }
      },
      "117": {
        start: {
          line: 95,
          column: 28
        },
        end: {
          line: 95,
          column: 53
        }
      },
      "118": {
        start: {
          line: 96,
          column: 32
        },
        end: {
          line: 96,
          column: 111
        }
      },
      "119": {
        start: {
          line: 98,
          column: 28
        },
        end: {
          line: 98,
          column: 46
        }
      },
      "120": {
        start: {
          line: 99,
          column: 28
        },
        end: {
          line: 102,
          column: 36
        }
      },
      "121": {
        start: {
          line: 103,
          column: 32
        },
        end: {
          line: 103,
          column: 112
        }
      },
      "122": {
        start: {
          line: 105,
          column: 28
        },
        end: {
          line: 105,
          column: 54
        }
      },
      "123": {
        start: {
          line: 106,
          column: 28
        },
        end: {
          line: 109,
          column: 36
        }
      },
      "124": {
        start: {
          line: 110,
          column: 32
        },
        end: {
          line: 110,
          column: 108
        }
      },
      "125": {
        start: {
          line: 112,
          column: 28
        },
        end: {
          line: 112,
          column: 51
        }
      },
      "126": {
        start: {
          line: 113,
          column: 28
        },
        end: {
          line: 116,
          column: 36
        }
      },
      "127": {
        start: {
          line: 117,
          column: 32
        },
        end: {
          line: 117,
          column: 117
        }
      },
      "128": {
        start: {
          line: 119,
          column: 28
        },
        end: {
          line: 119,
          column: 56
        }
      },
      "129": {
        start: {
          line: 120,
          column: 28
        },
        end: {
          line: 123,
          column: 36
        }
      },
      "130": {
        start: {
          line: 124,
          column: 33
        },
        end: {
          line: 124,
          column: 125
        }
      },
      "131": {
        start: {
          line: 126,
          column: 28
        },
        end: {
          line: 126,
          column: 56
        }
      },
      "132": {
        start: {
          line: 127,
          column: 28
        },
        end: {
          line: 130,
          column: 36
        }
      },
      "133": {
        start: {
          line: 132,
          column: 28
        },
        end: {
          line: 132,
          column: 80
        }
      },
      "134": {
        start: {
          line: 133,
          column: 28
        },
        end: {
          line: 133,
          column: 100
        }
      },
      "135": {
        start: {
          line: 134,
          column: 28
        },
        end: {
          line: 137,
          column: 36
        }
      },
      "136": {
        start: {
          line: 138,
          column: 33
        },
        end: {
          line: 143,
          column: 32
        }
      },
      "137": {
        start: {
          line: 145,
          column: 28
        },
        end: {
          line: 145,
          column: 144
        }
      },
      "138": {
        start: {
          line: 146,
          column: 28
        },
        end: {
          line: 155,
          column: 36
        }
      },
      "139": {
        start: {
          line: 162,
          column: 0
        },
        end: {
          line: 260,
          column: 7
        }
      },
      "140": {
        start: {
          line: 162,
          column: 94
        },
        end: {
          line: 260,
          column: 3
        }
      },
      "141": {
        start: {
          line: 163,
          column: 4
        },
        end: {
          line: 259,
          column: 7
        }
      },
      "142": {
        start: {
          line: 164,
          column: 8
        },
        end: {
          line: 258,
          column: 20
        }
      },
      "143": {
        start: {
          line: 164,
          column: 84
        },
        end: {
          line: 258,
          column: 15
        }
      },
      "144": {
        start: {
          line: 165,
          column: 16
        },
        end: {
          line: 257,
          column: 19
        }
      },
      "145": {
        start: {
          line: 166,
          column: 20
        },
        end: {
          line: 256,
          column: 32
        }
      },
      "146": {
        start: {
          line: 167,
          column: 38
        },
        end: {
          line: 256,
          column: 27
        }
      },
      "147": {
        start: {
          line: 171,
          column: 28
        },
        end: {
          line: 255,
          column: 31
        }
      },
      "148": {
        start: {
          line: 172,
          column: 32
        },
        end: {
          line: 254,
          column: 33
        }
      },
      "149": {
        start: {
          line: 173,
          column: 44
        },
        end: {
          line: 173,
          column: 120
        }
      },
      "150": {
        start: {
          line: 175,
          column: 40
        },
        end: {
          line: 175,
          column: 60
        }
      },
      "151": {
        start: {
          line: 176,
          column: 40
        },
        end: {
          line: 180,
          column: 41
        }
      },
      "152": {
        start: {
          line: 177,
          column: 44
        },
        end: {
          line: 177,
          column: 89
        }
      },
      "153": {
        start: {
          line: 178,
          column: 44
        },
        end: {
          line: 178,
          column: 67
        }
      },
      "154": {
        start: {
          line: 179,
          column: 44
        },
        end: {
          line: 179,
          column: 56
        }
      },
      "155": {
        start: {
          line: 181,
          column: 40
        },
        end: {
          line: 181,
          column: 109
        }
      },
      "156": {
        start: {
          line: 183,
          column: 40
        },
        end: {
          line: 183,
          column: 60
        }
      },
      "157": {
        start: {
          line: 184,
          column: 40
        },
        end: {
          line: 188,
          column: 41
        }
      },
      "158": {
        start: {
          line: 185,
          column: 44
        },
        end: {
          line: 185,
          column: 87
        }
      },
      "159": {
        start: {
          line: 186,
          column: 44
        },
        end: {
          line: 186,
          column: 67
        }
      },
      "160": {
        start: {
          line: 187,
          column: 44
        },
        end: {
          line: 187,
          column: 56
        }
      },
      "161": {
        start: {
          line: 189,
          column: 40
        },
        end: {
          line: 189,
          column: 77
        }
      },
      "162": {
        start: {
          line: 191,
          column: 40
        },
        end: {
          line: 191,
          column: 57
        }
      },
      "163": {
        start: {
          line: 192,
          column: 40
        },
        end: {
          line: 192,
          column: 61
        }
      },
      "164": {
        start: {
          line: 193,
          column: 40
        },
        end: {
          line: 193,
          column: 52
        }
      },
      "165": {
        start: {
          line: 194,
          column: 40
        },
        end: {
          line: 199,
          column: 41
        }
      },
      "166": {
        start: {
          line: 195,
          column: 66
        },
        end: {
          line: 195,
          column: 90
        }
      },
      "167": {
        start: {
          line: 196,
          column: 61
        },
        end: {
          line: 196,
          column: 85
        }
      },
      "168": {
        start: {
          line: 197,
          column: 66
        },
        end: {
          line: 197,
          column: 90
        }
      },
      "169": {
        start: {
          line: 198,
          column: 72
        },
        end: {
          line: 198,
          column: 96
        }
      },
      "170": {
        start: {
          line: 200,
          column: 40
        },
        end: {
          line: 200,
          column: 65
        }
      },
      "171": {
        start: {
          line: 201,
          column: 44
        },
        end: {
          line: 201,
          column: 130
        }
      },
      "172": {
        start: {
          line: 203,
          column: 40
        },
        end: {
          line: 203,
          column: 64
        }
      },
      "173": {
        start: {
          line: 204,
          column: 40
        },
        end: {
          line: 204,
          column: 220
        }
      },
      "174": {
        start: {
          line: 205,
          column: 44
        },
        end: {
          line: 205,
          column: 123
        }
      },
      "175": {
        start: {
          line: 207,
          column: 40
        },
        end: {
          line: 207,
          column: 67
        }
      },
      "176": {
        start: {
          line: 208,
          column: 40
        },
        end: {
          line: 211,
          column: 48
        }
      },
      "177": {
        start: {
          line: 213,
          column: 40
        },
        end: {
          line: 213,
          column: 93
        }
      },
      "178": {
        start: {
          line: 214,
          column: 40
        },
        end: {
          line: 217,
          column: 48
        }
      },
      "179": {
        start: {
          line: 218,
          column: 44
        },
        end: {
          line: 222,
          column: 44
        }
      },
      "180": {
        start: {
          line: 224,
          column: 40
        },
        end: {
          line: 224,
          column: 113
        }
      },
      "181": {
        start: {
          line: 225,
          column: 40
        },
        end: {
          line: 240,
          column: 42
        }
      },
      "182": {
        start: {
          line: 241,
          column: 40
        },
        end: {
          line: 241,
          column: 132
        }
      },
      "183": {
        start: {
          line: 243,
          column: 40
        },
        end: {
          line: 244,
          column: 48
        }
      },
      "184": {
        start: {
          line: 245,
          column: 40
        },
        end: {
          line: 249,
          column: 48
        }
      },
      "185": {
        start: {
          line: 251,
          column: 40
        },
        end: {
          line: 251,
          column: 148
        }
      },
      "186": {
        start: {
          line: 252,
          column: 40
        },
        end: {
          line: 252,
          column: 63
        }
      },
      "187": {
        start: {
          line: 253,
          column: 40
        },
        end: {
          line: 253,
          column: 52
        }
      },
      "188": {
        start: {
          line: 262,
          column: 0
        },
        end: {
          line: 309,
          column: 7
        }
      },
      "189": {
        start: {
          line: 262,
          column: 93
        },
        end: {
          line: 309,
          column: 3
        }
      },
      "190": {
        start: {
          line: 263,
          column: 4
        },
        end: {
          line: 308,
          column: 7
        }
      },
      "191": {
        start: {
          line: 264,
          column: 8
        },
        end: {
          line: 307,
          column: 20
        }
      },
      "192": {
        start: {
          line: 265,
          column: 26
        },
        end: {
          line: 307,
          column: 15
        }
      },
      "193": {
        start: {
          line: 268,
          column: 16
        },
        end: {
          line: 306,
          column: 19
        }
      },
      "194": {
        start: {
          line: 269,
          column: 20
        },
        end: {
          line: 305,
          column: 21
        }
      },
      "195": {
        start: {
          line: 270,
          column: 32
        },
        end: {
          line: 270,
          column: 108
        }
      },
      "196": {
        start: {
          line: 272,
          column: 28
        },
        end: {
          line: 272,
          column: 48
        }
      },
      "197": {
        start: {
          line: 273,
          column: 28
        },
        end: {
          line: 277,
          column: 29
        }
      },
      "198": {
        start: {
          line: 274,
          column: 32
        },
        end: {
          line: 274,
          column: 77
        }
      },
      "199": {
        start: {
          line: 275,
          column: 32
        },
        end: {
          line: 275,
          column: 55
        }
      },
      "200": {
        start: {
          line: 276,
          column: 32
        },
        end: {
          line: 276,
          column: 44
        }
      },
      "201": {
        start: {
          line: 278,
          column: 28
        },
        end: {
          line: 278,
          column: 97
        }
      },
      "202": {
        start: {
          line: 280,
          column: 28
        },
        end: {
          line: 280,
          column: 48
        }
      },
      "203": {
        start: {
          line: 281,
          column: 28
        },
        end: {
          line: 285,
          column: 29
        }
      },
      "204": {
        start: {
          line: 282,
          column: 32
        },
        end: {
          line: 282,
          column: 75
        }
      },
      "205": {
        start: {
          line: 283,
          column: 32
        },
        end: {
          line: 283,
          column: 55
        }
      },
      "206": {
        start: {
          line: 284,
          column: 32
        },
        end: {
          line: 284,
          column: 44
        }
      },
      "207": {
        start: {
          line: 286,
          column: 28
        },
        end: {
          line: 286,
          column: 65
        }
      },
      "208": {
        start: {
          line: 288,
          column: 28
        },
        end: {
          line: 288,
          column: 45
        }
      },
      "209": {
        start: {
          line: 289,
          column: 28
        },
        end: {
          line: 289,
          column: 71
        }
      },
      "210": {
        start: {
          line: 290,
          column: 28
        },
        end: {
          line: 295,
          column: 30
        }
      },
      "211": {
        start: {
          line: 296,
          column: 28
        },
        end: {
          line: 304,
          column: 36
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 2,
            column: 43
          }
        },
        loc: {
          start: {
            line: 2,
            column: 54
          },
          end: {
            line: 12,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 3,
            column: 33
          }
        },
        loc: {
          start: {
            line: 3,
            column: 44
          },
          end: {
            line: 10,
            column: 5
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 13,
            column: 45
          }
        },
        loc: {
          start: {
            line: 13,
            column: 89
          },
          end: {
            line: 21,
            column: 1
          }
        },
        line: 13
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 18
          }
        },
        loc: {
          start: {
            line: 14,
            column: 26
          },
          end: {
            line: 14,
            column: 112
          }
        },
        line: 14
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 14,
            column: 70
          },
          end: {
            line: 14,
            column: 71
          }
        },
        loc: {
          start: {
            line: 14,
            column: 89
          },
          end: {
            line: 14,
            column: 108
          }
        },
        line: 14
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 15,
            column: 36
          },
          end: {
            line: 15,
            column: 37
          }
        },
        loc: {
          start: {
            line: 15,
            column: 63
          },
          end: {
            line: 20,
            column: 5
          }
        },
        line: 15
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 16,
            column: 17
          },
          end: {
            line: 16,
            column: 26
          }
        },
        loc: {
          start: {
            line: 16,
            column: 34
          },
          end: {
            line: 16,
            column: 99
          }
        },
        line: 16
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 25
          }
        },
        loc: {
          start: {
            line: 17,
            column: 33
          },
          end: {
            line: 17,
            column: 102
          }
        },
        line: 17
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 18,
            column: 17
          },
          end: {
            line: 18,
            column: 21
          }
        },
        loc: {
          start: {
            line: 18,
            column: 30
          },
          end: {
            line: 18,
            column: 118
          }
        },
        line: 18
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 22,
            column: 49
          }
        },
        loc: {
          start: {
            line: 22,
            column: 73
          },
          end: {
            line: 48,
            column: 1
          }
        },
        line: 22
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 41
          },
          end: {
            line: 23,
            column: 83
          }
        },
        line: 23
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 24,
            column: 128
          },
          end: {
            line: 24,
            column: 129
          }
        },
        loc: {
          start: {
            line: 24,
            column: 139
          },
          end: {
            line: 24,
            column: 155
          }
        },
        line: 24
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 25,
            column: 13
          },
          end: {
            line: 25,
            column: 17
          }
        },
        loc: {
          start: {
            line: 25,
            column: 21
          },
          end: {
            line: 25,
            column: 70
          }
        },
        line: 25
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 25,
            column: 30
          },
          end: {
            line: 25,
            column: 31
          }
        },
        loc: {
          start: {
            line: 25,
            column: 43
          },
          end: {
            line: 25,
            column: 67
          }
        },
        line: 25
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 26,
            column: 13
          },
          end: {
            line: 26,
            column: 17
          }
        },
        loc: {
          start: {
            line: 26,
            column: 22
          },
          end: {
            line: 47,
            column: 5
          }
        },
        line: 26
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 60,
            column: 72
          },
          end: {
            line: 60,
            column: 73
          }
        },
        loc: {
          start: {
            line: 60,
            column: 91
          },
          end: {
            line: 160,
            column: 5
          }
        },
        line: 60
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 60,
            column: 134
          },
          end: {
            line: 60,
            column: 135
          }
        },
        loc: {
          start: {
            line: 60,
            column: 146
          },
          end: {
            line: 160,
            column: 1
          }
        },
        line: 60
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 61,
            column: 29
          },
          end: {
            line: 61,
            column: 30
          }
        },
        loc: {
          start: {
            line: 61,
            column: 43
          },
          end: {
            line: 159,
            column: 5
          }
        },
        line: 61
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 63,
            column: 12
          },
          end: {
            line: 63,
            column: 13
          }
        },
        loc: {
          start: {
            line: 63,
            column: 24
          },
          end: {
            line: 158,
            column: 17
          }
        },
        line: 63
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 63,
            column: 67
          },
          end: {
            line: 63,
            column: 68
          }
        },
        loc: {
          start: {
            line: 63,
            column: 79
          },
          end: {
            line: 158,
            column: 13
          }
        },
        line: 63
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 66,
            column: 41
          },
          end: {
            line: 66,
            column: 42
          }
        },
        loc: {
          start: {
            line: 66,
            column: 55
          },
          end: {
            line: 157,
            column: 17
          }
        },
        line: 66
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 162,
            column: 73
          },
          end: {
            line: 162,
            column: 74
          }
        },
        loc: {
          start: {
            line: 162,
            column: 92
          },
          end: {
            line: 260,
            column: 5
          }
        },
        line: 162
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 162,
            column: 135
          },
          end: {
            line: 162,
            column: 136
          }
        },
        loc: {
          start: {
            line: 162,
            column: 147
          },
          end: {
            line: 260,
            column: 1
          }
        },
        line: 162
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 163,
            column: 29
          },
          end: {
            line: 163,
            column: 30
          }
        },
        loc: {
          start: {
            line: 163,
            column: 43
          },
          end: {
            line: 259,
            column: 5
          }
        },
        line: 163
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 164,
            column: 70
          },
          end: {
            line: 164,
            column: 71
          }
        },
        loc: {
          start: {
            line: 164,
            column: 82
          },
          end: {
            line: 258,
            column: 17
          }
        },
        line: 164
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 164,
            column: 125
          },
          end: {
            line: 164,
            column: 126
          }
        },
        loc: {
          start: {
            line: 164,
            column: 137
          },
          end: {
            line: 258,
            column: 13
          }
        },
        line: 164
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 165,
            column: 41
          },
          end: {
            line: 165,
            column: 42
          }
        },
        loc: {
          start: {
            line: 165,
            column: 55
          },
          end: {
            line: 257,
            column: 17
          }
        },
        line: 165
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 167,
            column: 24
          },
          end: {
            line: 167,
            column: 25
          }
        },
        loc: {
          start: {
            line: 167,
            column: 36
          },
          end: {
            line: 256,
            column: 29
          }
        },
        line: 167
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 167,
            column: 79
          },
          end: {
            line: 167,
            column: 80
          }
        },
        loc: {
          start: {
            line: 167,
            column: 91
          },
          end: {
            line: 256,
            column: 25
          }
        },
        line: 167
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 171,
            column: 53
          },
          end: {
            line: 171,
            column: 54
          }
        },
        loc: {
          start: {
            line: 171,
            column: 67
          },
          end: {
            line: 255,
            column: 29
          }
        },
        line: 171
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 262,
            column: 72
          },
          end: {
            line: 262,
            column: 73
          }
        },
        loc: {
          start: {
            line: 262,
            column: 91
          },
          end: {
            line: 309,
            column: 5
          }
        },
        line: 262
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 262,
            column: 134
          },
          end: {
            line: 262,
            column: 135
          }
        },
        loc: {
          start: {
            line: 262,
            column: 146
          },
          end: {
            line: 309,
            column: 1
          }
        },
        line: 262
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 263,
            column: 29
          },
          end: {
            line: 263,
            column: 30
          }
        },
        loc: {
          start: {
            line: 263,
            column: 43
          },
          end: {
            line: 308,
            column: 5
          }
        },
        line: 263
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 265,
            column: 12
          },
          end: {
            line: 265,
            column: 13
          }
        },
        loc: {
          start: {
            line: 265,
            column: 24
          },
          end: {
            line: 307,
            column: 17
          }
        },
        line: 265
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 265,
            column: 67
          },
          end: {
            line: 265,
            column: 68
          }
        },
        loc: {
          start: {
            line: 265,
            column: 79
          },
          end: {
            line: 307,
            column: 13
          }
        },
        line: 265
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 268,
            column: 41
          },
          end: {
            line: 268,
            column: 42
          }
        },
        loc: {
          start: {
            line: 268,
            column: 55
          },
          end: {
            line: 306,
            column: 17
          }
        },
        line: 268
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 15
          },
          end: {
            line: 12,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 2,
            column: 20
          }
        }, {
          start: {
            line: 2,
            column: 24
          },
          end: {
            line: 2,
            column: 37
          }
        }, {
          start: {
            line: 2,
            column: 42
          },
          end: {
            line: 12,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 10,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 3,
            column: 15
          },
          end: {
            line: 3,
            column: 28
          }
        }, {
          start: {
            line: 3,
            column: 32
          },
          end: {
            line: 10,
            column: 5
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 6,
            column: 29
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 6
      },
      "3": {
        loc: {
          start: {
            line: 13,
            column: 16
          },
          end: {
            line: 21,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 17
          },
          end: {
            line: 13,
            column: 21
          }
        }, {
          start: {
            line: 13,
            column: 25
          },
          end: {
            line: 13,
            column: 39
          }
        }, {
          start: {
            line: 13,
            column: 44
          },
          end: {
            line: 21,
            column: 1
          }
        }],
        line: 13
      },
      "4": {
        loc: {
          start: {
            line: 14,
            column: 35
          },
          end: {
            line: 14,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 14,
            column: 56
          },
          end: {
            line: 14,
            column: 61
          }
        }, {
          start: {
            line: 14,
            column: 64
          },
          end: {
            line: 14,
            column: 109
          }
        }],
        line: 14
      },
      "5": {
        loc: {
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 15,
            column: 16
          },
          end: {
            line: 15,
            column: 17
          }
        }, {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 15,
            column: 33
          }
        }],
        line: 15
      },
      "6": {
        loc: {
          start: {
            line: 18,
            column: 32
          },
          end: {
            line: 18,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 46
          },
          end: {
            line: 18,
            column: 67
          }
        }, {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "7": {
        loc: {
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 51
          },
          end: {
            line: 19,
            column: 61
          }
        }, {
          start: {
            line: 19,
            column: 65
          },
          end: {
            line: 19,
            column: 67
          }
        }],
        line: 19
      },
      "8": {
        loc: {
          start: {
            line: 22,
            column: 18
          },
          end: {
            line: 48,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 19
          },
          end: {
            line: 22,
            column: 23
          }
        }, {
          start: {
            line: 22,
            column: 27
          },
          end: {
            line: 22,
            column: 43
          }
        }, {
          start: {
            line: 22,
            column: 48
          },
          end: {
            line: 48,
            column: 1
          }
        }],
        line: 22
      },
      "9": {
        loc: {
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 43
          },
          end: {
            line: 23,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 23
      },
      "10": {
        loc: {
          start: {
            line: 23,
            column: 134
          },
          end: {
            line: 23,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 23,
            column: 167
          },
          end: {
            line: 23,
            column: 175
          }
        }, {
          start: {
            line: 23,
            column: 178
          },
          end: {
            line: 23,
            column: 184
          }
        }],
        line: 23
      },
      "11": {
        loc: {
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 74
          },
          end: {
            line: 24,
            column: 102
          }
        }, {
          start: {
            line: 24,
            column: 107
          },
          end: {
            line: 24,
            column: 155
          }
        }],
        line: 24
      },
      "12": {
        loc: {
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 8
          },
          end: {
            line: 27,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "13": {
        loc: {
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 15
          },
          end: {
            line: 28,
            column: 16
          }
        }, {
          start: {
            line: 28,
            column: 21
          },
          end: {
            line: 28,
            column: 44
          }
        }],
        line: 28
      },
      "14": {
        loc: {
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 28
          },
          end: {
            line: 28,
            column: 33
          }
        }, {
          start: {
            line: 28,
            column: 38
          },
          end: {
            line: 28,
            column: 43
          }
        }],
        line: 28
      },
      "15": {
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "16": {
        loc: {
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 23
          },
          end: {
            line: 29,
            column: 24
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 125
          }
        }, {
          start: {
            line: 29,
            column: 130
          },
          end: {
            line: 29,
            column: 158
          }
        }],
        line: 29
      },
      "17": {
        loc: {
          start: {
            line: 29,
            column: 33
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 45
          },
          end: {
            line: 29,
            column: 56
          }
        }, {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "18": {
        loc: {
          start: {
            line: 29,
            column: 59
          },
          end: {
            line: 29,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        }, {
          start: {
            line: 29,
            column: 119
          },
          end: {
            line: 29,
            column: 125
          }
        }],
        line: 29
      },
      "19": {
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 29,
            column: 77
          }
        }, {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 115
          }
        }],
        line: 29
      },
      "20": {
        loc: {
          start: {
            line: 29,
            column: 82
          },
          end: {
            line: 29,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 83
          },
          end: {
            line: 29,
            column: 98
          }
        }, {
          start: {
            line: 29,
            column: 103
          },
          end: {
            line: 29,
            column: 112
          }
        }],
        line: 29
      },
      "21": {
        loc: {
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 12
          },
          end: {
            line: 30,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "22": {
        loc: {
          start: {
            line: 31,
            column: 12
          },
          end: {
            line: 43,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 23
          }
        }, {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 46
          }
        }, {
          start: {
            line: 33,
            column: 16
          },
          end: {
            line: 33,
            column: 72
          }
        }, {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 16
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 42,
            column: 43
          }
        }],
        line: 31
      },
      "23": {
        loc: {
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 37,
            column: 20
          },
          end: {
            line: 37,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 37
      },
      "24": {
        loc: {
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 24
          },
          end: {
            line: 37,
            column: 74
          }
        }, {
          start: {
            line: 37,
            column: 79
          },
          end: {
            line: 37,
            column: 90
          }
        }, {
          start: {
            line: 37,
            column: 94
          },
          end: {
            line: 37,
            column: 105
          }
        }],
        line: 37
      },
      "25": {
        loc: {
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 42
          },
          end: {
            line: 37,
            column: 54
          }
        }, {
          start: {
            line: 37,
            column: 58
          },
          end: {
            line: 37,
            column: 73
          }
        }],
        line: 37
      },
      "26": {
        loc: {
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 20
          },
          end: {
            line: 38,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "27": {
        loc: {
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 38,
            column: 24
          },
          end: {
            line: 38,
            column: 35
          }
        }, {
          start: {
            line: 38,
            column: 40
          },
          end: {
            line: 38,
            column: 42
          }
        }, {
          start: {
            line: 38,
            column: 47
          },
          end: {
            line: 38,
            column: 59
          }
        }, {
          start: {
            line: 38,
            column: 63
          },
          end: {
            line: 38,
            column: 75
          }
        }],
        line: 38
      },
      "28": {
        loc: {
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 39
      },
      "29": {
        loc: {
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 39,
            column: 24
          },
          end: {
            line: 39,
            column: 35
          }
        }, {
          start: {
            line: 39,
            column: 39
          },
          end: {
            line: 39,
            column: 53
          }
        }],
        line: 39
      },
      "30": {
        loc: {
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 40,
            column: 20
          },
          end: {
            line: 40,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 40
      },
      "31": {
        loc: {
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 40,
            column: 24
          },
          end: {
            line: 40,
            column: 25
          }
        }, {
          start: {
            line: 40,
            column: 29
          },
          end: {
            line: 40,
            column: 43
          }
        }],
        line: 40
      },
      "32": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "33": {
        loc: {
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 8
          },
          end: {
            line: 46,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "34": {
        loc: {
          start: {
            line: 46,
            column: 52
          },
          end: {
            line: 46,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 46,
            column: 60
          },
          end: {
            line: 46,
            column: 65
          }
        }, {
          start: {
            line: 46,
            column: 68
          },
          end: {
            line: 46,
            column: 74
          }
        }],
        line: 46
      },
      "35": {
        loc: {
          start: {
            line: 67,
            column: 20
          },
          end: {
            line: 156,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 68,
            column: 24
          },
          end: {
            line: 68,
            column: 108
          }
        }, {
          start: {
            line: 69,
            column: 24
          },
          end: {
            line: 76,
            column: 97
          }
        }, {
          start: {
            line: 77,
            column: 24
          },
          end: {
            line: 95,
            column: 53
          }
        }, {
          start: {
            line: 96,
            column: 24
          },
          end: {
            line: 96,
            column: 111
          }
        }, {
          start: {
            line: 97,
            column: 24
          },
          end: {
            line: 102,
            column: 36
          }
        }, {
          start: {
            line: 103,
            column: 24
          },
          end: {
            line: 103,
            column: 112
          }
        }, {
          start: {
            line: 104,
            column: 24
          },
          end: {
            line: 109,
            column: 36
          }
        }, {
          start: {
            line: 110,
            column: 24
          },
          end: {
            line: 110,
            column: 108
          }
        }, {
          start: {
            line: 111,
            column: 24
          },
          end: {
            line: 116,
            column: 36
          }
        }, {
          start: {
            line: 117,
            column: 24
          },
          end: {
            line: 117,
            column: 117
          }
        }, {
          start: {
            line: 118,
            column: 24
          },
          end: {
            line: 123,
            column: 36
          }
        }, {
          start: {
            line: 124,
            column: 24
          },
          end: {
            line: 124,
            column: 125
          }
        }, {
          start: {
            line: 125,
            column: 24
          },
          end: {
            line: 130,
            column: 36
          }
        }, {
          start: {
            line: 131,
            column: 24
          },
          end: {
            line: 137,
            column: 36
          }
        }, {
          start: {
            line: 138,
            column: 24
          },
          end: {
            line: 143,
            column: 32
          }
        }, {
          start: {
            line: 144,
            column: 24
          },
          end: {
            line: 155,
            column: 36
          }
        }],
        line: 67
      },
      "36": {
        loc: {
          start: {
            line: 71,
            column: 28
          },
          end: {
            line: 75,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 71,
            column: 28
          },
          end: {
            line: 75,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 71
      },
      "37": {
        loc: {
          start: {
            line: 71,
            column: 34
          },
          end: {
            line: 71,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 71,
            column: 132
          },
          end: {
            line: 71,
            column: 138
          }
        }, {
          start: {
            line: 71,
            column: 141
          },
          end: {
            line: 71,
            column: 146
          }
        }],
        line: 71
      },
      "38": {
        loc: {
          start: {
            line: 71,
            column: 34
          },
          end: {
            line: 71,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 71,
            column: 34
          },
          end: {
            line: 71,
            column: 112
          }
        }, {
          start: {
            line: 71,
            column: 116
          },
          end: {
            line: 71,
            column: 129
          }
        }],
        line: 71
      },
      "39": {
        loc: {
          start: {
            line: 71,
            column: 40
          },
          end: {
            line: 71,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 71,
            column: 81
          },
          end: {
            line: 71,
            column: 87
          }
        }, {
          start: {
            line: 71,
            column: 90
          },
          end: {
            line: 71,
            column: 102
          }
        }],
        line: 71
      },
      "40": {
        loc: {
          start: {
            line: 71,
            column: 40
          },
          end: {
            line: 71,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 71,
            column: 40
          },
          end: {
            line: 71,
            column: 56
          }
        }, {
          start: {
            line: 71,
            column: 60
          },
          end: {
            line: 71,
            column: 78
          }
        }],
        line: 71
      },
      "41": {
        loc: {
          start: {
            line: 79,
            column: 28
          },
          end: {
            line: 83,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 79,
            column: 28
          },
          end: {
            line: 83,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 79
      },
      "42": {
        loc: {
          start: {
            line: 87,
            column: 28
          },
          end: {
            line: 94,
            column: 29
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 88,
            column: 32
          },
          end: {
            line: 88,
            column: 70
          }
        }, {
          start: {
            line: 89,
            column: 32
          },
          end: {
            line: 89,
            column: 72
          }
        }, {
          start: {
            line: 90,
            column: 32
          },
          end: {
            line: 90,
            column: 71
          }
        }, {
          start: {
            line: 91,
            column: 32
          },
          end: {
            line: 91,
            column: 76
          }
        }, {
          start: {
            line: 92,
            column: 32
          },
          end: {
            line: 92,
            column: 81
          }
        }, {
          start: {
            line: 93,
            column: 32
          },
          end: {
            line: 93,
            column: 73
          }
        }],
        line: 87
      },
      "43": {
        loc: {
          start: {
            line: 132,
            column: 45
          },
          end: {
            line: 132,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 132,
            column: 45
          },
          end: {
            line: 132,
            column: 70
          }
        }, {
          start: {
            line: 132,
            column: 74
          },
          end: {
            line: 132,
            column: 78
          }
        }],
        line: 132
      },
      "44": {
        loc: {
          start: {
            line: 172,
            column: 32
          },
          end: {
            line: 254,
            column: 33
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 173,
            column: 36
          },
          end: {
            line: 173,
            column: 120
          }
        }, {
          start: {
            line: 174,
            column: 36
          },
          end: {
            line: 181,
            column: 109
          }
        }, {
          start: {
            line: 182,
            column: 36
          },
          end: {
            line: 189,
            column: 77
          }
        }, {
          start: {
            line: 190,
            column: 36
          },
          end: {
            line: 200,
            column: 65
          }
        }, {
          start: {
            line: 201,
            column: 36
          },
          end: {
            line: 201,
            column: 130
          }
        }, {
          start: {
            line: 202,
            column: 36
          },
          end: {
            line: 204,
            column: 220
          }
        }, {
          start: {
            line: 205,
            column: 36
          },
          end: {
            line: 205,
            column: 123
          }
        }, {
          start: {
            line: 206,
            column: 36
          },
          end: {
            line: 211,
            column: 48
          }
        }, {
          start: {
            line: 212,
            column: 36
          },
          end: {
            line: 217,
            column: 48
          }
        }, {
          start: {
            line: 218,
            column: 36
          },
          end: {
            line: 222,
            column: 44
          }
        }, {
          start: {
            line: 223,
            column: 36
          },
          end: {
            line: 241,
            column: 132
          }
        }, {
          start: {
            line: 242,
            column: 36
          },
          end: {
            line: 249,
            column: 48
          }
        }, {
          start: {
            line: 250,
            column: 36
          },
          end: {
            line: 253,
            column: 52
          }
        }],
        line: 172
      },
      "45": {
        loc: {
          start: {
            line: 176,
            column: 40
          },
          end: {
            line: 180,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 176,
            column: 40
          },
          end: {
            line: 180,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 176
      },
      "46": {
        loc: {
          start: {
            line: 176,
            column: 46
          },
          end: {
            line: 176,
            column: 158
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 176,
            column: 144
          },
          end: {
            line: 176,
            column: 150
          }
        }, {
          start: {
            line: 176,
            column: 153
          },
          end: {
            line: 176,
            column: 158
          }
        }],
        line: 176
      },
      "47": {
        loc: {
          start: {
            line: 176,
            column: 46
          },
          end: {
            line: 176,
            column: 141
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 176,
            column: 46
          },
          end: {
            line: 176,
            column: 124
          }
        }, {
          start: {
            line: 176,
            column: 128
          },
          end: {
            line: 176,
            column: 141
          }
        }],
        line: 176
      },
      "48": {
        loc: {
          start: {
            line: 176,
            column: 52
          },
          end: {
            line: 176,
            column: 114
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 176,
            column: 93
          },
          end: {
            line: 176,
            column: 99
          }
        }, {
          start: {
            line: 176,
            column: 102
          },
          end: {
            line: 176,
            column: 114
          }
        }],
        line: 176
      },
      "49": {
        loc: {
          start: {
            line: 176,
            column: 52
          },
          end: {
            line: 176,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 176,
            column: 52
          },
          end: {
            line: 176,
            column: 68
          }
        }, {
          start: {
            line: 176,
            column: 72
          },
          end: {
            line: 176,
            column: 90
          }
        }],
        line: 176
      },
      "50": {
        loc: {
          start: {
            line: 184,
            column: 40
          },
          end: {
            line: 188,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 184,
            column: 40
          },
          end: {
            line: 188,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 184
      },
      "51": {
        loc: {
          start: {
            line: 194,
            column: 40
          },
          end: {
            line: 199,
            column: 41
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 195,
            column: 44
          },
          end: {
            line: 195,
            column: 90
          }
        }, {
          start: {
            line: 196,
            column: 44
          },
          end: {
            line: 196,
            column: 85
          }
        }, {
          start: {
            line: 197,
            column: 44
          },
          end: {
            line: 197,
            column: 90
          }
        }, {
          start: {
            line: 198,
            column: 44
          },
          end: {
            line: 198,
            column: 96
          }
        }],
        line: 194
      },
      "52": {
        loc: {
          start: {
            line: 204,
            column: 163
          },
          end: {
            line: 204,
            column: 215
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 204,
            column: 163
          },
          end: {
            line: 204,
            column: 181
          }
        }, {
          start: {
            line: 204,
            column: 185
          },
          end: {
            line: 204,
            column: 215
          }
        }],
        line: 204
      },
      "53": {
        loc: {
          start: {
            line: 269,
            column: 20
          },
          end: {
            line: 305,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 270,
            column: 24
          },
          end: {
            line: 270,
            column: 108
          }
        }, {
          start: {
            line: 271,
            column: 24
          },
          end: {
            line: 278,
            column: 97
          }
        }, {
          start: {
            line: 279,
            column: 24
          },
          end: {
            line: 286,
            column: 65
          }
        }, {
          start: {
            line: 287,
            column: 24
          },
          end: {
            line: 304,
            column: 36
          }
        }],
        line: 269
      },
      "54": {
        loc: {
          start: {
            line: 273,
            column: 28
          },
          end: {
            line: 277,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 273,
            column: 28
          },
          end: {
            line: 277,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 273
      },
      "55": {
        loc: {
          start: {
            line: 273,
            column: 34
          },
          end: {
            line: 273,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 273,
            column: 132
          },
          end: {
            line: 273,
            column: 138
          }
        }, {
          start: {
            line: 273,
            column: 141
          },
          end: {
            line: 273,
            column: 146
          }
        }],
        line: 273
      },
      "56": {
        loc: {
          start: {
            line: 273,
            column: 34
          },
          end: {
            line: 273,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 273,
            column: 34
          },
          end: {
            line: 273,
            column: 112
          }
        }, {
          start: {
            line: 273,
            column: 116
          },
          end: {
            line: 273,
            column: 129
          }
        }],
        line: 273
      },
      "57": {
        loc: {
          start: {
            line: 273,
            column: 40
          },
          end: {
            line: 273,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 273,
            column: 81
          },
          end: {
            line: 273,
            column: 87
          }
        }, {
          start: {
            line: 273,
            column: 90
          },
          end: {
            line: 273,
            column: 102
          }
        }],
        line: 273
      },
      "58": {
        loc: {
          start: {
            line: 273,
            column: 40
          },
          end: {
            line: 273,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 273,
            column: 40
          },
          end: {
            line: 273,
            column: 56
          }
        }, {
          start: {
            line: 273,
            column: 60
          },
          end: {
            line: 273,
            column: 78
          }
        }],
        line: 273
      },
      "59": {
        loc: {
          start: {
            line: 281,
            column: 28
          },
          end: {
            line: 285,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 281,
            column: 28
          },
          end: {
            line: 285,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 281
      },
      "60": {
        loc: {
          start: {
            line: 291,
            column: 52
          },
          end: {
            line: 291,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 291,
            column: 52
          },
          end: {
            line: 291,
            column: 93
          }
        }, {
          start: {
            line: 291,
            column: 97
          },
          end: {
            line: 291,
            column: 106
          }
        }],
        line: 291
      },
      "61": {
        loc: {
          start: {
            line: 292,
            column: 46
          },
          end: {
            line: 292,
            column: 93
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 292,
            column: 46
          },
          end: {
            line: 292,
            column: 80
          }
        }, {
          start: {
            line: 292,
            column: 84
          },
          end: {
            line: 292,
            column: 93
          }
        }],
        line: 292
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0, 0, 0, 0, 0],
      "43": [0, 0],
      "44": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0, 0, 0],
      "52": [0, 0],
      "53": [0, 0, 0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/admin/database/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAA6C;AAC7C,mCAAyC;AACzC,4EAAqE;AACrE,6CAAgD;AAChD,mCAAgD;AAChD,+CAA+C;AAC/C,6EAAwF;AASxF,uCAAuC;AAC1B,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;QACrE,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,4BAA4B;YAC1E;;;;;gCACkB,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BACnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;gCACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;gCAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAGe,qBAAM,IAAA,wBAAW,EAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAA;;4BAA5C,OAAO,GAAG,SAAkC;4BAClD,IAAI,CAAC,OAAO,EAAE,CAAC;gCACP,KAAK,GAAG,IAAI,KAAK,CAAC,uBAAuB,CAAQ,CAAC;gCACxD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAEO,YAAY,GAAK,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,aAAzB,CAA0B;4BACxC,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;4BAElC,KAAA,MAAM,CAAA;;qCACP,OAAO,CAAC,CAAR,wBAAO;qCAOP,SAAS,CAAC,CAAV,wBAAS;qCAOT,QAAQ,CAAC,CAAT,wBAAQ;qCAOR,aAAa,CAAC,CAAd,wBAAa;qCAOb,iBAAiB,CAAC,CAAlB,yBAAiB;qCAOjB,SAAS,CAAC,CAAV,yBAAS;;;gCAlCE,qBAAM,qCAAc,CAAC,gBAAgB,EAAE,EAAA;;4BAA/C,KAAK,GAAG,SAAuC;4BACrD,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,KAAK;iCACZ,CAAC,EAAC;gCAGmB,qBAAM,qCAAc,CAAC,iBAAiB,EAAE,EAAA;;4BAAxD,aAAa,GAAG,SAAwC;4BAC9D,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,aAAa;iCACpB,CAAC,EAAC;gCAGgB,qBAAM,qCAAc,CAAC,aAAa,EAAE,EAAA;;4BAAjD,UAAU,GAAG,SAAoC;4BACvD,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,UAAU;iCACjB,CAAC,EAAC;gCAGqB,qBAAM,qCAAc,CAAC,sBAAsB,EAAE,EAAA;;4BAA/D,eAAe,GAAG,SAA6C;4BACrE,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,eAAe;iCACtB,CAAC,EAAC;iCAGqB,qBAAM,qCAAc,CAAC,6BAA6B,EAAE,EAAA;;4BAAtE,eAAe,GAAG,SAAoD;4BAC5E,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,eAAe;iCACtB,CAAC,EAAC;;4BAGG,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC;4BACpD,OAAO,GAAG,qCAAc,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;4BACvD,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,OAAO;iCACd,CAAC,EAAC;iCASC,qBAAM,OAAO,CAAC,GAAG,CAAC;gCACpB,qCAAc,CAAC,gBAAgB,EAAE;gCACjC,qCAAc,CAAC,aAAa,EAAE;gCAC9B,qCAAc,CAAC,6BAA6B,EAAE;gCAC9C,OAAO,CAAC,OAAO,CAAC,qCAAc,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC;6BACrD,CAAC,EAAA;;4BAVI,KAKF,SAKF,EATA,aAAa,QAAA,EACb,SAAS,QAAA,EACT,0BAA0B,QAAA,EAC1B,aAAa,QAAA;4BAQf,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE;wCACJ,QAAQ,EAAE,aAAa;wCACvB,MAAM,EAAE,SAAS;wCACjB,eAAe,EAAE,0BAA0B;wCAC3C,aAAa,eAAA;wCACb,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qCACpC;iCACF,CAAC,EAAC;;;iBAER,CACF,EAAC;;KACH,CAAC,CAAC;AAEH,uCAAuC;AAC1B,QAAA,IAAI,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;QACtE,sBAAO,IAAA,yBAAkB,EAAC,OAAO,EAAE;;oBACjC,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,wCAAwC;wBACtF;;;;;;4CACkB,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;wCAA7C,OAAO,GAAG,SAAmC;wCACnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;4CACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;4CAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAGe,qBAAM,IAAA,wBAAW,EAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAA;;wCAA5C,OAAO,GAAG,SAAkC;wCAClD,IAAI,CAAC,OAAO,EAAE,CAAC;4CACP,KAAK,GAAG,IAAI,KAAK,CAAC,uBAAuB,CAAQ,CAAC;4CACxD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;4CACvB,MAAM,KAAK,CAAC;wCACd,CAAC;wCAEY,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;wCAA3B,IAAI,GAAG,SAAoB;wCACzB,MAAM,GAAK,IAAI,OAAT,CAAU;wCAEhB,KAAA,MAAM,CAAA;;iDACP,eAAe,CAAC,CAAhB,wBAAe;iDAQf,UAAU,CAAC,CAAX,wBAAU;iDAOV,eAAe,CAAC,CAAhB,wBAAe;iDAOf,qBAAqB,CAAC,CAAtB,wBAAqB;;;4CArBJ,qBAAM,qCAAc,CAAC,uBAAuB,EAAE,EAAA;;wCAA5D,WAAW,GAAG,SAA8C;wCAClE,sBAAO,qBAAY,CAAC,IAAI,YACtB,OAAO,EAAE,WAAW,CAAC,OAAO,EAC5B,OAAO,EAAE,WAAW,CAAC,OAAO,IACzB,CAAC,WAAW,CAAC,MAAM,IAAI,EAAE,MAAM,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,EACzD,EAAC;4CAGoB,qBAAM,qCAAc,CAAC,gBAAgB,EAAE,EAAA;;wCAAxD,cAAc,GAAG,SAAuC;wCAC9D,sBAAO,qBAAY,CAAC,IAAI,CAAC;gDACvB,OAAO,EAAE,cAAc,CAAC,OAAO;gDAC/B,OAAO,EAAE,cAAc,CAAC,OAAO;6CAChC,CAAC,EAAC;;wCAGH,qCAAc,CAAC,YAAY,EAAE,CAAC;wCAC9B,sBAAO,qBAAY,CAAC,IAAI,CAAC;gDACvB,OAAO,EAAE,IAAI;gDACb,OAAO,EAAE,oCAAoC;6CAC9C,CAAC,EAAC;4CAIwC,qBAAM,OAAO,CAAC,GAAG,CAAC;4CAC3D,qCAAc,CAAC,gBAAgB,EAAE;4CACjC,qCAAc,CAAC,iBAAiB,EAAE;4CAClC,qCAAc,CAAC,aAAa,EAAE;yCAC/B,CAAC,EAAA;;wCAJI,KAAqC,SAIzC,EAJK,KAAK,QAAA,EAAE,aAAa,QAAA,EAAE,UAAU,QAAA;;4CAOrC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;4CACnC,WAAW,EAAE;gDACX,YAAY,EAAE,KAAK,CAAC,YAAY;gDAChC,gBAAgB,EAAE,KAAK,CAAC,WAAW,CAAC,MAAM;gDAC1C,YAAY,EAAE,qCAAc,CAAC,gBAAgB,EAAE,CAAC,MAAM;6CACvD;4CACD,UAAU,EAAE;gDACV,UAAU,EAAE,KAAK,CAAC,UAAU;gDAC5B,cAAc,EAAE,KAAK,CAAC,sBAAsB;gDAC5C,UAAU,EAAE,KAAK,CAAC,kBAAkB;gDACpC,iBAAiB,EAAE,KAAK,CAAC,iBAAiB;6CAC3C;4CACD,OAAO,EAAE,aAAa;4CACtB,MAAM,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;;wCACd,qBAAM,qCAAc,CAAC,6BAA6B,EAAE,EAAA;;wCAfjE,QAAQ,IAeZ,kBAAe,GAAE,SAAoD;+CACtE;wCAED,sBAAO,qBAAY,CAAC,IAAI,CAAC;gDACvB,OAAO,EAAE,IAAI;gDACb,IAAI,EAAE,QAAQ;gDACd,OAAO,EAAE,gCAAgC;6CAC1C,CAAC,EAAC;;wCAGG,KAAK,GAAG,IAAI,KAAK,CAAC,wFAAwF,CAAQ,CAAC;wCACzH,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;wCACvB,MAAM,KAAK,CAAC;;;6BAGjB,CACF,EAAC;;iBACH,CAAC,EAAC;;KACJ,CAAC,CAAC;AAEH,sCAAsC;AACzB,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;QACrE,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAAE,0CAA0C;YACzF;;;;;gCACkB,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BACnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;gCACjB,KAAK,GAAG,IAAI,KAAK,CAAC,yBAAyB,CAAQ,CAAC;gCAC1D,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAGe,qBAAM,IAAA,wBAAW,EAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAA;;4BAA5C,OAAO,GAAG,SAAkC;4BAClD,IAAI,CAAC,OAAO,EAAE,CAAC;gCACP,KAAK,GAAG,IAAI,KAAK,CAAC,uBAAuB,CAAQ,CAAC;gCACxD,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;gCACvB,MAAM,KAAK,CAAC;4BACd,CAAC;4BAEY,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;4BAA3B,IAAI,GAAG,SAAoB;4BACzB,OAAO,GAAY,IAAI,QAAhB,EAAE,KAAK,GAAK,IAAI,MAAT,CAAU;4BAI1B,aAAa,GAAG;gCACpB,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,SAAS;gCAC1E,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,SAAS;gCAC7D,cAAc,EAAE,oBAAoB;gCACpC,YAAY,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS;6BACtC,CAAC;4BAEF,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,OAAO,EAAE,+FAA+F;oCACxG,IAAI,EAAE;wCACJ,OAAO,EAAE,aAAa;wCACtB,SAAS,EAAE,EAAE,OAAO,SAAA,EAAE,KAAK,OAAA,EAAE;wCAC7B,IAAI,EAAE,+EAA+E;qCACtF;iCACF,CAAC,EAAC;;;iBACJ,CACF,EAAC;;KACH,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/admin/database/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { dbOptimization } from '@/lib/services/databaseOptimization';\nimport { withRateLimit } from '@/lib/rateLimit';\nimport { withCSRFProtection } from '@/lib/csrf';\nimport { isUserAdmin } from '@/lib/auth-utils';\nimport { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';\n\ninterface DatabaseResponse {\n  success: boolean;\n  data?: any;\n  message?: string;\n  errors?: any[];\n}\n\n// GET - Database statistics and health\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest) => {\n  return withRateLimit(\n    request,\n    { windowMs: 5 * 60 * 1000, maxRequests: 20 }, // 20 requests per 5 minutes\n    async () => {\n      const session = await getServerSession(authOptions);\n      if (!session?.user?.id) {\n        const error = new Error('Authentication required') as any;\n        error.statusCode = 401;\n        throw error;\n      }\n\n      // Check admin access using proper role-based authorization\n      const isAdmin = await isUserAdmin(session.user.id);\n      if (!isAdmin) {\n        const error = new Error('Admin access required') as any;\n        error.statusCode = 403;\n        throw error;\n      }\n\n      const { searchParams } = new URL(request.url);\n      const action = searchParams.get('action');\n\n      switch (action) {\n        case 'stats':\n          const stats = await dbOptimization.getDatabaseStats();\n          return NextResponse.json({\n            success: true,\n            data: stats\n          });\n\n        case 'indexes':\n          const indexAnalysis = await dbOptimization.analyzeIndexUsage();\n          return NextResponse.json({\n            success: true,\n            data: indexAnalysis\n          });\n\n        case 'tables':\n          const tableSizes = await dbOptimization.getTableSizes();\n          return NextResponse.json({\n            success: true,\n            data: tableSizes\n          });\n\n        case 'connections':\n          const connectionStats = await dbOptimization.getConnectionPoolStats();\n          return NextResponse.json({\n            success: true,\n            data: connectionStats\n          });\n\n        case 'recommendations':\n          const recommendations = await dbOptimization.getPerformanceRecommendations();\n          return NextResponse.json({\n            success: true,\n            data: recommendations\n          });\n\n        case 'metrics':\n          const limit = parseInt(searchParams.get('limit') || '50');\n          const metrics = dbOptimization.getRecentMetrics(limit);\n          return NextResponse.json({\n            success: true,\n            data: metrics\n          });\n\n        default:\n          // Return comprehensive database health report\n          const [\n            databaseStats,\n            tableInfo,\n            performanceRecommendations,\n            recentMetrics\n          ] = await Promise.all([\n            dbOptimization.getDatabaseStats(),\n            dbOptimization.getTableSizes(),\n            dbOptimization.getPerformanceRecommendations(),\n            Promise.resolve(dbOptimization.getRecentMetrics(20))\n          ]);\n\n          return NextResponse.json({\n            success: true,\n            data: {\n              overview: databaseStats,\n              tables: tableInfo,\n              recommendations: performanceRecommendations,\n              recentMetrics,\n              timestamp: new Date().toISOString()\n            }\n          });\n      }\n    }\n  );\n});\n\n// POST - Database optimization actions\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest) => {\n  return withCSRFProtection(request, async () => {\n    return withRateLimit(\n      request,\n      { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 optimization actions per 15 minutes\n      async () => {\n        const session = await getServerSession(authOptions);\n        if (!session?.user?.id) {\n          const error = new Error('Authentication required') as any;\n          error.statusCode = 401;\n          throw error;\n        }\n\n        // Check admin access using proper role-based authorization\n        const isAdmin = await isUserAdmin(session.user.id);\n        if (!isAdmin) {\n          const error = new Error('Admin access required') as any;\n          error.statusCode = 403;\n          throw error;\n        }\n\n        const body = await request.json();\n        const { action } = body;\n\n        switch (action) {\n          case 'apply_indexes':\n            const indexResult = await dbOptimization.applyPerformanceIndexes();\n            return NextResponse.json({\n              success: indexResult.success,\n              message: indexResult.message,\n              ...(indexResult.errors && { errors: indexResult.errors })\n            });\n\n          case 'optimize':\n            const optimizeResult = await dbOptimization.optimizeDatabase();\n            return NextResponse.json({\n              success: optimizeResult.success,\n              message: optimizeResult.message\n            });\n\n          case 'clear_metrics':\n            dbOptimization.clearMetrics();\n            return NextResponse.json({\n              success: true,\n              message: 'Query metrics cleared successfully'\n            });\n\n          case 'analyze_performance':\n            // Run a comprehensive performance analysis\n            const [stats, indexAnalysis, tableSizes] = await Promise.all([\n              dbOptimization.getDatabaseStats(),\n              dbOptimization.analyzeIndexUsage(),\n              dbOptimization.getTableSizes()\n            ]);\n\n            const analysis = {\n              timestamp: new Date().toISOString(),\n              performance: {\n                avgQueryTime: stats.avgQueryTime,\n                slowQueriesCount: stats.slowQueries.length,\n                totalQueries: dbOptimization.getRecentMetrics().length\n              },\n              dataVolume: {\n                totalUsers: stats.totalUsers,\n                totalResources: stats.totalLearningResources,\n                totalPaths: stats.totalLearningPaths,\n                activeEnrollments: stats.activeEnrollments\n              },\n              indexes: indexAnalysis,\n              tables: tableSizes.slice(0, 10), // Top 10 largest tables\n              recommendations: await dbOptimization.getPerformanceRecommendations()\n            };\n\n            return NextResponse.json({\n              success: true,\n              data: analysis,\n              message: 'Performance analysis completed'\n            });\n\n          default:\n            const error = new Error('Invalid action. Available: apply_indexes, optimize, clear_metrics, analyze_performance') as any;\n            error.statusCode = 400;\n            throw error;\n        }\n\n      }\n    );\n  });\n});\n\n// PUT - Update database configuration\nexport const PUT = withUnifiedErrorHandling(async (request: NextRequest) => {\n  return withRateLimit(\n    request,\n    { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 configuration updates per 15 minutes\n    async () => {\n      const session = await getServerSession(authOptions);\n      if (!session?.user?.id) {\n        const error = new Error('Authentication required') as any;\n        error.statusCode = 401;\n        throw error;\n      }\n\n      // Check admin access using proper role-based authorization\n      const isAdmin = await isUserAdmin(session.user.id);\n      if (!isAdmin) {\n        const error = new Error('Admin access required') as any;\n        error.statusCode = 403;\n        throw error;\n      }\n\n      const body = await request.json();\n      const { setting, value } = body;\n\n      // This would typically update database configuration\n      // For now, we'll just return the current configuration\n      const currentConfig = {\n        connectionPoolSize: process.env.DATABASE_CONNECTION_POOL_SIZE || 'default',\n        queryTimeout: process.env.DATABASE_QUERY_TIMEOUT || 'default',\n        maxConnections: 'database-dependent',\n        cacheEnabled: !!process.env.REDIS_URL,\n      };\n\n      return NextResponse.json({\n        success: true,\n        message: 'Database configuration retrieved (update functionality requires environment variable changes)',\n        data: {\n          current: currentConfig,\n          requested: { setting, value },\n          note: 'Configuration changes require server restart and environment variable updates'\n        }\n      });\n    }\n  );\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "42f57ec0bd924d8cf524aae191dfcc83d21822ff"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_bhy9hk6ya = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_bhy9hk6ya();
var __assign =
/* istanbul ignore next */
(cov_bhy9hk6ya().s[0]++,
/* istanbul ignore next */
(cov_bhy9hk6ya().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_bhy9hk6ya().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_bhy9hk6ya().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_bhy9hk6ya().f[0]++;
  cov_bhy9hk6ya().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_bhy9hk6ya().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_bhy9hk6ya().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_bhy9hk6ya().f[1]++;
    cov_bhy9hk6ya().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_bhy9hk6ya().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_bhy9hk6ya().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_bhy9hk6ya().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_bhy9hk6ya().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_bhy9hk6ya().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_bhy9hk6ya().b[2][0]++;
          cov_bhy9hk6ya().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_bhy9hk6ya().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_bhy9hk6ya().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_bhy9hk6ya().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_bhy9hk6ya().s[11]++,
/* istanbul ignore next */
(cov_bhy9hk6ya().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_bhy9hk6ya().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_bhy9hk6ya().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_bhy9hk6ya().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_bhy9hk6ya().f[3]++;
    cov_bhy9hk6ya().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_bhy9hk6ya().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_bhy9hk6ya().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_bhy9hk6ya().f[4]++;
      cov_bhy9hk6ya().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_bhy9hk6ya().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_bhy9hk6ya().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_bhy9hk6ya().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_bhy9hk6ya().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_bhy9hk6ya().f[6]++;
      cov_bhy9hk6ya().s[15]++;
      try {
        /* istanbul ignore next */
        cov_bhy9hk6ya().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_bhy9hk6ya().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_bhy9hk6ya().f[7]++;
      cov_bhy9hk6ya().s[18]++;
      try {
        /* istanbul ignore next */
        cov_bhy9hk6ya().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_bhy9hk6ya().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_bhy9hk6ya().f[8]++;
      cov_bhy9hk6ya().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_bhy9hk6ya().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_bhy9hk6ya().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_bhy9hk6ya().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_bhy9hk6ya().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_bhy9hk6ya().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_bhy9hk6ya().s[23]++,
/* istanbul ignore next */
(cov_bhy9hk6ya().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_bhy9hk6ya().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_bhy9hk6ya().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_bhy9hk6ya().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_bhy9hk6ya().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_bhy9hk6ya().f[10]++;
        cov_bhy9hk6ya().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_bhy9hk6ya().b[9][0]++;
          cov_bhy9hk6ya().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_bhy9hk6ya().b[9][1]++;
        }
        cov_bhy9hk6ya().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_bhy9hk6ya().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_bhy9hk6ya().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_bhy9hk6ya().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_bhy9hk6ya().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_bhy9hk6ya().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_bhy9hk6ya().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_bhy9hk6ya().f[11]++;
    cov_bhy9hk6ya().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_bhy9hk6ya().f[12]++;
    cov_bhy9hk6ya().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_bhy9hk6ya().f[13]++;
      cov_bhy9hk6ya().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_bhy9hk6ya().f[14]++;
    cov_bhy9hk6ya().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_bhy9hk6ya().b[12][0]++;
      cov_bhy9hk6ya().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_bhy9hk6ya().b[12][1]++;
    }
    cov_bhy9hk6ya().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_bhy9hk6ya().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_bhy9hk6ya().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_bhy9hk6ya().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_bhy9hk6ya().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_bhy9hk6ya().s[36]++;
      try {
        /* istanbul ignore next */
        cov_bhy9hk6ya().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_bhy9hk6ya().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_bhy9hk6ya().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_bhy9hk6ya().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_bhy9hk6ya().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_bhy9hk6ya().b[18][0]++,
        /* istanbul ignore next */
        (cov_bhy9hk6ya().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_bhy9hk6ya().b[19][1]++,
        /* istanbul ignore next */
        (cov_bhy9hk6ya().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_bhy9hk6ya().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_bhy9hk6ya().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_bhy9hk6ya().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_bhy9hk6ya().b[15][0]++;
          cov_bhy9hk6ya().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_bhy9hk6ya().b[15][1]++;
        }
        cov_bhy9hk6ya().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_bhy9hk6ya().b[21][0]++;
          cov_bhy9hk6ya().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_bhy9hk6ya().b[21][1]++;
        }
        cov_bhy9hk6ya().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_bhy9hk6ya().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_bhy9hk6ya().b[22][1]++;
            cov_bhy9hk6ya().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_bhy9hk6ya().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_bhy9hk6ya().b[22][2]++;
            cov_bhy9hk6ya().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_bhy9hk6ya().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_bhy9hk6ya().b[22][3]++;
            cov_bhy9hk6ya().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_bhy9hk6ya().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_bhy9hk6ya().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_bhy9hk6ya().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_bhy9hk6ya().b[22][4]++;
            cov_bhy9hk6ya().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_bhy9hk6ya().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_bhy9hk6ya().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_bhy9hk6ya().b[22][5]++;
            cov_bhy9hk6ya().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_bhy9hk6ya().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_bhy9hk6ya().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_bhy9hk6ya().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_bhy9hk6ya().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_bhy9hk6ya().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_bhy9hk6ya().b[23][0]++;
              cov_bhy9hk6ya().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_bhy9hk6ya().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_bhy9hk6ya().b[23][1]++;
            }
            cov_bhy9hk6ya().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_bhy9hk6ya().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_bhy9hk6ya().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_bhy9hk6ya().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_bhy9hk6ya().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_bhy9hk6ya().b[26][0]++;
              cov_bhy9hk6ya().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_bhy9hk6ya().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_bhy9hk6ya().b[26][1]++;
            }
            cov_bhy9hk6ya().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_bhy9hk6ya().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_bhy9hk6ya().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_bhy9hk6ya().b[28][0]++;
              cov_bhy9hk6ya().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_bhy9hk6ya().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_bhy9hk6ya().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_bhy9hk6ya().b[28][1]++;
            }
            cov_bhy9hk6ya().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_bhy9hk6ya().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_bhy9hk6ya().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_bhy9hk6ya().b[30][0]++;
              cov_bhy9hk6ya().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_bhy9hk6ya().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_bhy9hk6ya().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_bhy9hk6ya().b[30][1]++;
            }
            cov_bhy9hk6ya().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_bhy9hk6ya().b[32][0]++;
              cov_bhy9hk6ya().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_bhy9hk6ya().b[32][1]++;
            }
            cov_bhy9hk6ya().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_bhy9hk6ya().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_bhy9hk6ya().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_bhy9hk6ya().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_bhy9hk6ya().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_bhy9hk6ya().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_bhy9hk6ya().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_bhy9hk6ya().b[33][0]++;
      cov_bhy9hk6ya().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_bhy9hk6ya().b[33][1]++;
    }
    cov_bhy9hk6ya().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_bhy9hk6ya().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_bhy9hk6ya().b[34][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_bhy9hk6ya().s[78]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_bhy9hk6ya().s[79]++;
exports.PUT = exports.POST = exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_bhy9hk6ya().s[80]++, require("next/server"));
var next_auth_1 =
/* istanbul ignore next */
(cov_bhy9hk6ya().s[81]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_bhy9hk6ya().s[82]++, require("@/lib/auth"));
var databaseOptimization_1 =
/* istanbul ignore next */
(cov_bhy9hk6ya().s[83]++, require("@/lib/services/databaseOptimization"));
var rateLimit_1 =
/* istanbul ignore next */
(cov_bhy9hk6ya().s[84]++, require("@/lib/rateLimit"));
var csrf_1 =
/* istanbul ignore next */
(cov_bhy9hk6ya().s[85]++, require("@/lib/csrf"));
var auth_utils_1 =
/* istanbul ignore next */
(cov_bhy9hk6ya().s[86]++, require("@/lib/auth-utils"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_bhy9hk6ya().s[87]++, require("@/lib/unified-api-error-handler"));
// GET - Database statistics and health
/* istanbul ignore next */
cov_bhy9hk6ya().s[88]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_bhy9hk6ya().f[15]++;
  cov_bhy9hk6ya().s[89]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_bhy9hk6ya().f[16]++;
    cov_bhy9hk6ya().s[90]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_bhy9hk6ya().f[17]++;
      cov_bhy9hk6ya().s[91]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 5 * 60 * 1000,
        maxRequests: 20
      },
      // 20 requests per 5 minutes
      function () {
        /* istanbul ignore next */
        cov_bhy9hk6ya().f[18]++;
        cov_bhy9hk6ya().s[92]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_bhy9hk6ya().f[19]++;
          var session, error, isAdmin, error, searchParams, action, _a, stats, indexAnalysis, tableSizes, connectionStats, recommendations, limit, metrics, _b, databaseStats, tableInfo, performanceRecommendations, recentMetrics;
          var _c;
          /* istanbul ignore next */
          cov_bhy9hk6ya().s[93]++;
          return __generator(this, function (_d) {
            /* istanbul ignore next */
            cov_bhy9hk6ya().f[20]++;
            cov_bhy9hk6ya().s[94]++;
            switch (_d.label) {
              case 0:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[35][0]++;
                cov_bhy9hk6ya().s[95]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[35][1]++;
                cov_bhy9hk6ya().s[96]++;
                session = _d.sent();
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[97]++;
                if (!(
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[38][0]++, (_c =
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[40][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[40][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[39][0]++, void 0) :
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[39][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[38][1]++, _c === void 0) ?
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[37][0]++, void 0) :
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[37][1]++, _c.id))) {
                  /* istanbul ignore next */
                  cov_bhy9hk6ya().b[36][0]++;
                  cov_bhy9hk6ya().s[98]++;
                  error = new Error('Authentication required');
                  /* istanbul ignore next */
                  cov_bhy9hk6ya().s[99]++;
                  error.statusCode = 401;
                  /* istanbul ignore next */
                  cov_bhy9hk6ya().s[100]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_bhy9hk6ya().b[36][1]++;
                }
                cov_bhy9hk6ya().s[101]++;
                return [4 /*yield*/, (0, auth_utils_1.isUserAdmin)(session.user.id)];
              case 2:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[35][2]++;
                cov_bhy9hk6ya().s[102]++;
                isAdmin = _d.sent();
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[103]++;
                if (!isAdmin) {
                  /* istanbul ignore next */
                  cov_bhy9hk6ya().b[41][0]++;
                  cov_bhy9hk6ya().s[104]++;
                  error = new Error('Admin access required');
                  /* istanbul ignore next */
                  cov_bhy9hk6ya().s[105]++;
                  error.statusCode = 403;
                  /* istanbul ignore next */
                  cov_bhy9hk6ya().s[106]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_bhy9hk6ya().b[41][1]++;
                }
                cov_bhy9hk6ya().s[107]++;
                searchParams = new URL(request.url).searchParams;
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[108]++;
                action = searchParams.get('action');
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[109]++;
                _a = action;
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[110]++;
                switch (_a) {
                  case 'stats':
                    /* istanbul ignore next */
                    cov_bhy9hk6ya().b[42][0]++;
                    cov_bhy9hk6ya().s[111]++;
                    return [3 /*break*/, 3];
                  case 'indexes':
                    /* istanbul ignore next */
                    cov_bhy9hk6ya().b[42][1]++;
                    cov_bhy9hk6ya().s[112]++;
                    return [3 /*break*/, 5];
                  case 'tables':
                    /* istanbul ignore next */
                    cov_bhy9hk6ya().b[42][2]++;
                    cov_bhy9hk6ya().s[113]++;
                    return [3 /*break*/, 7];
                  case 'connections':
                    /* istanbul ignore next */
                    cov_bhy9hk6ya().b[42][3]++;
                    cov_bhy9hk6ya().s[114]++;
                    return [3 /*break*/, 9];
                  case 'recommendations':
                    /* istanbul ignore next */
                    cov_bhy9hk6ya().b[42][4]++;
                    cov_bhy9hk6ya().s[115]++;
                    return [3 /*break*/, 11];
                  case 'metrics':
                    /* istanbul ignore next */
                    cov_bhy9hk6ya().b[42][5]++;
                    cov_bhy9hk6ya().s[116]++;
                    return [3 /*break*/, 13];
                }
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[117]++;
                return [3 /*break*/, 14];
              case 3:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[35][3]++;
                cov_bhy9hk6ya().s[118]++;
                return [4 /*yield*/, databaseOptimization_1.dbOptimization.getDatabaseStats()];
              case 4:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[35][4]++;
                cov_bhy9hk6ya().s[119]++;
                stats = _d.sent();
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[120]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: stats
                })];
              case 5:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[35][5]++;
                cov_bhy9hk6ya().s[121]++;
                return [4 /*yield*/, databaseOptimization_1.dbOptimization.analyzeIndexUsage()];
              case 6:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[35][6]++;
                cov_bhy9hk6ya().s[122]++;
                indexAnalysis = _d.sent();
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[123]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: indexAnalysis
                })];
              case 7:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[35][7]++;
                cov_bhy9hk6ya().s[124]++;
                return [4 /*yield*/, databaseOptimization_1.dbOptimization.getTableSizes()];
              case 8:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[35][8]++;
                cov_bhy9hk6ya().s[125]++;
                tableSizes = _d.sent();
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[126]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: tableSizes
                })];
              case 9:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[35][9]++;
                cov_bhy9hk6ya().s[127]++;
                return [4 /*yield*/, databaseOptimization_1.dbOptimization.getConnectionPoolStats()];
              case 10:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[35][10]++;
                cov_bhy9hk6ya().s[128]++;
                connectionStats = _d.sent();
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[129]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: connectionStats
                })];
              case 11:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[35][11]++;
                cov_bhy9hk6ya().s[130]++;
                return [4 /*yield*/, databaseOptimization_1.dbOptimization.getPerformanceRecommendations()];
              case 12:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[35][12]++;
                cov_bhy9hk6ya().s[131]++;
                recommendations = _d.sent();
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[132]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: recommendations
                })];
              case 13:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[35][13]++;
                cov_bhy9hk6ya().s[133]++;
                limit = parseInt(
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[43][0]++, searchParams.get('limit')) ||
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[43][1]++, '50'));
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[134]++;
                metrics = databaseOptimization_1.dbOptimization.getRecentMetrics(limit);
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[135]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: metrics
                })];
              case 14:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[35][14]++;
                cov_bhy9hk6ya().s[136]++;
                return [4 /*yield*/, Promise.all([databaseOptimization_1.dbOptimization.getDatabaseStats(), databaseOptimization_1.dbOptimization.getTableSizes(), databaseOptimization_1.dbOptimization.getPerformanceRecommendations(), Promise.resolve(databaseOptimization_1.dbOptimization.getRecentMetrics(20))])];
              case 15:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[35][15]++;
                cov_bhy9hk6ya().s[137]++;
                _b = _d.sent(), databaseStats = _b[0], tableInfo = _b[1], performanceRecommendations = _b[2], recentMetrics = _b[3];
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[138]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: {
                    overview: databaseStats,
                    tables: tableInfo,
                    recommendations: performanceRecommendations,
                    recentMetrics: recentMetrics,
                    timestamp: new Date().toISOString()
                  }
                })];
            }
          });
        });
      })];
    });
  });
});
// POST - Database optimization actions
/* istanbul ignore next */
cov_bhy9hk6ya().s[139]++;
exports.POST = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_bhy9hk6ya().f[21]++;
  cov_bhy9hk6ya().s[140]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_bhy9hk6ya().f[22]++;
    cov_bhy9hk6ya().s[141]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_bhy9hk6ya().f[23]++;
      cov_bhy9hk6ya().s[142]++;
      return [2 /*return*/, (0, csrf_1.withCSRFProtection)(request, function () {
        /* istanbul ignore next */
        cov_bhy9hk6ya().f[24]++;
        cov_bhy9hk6ya().s[143]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_bhy9hk6ya().f[25]++;
          cov_bhy9hk6ya().s[144]++;
          return __generator(this, function (_a) {
            /* istanbul ignore next */
            cov_bhy9hk6ya().f[26]++;
            cov_bhy9hk6ya().s[145]++;
            return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
              windowMs: 15 * 60 * 1000,
              maxRequests: 5
            },
            // 5 optimization actions per 15 minutes
            function () {
              /* istanbul ignore next */
              cov_bhy9hk6ya().f[27]++;
              cov_bhy9hk6ya().s[146]++;
              return __awaiter(void 0, void 0, void 0, function () {
                /* istanbul ignore next */
                cov_bhy9hk6ya().f[28]++;
                var session, error, isAdmin, error, body, action, _a, indexResult, optimizeResult, _b, stats, indexAnalysis, tableSizes, analysis, error;
                var _c;
                var _d;
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[147]++;
                return __generator(this, function (_e) {
                  /* istanbul ignore next */
                  cov_bhy9hk6ya().f[29]++;
                  cov_bhy9hk6ya().s[148]++;
                  switch (_e.label) {
                    case 0:
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().b[44][0]++;
                      cov_bhy9hk6ya().s[149]++;
                      return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
                    case 1:
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().b[44][1]++;
                      cov_bhy9hk6ya().s[150]++;
                      session = _e.sent();
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().s[151]++;
                      if (!(
                      /* istanbul ignore next */
                      (cov_bhy9hk6ya().b[47][0]++, (_d =
                      /* istanbul ignore next */
                      (cov_bhy9hk6ya().b[49][0]++, session === null) ||
                      /* istanbul ignore next */
                      (cov_bhy9hk6ya().b[49][1]++, session === void 0) ?
                      /* istanbul ignore next */
                      (cov_bhy9hk6ya().b[48][0]++, void 0) :
                      /* istanbul ignore next */
                      (cov_bhy9hk6ya().b[48][1]++, session.user)) === null) ||
                      /* istanbul ignore next */
                      (cov_bhy9hk6ya().b[47][1]++, _d === void 0) ?
                      /* istanbul ignore next */
                      (cov_bhy9hk6ya().b[46][0]++, void 0) :
                      /* istanbul ignore next */
                      (cov_bhy9hk6ya().b[46][1]++, _d.id))) {
                        /* istanbul ignore next */
                        cov_bhy9hk6ya().b[45][0]++;
                        cov_bhy9hk6ya().s[152]++;
                        error = new Error('Authentication required');
                        /* istanbul ignore next */
                        cov_bhy9hk6ya().s[153]++;
                        error.statusCode = 401;
                        /* istanbul ignore next */
                        cov_bhy9hk6ya().s[154]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_bhy9hk6ya().b[45][1]++;
                      }
                      cov_bhy9hk6ya().s[155]++;
                      return [4 /*yield*/, (0, auth_utils_1.isUserAdmin)(session.user.id)];
                    case 2:
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().b[44][2]++;
                      cov_bhy9hk6ya().s[156]++;
                      isAdmin = _e.sent();
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().s[157]++;
                      if (!isAdmin) {
                        /* istanbul ignore next */
                        cov_bhy9hk6ya().b[50][0]++;
                        cov_bhy9hk6ya().s[158]++;
                        error = new Error('Admin access required');
                        /* istanbul ignore next */
                        cov_bhy9hk6ya().s[159]++;
                        error.statusCode = 403;
                        /* istanbul ignore next */
                        cov_bhy9hk6ya().s[160]++;
                        throw error;
                      } else
                      /* istanbul ignore next */
                      {
                        cov_bhy9hk6ya().b[50][1]++;
                      }
                      cov_bhy9hk6ya().s[161]++;
                      return [4 /*yield*/, request.json()];
                    case 3:
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().b[44][3]++;
                      cov_bhy9hk6ya().s[162]++;
                      body = _e.sent();
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().s[163]++;
                      action = body.action;
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().s[164]++;
                      _a = action;
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().s[165]++;
                      switch (_a) {
                        case 'apply_indexes':
                          /* istanbul ignore next */
                          cov_bhy9hk6ya().b[51][0]++;
                          cov_bhy9hk6ya().s[166]++;
                          return [3 /*break*/, 4];
                        case 'optimize':
                          /* istanbul ignore next */
                          cov_bhy9hk6ya().b[51][1]++;
                          cov_bhy9hk6ya().s[167]++;
                          return [3 /*break*/, 6];
                        case 'clear_metrics':
                          /* istanbul ignore next */
                          cov_bhy9hk6ya().b[51][2]++;
                          cov_bhy9hk6ya().s[168]++;
                          return [3 /*break*/, 8];
                        case 'analyze_performance':
                          /* istanbul ignore next */
                          cov_bhy9hk6ya().b[51][3]++;
                          cov_bhy9hk6ya().s[169]++;
                          return [3 /*break*/, 9];
                      }
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().s[170]++;
                      return [3 /*break*/, 12];
                    case 4:
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().b[44][4]++;
                      cov_bhy9hk6ya().s[171]++;
                      return [4 /*yield*/, databaseOptimization_1.dbOptimization.applyPerformanceIndexes()];
                    case 5:
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().b[44][5]++;
                      cov_bhy9hk6ya().s[172]++;
                      indexResult = _e.sent();
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().s[173]++;
                      return [2 /*return*/, server_1.NextResponse.json(__assign({
                        success: indexResult.success,
                        message: indexResult.message
                      },
                      /* istanbul ignore next */
                      (cov_bhy9hk6ya().b[52][0]++, indexResult.errors) &&
                      /* istanbul ignore next */
                      (cov_bhy9hk6ya().b[52][1]++, {
                        errors: indexResult.errors
                      })))];
                    case 6:
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().b[44][6]++;
                      cov_bhy9hk6ya().s[174]++;
                      return [4 /*yield*/, databaseOptimization_1.dbOptimization.optimizeDatabase()];
                    case 7:
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().b[44][7]++;
                      cov_bhy9hk6ya().s[175]++;
                      optimizeResult = _e.sent();
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().s[176]++;
                      return [2 /*return*/, server_1.NextResponse.json({
                        success: optimizeResult.success,
                        message: optimizeResult.message
                      })];
                    case 8:
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().b[44][8]++;
                      cov_bhy9hk6ya().s[177]++;
                      databaseOptimization_1.dbOptimization.clearMetrics();
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().s[178]++;
                      return [2 /*return*/, server_1.NextResponse.json({
                        success: true,
                        message: 'Query metrics cleared successfully'
                      })];
                    case 9:
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().b[44][9]++;
                      cov_bhy9hk6ya().s[179]++;
                      return [4 /*yield*/, Promise.all([databaseOptimization_1.dbOptimization.getDatabaseStats(), databaseOptimization_1.dbOptimization.analyzeIndexUsage(), databaseOptimization_1.dbOptimization.getTableSizes()])];
                    case 10:
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().b[44][10]++;
                      cov_bhy9hk6ya().s[180]++;
                      _b = _e.sent(), stats = _b[0], indexAnalysis = _b[1], tableSizes = _b[2];
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().s[181]++;
                      _c = {
                        timestamp: new Date().toISOString(),
                        performance: {
                          avgQueryTime: stats.avgQueryTime,
                          slowQueriesCount: stats.slowQueries.length,
                          totalQueries: databaseOptimization_1.dbOptimization.getRecentMetrics().length
                        },
                        dataVolume: {
                          totalUsers: stats.totalUsers,
                          totalResources: stats.totalLearningResources,
                          totalPaths: stats.totalLearningPaths,
                          activeEnrollments: stats.activeEnrollments
                        },
                        indexes: indexAnalysis,
                        tables: tableSizes.slice(0, 10)
                      };
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().s[182]++;
                      return [4 /*yield*/, databaseOptimization_1.dbOptimization.getPerformanceRecommendations()];
                    case 11:
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().b[44][11]++;
                      cov_bhy9hk6ya().s[183]++;
                      analysis = (_c.recommendations = _e.sent(), _c);
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().s[184]++;
                      return [2 /*return*/, server_1.NextResponse.json({
                        success: true,
                        data: analysis,
                        message: 'Performance analysis completed'
                      })];
                    case 12:
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().b[44][12]++;
                      cov_bhy9hk6ya().s[185]++;
                      error = new Error('Invalid action. Available: apply_indexes, optimize, clear_metrics, analyze_performance');
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().s[186]++;
                      error.statusCode = 400;
                      /* istanbul ignore next */
                      cov_bhy9hk6ya().s[187]++;
                      throw error;
                  }
                });
              });
            })];
          });
        });
      })];
    });
  });
});
// PUT - Update database configuration
/* istanbul ignore next */
cov_bhy9hk6ya().s[188]++;
exports.PUT = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_bhy9hk6ya().f[30]++;
  cov_bhy9hk6ya().s[189]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_bhy9hk6ya().f[31]++;
    cov_bhy9hk6ya().s[190]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_bhy9hk6ya().f[32]++;
      cov_bhy9hk6ya().s[191]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: 10
      },
      // 10 configuration updates per 15 minutes
      function () {
        /* istanbul ignore next */
        cov_bhy9hk6ya().f[33]++;
        cov_bhy9hk6ya().s[192]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_bhy9hk6ya().f[34]++;
          var session, error, isAdmin, error, body, setting, value, currentConfig;
          var _a;
          /* istanbul ignore next */
          cov_bhy9hk6ya().s[193]++;
          return __generator(this, function (_b) {
            /* istanbul ignore next */
            cov_bhy9hk6ya().f[35]++;
            cov_bhy9hk6ya().s[194]++;
            switch (_b.label) {
              case 0:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[53][0]++;
                cov_bhy9hk6ya().s[195]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[53][1]++;
                cov_bhy9hk6ya().s[196]++;
                session = _b.sent();
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[197]++;
                if (!(
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[56][0]++, (_a =
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[58][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[58][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[57][0]++, void 0) :
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[57][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[56][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[55][0]++, void 0) :
                /* istanbul ignore next */
                (cov_bhy9hk6ya().b[55][1]++, _a.id))) {
                  /* istanbul ignore next */
                  cov_bhy9hk6ya().b[54][0]++;
                  cov_bhy9hk6ya().s[198]++;
                  error = new Error('Authentication required');
                  /* istanbul ignore next */
                  cov_bhy9hk6ya().s[199]++;
                  error.statusCode = 401;
                  /* istanbul ignore next */
                  cov_bhy9hk6ya().s[200]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_bhy9hk6ya().b[54][1]++;
                }
                cov_bhy9hk6ya().s[201]++;
                return [4 /*yield*/, (0, auth_utils_1.isUserAdmin)(session.user.id)];
              case 2:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[53][2]++;
                cov_bhy9hk6ya().s[202]++;
                isAdmin = _b.sent();
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[203]++;
                if (!isAdmin) {
                  /* istanbul ignore next */
                  cov_bhy9hk6ya().b[59][0]++;
                  cov_bhy9hk6ya().s[204]++;
                  error = new Error('Admin access required');
                  /* istanbul ignore next */
                  cov_bhy9hk6ya().s[205]++;
                  error.statusCode = 403;
                  /* istanbul ignore next */
                  cov_bhy9hk6ya().s[206]++;
                  throw error;
                } else
                /* istanbul ignore next */
                {
                  cov_bhy9hk6ya().b[59][1]++;
                }
                cov_bhy9hk6ya().s[207]++;
                return [4 /*yield*/, request.json()];
              case 3:
                /* istanbul ignore next */
                cov_bhy9hk6ya().b[53][3]++;
                cov_bhy9hk6ya().s[208]++;
                body = _b.sent();
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[209]++;
                setting = body.setting, value = body.value;
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[210]++;
                currentConfig = {
                  connectionPoolSize:
                  /* istanbul ignore next */
                  (cov_bhy9hk6ya().b[60][0]++, process.env.DATABASE_CONNECTION_POOL_SIZE) ||
                  /* istanbul ignore next */
                  (cov_bhy9hk6ya().b[60][1]++, 'default'),
                  queryTimeout:
                  /* istanbul ignore next */
                  (cov_bhy9hk6ya().b[61][0]++, process.env.DATABASE_QUERY_TIMEOUT) ||
                  /* istanbul ignore next */
                  (cov_bhy9hk6ya().b[61][1]++, 'default'),
                  maxConnections: 'database-dependent',
                  cacheEnabled: !!process.env.REDIS_URL
                };
                /* istanbul ignore next */
                cov_bhy9hk6ya().s[211]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  message: 'Database configuration retrieved (update functionality requires environment variable changes)',
                  data: {
                    current: currentConfig,
                    requested: {
                      setting: setting,
                      value: value
                    },
                    note: 'Configuration changes require server restart and environment variable updates'
                  }
                })];
            }
          });
        });
      })];
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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