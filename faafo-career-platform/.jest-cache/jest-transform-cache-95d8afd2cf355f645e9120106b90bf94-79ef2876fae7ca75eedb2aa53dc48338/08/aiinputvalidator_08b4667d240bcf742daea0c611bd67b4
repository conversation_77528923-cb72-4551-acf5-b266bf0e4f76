4c169c8b935102f8ea113c422d924019
"use strict";

/**
 * Comprehensive input validation and security hardening for AI service
 * Protects against malicious inputs, injection attacks, and data integrity issues
 */
/* istanbul ignore next */
function cov_18vb18im9x() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/ai-input-validator.ts";
  var hash = "5435ac71739bd56ff2ea16a01d7cdaa4495abd86";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/ai-input-validator.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 15
        },
        end: {
          line: 16,
          column: 1
        }
      },
      "1": {
        start: {
          line: 7,
          column: 4
        },
        end: {
          line: 14,
          column: 6
        }
      },
      "2": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 12,
          column: 9
        }
      },
      "3": {
        start: {
          line: 8,
          column: 24
        },
        end: {
          line: 8,
          column: 25
        }
      },
      "4": {
        start: {
          line: 8,
          column: 31
        },
        end: {
          line: 8,
          column: 47
        }
      },
      "5": {
        start: {
          line: 9,
          column: 12
        },
        end: {
          line: 9,
          column: 29
        }
      },
      "6": {
        start: {
          line: 10,
          column: 12
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "7": {
        start: {
          line: 10,
          column: 29
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "8": {
        start: {
          line: 11,
          column: 16
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "9": {
        start: {
          line: 13,
          column: 8
        },
        end: {
          line: 13,
          column: 17
        }
      },
      "10": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 43
        }
      },
      "11": {
        start: {
          line: 17,
          column: 22
        },
        end: {
          line: 19,
          column: 1
        }
      },
      "12": {
        start: {
          line: 18,
          column: 4
        },
        end: {
          line: 18,
          column: 62
        }
      },
      "13": {
        start: {
          line: 20,
          column: 0
        },
        end: {
          line: 20,
          column: 62
        }
      },
      "14": {
        start: {
          line: 21,
          column: 0
        },
        end: {
          line: 21,
          column: 34
        }
      },
      "15": {
        start: {
          line: 22,
          column: 29
        },
        end: {
          line: 22,
          column: 77
        }
      },
      "16": {
        start: {
          line: 23,
          column: 38
        },
        end: {
          line: 330,
          column: 3
        }
      },
      "17": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 103,
          column: 6
        }
      },
      "18": {
        start: {
          line: 30,
          column: 8
        },
        end: {
          line: 30,
          column: 49
        }
      },
      "19": {
        start: {
          line: 30,
          column: 34
        },
        end: {
          line: 30,
          column: 47
        }
      },
      "20": {
        start: {
          line: 31,
          column: 21
        },
        end: {
          line: 31,
          column: 23
        }
      },
      "21": {
        start: {
          line: 32,
          column: 23
        },
        end: {
          line: 32,
          column: 25
        }
      },
      "22": {
        start: {
          line: 34,
          column: 19
        },
        end: {
          line: 34,
          column: 143
        }
      },
      "23": {
        start: {
          line: 36,
          column: 8
        },
        end: {
          line: 39,
          column: 9
        }
      },
      "24": {
        start: {
          line: 37,
          column: 12
        },
        end: {
          line: 37,
          column: 60
        }
      },
      "25": {
        start: {
          line: 38,
          column: 12
        },
        end: {
          line: 38,
          column: 74
        }
      },
      "26": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 43,
          column: 9
        }
      },
      "27": {
        start: {
          line: 42,
          column: 12
        },
        end: {
          line: 42,
          column: 94
        }
      },
      "28": {
        start: {
          line: 44,
          column: 8
        },
        end: {
          line: 46,
          column: 9
        }
      },
      "29": {
        start: {
          line: 45,
          column: 12
        },
        end: {
          line: 45,
          column: 88
        }
      },
      "30": {
        start: {
          line: 48,
          column: 8
        },
        end: {
          line: 54,
          column: 9
        }
      },
      "31": {
        start: {
          line: 48,
          column: 22
        },
        end: {
          line: 48,
          column: 23
        }
      },
      "32": {
        start: {
          line: 48,
          column: 30
        },
        end: {
          line: 48,
          column: 53
        }
      },
      "33": {
        start: {
          line: 49,
          column: 26
        },
        end: {
          line: 49,
          column: 32
        }
      },
      "34": {
        start: {
          line: 50,
          column: 12
        },
        end: {
          line: 53,
          column: 13
        }
      },
      "35": {
        start: {
          line: 51,
          column: 16
        },
        end: {
          line: 51,
          column: 76
        }
      },
      "36": {
        start: {
          line: 52,
          column: 16
        },
        end: {
          line: 52,
          column: 22
        }
      },
      "37": {
        start: {
          line: 56,
          column: 8
        },
        end: {
          line: 62,
          column: 9
        }
      },
      "38": {
        start: {
          line: 56,
          column: 22
        },
        end: {
          line: 56,
          column: 23
        }
      },
      "39": {
        start: {
          line: 56,
          column: 30
        },
        end: {
          line: 56,
          column: 54
        }
      },
      "40": {
        start: {
          line: 57,
          column: 26
        },
        end: {
          line: 57,
          column: 32
        }
      },
      "41": {
        start: {
          line: 58,
          column: 12
        },
        end: {
          line: 61,
          column: 13
        }
      },
      "42": {
        start: {
          line: 59,
          column: 16
        },
        end: {
          line: 59,
          column: 68
        }
      },
      "43": {
        start: {
          line: 60,
          column: 16
        },
        end: {
          line: 60,
          column: 22
        }
      },
      "44": {
        start: {
          line: 64,
          column: 8
        },
        end: {
          line: 71,
          column: 9
        }
      },
      "45": {
        start: {
          line: 65,
          column: 12
        },
        end: {
          line: 70,
          column: 13
        }
      },
      "46": {
        start: {
          line: 65,
          column: 26
        },
        end: {
          line: 65,
          column: 27
        }
      },
      "47": {
        start: {
          line: 65,
          column: 34
        },
        end: {
          line: 65,
          column: 53
        }
      },
      "48": {
        start: {
          line: 66,
          column: 30
        },
        end: {
          line: 66,
          column: 36
        }
      },
      "49": {
        start: {
          line: 67,
          column: 16
        },
        end: {
          line: 69,
          column: 17
        }
      },
      "50": {
        start: {
          line: 68,
          column: 20
        },
        end: {
          line: 68,
          column: 73
        }
      },
      "51": {
        start: {
          line: 73,
          column: 8
        },
        end: {
          line: 75,
          column: 9
        }
      },
      "52": {
        start: {
          line: 74,
          column: 12
        },
        end: {
          line: 74,
          column: 82
        }
      },
      "53": {
        start: {
          line: 77,
          column: 29
        },
        end: {
          line: 77,
          column: 34
        }
      },
      "54": {
        start: {
          line: 79,
          column: 8
        },
        end: {
          line: 79,
          column: 89
        }
      },
      "55": {
        start: {
          line: 81,
          column: 8
        },
        end: {
          line: 90,
          column: 9
        }
      },
      "56": {
        start: {
          line: 82,
          column: 12
        },
        end: {
          line: 85,
          column: 15
        }
      },
      "57": {
        start: {
          line: 89,
          column: 12
        },
        end: {
          line: 89,
          column: 85
        }
      },
      "58": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 92,
          column: 68
        }
      },
      "59": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 96,
          column: 9
        }
      },
      "60": {
        start: {
          line: 95,
          column: 12
        },
        end: {
          line: 95,
          column: 74
        }
      },
      "61": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 102,
          column: 10
        }
      },
      "62": {
        start: {
          line: 107,
          column: 4
        },
        end: {
          line: 165,
          column: 6
        }
      },
      "63": {
        start: {
          line: 108,
          column: 21
        },
        end: {
          line: 108,
          column: 23
        }
      },
      "64": {
        start: {
          line: 109,
          column: 23
        },
        end: {
          line: 109,
          column: 25
        }
      },
      "65": {
        start: {
          line: 111,
          column: 32
        },
        end: {
          line: 111,
          column: 113
        }
      },
      "66": {
        start: {
          line: 112,
          column: 8
        },
        end: {
          line: 114,
          column: 9
        }
      },
      "67": {
        start: {
          line: 113,
          column: 12
        },
        end: {
          line: 113,
          column: 48
        }
      },
      "68": {
        start: {
          line: 116,
          column: 36
        },
        end: {
          line: 116,
          column: 94
        }
      },
      "69": {
        start: {
          line: 117,
          column: 8
        },
        end: {
          line: 119,
          column: 9
        }
      },
      "70": {
        start: {
          line: 118,
          column: 12
        },
        end: {
          line: 118,
          column: 52
        }
      },
      "71": {
        start: {
          line: 121,
          column: 32
        },
        end: {
          line: 121,
          column: 82
        }
      },
      "72": {
        start: {
          line: 122,
          column: 8
        },
        end: {
          line: 124,
          column: 9
        }
      },
      "73": {
        start: {
          line: 123,
          column: 12
        },
        end: {
          line: 123,
          column: 52
        }
      },
      "74": {
        start: {
          line: 126,
          column: 8
        },
        end: {
          line: 131,
          column: 9
        }
      },
      "75": {
        start: {
          line: 127,
          column: 24
        },
        end: {
          line: 127,
          column: 46
        }
      },
      "76": {
        start: {
          line: 128,
          column: 12
        },
        end: {
          line: 130,
          column: 13
        }
      },
      "77": {
        start: {
          line: 129,
          column: 16
        },
        end: {
          line: 129,
          column: 71
        }
      },
      "78": {
        start: {
          line: 133,
          column: 27
        },
        end: {
          line: 133,
          column: 106
        }
      },
      "79": {
        start: {
          line: 134,
          column: 8
        },
        end: {
          line: 146,
          column: 9
        }
      },
      "80": {
        start: {
          line: 134,
          column: 22
        },
        end: {
          line: 134,
          column: 23
        }
      },
      "81": {
        start: {
          line: 134,
          column: 42
        },
        end: {
          line: 134,
          column: 54
        }
      },
      "82": {
        start: {
          line: 135,
          column: 24
        },
        end: {
          line: 135,
          column: 42
        }
      },
      "83": {
        start: {
          line: 136,
          column: 12
        },
        end: {
          line: 145,
          column: 13
        }
      },
      "84": {
        start: {
          line: 137,
          column: 33
        },
        end: {
          line: 141,
          column: 18
        }
      },
      "85": {
        start: {
          line: 142,
          column: 16
        },
        end: {
          line: 144,
          column: 17
        }
      },
      "86": {
        start: {
          line: 143,
          column: 20
        },
        end: {
          line: 143,
          column: 101
        }
      },
      "87": {
        start: {
          line: 148,
          column: 8
        },
        end: {
          line: 159,
          column: 9
        }
      },
      "88": {
        start: {
          line: 149,
          column: 12
        },
        end: {
          line: 151,
          column: 13
        }
      },
      "89": {
        start: {
          line: 150,
          column: 16
        },
        end: {
          line: 150,
          column: 65
        }
      },
      "90": {
        start: {
          line: 152,
          column: 12
        },
        end: {
          line: 158,
          column: 13
        }
      },
      "91": {
        start: {
          line: 152,
          column: 26
        },
        end: {
          line: 152,
          column: 27
        }
      },
      "92": {
        start: {
          line: 152,
          column: 34
        },
        end: {
          line: 152,
          column: 51
        }
      },
      "93": {
        start: {
          line: 153,
          column: 27
        },
        end: {
          line: 153,
          column: 33
        }
      },
      "94": {
        start: {
          line: 154,
          column: 16
        },
        end: {
          line: 157,
          column: 17
        }
      },
      "95": {
        start: {
          line: 155,
          column: 20
        },
        end: {
          line: 155,
          column: 54
        }
      },
      "96": {
        start: {
          line: 156,
          column: 20
        },
        end: {
          line: 156,
          column: 26
        }
      },
      "97": {
        start: {
          line: 160,
          column: 8
        },
        end: {
          line: 164,
          column: 10
        }
      },
      "98": {
        start: {
          line: 169,
          column: 4
        },
        end: {
          line: 177,
          column: 6
        }
      },
      "99": {
        start: {
          line: 170,
          column: 8
        },
        end: {
          line: 176,
          column: 11
        }
      },
      "100": {
        start: {
          line: 181,
          column: 4
        },
        end: {
          line: 189,
          column: 6
        }
      },
      "101": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 188,
          column: 11
        }
      },
      "102": {
        start: {
          line: 193,
          column: 4
        },
        end: {
          line: 229,
          column: 6
        }
      },
      "103": {
        start: {
          line: 194,
          column: 21
        },
        end: {
          line: 194,
          column: 23
        }
      },
      "104": {
        start: {
          line: 195,
          column: 23
        },
        end: {
          line: 195,
          column: 25
        }
      },
      "105": {
        start: {
          line: 196,
          column: 8
        },
        end: {
          line: 199,
          column: 9
        }
      },
      "106": {
        start: {
          line: 197,
          column: 12
        },
        end: {
          line: 197,
          column: 63
        }
      },
      "107": {
        start: {
          line: 198,
          column: 12
        },
        end: {
          line: 198,
          column: 74
        }
      },
      "108": {
        start: {
          line: 200,
          column: 8
        },
        end: {
          line: 202,
          column: 9
        }
      },
      "109": {
        start: {
          line: 201,
          column: 12
        },
        end: {
          line: 201,
          column: 63
        }
      },
      "110": {
        start: {
          line: 203,
          column: 8
        },
        end: {
          line: 205,
          column: 9
        }
      },
      "111": {
        start: {
          line: 204,
          column: 12
        },
        end: {
          line: 204,
          column: 65
        }
      },
      "112": {
        start: {
          line: 206,
          column: 30
        },
        end: {
          line: 206,
          column: 32
        }
      },
      "113": {
        start: {
          line: 207,
          column: 8
        },
        end: {
          line: 222,
          column: 9
        }
      },
      "114": {
        start: {
          line: 207,
          column: 22
        },
        end: {
          line: 207,
          column: 23
        }
      },
      "115": {
        start: {
          line: 207,
          column: 36
        },
        end: {
          line: 207,
          column: 42
        }
      },
      "116": {
        start: {
          line: 208,
          column: 24
        },
        end: {
          line: 208,
          column: 36
        }
      },
      "117": {
        start: {
          line: 209,
          column: 29
        },
        end: {
          line: 215,
          column: 14
        }
      },
      "118": {
        start: {
          line: 216,
          column: 12
        },
        end: {
          line: 221,
          column: 13
        }
      },
      "119": {
        start: {
          line: 217,
          column: 16
        },
        end: {
          line: 217,
          column: 64
        }
      },
      "120": {
        start: {
          line: 220,
          column: 16
        },
        end: {
          line: 220,
          column: 71
        }
      },
      "121": {
        start: {
          line: 223,
          column: 8
        },
        end: {
          line: 228,
          column: 10
        }
      },
      "122": {
        start: {
          line: 233,
          column: 4
        },
        end: {
          line: 253,
          column: 6
        }
      },
      "123": {
        start: {
          line: 234,
          column: 21
        },
        end: {
          line: 234,
          column: 23
        }
      },
      "124": {
        start: {
          line: 235,
          column: 23
        },
        end: {
          line: 235,
          column: 25
        }
      },
      "125": {
        start: {
          line: 237,
          column: 8
        },
        end: {
          line: 242,
          column: 9
        }
      },
      "126": {
        start: {
          line: 238,
          column: 12
        },
        end: {
          line: 238,
          column: 43
        }
      },
      "127": {
        start: {
          line: 240,
          column: 13
        },
        end: {
          line: 242,
          column: 9
        }
      },
      "128": {
        start: {
          line: 241,
          column: 12
        },
        end: {
          line: 241,
          column: 44
        }
      },
      "129": {
        start: {
          line: 244,
          column: 32
        },
        end: {
          line: 244,
          column: 121
        }
      },
      "130": {
        start: {
          line: 245,
          column: 8
        },
        end: {
          line: 247,
          column: 9
        }
      },
      "131": {
        start: {
          line: 246,
          column: 12
        },
        end: {
          line: 246,
          column: 48
        }
      },
      "132": {
        start: {
          line: 248,
          column: 8
        },
        end: {
          line: 252,
          column: 10
        }
      },
      "133": {
        start: {
          line: 257,
          column: 4
        },
        end: {
          line: 286,
          column: 6
        }
      },
      "134": {
        start: {
          line: 258,
          column: 22
        },
        end: {
          line: 258,
          column: 24
        }
      },
      "135": {
        start: {
          line: 259,
          column: 24
        },
        end: {
          line: 259,
          column: 29
        }
      },
      "136": {
        start: {
          line: 261,
          column: 8
        },
        end: {
          line: 268,
          column: 9
        }
      },
      "137": {
        start: {
          line: 261,
          column: 22
        },
        end: {
          line: 261,
          column: 23
        }
      },
      "138": {
        start: {
          line: 261,
          column: 30
        },
        end: {
          line: 261,
          column: 53
        }
      },
      "139": {
        start: {
          line: 262,
          column: 26
        },
        end: {
          line: 262,
          column: 32
        }
      },
      "140": {
        start: {
          line: 263,
          column: 12
        },
        end: {
          line: 267,
          column: 13
        }
      },
      "141": {
        start: {
          line: 264,
          column: 16
        },
        end: {
          line: 264,
          column: 66
        }
      },
      "142": {
        start: {
          line: 265,
          column: 16
        },
        end: {
          line: 265,
          column: 35
        }
      },
      "143": {
        start: {
          line: 266,
          column: 16
        },
        end: {
          line: 266,
          column: 22
        }
      },
      "144": {
        start: {
          line: 270,
          column: 8
        },
        end: {
          line: 279,
          column: 9
        }
      },
      "145": {
        start: {
          line: 271,
          column: 12
        },
        end: {
          line: 278,
          column: 13
        }
      },
      "146": {
        start: {
          line: 271,
          column: 26
        },
        end: {
          line: 271,
          column: 27
        }
      },
      "147": {
        start: {
          line: 271,
          column: 34
        },
        end: {
          line: 271,
          column: 58
        }
      },
      "148": {
        start: {
          line: 272,
          column: 30
        },
        end: {
          line: 272,
          column: 36
        }
      },
      "149": {
        start: {
          line: 273,
          column: 16
        },
        end: {
          line: 277,
          column: 17
        }
      },
      "150": {
        start: {
          line: 274,
          column: 20
        },
        end: {
          line: 274,
          column: 72
        }
      },
      "151": {
        start: {
          line: 275,
          column: 20
        },
        end: {
          line: 275,
          column: 41
        }
      },
      "152": {
        start: {
          line: 276,
          column: 20
        },
        end: {
          line: 276,
          column: 26
        }
      },
      "153": {
        start: {
          line: 281,
          column: 8
        },
        end: {
          line: 284,
          column: 9
        }
      },
      "154": {
        start: {
          line: 282,
          column: 12
        },
        end: {
          line: 282,
          column: 51
        }
      },
      "155": {
        start: {
          line: 283,
          column: 12
        },
        end: {
          line: 283,
          column: 67
        }
      },
      "156": {
        start: {
          line: 285,
          column: 8
        },
        end: {
          line: 285,
          column: 58
        }
      },
      "157": {
        start: {
          line: 288,
          column: 4
        },
        end: {
          line: 313,
          column: 6
        }
      },
      "158": {
        start: {
          line: 315,
          column: 4
        },
        end: {
          line: 328,
          column: 6
        }
      },
      "159": {
        start: {
          line: 329,
          column: 4
        },
        end: {
          line: 329,
          column: 28
        }
      },
      "160": {
        start: {
          line: 331,
          column: 0
        },
        end: {
          line: 331,
          column: 44
        }
      },
      "161": {
        start: {
          line: 332,
          column: 0
        },
        end: {
          line: 332,
          column: 35
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 42
          },
          end: {
            line: 6,
            column: 43
          }
        },
        loc: {
          start: {
            line: 6,
            column: 54
          },
          end: {
            line: 16,
            column: 1
          }
        },
        line: 6
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 33
          }
        },
        loc: {
          start: {
            line: 7,
            column: 44
          },
          end: {
            line: 14,
            column: 5
          }
        },
        line: 7
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 17,
            column: 56
          },
          end: {
            line: 17,
            column: 57
          }
        },
        loc: {
          start: {
            line: 17,
            column: 71
          },
          end: {
            line: 19,
            column: 1
          }
        },
        line: 17
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 23,
            column: 38
          },
          end: {
            line: 23,
            column: 39
          }
        },
        loc: {
          start: {
            line: 23,
            column: 50
          },
          end: {
            line: 330,
            column: 1
          }
        },
        line: 23
      },
      "4": {
        name: "AIInputValidator",
        decl: {
          start: {
            line: 24,
            column: 13
          },
          end: {
            line: 24,
            column: 29
          }
        },
        loc: {
          start: {
            line: 24,
            column: 32
          },
          end: {
            line: 25,
            column: 5
          }
        },
        line: 24
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 29,
            column: 41
          },
          end: {
            line: 29,
            column: 42
          }
        },
        loc: {
          start: {
            line: 29,
            column: 67
          },
          end: {
            line: 103,
            column: 5
          }
        },
        line: 29
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 107,
            column: 47
          },
          end: {
            line: 107,
            column: 48
          }
        },
        loc: {
          start: {
            line: 107,
            column: 65
          },
          end: {
            line: 165,
            column: 5
          }
        },
        line: 107
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 169,
            column: 49
          },
          end: {
            line: 169,
            column: 50
          }
        },
        loc: {
          start: {
            line: 169,
            column: 73
          },
          end: {
            line: 177,
            column: 5
          }
        },
        line: 169
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 181,
            column: 42
          },
          end: {
            line: 181,
            column: 43
          }
        },
        loc: {
          start: {
            line: 181,
            column: 64
          },
          end: {
            line: 189,
            column: 5
          }
        },
        line: 181
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 193,
            column: 43
          },
          end: {
            line: 193,
            column: 44
          }
        },
        loc: {
          start: {
            line: 193,
            column: 61
          },
          end: {
            line: 229,
            column: 5
          }
        },
        line: 193
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 233,
            column: 41
          },
          end: {
            line: 233,
            column: 42
          }
        },
        loc: {
          start: {
            line: 233,
            column: 72
          },
          end: {
            line: 253,
            column: 5
          }
        },
        line: 233
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 257,
            column: 36
          },
          end: {
            line: 257,
            column: 37
          }
        },
        loc: {
          start: {
            line: 257,
            column: 53
          },
          end: {
            line: 286,
            column: 5
          }
        },
        line: 257
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 15
          },
          end: {
            line: 16,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 16
          },
          end: {
            line: 6,
            column: 20
          }
        }, {
          start: {
            line: 6,
            column: 24
          },
          end: {
            line: 6,
            column: 37
          }
        }, {
          start: {
            line: 6,
            column: 42
          },
          end: {
            line: 16,
            column: 1
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 14,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 14,
            column: 5
          }
        }],
        line: 7
      },
      "2": {
        loc: {
          start: {
            line: 10,
            column: 29
          },
          end: {
            line: 11,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 10,
            column: 29
          },
          end: {
            line: 11,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 10
      },
      "3": {
        loc: {
          start: {
            line: 17,
            column: 22
          },
          end: {
            line: 19,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 23
          },
          end: {
            line: 17,
            column: 27
          }
        }, {
          start: {
            line: 17,
            column: 31
          },
          end: {
            line: 17,
            column: 51
          }
        }, {
          start: {
            line: 17,
            column: 56
          },
          end: {
            line: 19,
            column: 1
          }
        }],
        line: 17
      },
      "4": {
        loc: {
          start: {
            line: 18,
            column: 11
          },
          end: {
            line: 18,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 37
          },
          end: {
            line: 18,
            column: 40
          }
        }, {
          start: {
            line: 18,
            column: 43
          },
          end: {
            line: 18,
            column: 61
          }
        }],
        line: 18
      },
      "5": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 15
          }
        }, {
          start: {
            line: 18,
            column: 19
          },
          end: {
            line: 18,
            column: 33
          }
        }],
        line: 18
      },
      "6": {
        loc: {
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 30,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 8
          },
          end: {
            line: 30,
            column: 49
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "7": {
        loc: {
          start: {
            line: 36,
            column: 8
          },
          end: {
            line: 39,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 36,
            column: 8
          },
          end: {
            line: 39,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 36
      },
      "8": {
        loc: {
          start: {
            line: 36,
            column: 12
          },
          end: {
            line: 36,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 36,
            column: 12
          },
          end: {
            line: 36,
            column: 18
          }
        }, {
          start: {
            line: 36,
            column: 22
          },
          end: {
            line: 36,
            column: 47
          }
        }],
        line: 36
      },
      "9": {
        loc: {
          start: {
            line: 41,
            column: 8
          },
          end: {
            line: 43,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 8
          },
          end: {
            line: 43,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "10": {
        loc: {
          start: {
            line: 44,
            column: 8
          },
          end: {
            line: 46,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 8
          },
          end: {
            line: 46,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "11": {
        loc: {
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 53,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 12
          },
          end: {
            line: 53,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "12": {
        loc: {
          start: {
            line: 58,
            column: 12
          },
          end: {
            line: 61,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 58,
            column: 12
          },
          end: {
            line: 61,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 58
      },
      "13": {
        loc: {
          start: {
            line: 64,
            column: 8
          },
          end: {
            line: 71,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 64,
            column: 8
          },
          end: {
            line: 71,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 64
      },
      "14": {
        loc: {
          start: {
            line: 67,
            column: 16
          },
          end: {
            line: 69,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 67,
            column: 16
          },
          end: {
            line: 69,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 67
      },
      "15": {
        loc: {
          start: {
            line: 73,
            column: 8
          },
          end: {
            line: 75,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 73,
            column: 8
          },
          end: {
            line: 75,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 73
      },
      "16": {
        loc: {
          start: {
            line: 73,
            column: 12
          },
          end: {
            line: 73,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 73,
            column: 12
          },
          end: {
            line: 73,
            column: 36
          }
        }, {
          start: {
            line: 73,
            column: 40
          },
          end: {
            line: 73,
            column: 66
          }
        }],
        line: 73
      },
      "17": {
        loc: {
          start: {
            line: 81,
            column: 8
          },
          end: {
            line: 90,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 81,
            column: 8
          },
          end: {
            line: 90,
            column: 9
          }
        }, {
          start: {
            line: 87,
            column: 13
          },
          end: {
            line: 90,
            column: 9
          }
        }],
        line: 81
      },
      "18": {
        loc: {
          start: {
            line: 94,
            column: 8
          },
          end: {
            line: 96,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 94,
            column: 8
          },
          end: {
            line: 96,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 94
      },
      "19": {
        loc: {
          start: {
            line: 112,
            column: 8
          },
          end: {
            line: 114,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 112,
            column: 8
          },
          end: {
            line: 114,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 112
      },
      "20": {
        loc: {
          start: {
            line: 112,
            column: 12
          },
          end: {
            line: 112,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 112,
            column: 12
          },
          end: {
            line: 112,
            column: 30
          }
        }, {
          start: {
            line: 112,
            column: 34
          },
          end: {
            line: 112,
            column: 81
          }
        }],
        line: 112
      },
      "21": {
        loc: {
          start: {
            line: 117,
            column: 8
          },
          end: {
            line: 119,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 117,
            column: 8
          },
          end: {
            line: 119,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 117
      },
      "22": {
        loc: {
          start: {
            line: 117,
            column: 12
          },
          end: {
            line: 117,
            column: 93
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 117,
            column: 12
          },
          end: {
            line: 117,
            column: 34
          }
        }, {
          start: {
            line: 117,
            column: 38
          },
          end: {
            line: 117,
            column: 93
          }
        }],
        line: 117
      },
      "23": {
        loc: {
          start: {
            line: 122,
            column: 8
          },
          end: {
            line: 124,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 122,
            column: 8
          },
          end: {
            line: 124,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 122
      },
      "24": {
        loc: {
          start: {
            line: 122,
            column: 12
          },
          end: {
            line: 122,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 122,
            column: 12
          },
          end: {
            line: 122,
            column: 29
          }
        }, {
          start: {
            line: 122,
            column: 33
          },
          end: {
            line: 122,
            column: 79
          }
        }],
        line: 122
      },
      "25": {
        loc: {
          start: {
            line: 126,
            column: 8
          },
          end: {
            line: 131,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 126,
            column: 8
          },
          end: {
            line: 131,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 126
      },
      "26": {
        loc: {
          start: {
            line: 128,
            column: 12
          },
          end: {
            line: 130,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 128,
            column: 12
          },
          end: {
            line: 130,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 128
      },
      "27": {
        loc: {
          start: {
            line: 128,
            column: 16
          },
          end: {
            line: 128,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 128,
            column: 16
          },
          end: {
            line: 128,
            column: 28
          }
        }, {
          start: {
            line: 128,
            column: 32
          },
          end: {
            line: 128,
            column: 41
          }
        }, {
          start: {
            line: 128,
            column: 45
          },
          end: {
            line: 128,
            column: 55
          }
        }],
        line: 128
      },
      "28": {
        loc: {
          start: {
            line: 136,
            column: 12
          },
          end: {
            line: 145,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 12
          },
          end: {
            line: 145,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 136
      },
      "29": {
        loc: {
          start: {
            line: 142,
            column: 16
          },
          end: {
            line: 144,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 142,
            column: 16
          },
          end: {
            line: 144,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 142
      },
      "30": {
        loc: {
          start: {
            line: 148,
            column: 8
          },
          end: {
            line: 159,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 148,
            column: 8
          },
          end: {
            line: 159,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 148
      },
      "31": {
        loc: {
          start: {
            line: 148,
            column: 12
          },
          end: {
            line: 148,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 148,
            column: 12
          },
          end: {
            line: 148,
            column: 29
          }
        }, {
          start: {
            line: 148,
            column: 33
          },
          end: {
            line: 148,
            column: 65
          }
        }],
        line: 148
      },
      "32": {
        loc: {
          start: {
            line: 149,
            column: 12
          },
          end: {
            line: 151,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 149,
            column: 12
          },
          end: {
            line: 151,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 149
      },
      "33": {
        loc: {
          start: {
            line: 154,
            column: 16
          },
          end: {
            line: 157,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 154,
            column: 16
          },
          end: {
            line: 157,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 154
      },
      "34": {
        loc: {
          start: {
            line: 154,
            column: 20
          },
          end: {
            line: 154,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 154,
            column: 20
          },
          end: {
            line: 154,
            column: 44
          }
        }, {
          start: {
            line: 154,
            column: 48
          },
          end: {
            line: 154,
            column: 65
          }
        }],
        line: 154
      },
      "35": {
        loc: {
          start: {
            line: 196,
            column: 8
          },
          end: {
            line: 199,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 196,
            column: 8
          },
          end: {
            line: 199,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 196
      },
      "36": {
        loc: {
          start: {
            line: 200,
            column: 8
          },
          end: {
            line: 202,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 200,
            column: 8
          },
          end: {
            line: 202,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 200
      },
      "37": {
        loc: {
          start: {
            line: 203,
            column: 8
          },
          end: {
            line: 205,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 203,
            column: 8
          },
          end: {
            line: 205,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 203
      },
      "38": {
        loc: {
          start: {
            line: 216,
            column: 12
          },
          end: {
            line: 221,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 216,
            column: 12
          },
          end: {
            line: 221,
            column: 13
          }
        }, {
          start: {
            line: 219,
            column: 17
          },
          end: {
            line: 221,
            column: 13
          }
        }],
        line: 216
      },
      "39": {
        loc: {
          start: {
            line: 216,
            column: 16
          },
          end: {
            line: 216,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 216,
            column: 16
          },
          end: {
            line: 216,
            column: 34
          }
        }, {
          start: {
            line: 216,
            column: 38
          },
          end: {
            line: 216,
            column: 63
          }
        }],
        line: 216
      },
      "40": {
        loc: {
          start: {
            line: 224,
            column: 21
          },
          end: {
            line: 224,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 224,
            column: 21
          },
          end: {
            line: 224,
            column: 40
          }
        }, {
          start: {
            line: 224,
            column: 44
          },
          end: {
            line: 224,
            column: 70
          }
        }],
        line: 224
      },
      "41": {
        loc: {
          start: {
            line: 237,
            column: 8
          },
          end: {
            line: 242,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 237,
            column: 8
          },
          end: {
            line: 242,
            column: 9
          }
        }, {
          start: {
            line: 240,
            column: 13
          },
          end: {
            line: 242,
            column: 9
          }
        }],
        line: 237
      },
      "42": {
        loc: {
          start: {
            line: 237,
            column: 12
          },
          end: {
            line: 237,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 237,
            column: 12
          },
          end: {
            line: 237,
            column: 19
          }
        }, {
          start: {
            line: 237,
            column: 23
          },
          end: {
            line: 237,
            column: 49
          }
        }],
        line: 237
      },
      "43": {
        loc: {
          start: {
            line: 240,
            column: 13
          },
          end: {
            line: 242,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 240,
            column: 13
          },
          end: {
            line: 242,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 240
      },
      "44": {
        loc: {
          start: {
            line: 245,
            column: 8
          },
          end: {
            line: 247,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 8
          },
          end: {
            line: 247,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 245
      },
      "45": {
        loc: {
          start: {
            line: 263,
            column: 12
          },
          end: {
            line: 267,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 12
          },
          end: {
            line: 267,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 263
      },
      "46": {
        loc: {
          start: {
            line: 270,
            column: 8
          },
          end: {
            line: 279,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 270,
            column: 8
          },
          end: {
            line: 279,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 270
      },
      "47": {
        loc: {
          start: {
            line: 273,
            column: 16
          },
          end: {
            line: 277,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 273,
            column: 16
          },
          end: {
            line: 277,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 273
      },
      "48": {
        loc: {
          start: {
            line: 281,
            column: 8
          },
          end: {
            line: 284,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 281,
            column: 8
          },
          end: {
            line: 284,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 281
      },
      "49": {
        loc: {
          start: {
            line: 283,
            column: 24
          },
          end: {
            line: 283,
            column: 66
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 283,
            column: 46
          },
          end: {
            line: 283,
            column: 54
          }
        }, {
          start: {
            line: 283,
            column: 57
          },
          end: {
            line: 283,
            column: 66
          }
        }],
        line: 283
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/ai-input-validator.ts",
      mappings: ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;AAEH,8EAA6C;AAkB7C;IAAA;IAiWA,CAAC;IA3SC;;OAEG;IACI,kCAAiB,GAAxB,UACE,KAAa,EACb,OAA+B;QAA/B,wBAAA,EAAA,YAA+B;QAE/B,IAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,sBAAsB;QACtB,IAAM,IAAI,cACR,SAAS,EAAE,KAAK,EAChB,SAAS,EAAE,CAAC,EACZ,SAAS,EAAE,KAAK,EAChB,iBAAiB,EAAE,IAAI,EACvB,mBAAmB,EAAE,KAAK,IACvB,OAAO,CACX,CAAC;QAEF,mBAAmB;QACnB,IAAI,CAAC,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YACxC,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAChD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,QAAA,EAAE,QAAQ,UAAA,EAAE,CAAC;QAC9C,CAAC;QAED,oBAAoB;QACpB,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,iCAA0B,IAAI,CAAC,SAAS,qBAAkB,CAAC,CAAC;QAC1E,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAClC,MAAM,CAAC,IAAI,CAAC,gCAAyB,IAAI,CAAC,SAAS,gBAAa,CAAC,CAAC;QACpE,CAAC;QAED,+BAA+B;QAC/B,KAAsB,UAAuB,EAAvB,KAAA,IAAI,CAAC,kBAAkB,EAAvB,cAAuB,EAAvB,IAAuB,EAAE,CAAC;YAA3C,IAAM,OAAO,SAAA;YAChB,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxB,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;gBAC5D,MAAM;YACR,CAAC;QACH,CAAC;QAED,gCAAgC;QAChC,KAAsB,UAAwB,EAAxB,KAAA,IAAI,CAAC,mBAAmB,EAAxB,cAAwB,EAAxB,IAAwB,EAAE,CAAC;YAA5C,IAAM,OAAO,SAAA;YAChB,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxB,QAAQ,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;gBACpD,MAAM;YACR,CAAC;QACH,CAAC;QAED,4BAA4B;QAC5B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;YACxB,KAAsB,UAAmB,EAAnB,KAAA,IAAI,CAAC,cAAc,EAAnB,cAAmB,EAAnB,IAAmB,EAAE,CAAC;gBAAvC,IAAM,OAAO,SAAA;gBAChB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBACzB,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;gBACvD,CAAC;YACH,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,IAAI,IAAI,CAAC,mBAAmB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC3D,MAAM,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;QACxE,CAAC;QAED,iBAAiB;QACjB,IAAI,cAAc,GAAG,KAAK,CAAC;QAE3B,2CAA2C;QAC3C,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,mCAAmC,EAAE,EAAE,CAAC,CAAC;QAEjF,mCAAmC;QACnC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,cAAc,GAAG,8BAAS,CAAC,QAAQ,CAAC,cAAc,EAAE;gBAClD,YAAY,EAAE,EAAE;gBAChB,YAAY,EAAE,EAAE;aACjB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,0BAA0B;YAC1B,cAAc,GAAG,8BAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;QACtD,CAAC;QAED,uBAAuB;QACvB,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QAE5D,6BAA6B;QAC7B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC5B,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QAChE,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,cAAc,gBAAA;YACd,MAAM,QAAA;YACN,QAAQ,UAAA;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,wCAAuB,GAA9B,UAA+B,MAAW;QACxC,IAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,wBAAwB;QACxB,IAAM,iBAAiB,GAAG,CAAC,oBAAoB,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,gBAAgB,CAAC,CAAC;QAC5G,IAAI,MAAM,CAAC,WAAW,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC;YAC1E,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACtC,CAAC;QAED,4BAA4B;QAC5B,IAAM,qBAAqB,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;QACzF,IAAI,MAAM,CAAC,eAAe,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC;YACtF,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC1C,CAAC;QAED,sBAAsB;QACtB,IAAM,iBAAiB,GAAG,CAAC,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC7E,IAAI,MAAM,CAAC,UAAU,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;YACxE,MAAM,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAC1C,CAAC;QAED,iBAAiB;QACjB,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,IAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACrC,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC;gBAC5C,MAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;QAED,6BAA6B;QAC7B,IAAM,YAAY,GAAG,CAAC,YAAY,EAAE,aAAa,EAAE,eAAe,EAAE,cAAc,EAAE,eAAe,CAAC,CAAC;QACrG,KAAoB,UAAY,EAAZ,6BAAY,EAAZ,0BAAY,EAAZ,IAAY,EAAE,CAAC;YAA9B,IAAM,KAAK,qBAAA;YACd,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;gBAClB,IAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;oBACvD,SAAS,EAAE,GAAG;oBACd,SAAS,EAAE,KAAK;oBAChB,iBAAiB,EAAE,IAAI;iBACxB,CAAC,CAAC;gBACH,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;oBACxB,MAAM,CAAC,IAAI,CAAC,kBAAW,KAAK,eAAK,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CAAC;gBACnE,CAAC;YACH,CAAC;QACH,CAAC;QAED,6BAA6B;QAC7B,IAAI,MAAM,CAAC,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC;YAC1D,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;gBAClC,MAAM,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;YACnD,CAAC;YACD,KAAmB,UAAiB,EAAjB,KAAA,MAAM,CAAC,UAAU,EAAjB,cAAiB,EAAjB,IAAiB,EAAE,CAAC;gBAAlC,IAAM,IAAI,SAAA;gBACb,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;oBAClD,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;oBAClC,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM,QAAA;YACN,QAAQ,UAAA;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,0CAAyB,GAAhC,UAAiC,YAAoB;QACnD,OAAO,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE;YAC1C,SAAS,EAAE,IAAI;YACf,SAAS,EAAE,EAAE;YACb,SAAS,EAAE,KAAK;YAChB,iBAAiB,EAAE,IAAI;YACvB,mBAAmB,EAAE,IAAI;SAC1B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,mCAAkB,GAAzB,UAA0B,UAAkB;QAC1C,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE;YACxC,SAAS,EAAE,KAAK;YAChB,SAAS,EAAE,GAAG;YACd,SAAS,EAAE,KAAK;YAChB,iBAAiB,EAAE,IAAI;YACvB,mBAAmB,EAAE,IAAI;SAC1B,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,oCAAmB,GAA1B,UAA2B,MAAgB;QACzC,IAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3B,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACnD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,QAAA,EAAE,QAAQ,UAAA,EAAE,CAAC;QAC9C,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACxB,MAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YACvB,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;QACvD,CAAC;QAED,IAAM,eAAe,GAAa,EAAE,CAAC;QACrC,KAAoB,UAAM,EAAN,iBAAM,EAAN,oBAAM,EAAN,IAAM,EAAE,CAAC;YAAxB,IAAM,KAAK,eAAA;YACd,IAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE;gBAC/C,SAAS,EAAE,GAAG;gBACd,SAAS,EAAE,CAAC;gBACZ,SAAS,EAAE,KAAK;gBAChB,iBAAiB,EAAE,KAAK;gBACxB,mBAAmB,EAAE,IAAI;aAC1B,CAAC,CAAC;YAEH,IAAI,UAAU,CAAC,OAAO,IAAI,UAAU,CAAC,cAAc,EAAE,CAAC;gBACpD,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;YAClD,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,IAAI,CAAC,iCAA0B,KAAK,CAAE,CAAC,CAAC;YACnD,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC;YAC1D,cAAc,EAAE,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,mCAAmC;YAC9E,MAAM,QAAA;YACN,QAAQ,UAAA;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,kCAAiB,GAAxB,UAAyB,MAAc,EAAE,WAAmB;QAC1D,IAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,0BAA0B;QAC1B,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACjC,CAAC;aAAM,IAAI,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YAC/B,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QAClC,CAAC;QAED,wBAAwB;QACxB,IAAM,iBAAiB,GAAG,CAAC,qBAAqB,EAAE,mBAAmB,EAAE,iBAAiB,EAAE,wBAAwB,CAAC,CAAC;QACpH,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YAC7C,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;QACtC,CAAC;QAED,OAAO;YACL,OAAO,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC5B,MAAM,QAAA;YACN,QAAQ,UAAA;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,6BAAY,GAAnB,UAAoB,KAAa;QAC/B,IAAM,OAAO,GAAa,EAAE,CAAC;QAC7B,IAAI,SAAS,GAA8B,KAAK,CAAC;QAEjD,+BAA+B;QAC/B,KAAsB,UAAuB,EAAvB,KAAA,IAAI,CAAC,kBAAkB,EAAvB,cAAuB,EAAvB,IAAuB,EAAE,CAAC;YAA3C,IAAM,OAAO,SAAA;YAChB,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxB,OAAO,CAAC,IAAI,CAAC,mCAAmC,CAAC,CAAC;gBAClD,SAAS,GAAG,MAAM,CAAC;gBACnB,MAAM;YACR,CAAC;QACH,CAAC;QAED,iCAAiC;QACjC,IAAI,SAAS,KAAK,MAAM,EAAE,CAAC;YACzB,KAAsB,UAAwB,EAAxB,KAAA,IAAI,CAAC,mBAAmB,EAAxB,cAAwB,EAAxB,IAAwB,EAAE,CAAC;gBAA5C,IAAM,OAAO,SAAA;gBAChB,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;oBACxB,OAAO,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;oBACpD,SAAS,GAAG,QAAQ,CAAC;oBACrB,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,6BAA6B;QAC7B,IAAI,KAAK,CAAC,MAAM,GAAG,KAAK,EAAE,CAAC;YACzB,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YACvC,SAAS,GAAG,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC;QACzD,CAAC;QAED,OAAO,EAAE,OAAO,SAAA,EAAE,SAAS,WAAA,EAAE,CAAC;IAChC,CAAC;IA/VD,4CAA4C;IACpB,mCAAkB,GAAG;QAC3C,mBAAmB;QACnB,qDAAqD;QACrD,eAAe;QACf,aAAa;QACb,mBAAmB;QAEnB,iBAAiB;QACjB,aAAa;QAEb,kEAAkE;QAClE,+HAA+H;QAE/H,6CAA6C;QAC7C,oDAAoD;QAEpD,iBAAiB;QACjB,SAAS;QACT,WAAW;QAEX,eAAe;QACf,WAAW;QACX,WAAW;QACX,UAAU;QACV,SAAS;QACT,SAAS;QAET,uBAAuB;QACvB,SAAS;QACT,QAAQ;QACR,SAAS;KACV,CAAC;IAEF,8BAA8B;IACN,oCAAmB,GAAG;QAC5C,uBAAuB;QACvB,aAAa;QAEb,+BAA+B;QAC/B,6CAA6C;QAE7C,6BAA6B;QAC7B,oDAAoD;QACpD,iBAAiB;QACjB,oBAAoB;QACpB,gBAAgB;QAEhB,qCAAqC;QACrC,qDAAqD;QACrD,uDAAuD;KACxD,CAAC;IA6SJ,uBAAC;CAAA,AAjWD,IAiWC;AAjWY,4CAAgB;AAmW7B,kBAAe,gBAAgB,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/ai-input-validator.ts"],
      sourcesContent: ["/**\n * Comprehensive input validation and security hardening for AI service\n * Protects against malicious inputs, injection attacks, and data integrity issues\n */\n\nimport DOMPurify from 'isomorphic-dompurify';\n\ninterface ValidationResult {\n  isValid: boolean;\n  sanitizedInput?: string;\n  errors: string[];\n  warnings: string[];\n}\n\ninterface ValidationOptions {\n  maxLength?: number;\n  minLength?: number;\n  allowHtml?: boolean;\n  allowSpecialChars?: boolean;\n  requireAlphanumeric?: boolean;\n  customPatterns?: RegExp[];\n}\n\nexport class AIInputValidator {\n  // Dangerous patterns that should be blocked\n  private static readonly DANGEROUS_PATTERNS = [\n    // Script injection\n    /<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi,\n    /javascript:/gi,\n    /vbscript:/gi,\n    /data:text\\/html/gi,\n    \n    // Event handlers\n    /on\\w+\\s*=/gi,\n    \n    // SQL injection patterns (more specific to avoid false positives)\n    /(\\b(SELECT\\s+\\*\\s+FROM|INSERT\\s+INTO|UPDATE\\s+\\w+\\s+SET|DELETE\\s+FROM|DROP\\s+TABLE|ALTER\\s+TABLE|EXEC\\s+|UNION\\s+SELECT)\\b)/gi,\n    \n    // Command injection (more specific patterns)\n    /(\\|\\s*\\w+|\\&\\&\\s*\\w+|;\\s*\\w+|\\$\\([^)]*\\)|`[^`]*`)/g,\n    \n    // Path traversal\n    /\\.\\.\\//g,\n    /(\\.\\.\\\\)/g,\n    \n    // XSS patterns\n    /<iframe/gi,\n    /<object/gi,\n    /<embed/gi,\n    /<link/gi,\n    /<meta/gi,\n    \n    // Suspicious protocols\n    /file:/gi,\n    /ftp:/gi,\n    /ldap:/gi,\n  ];\n\n  // Suspicious content patterns\n  private static readonly SUSPICIOUS_PATTERNS = [\n    // Excessive repetition\n    /(.)\\1{50,}/g,\n    \n    // Excessive special characters\n    /[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?]{20,}/g,\n    \n    // Potential prompt injection\n    /ignore\\s+(previous|all)\\s+(instructions|prompts)/gi,\n    /system\\s*:\\s*/gi,\n    /assistant\\s*:\\s*/gi,\n    /human\\s*:\\s*/gi,\n    \n    // Potential data extraction attempts\n    /show\\s+me\\s+(your|the)\\s+(system|internal|hidden)/gi,\n    /what\\s+(are\\s+)?your\\s+(instructions|prompts|rules)/gi,\n  ];\n\n  /**\n   * Validate and sanitize text input for AI processing\n   */\n  static validateTextInput(\n    input: string,\n    options: ValidationOptions = {}\n  ): ValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n    \n    // Set default options\n    const opts = {\n      maxLength: 10000,\n      minLength: 1,\n      allowHtml: false,\n      allowSpecialChars: true,\n      requireAlphanumeric: false,\n      ...options\n    };\n\n    // Basic validation\n    if (!input || typeof input !== 'string') {\n      errors.push('Input must be a non-empty string');\n      return { isValid: false, errors, warnings };\n    }\n\n    // Length validation\n    if (input.length < opts.minLength) {\n      errors.push(`Input must be at least ${opts.minLength} characters long`);\n    }\n\n    if (input.length > opts.maxLength) {\n      errors.push(`Input must not exceed ${opts.maxLength} characters`);\n    }\n\n    // Check for dangerous patterns\n    for (const pattern of this.DANGEROUS_PATTERNS) {\n      if (pattern.test(input)) {\n        errors.push('Input contains potentially dangerous content');\n        break;\n      }\n    }\n\n    // Check for suspicious patterns\n    for (const pattern of this.SUSPICIOUS_PATTERNS) {\n      if (pattern.test(input)) {\n        warnings.push('Input contains suspicious patterns');\n        break;\n      }\n    }\n\n    // Custom pattern validation\n    if (opts.customPatterns) {\n      for (const pattern of opts.customPatterns) {\n        if (!pattern.test(input)) {\n          errors.push('Input does not match required pattern');\n        }\n      }\n    }\n\n    // Alphanumeric requirement\n    if (opts.requireAlphanumeric && !/[a-zA-Z0-9]/.test(input)) {\n      errors.push('Input must contain at least one alphanumeric character');\n    }\n\n    // Sanitize input\n    let sanitizedInput = input;\n\n    // Remove null bytes and control characters\n    sanitizedInput = sanitizedInput.replace(/[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]/g, '');\n\n    // HTML sanitization if not allowed\n    if (!opts.allowHtml) {\n      sanitizedInput = DOMPurify.sanitize(sanitizedInput, {\n        ALLOWED_TAGS: [],\n        ALLOWED_ATTR: []\n      });\n    } else {\n      // Basic HTML sanitization\n      sanitizedInput = DOMPurify.sanitize(sanitizedInput);\n    }\n\n    // Normalize whitespace\n    sanitizedInput = sanitizedInput.replace(/\\s+/g, ' ').trim();\n\n    // Special character handling\n    if (!opts.allowSpecialChars) {\n      sanitizedInput = sanitizedInput.replace(/[^\\w\\s\\-.,!?]/g, '');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      sanitizedInput,\n      errors,\n      warnings\n    };\n  }\n\n  /**\n   * Validate interview question parameters\n   */\n  static validateInterviewParams(params: any): ValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    // Validate session type\n    const validSessionTypes = ['TECHNICAL_PRACTICE', 'BEHAVIORAL_PRACTICE', 'MOCK_INTERVIEW', 'QUICK_PRACTICE'];\n    if (params.sessionType && !validSessionTypes.includes(params.sessionType)) {\n      errors.push('Invalid session type');\n    }\n\n    // Validate experience level\n    const validExperienceLevels = ['ENTRY', 'JUNIOR', 'INTERMEDIATE', 'SENIOR', 'EXECUTIVE'];\n    if (params.experienceLevel && !validExperienceLevels.includes(params.experienceLevel)) {\n      errors.push('Invalid experience level');\n    }\n\n    // Validate difficulty\n    const validDifficulties = ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT'];\n    if (params.difficulty && !validDifficulties.includes(params.difficulty)) {\n      errors.push('Invalid difficulty level');\n    }\n\n    // Validate count\n    if (params.count) {\n      const count = parseInt(params.count);\n      if (isNaN(count) || count < 1 || count > 50) {\n        errors.push('Question count must be between 1 and 50');\n      }\n    }\n\n    // Validate string parameters\n    const stringParams = ['careerPath', 'companyType', 'industryFocus', 'specificRole', 'interviewType'];\n    for (const param of stringParams) {\n      if (params[param]) {\n        const validation = this.validateTextInput(params[param], {\n          maxLength: 200,\n          allowHtml: false,\n          allowSpecialChars: true\n        });\n        if (!validation.isValid) {\n          errors.push(`Invalid ${param}: ${validation.errors.join(', ')}`);\n        }\n      }\n    }\n\n    // Validate focus areas array\n    if (params.focusAreas && Array.isArray(params.focusAreas)) {\n      if (params.focusAreas.length > 10) {\n        errors.push('Too many focus areas (maximum 10)');\n      }\n      for (const area of params.focusAreas) {\n        if (typeof area !== 'string' || area.length > 100) {\n          errors.push('Invalid focus area');\n          break;\n        }\n      }\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n\n  /**\n   * Validate interview response text\n   */\n  static validateInterviewResponse(responseText: string): ValidationResult {\n    return this.validateTextInput(responseText, {\n      maxLength: 5000,\n      minLength: 10,\n      allowHtml: false,\n      allowSpecialChars: true,\n      requireAlphanumeric: true\n    });\n  }\n\n  /**\n   * Validate resume text\n   */\n  static validateResumeText(resumeText: string): ValidationResult {\n    return this.validateTextInput(resumeText, {\n      maxLength: 20000,\n      minLength: 100,\n      allowHtml: false,\n      allowSpecialChars: true,\n      requireAlphanumeric: true\n    });\n  }\n\n  /**\n   * Validate career skills array\n   */\n  static validateSkillsArray(skills: string[]): ValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    if (!Array.isArray(skills)) {\n      errors.push('Skills must be provided as an array');\n      return { isValid: false, errors, warnings };\n    }\n\n    if (skills.length === 0) {\n      errors.push('At least one skill must be provided');\n    }\n\n    if (skills.length > 50) {\n      errors.push('Too many skills provided (maximum 50)');\n    }\n\n    const validatedSkills: string[] = [];\n    for (const skill of skills) {\n      const validation = this.validateTextInput(skill, {\n        maxLength: 100,\n        minLength: 2,\n        allowHtml: false,\n        allowSpecialChars: false,\n        requireAlphanumeric: true\n      });\n\n      if (validation.isValid && validation.sanitizedInput) {\n        validatedSkills.push(validation.sanitizedInput);\n      } else {\n        warnings.push(`Invalid skill ignored: ${skill}`);\n      }\n    }\n\n    return {\n      isValid: errors.length === 0 && validatedSkills.length > 0,\n      sanitizedInput: validatedSkills.join(','), // Return as comma-separated string\n      errors,\n      warnings\n    };\n  }\n\n  /**\n   * Rate limiting validation\n   */\n  static validateRateLimit(userId: string, requestType: string): ValidationResult {\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    // Validate user ID format\n    if (!userId || typeof userId !== 'string') {\n      errors.push('Invalid user ID');\n    } else if (userId.length > 100) {\n      errors.push('User ID too long');\n    }\n\n    // Validate request type\n    const validRequestTypes = ['question_generation', 'response_analysis', 'resume_analysis', 'career_recommendations'];\n    if (!validRequestTypes.includes(requestType)) {\n      errors.push('Invalid request type');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n\n  /**\n   * Comprehensive security scan\n   */\n  static securityScan(input: string): { threats: string[]; riskLevel: 'low' | 'medium' | 'high' } {\n    const threats: string[] = [];\n    let riskLevel: 'low' | 'medium' | 'high' = 'low';\n\n    // Check for high-risk patterns\n    for (const pattern of this.DANGEROUS_PATTERNS) {\n      if (pattern.test(input)) {\n        threats.push('Potential code injection detected');\n        riskLevel = 'high';\n        break;\n      }\n    }\n\n    // Check for medium-risk patterns\n    if (riskLevel !== 'high') {\n      for (const pattern of this.SUSPICIOUS_PATTERNS) {\n        if (pattern.test(input)) {\n          threats.push('Suspicious content pattern detected');\n          riskLevel = 'medium';\n          break;\n        }\n      }\n    }\n\n    // Check for excessive length\n    if (input.length > 50000) {\n      threats.push('Excessive input length');\n      riskLevel = riskLevel === 'low' ? 'medium' : riskLevel;\n    }\n\n    return { threats, riskLevel };\n  }\n}\n\nexport default AIInputValidator;\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5435ac71739bd56ff2ea16a01d7cdaa4495abd86"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_18vb18im9x = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_18vb18im9x();
var __assign =
/* istanbul ignore next */
(cov_18vb18im9x().s[0]++,
/* istanbul ignore next */
(cov_18vb18im9x().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_18vb18im9x().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_18vb18im9x().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_18vb18im9x().f[0]++;
  cov_18vb18im9x().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_18vb18im9x().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_18vb18im9x().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_18vb18im9x().f[1]++;
    cov_18vb18im9x().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_18vb18im9x().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_18vb18im9x().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_18vb18im9x().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_18vb18im9x().b[2][0]++;
          cov_18vb18im9x().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_18vb18im9x().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_18vb18im9x().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_18vb18im9x().s[10]++;
  return __assign.apply(this, arguments);
}));
var __importDefault =
/* istanbul ignore next */
(cov_18vb18im9x().s[11]++,
/* istanbul ignore next */
(cov_18vb18im9x().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_18vb18im9x().b[3][1]++, this.__importDefault) ||
/* istanbul ignore next */
(cov_18vb18im9x().b[3][2]++, function (mod) {
  /* istanbul ignore next */
  cov_18vb18im9x().f[2]++;
  cov_18vb18im9x().s[12]++;
  return /* istanbul ignore next */(cov_18vb18im9x().b[5][0]++, mod) &&
  /* istanbul ignore next */
  (cov_18vb18im9x().b[5][1]++, mod.__esModule) ?
  /* istanbul ignore next */
  (cov_18vb18im9x().b[4][0]++, mod) :
  /* istanbul ignore next */
  (cov_18vb18im9x().b[4][1]++, {
    "default": mod
  });
}));
/* istanbul ignore next */
cov_18vb18im9x().s[13]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_18vb18im9x().s[14]++;
exports.AIInputValidator = void 0;
var isomorphic_dompurify_1 =
/* istanbul ignore next */
(cov_18vb18im9x().s[15]++, __importDefault(require("isomorphic-dompurify")));
var AIInputValidator =
/* istanbul ignore next */
(/** @class */cov_18vb18im9x().s[16]++, function () {
  /* istanbul ignore next */
  cov_18vb18im9x().f[3]++;
  function AIInputValidator() {
    /* istanbul ignore next */
    cov_18vb18im9x().f[4]++;
  }
  /**
   * Validate and sanitize text input for AI processing
   */
  /* istanbul ignore next */
  cov_18vb18im9x().s[17]++;
  AIInputValidator.validateTextInput = function (input, options) {
    /* istanbul ignore next */
    cov_18vb18im9x().f[5]++;
    cov_18vb18im9x().s[18]++;
    if (options === void 0) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[6][0]++;
      cov_18vb18im9x().s[19]++;
      options = {};
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[6][1]++;
    }
    var errors =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[20]++, []);
    var warnings =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[21]++, []);
    // Set default options
    var opts =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[22]++, __assign({
      maxLength: 10000,
      minLength: 1,
      allowHtml: false,
      allowSpecialChars: true,
      requireAlphanumeric: false
    }, options));
    // Basic validation
    /* istanbul ignore next */
    cov_18vb18im9x().s[23]++;
    if (
    /* istanbul ignore next */
    (cov_18vb18im9x().b[8][0]++, !input) ||
    /* istanbul ignore next */
    (cov_18vb18im9x().b[8][1]++, typeof input !== 'string')) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[7][0]++;
      cov_18vb18im9x().s[24]++;
      errors.push('Input must be a non-empty string');
      /* istanbul ignore next */
      cov_18vb18im9x().s[25]++;
      return {
        isValid: false,
        errors: errors,
        warnings: warnings
      };
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[7][1]++;
    }
    // Length validation
    cov_18vb18im9x().s[26]++;
    if (input.length < opts.minLength) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[9][0]++;
      cov_18vb18im9x().s[27]++;
      errors.push("Input must be at least ".concat(opts.minLength, " characters long"));
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[9][1]++;
    }
    cov_18vb18im9x().s[28]++;
    if (input.length > opts.maxLength) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[10][0]++;
      cov_18vb18im9x().s[29]++;
      errors.push("Input must not exceed ".concat(opts.maxLength, " characters"));
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[10][1]++;
    }
    // Check for dangerous patterns
    cov_18vb18im9x().s[30]++;
    for (var _i =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[31]++, 0), _a =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[32]++, this.DANGEROUS_PATTERNS); _i < _a.length; _i++) {
      var pattern =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[33]++, _a[_i]);
      /* istanbul ignore next */
      cov_18vb18im9x().s[34]++;
      if (pattern.test(input)) {
        /* istanbul ignore next */
        cov_18vb18im9x().b[11][0]++;
        cov_18vb18im9x().s[35]++;
        errors.push('Input contains potentially dangerous content');
        /* istanbul ignore next */
        cov_18vb18im9x().s[36]++;
        break;
      } else
      /* istanbul ignore next */
      {
        cov_18vb18im9x().b[11][1]++;
      }
    }
    // Check for suspicious patterns
    /* istanbul ignore next */
    cov_18vb18im9x().s[37]++;
    for (var _b =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[38]++, 0), _c =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[39]++, this.SUSPICIOUS_PATTERNS); _b < _c.length; _b++) {
      var pattern =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[40]++, _c[_b]);
      /* istanbul ignore next */
      cov_18vb18im9x().s[41]++;
      if (pattern.test(input)) {
        /* istanbul ignore next */
        cov_18vb18im9x().b[12][0]++;
        cov_18vb18im9x().s[42]++;
        warnings.push('Input contains suspicious patterns');
        /* istanbul ignore next */
        cov_18vb18im9x().s[43]++;
        break;
      } else
      /* istanbul ignore next */
      {
        cov_18vb18im9x().b[12][1]++;
      }
    }
    // Custom pattern validation
    /* istanbul ignore next */
    cov_18vb18im9x().s[44]++;
    if (opts.customPatterns) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[13][0]++;
      cov_18vb18im9x().s[45]++;
      for (var _d =
        /* istanbul ignore next */
        (cov_18vb18im9x().s[46]++, 0), _e =
        /* istanbul ignore next */
        (cov_18vb18im9x().s[47]++, opts.customPatterns); _d < _e.length; _d++) {
        var pattern =
        /* istanbul ignore next */
        (cov_18vb18im9x().s[48]++, _e[_d]);
        /* istanbul ignore next */
        cov_18vb18im9x().s[49]++;
        if (!pattern.test(input)) {
          /* istanbul ignore next */
          cov_18vb18im9x().b[14][0]++;
          cov_18vb18im9x().s[50]++;
          errors.push('Input does not match required pattern');
        } else
        /* istanbul ignore next */
        {
          cov_18vb18im9x().b[14][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[13][1]++;
    }
    // Alphanumeric requirement
    cov_18vb18im9x().s[51]++;
    if (
    /* istanbul ignore next */
    (cov_18vb18im9x().b[16][0]++, opts.requireAlphanumeric) &&
    /* istanbul ignore next */
    (cov_18vb18im9x().b[16][1]++, !/[a-zA-Z0-9]/.test(input))) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[15][0]++;
      cov_18vb18im9x().s[52]++;
      errors.push('Input must contain at least one alphanumeric character');
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[15][1]++;
    }
    // Sanitize input
    var sanitizedInput =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[53]++, input);
    // Remove null bytes and control characters
    /* istanbul ignore next */
    cov_18vb18im9x().s[54]++;
    sanitizedInput = sanitizedInput.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
    // HTML sanitization if not allowed
    /* istanbul ignore next */
    cov_18vb18im9x().s[55]++;
    if (!opts.allowHtml) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[17][0]++;
      cov_18vb18im9x().s[56]++;
      sanitizedInput = isomorphic_dompurify_1.default.sanitize(sanitizedInput, {
        ALLOWED_TAGS: [],
        ALLOWED_ATTR: []
      });
    } else {
      /* istanbul ignore next */
      cov_18vb18im9x().b[17][1]++;
      cov_18vb18im9x().s[57]++;
      // Basic HTML sanitization
      sanitizedInput = isomorphic_dompurify_1.default.sanitize(sanitizedInput);
    }
    // Normalize whitespace
    /* istanbul ignore next */
    cov_18vb18im9x().s[58]++;
    sanitizedInput = sanitizedInput.replace(/\s+/g, ' ').trim();
    // Special character handling
    /* istanbul ignore next */
    cov_18vb18im9x().s[59]++;
    if (!opts.allowSpecialChars) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[18][0]++;
      cov_18vb18im9x().s[60]++;
      sanitizedInput = sanitizedInput.replace(/[^\w\s\-.,!?]/g, '');
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[18][1]++;
    }
    cov_18vb18im9x().s[61]++;
    return {
      isValid: errors.length === 0,
      sanitizedInput: sanitizedInput,
      errors: errors,
      warnings: warnings
    };
  };
  /**
   * Validate interview question parameters
   */
  /* istanbul ignore next */
  cov_18vb18im9x().s[62]++;
  AIInputValidator.validateInterviewParams = function (params) {
    /* istanbul ignore next */
    cov_18vb18im9x().f[6]++;
    var errors =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[63]++, []);
    var warnings =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[64]++, []);
    // Validate session type
    var validSessionTypes =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[65]++, ['TECHNICAL_PRACTICE', 'BEHAVIORAL_PRACTICE', 'MOCK_INTERVIEW', 'QUICK_PRACTICE']);
    /* istanbul ignore next */
    cov_18vb18im9x().s[66]++;
    if (
    /* istanbul ignore next */
    (cov_18vb18im9x().b[20][0]++, params.sessionType) &&
    /* istanbul ignore next */
    (cov_18vb18im9x().b[20][1]++, !validSessionTypes.includes(params.sessionType))) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[19][0]++;
      cov_18vb18im9x().s[67]++;
      errors.push('Invalid session type');
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[19][1]++;
    }
    // Validate experience level
    var validExperienceLevels =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[68]++, ['ENTRY', 'JUNIOR', 'INTERMEDIATE', 'SENIOR', 'EXECUTIVE']);
    /* istanbul ignore next */
    cov_18vb18im9x().s[69]++;
    if (
    /* istanbul ignore next */
    (cov_18vb18im9x().b[22][0]++, params.experienceLevel) &&
    /* istanbul ignore next */
    (cov_18vb18im9x().b[22][1]++, !validExperienceLevels.includes(params.experienceLevel))) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[21][0]++;
      cov_18vb18im9x().s[70]++;
      errors.push('Invalid experience level');
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[21][1]++;
    }
    // Validate difficulty
    var validDifficulties =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[71]++, ['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT']);
    /* istanbul ignore next */
    cov_18vb18im9x().s[72]++;
    if (
    /* istanbul ignore next */
    (cov_18vb18im9x().b[24][0]++, params.difficulty) &&
    /* istanbul ignore next */
    (cov_18vb18im9x().b[24][1]++, !validDifficulties.includes(params.difficulty))) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[23][0]++;
      cov_18vb18im9x().s[73]++;
      errors.push('Invalid difficulty level');
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[23][1]++;
    }
    // Validate count
    cov_18vb18im9x().s[74]++;
    if (params.count) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[25][0]++;
      var count =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[75]++, parseInt(params.count));
      /* istanbul ignore next */
      cov_18vb18im9x().s[76]++;
      if (
      /* istanbul ignore next */
      (cov_18vb18im9x().b[27][0]++, isNaN(count)) ||
      /* istanbul ignore next */
      (cov_18vb18im9x().b[27][1]++, count < 1) ||
      /* istanbul ignore next */
      (cov_18vb18im9x().b[27][2]++, count > 50)) {
        /* istanbul ignore next */
        cov_18vb18im9x().b[26][0]++;
        cov_18vb18im9x().s[77]++;
        errors.push('Question count must be between 1 and 50');
      } else
      /* istanbul ignore next */
      {
        cov_18vb18im9x().b[26][1]++;
      }
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[25][1]++;
    }
    // Validate string parameters
    var stringParams =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[78]++, ['careerPath', 'companyType', 'industryFocus', 'specificRole', 'interviewType']);
    /* istanbul ignore next */
    cov_18vb18im9x().s[79]++;
    for (var _i =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[80]++, 0), stringParams_1 =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[81]++, stringParams); _i < stringParams_1.length; _i++) {
      var param =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[82]++, stringParams_1[_i]);
      /* istanbul ignore next */
      cov_18vb18im9x().s[83]++;
      if (params[param]) {
        /* istanbul ignore next */
        cov_18vb18im9x().b[28][0]++;
        var validation =
        /* istanbul ignore next */
        (cov_18vb18im9x().s[84]++, this.validateTextInput(params[param], {
          maxLength: 200,
          allowHtml: false,
          allowSpecialChars: true
        }));
        /* istanbul ignore next */
        cov_18vb18im9x().s[85]++;
        if (!validation.isValid) {
          /* istanbul ignore next */
          cov_18vb18im9x().b[29][0]++;
          cov_18vb18im9x().s[86]++;
          errors.push("Invalid ".concat(param, ": ").concat(validation.errors.join(', ')));
        } else
        /* istanbul ignore next */
        {
          cov_18vb18im9x().b[29][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_18vb18im9x().b[28][1]++;
      }
    }
    // Validate focus areas array
    /* istanbul ignore next */
    cov_18vb18im9x().s[87]++;
    if (
    /* istanbul ignore next */
    (cov_18vb18im9x().b[31][0]++, params.focusAreas) &&
    /* istanbul ignore next */
    (cov_18vb18im9x().b[31][1]++, Array.isArray(params.focusAreas))) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[30][0]++;
      cov_18vb18im9x().s[88]++;
      if (params.focusAreas.length > 10) {
        /* istanbul ignore next */
        cov_18vb18im9x().b[32][0]++;
        cov_18vb18im9x().s[89]++;
        errors.push('Too many focus areas (maximum 10)');
      } else
      /* istanbul ignore next */
      {
        cov_18vb18im9x().b[32][1]++;
      }
      cov_18vb18im9x().s[90]++;
      for (var _a =
        /* istanbul ignore next */
        (cov_18vb18im9x().s[91]++, 0), _b =
        /* istanbul ignore next */
        (cov_18vb18im9x().s[92]++, params.focusAreas); _a < _b.length; _a++) {
        var area =
        /* istanbul ignore next */
        (cov_18vb18im9x().s[93]++, _b[_a]);
        /* istanbul ignore next */
        cov_18vb18im9x().s[94]++;
        if (
        /* istanbul ignore next */
        (cov_18vb18im9x().b[34][0]++, typeof area !== 'string') ||
        /* istanbul ignore next */
        (cov_18vb18im9x().b[34][1]++, area.length > 100)) {
          /* istanbul ignore next */
          cov_18vb18im9x().b[33][0]++;
          cov_18vb18im9x().s[95]++;
          errors.push('Invalid focus area');
          /* istanbul ignore next */
          cov_18vb18im9x().s[96]++;
          break;
        } else
        /* istanbul ignore next */
        {
          cov_18vb18im9x().b[33][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[30][1]++;
    }
    cov_18vb18im9x().s[97]++;
    return {
      isValid: errors.length === 0,
      errors: errors,
      warnings: warnings
    };
  };
  /**
   * Validate interview response text
   */
  /* istanbul ignore next */
  cov_18vb18im9x().s[98]++;
  AIInputValidator.validateInterviewResponse = function (responseText) {
    /* istanbul ignore next */
    cov_18vb18im9x().f[7]++;
    cov_18vb18im9x().s[99]++;
    return this.validateTextInput(responseText, {
      maxLength: 5000,
      minLength: 10,
      allowHtml: false,
      allowSpecialChars: true,
      requireAlphanumeric: true
    });
  };
  /**
   * Validate resume text
   */
  /* istanbul ignore next */
  cov_18vb18im9x().s[100]++;
  AIInputValidator.validateResumeText = function (resumeText) {
    /* istanbul ignore next */
    cov_18vb18im9x().f[8]++;
    cov_18vb18im9x().s[101]++;
    return this.validateTextInput(resumeText, {
      maxLength: 20000,
      minLength: 100,
      allowHtml: false,
      allowSpecialChars: true,
      requireAlphanumeric: true
    });
  };
  /**
   * Validate career skills array
   */
  /* istanbul ignore next */
  cov_18vb18im9x().s[102]++;
  AIInputValidator.validateSkillsArray = function (skills) {
    /* istanbul ignore next */
    cov_18vb18im9x().f[9]++;
    var errors =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[103]++, []);
    var warnings =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[104]++, []);
    /* istanbul ignore next */
    cov_18vb18im9x().s[105]++;
    if (!Array.isArray(skills)) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[35][0]++;
      cov_18vb18im9x().s[106]++;
      errors.push('Skills must be provided as an array');
      /* istanbul ignore next */
      cov_18vb18im9x().s[107]++;
      return {
        isValid: false,
        errors: errors,
        warnings: warnings
      };
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[35][1]++;
    }
    cov_18vb18im9x().s[108]++;
    if (skills.length === 0) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[36][0]++;
      cov_18vb18im9x().s[109]++;
      errors.push('At least one skill must be provided');
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[36][1]++;
    }
    cov_18vb18im9x().s[110]++;
    if (skills.length > 50) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[37][0]++;
      cov_18vb18im9x().s[111]++;
      errors.push('Too many skills provided (maximum 50)');
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[37][1]++;
    }
    var validatedSkills =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[112]++, []);
    /* istanbul ignore next */
    cov_18vb18im9x().s[113]++;
    for (var _i =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[114]++, 0), skills_1 =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[115]++, skills); _i < skills_1.length; _i++) {
      var skill =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[116]++, skills_1[_i]);
      var validation =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[117]++, this.validateTextInput(skill, {
        maxLength: 100,
        minLength: 2,
        allowHtml: false,
        allowSpecialChars: false,
        requireAlphanumeric: true
      }));
      /* istanbul ignore next */
      cov_18vb18im9x().s[118]++;
      if (
      /* istanbul ignore next */
      (cov_18vb18im9x().b[39][0]++, validation.isValid) &&
      /* istanbul ignore next */
      (cov_18vb18im9x().b[39][1]++, validation.sanitizedInput)) {
        /* istanbul ignore next */
        cov_18vb18im9x().b[38][0]++;
        cov_18vb18im9x().s[119]++;
        validatedSkills.push(validation.sanitizedInput);
      } else {
        /* istanbul ignore next */
        cov_18vb18im9x().b[38][1]++;
        cov_18vb18im9x().s[120]++;
        warnings.push("Invalid skill ignored: ".concat(skill));
      }
    }
    /* istanbul ignore next */
    cov_18vb18im9x().s[121]++;
    return {
      isValid:
      /* istanbul ignore next */
      (cov_18vb18im9x().b[40][0]++, errors.length === 0) &&
      /* istanbul ignore next */
      (cov_18vb18im9x().b[40][1]++, validatedSkills.length > 0),
      sanitizedInput: validatedSkills.join(','),
      // Return as comma-separated string
      errors: errors,
      warnings: warnings
    };
  };
  /**
   * Rate limiting validation
   */
  /* istanbul ignore next */
  cov_18vb18im9x().s[122]++;
  AIInputValidator.validateRateLimit = function (userId, requestType) {
    /* istanbul ignore next */
    cov_18vb18im9x().f[10]++;
    var errors =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[123]++, []);
    var warnings =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[124]++, []);
    // Validate user ID format
    /* istanbul ignore next */
    cov_18vb18im9x().s[125]++;
    if (
    /* istanbul ignore next */
    (cov_18vb18im9x().b[42][0]++, !userId) ||
    /* istanbul ignore next */
    (cov_18vb18im9x().b[42][1]++, typeof userId !== 'string')) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[41][0]++;
      cov_18vb18im9x().s[126]++;
      errors.push('Invalid user ID');
    } else {
      /* istanbul ignore next */
      cov_18vb18im9x().b[41][1]++;
      cov_18vb18im9x().s[127]++;
      if (userId.length > 100) {
        /* istanbul ignore next */
        cov_18vb18im9x().b[43][0]++;
        cov_18vb18im9x().s[128]++;
        errors.push('User ID too long');
      } else
      /* istanbul ignore next */
      {
        cov_18vb18im9x().b[43][1]++;
      }
    }
    // Validate request type
    var validRequestTypes =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[129]++, ['question_generation', 'response_analysis', 'resume_analysis', 'career_recommendations']);
    /* istanbul ignore next */
    cov_18vb18im9x().s[130]++;
    if (!validRequestTypes.includes(requestType)) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[44][0]++;
      cov_18vb18im9x().s[131]++;
      errors.push('Invalid request type');
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[44][1]++;
    }
    cov_18vb18im9x().s[132]++;
    return {
      isValid: errors.length === 0,
      errors: errors,
      warnings: warnings
    };
  };
  /**
   * Comprehensive security scan
   */
  /* istanbul ignore next */
  cov_18vb18im9x().s[133]++;
  AIInputValidator.securityScan = function (input) {
    /* istanbul ignore next */
    cov_18vb18im9x().f[11]++;
    var threats =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[134]++, []);
    var riskLevel =
    /* istanbul ignore next */
    (cov_18vb18im9x().s[135]++, 'low');
    // Check for high-risk patterns
    /* istanbul ignore next */
    cov_18vb18im9x().s[136]++;
    for (var _i =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[137]++, 0), _a =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[138]++, this.DANGEROUS_PATTERNS); _i < _a.length; _i++) {
      var pattern =
      /* istanbul ignore next */
      (cov_18vb18im9x().s[139]++, _a[_i]);
      /* istanbul ignore next */
      cov_18vb18im9x().s[140]++;
      if (pattern.test(input)) {
        /* istanbul ignore next */
        cov_18vb18im9x().b[45][0]++;
        cov_18vb18im9x().s[141]++;
        threats.push('Potential code injection detected');
        /* istanbul ignore next */
        cov_18vb18im9x().s[142]++;
        riskLevel = 'high';
        /* istanbul ignore next */
        cov_18vb18im9x().s[143]++;
        break;
      } else
      /* istanbul ignore next */
      {
        cov_18vb18im9x().b[45][1]++;
      }
    }
    // Check for medium-risk patterns
    /* istanbul ignore next */
    cov_18vb18im9x().s[144]++;
    if (riskLevel !== 'high') {
      /* istanbul ignore next */
      cov_18vb18im9x().b[46][0]++;
      cov_18vb18im9x().s[145]++;
      for (var _b =
        /* istanbul ignore next */
        (cov_18vb18im9x().s[146]++, 0), _c =
        /* istanbul ignore next */
        (cov_18vb18im9x().s[147]++, this.SUSPICIOUS_PATTERNS); _b < _c.length; _b++) {
        var pattern =
        /* istanbul ignore next */
        (cov_18vb18im9x().s[148]++, _c[_b]);
        /* istanbul ignore next */
        cov_18vb18im9x().s[149]++;
        if (pattern.test(input)) {
          /* istanbul ignore next */
          cov_18vb18im9x().b[47][0]++;
          cov_18vb18im9x().s[150]++;
          threats.push('Suspicious content pattern detected');
          /* istanbul ignore next */
          cov_18vb18im9x().s[151]++;
          riskLevel = 'medium';
          /* istanbul ignore next */
          cov_18vb18im9x().s[152]++;
          break;
        } else
        /* istanbul ignore next */
        {
          cov_18vb18im9x().b[47][1]++;
        }
      }
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[46][1]++;
    }
    // Check for excessive length
    cov_18vb18im9x().s[153]++;
    if (input.length > 50000) {
      /* istanbul ignore next */
      cov_18vb18im9x().b[48][0]++;
      cov_18vb18im9x().s[154]++;
      threats.push('Excessive input length');
      /* istanbul ignore next */
      cov_18vb18im9x().s[155]++;
      riskLevel = riskLevel === 'low' ?
      /* istanbul ignore next */
      (cov_18vb18im9x().b[49][0]++, 'medium') :
      /* istanbul ignore next */
      (cov_18vb18im9x().b[49][1]++, riskLevel);
    } else
    /* istanbul ignore next */
    {
      cov_18vb18im9x().b[48][1]++;
    }
    cov_18vb18im9x().s[156]++;
    return {
      threats: threats,
      riskLevel: riskLevel
    };
  };
  // Dangerous patterns that should be blocked
  /* istanbul ignore next */
  cov_18vb18im9x().s[157]++;
  AIInputValidator.DANGEROUS_PATTERNS = [
  // Script injection
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, /javascript:/gi, /vbscript:/gi, /data:text\/html/gi,
  // Event handlers
  /on\w+\s*=/gi,
  // SQL injection patterns (more specific to avoid false positives)
  /(\b(SELECT\s+\*\s+FROM|INSERT\s+INTO|UPDATE\s+\w+\s+SET|DELETE\s+FROM|DROP\s+TABLE|ALTER\s+TABLE|EXEC\s+|UNION\s+SELECT)\b)/gi,
  // Command injection (more specific patterns)
  /(\|\s*\w+|\&\&\s*\w+|;\s*\w+|\$\([^)]*\)|`[^`]*`)/g,
  // Path traversal
  /\.\.\//g, /(\.\.\\)/g,
  // XSS patterns
  /<iframe/gi, /<object/gi, /<embed/gi, /<link/gi, /<meta/gi,
  // Suspicious protocols
  /file:/gi, /ftp:/gi, /ldap:/gi];
  // Suspicious content patterns
  /* istanbul ignore next */
  cov_18vb18im9x().s[158]++;
  AIInputValidator.SUSPICIOUS_PATTERNS = [
  // Excessive repetition
  /(.)\1{50,}/g,
  // Excessive special characters
  /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{20,}/g,
  // Potential prompt injection
  /ignore\s+(previous|all)\s+(instructions|prompts)/gi, /system\s*:\s*/gi, /assistant\s*:\s*/gi, /human\s*:\s*/gi,
  // Potential data extraction attempts
  /show\s+me\s+(your|the)\s+(system|internal|hidden)/gi, /what\s+(are\s+)?your\s+(instructions|prompts|rules)/gi];
  /* istanbul ignore next */
  cov_18vb18im9x().s[159]++;
  return AIInputValidator;
}());
/* istanbul ignore next */
cov_18vb18im9x().s[160]++;
exports.AIInputValidator = AIInputValidator;
/* istanbul ignore next */
cov_18vb18im9x().s[161]++;
exports.default = AIInputValidator;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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