786ed3a67bfa93d551627aa8c744795e
"use strict";

/**
 * AI Service Health Monitoring and Analytics
 * Provides real-time monitoring, performance metrics, and usage analytics
 */
/* istanbul ignore next */
function cov_2df7457914() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/ai-service-monitor.ts";
  var hash = "0a0eb807da97771bbcb918343d1cfdd7f072d5b2";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/ai-service-monitor.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 15
        },
        end: {
          line: 16,
          column: 1
        }
      },
      "1": {
        start: {
          line: 7,
          column: 4
        },
        end: {
          line: 14,
          column: 6
        }
      },
      "2": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 12,
          column: 9
        }
      },
      "3": {
        start: {
          line: 8,
          column: 24
        },
        end: {
          line: 8,
          column: 25
        }
      },
      "4": {
        start: {
          line: 8,
          column: 31
        },
        end: {
          line: 8,
          column: 47
        }
      },
      "5": {
        start: {
          line: 9,
          column: 12
        },
        end: {
          line: 9,
          column: 29
        }
      },
      "6": {
        start: {
          line: 10,
          column: 12
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "7": {
        start: {
          line: 10,
          column: 29
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "8": {
        start: {
          line: 11,
          column: 16
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "9": {
        start: {
          line: 13,
          column: 8
        },
        end: {
          line: 13,
          column: 17
        }
      },
      "10": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 43
        }
      },
      "11": {
        start: {
          line: 17,
          column: 16
        },
        end: {
          line: 25,
          column: 1
        }
      },
      "12": {
        start: {
          line: 18,
          column: 28
        },
        end: {
          line: 18,
          column: 110
        }
      },
      "13": {
        start: {
          line: 18,
          column: 91
        },
        end: {
          line: 18,
          column: 106
        }
      },
      "14": {
        start: {
          line: 19,
          column: 4
        },
        end: {
          line: 24,
          column: 7
        }
      },
      "15": {
        start: {
          line: 20,
          column: 36
        },
        end: {
          line: 20,
          column: 97
        }
      },
      "16": {
        start: {
          line: 20,
          column: 42
        },
        end: {
          line: 20,
          column: 70
        }
      },
      "17": {
        start: {
          line: 20,
          column: 85
        },
        end: {
          line: 20,
          column: 95
        }
      },
      "18": {
        start: {
          line: 21,
          column: 35
        },
        end: {
          line: 21,
          column: 100
        }
      },
      "19": {
        start: {
          line: 21,
          column: 41
        },
        end: {
          line: 21,
          column: 73
        }
      },
      "20": {
        start: {
          line: 21,
          column: 88
        },
        end: {
          line: 21,
          column: 98
        }
      },
      "21": {
        start: {
          line: 22,
          column: 32
        },
        end: {
          line: 22,
          column: 116
        }
      },
      "22": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 78
        }
      },
      "23": {
        start: {
          line: 26,
          column: 18
        },
        end: {
          line: 52,
          column: 1
        }
      },
      "24": {
        start: {
          line: 27,
          column: 12
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "25": {
        start: {
          line: 27,
          column: 43
        },
        end: {
          line: 27,
          column: 68
        }
      },
      "26": {
        start: {
          line: 27,
          column: 57
        },
        end: {
          line: 27,
          column: 68
        }
      },
      "27": {
        start: {
          line: 27,
          column: 69
        },
        end: {
          line: 27,
          column: 81
        }
      },
      "28": {
        start: {
          line: 27,
          column: 119
        },
        end: {
          line: 27,
          column: 196
        }
      },
      "29": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 28,
          column: 160
        }
      },
      "30": {
        start: {
          line: 28,
          column: 141
        },
        end: {
          line: 28,
          column: 153
        }
      },
      "31": {
        start: {
          line: 29,
          column: 23
        },
        end: {
          line: 29,
          column: 68
        }
      },
      "32": {
        start: {
          line: 29,
          column: 45
        },
        end: {
          line: 29,
          column: 65
        }
      },
      "33": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 70
        }
      },
      "34": {
        start: {
          line: 31,
          column: 15
        },
        end: {
          line: 31,
          column: 70
        }
      },
      "35": {
        start: {
          line: 32,
          column: 8
        },
        end: {
          line: 49,
          column: 66
        }
      },
      "36": {
        start: {
          line: 32,
          column: 50
        },
        end: {
          line: 49,
          column: 66
        }
      },
      "37": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 169
        }
      },
      "38": {
        start: {
          line: 33,
          column: 160
        },
        end: {
          line: 33,
          column: 169
        }
      },
      "39": {
        start: {
          line: 34,
          column: 12
        },
        end: {
          line: 34,
          column: 52
        }
      },
      "40": {
        start: {
          line: 34,
          column: 26
        },
        end: {
          line: 34,
          column: 52
        }
      },
      "41": {
        start: {
          line: 35,
          column: 12
        },
        end: {
          line: 47,
          column: 13
        }
      },
      "42": {
        start: {
          line: 36,
          column: 32
        },
        end: {
          line: 36,
          column: 39
        }
      },
      "43": {
        start: {
          line: 36,
          column: 40
        },
        end: {
          line: 36,
          column: 46
        }
      },
      "44": {
        start: {
          line: 37,
          column: 24
        },
        end: {
          line: 37,
          column: 34
        }
      },
      "45": {
        start: {
          line: 37,
          column: 35
        },
        end: {
          line: 37,
          column: 72
        }
      },
      "46": {
        start: {
          line: 38,
          column: 24
        },
        end: {
          line: 38,
          column: 34
        }
      },
      "47": {
        start: {
          line: 38,
          column: 35
        },
        end: {
          line: 38,
          column: 45
        }
      },
      "48": {
        start: {
          line: 38,
          column: 46
        },
        end: {
          line: 38,
          column: 55
        }
      },
      "49": {
        start: {
          line: 38,
          column: 56
        },
        end: {
          line: 38,
          column: 65
        }
      },
      "50": {
        start: {
          line: 39,
          column: 24
        },
        end: {
          line: 39,
          column: 41
        }
      },
      "51": {
        start: {
          line: 39,
          column: 42
        },
        end: {
          line: 39,
          column: 55
        }
      },
      "52": {
        start: {
          line: 39,
          column: 56
        },
        end: {
          line: 39,
          column: 65
        }
      },
      "53": {
        start: {
          line: 41,
          column: 20
        },
        end: {
          line: 41,
          column: 128
        }
      },
      "54": {
        start: {
          line: 41,
          column: 110
        },
        end: {
          line: 41,
          column: 116
        }
      },
      "55": {
        start: {
          line: 41,
          column: 117
        },
        end: {
          line: 41,
          column: 126
        }
      },
      "56": {
        start: {
          line: 42,
          column: 20
        },
        end: {
          line: 42,
          column: 106
        }
      },
      "57": {
        start: {
          line: 42,
          column: 81
        },
        end: {
          line: 42,
          column: 97
        }
      },
      "58": {
        start: {
          line: 42,
          column: 98
        },
        end: {
          line: 42,
          column: 104
        }
      },
      "59": {
        start: {
          line: 43,
          column: 20
        },
        end: {
          line: 43,
          column: 89
        }
      },
      "60": {
        start: {
          line: 43,
          column: 57
        },
        end: {
          line: 43,
          column: 72
        }
      },
      "61": {
        start: {
          line: 43,
          column: 73
        },
        end: {
          line: 43,
          column: 80
        }
      },
      "62": {
        start: {
          line: 43,
          column: 81
        },
        end: {
          line: 43,
          column: 87
        }
      },
      "63": {
        start: {
          line: 44,
          column: 20
        },
        end: {
          line: 44,
          column: 87
        }
      },
      "64": {
        start: {
          line: 44,
          column: 47
        },
        end: {
          line: 44,
          column: 62
        }
      },
      "65": {
        start: {
          line: 44,
          column: 63
        },
        end: {
          line: 44,
          column: 78
        }
      },
      "66": {
        start: {
          line: 44,
          column: 79
        },
        end: {
          line: 44,
          column: 85
        }
      },
      "67": {
        start: {
          line: 45,
          column: 20
        },
        end: {
          line: 45,
          column: 42
        }
      },
      "68": {
        start: {
          line: 45,
          column: 30
        },
        end: {
          line: 45,
          column: 42
        }
      },
      "69": {
        start: {
          line: 46,
          column: 20
        },
        end: {
          line: 46,
          column: 33
        }
      },
      "70": {
        start: {
          line: 46,
          column: 34
        },
        end: {
          line: 46,
          column: 43
        }
      },
      "71": {
        start: {
          line: 48,
          column: 12
        },
        end: {
          line: 48,
          column: 39
        }
      },
      "72": {
        start: {
          line: 49,
          column: 22
        },
        end: {
          line: 49,
          column: 34
        }
      },
      "73": {
        start: {
          line: 49,
          column: 35
        },
        end: {
          line: 49,
          column: 41
        }
      },
      "74": {
        start: {
          line: 49,
          column: 54
        },
        end: {
          line: 49,
          column: 64
        }
      },
      "75": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 50,
          column: 35
        }
      },
      "76": {
        start: {
          line: 50,
          column: 23
        },
        end: {
          line: 50,
          column: 35
        }
      },
      "77": {
        start: {
          line: 50,
          column: 36
        },
        end: {
          line: 50,
          column: 89
        }
      },
      "78": {
        start: {
          line: 53,
          column: 0
        },
        end: {
          line: 53,
          column: 62
        }
      },
      "79": {
        start: {
          line: 54,
          column: 0
        },
        end: {
          line: 54,
          column: 61
        }
      },
      "80": {
        start: {
          line: 55,
          column: 38
        },
        end: {
          line: 441,
          column: 3
        }
      },
      "81": {
        start: {
          line: 57,
          column: 8
        },
        end: {
          line: 57,
          column: 37
        }
      },
      "82": {
        start: {
          line: 58,
          column: 8
        },
        end: {
          line: 58,
          column: 32
        }
      },
      "83": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 59,
          column: 37
        }
      },
      "84": {
        start: {
          line: 60,
          column: 8
        },
        end: {
          line: 60,
          column: 43
        }
      },
      "85": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 61,
          column: 36
        }
      },
      "86": {
        start: {
          line: 62,
          column: 8
        },
        end: {
          line: 71,
          column: 10
        }
      },
      "87": {
        start: {
          line: 72,
          column: 8
        },
        end: {
          line: 72,
          column: 35
        }
      },
      "88": {
        start: {
          line: 74,
          column: 4
        },
        end: {
          line: 79,
          column: 6
        }
      },
      "89": {
        start: {
          line: 75,
          column: 8
        },
        end: {
          line: 77,
          column: 9
        }
      },
      "90": {
        start: {
          line: 76,
          column: 12
        },
        end: {
          line: 76,
          column: 63
        }
      },
      "91": {
        start: {
          line: 78,
          column: 8
        },
        end: {
          line: 78,
          column: 41
        }
      },
      "92": {
        start: {
          line: 83,
          column: 4
        },
        end: {
          line: 97,
          column: 6
        }
      },
      "93": {
        start: {
          line: 84,
          column: 8
        },
        end: {
          line: 84,
          column: 54
        }
      },
      "94": {
        start: {
          line: 84,
          column: 35
        },
        end: {
          line: 84,
          column: 52
        }
      },
      "95": {
        start: {
          line: 85,
          column: 21
        },
        end: {
          line: 93,
          column: 9
        }
      },
      "96": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 94,
          column: 45
        }
      },
      "97": {
        start: {
          line: 95,
          column: 8
        },
        end: {
          line: 95,
          column: 27
        }
      },
      "98": {
        start: {
          line: 96,
          column: 8
        },
        end: {
          line: 96,
          column: 35
        }
      },
      "99": {
        start: {
          line: 101,
          column: 4
        },
        end: {
          line: 104,
          column: 6
        }
      },
      "100": {
        start: {
          line: 102,
          column: 8
        },
        end: {
          line: 102,
          column: 37
        }
      },
      "101": {
        start: {
          line: 103,
          column: 8
        },
        end: {
          line: 103,
          column: 78
        }
      },
      "102": {
        start: {
          line: 108,
          column: 4
        },
        end: {
          line: 151,
          column: 6
        }
      },
      "103": {
        start: {
          line: 109,
          column: 8
        },
        end: {
          line: 150,
          column: 11
        }
      },
      "104": {
        start: {
          line: 111,
          column: 12
        },
        end: {
          line: 149,
          column: 15
        }
      },
      "105": {
        start: {
          line: 112,
          column: 16
        },
        end: {
          line: 148,
          column: 17
        }
      },
      "106": {
        start: {
          line: 114,
          column: 24
        },
        end: {
          line: 114,
          column: 50
        }
      },
      "107": {
        start: {
          line: 115,
          column: 24
        },
        end: {
          line: 115,
          column: 47
        }
      },
      "108": {
        start: {
          line: 116,
          column: 24
        },
        end: {
          line: 116,
          column: 74
        }
      },
      "109": {
        start: {
          line: 118,
          column: 24
        },
        end: {
          line: 118,
          column: 46
        }
      },
      "110": {
        start: {
          line: 119,
          column: 24
        },
        end: {
          line: 119,
          column: 70
        }
      },
      "111": {
        start: {
          line: 121,
          column: 24
        },
        end: {
          line: 121,
          column: 49
        }
      },
      "112": {
        start: {
          line: 122,
          column: 24
        },
        end: {
          line: 122,
          column: 73
        }
      },
      "113": {
        start: {
          line: 124,
          column: 24
        },
        end: {
          line: 124,
          column: 46
        }
      },
      "114": {
        start: {
          line: 125,
          column: 24
        },
        end: {
          line: 125,
          column: 62
        }
      },
      "115": {
        start: {
          line: 126,
          column: 24
        },
        end: {
          line: 126,
          column: 76
        }
      },
      "116": {
        start: {
          line: 127,
          column: 24
        },
        end: {
          line: 131,
          column: 26
        }
      },
      "117": {
        start: {
          line: 132,
          column: 24
        },
        end: {
          line: 132,
          column: 56
        }
      },
      "118": {
        start: {
          line: 133,
          column: 24
        },
        end: {
          line: 133,
          column: 49
        }
      },
      "119": {
        start: {
          line: 134,
          column: 24
        },
        end: {
          line: 134,
          column: 72
        }
      },
      "120": {
        start: {
          line: 135,
          column: 24
        },
        end: {
          line: 135,
          column: 54
        }
      },
      "121": {
        start: {
          line: 137,
          column: 24
        },
        end: {
          line: 137,
          column: 44
        }
      },
      "122": {
        start: {
          line: 138,
          column: 24
        },
        end: {
          line: 143,
          column: 26
        }
      },
      "123": {
        start: {
          line: 144,
          column: 24
        },
        end: {
          line: 144,
          column: 56
        }
      },
      "124": {
        start: {
          line: 145,
          column: 24
        },
        end: {
          line: 145,
          column: 49
        }
      },
      "125": {
        start: {
          line: 146,
          column: 24
        },
        end: {
          line: 146,
          column: 54
        }
      },
      "126": {
        start: {
          line: 147,
          column: 28
        },
        end: {
          line: 147,
          column: 50
        }
      },
      "127": {
        start: {
          line: 155,
          column: 4
        },
        end: {
          line: 159,
          column: 6
        }
      },
      "128": {
        start: {
          line: 156,
          column: 8
        },
        end: {
          line: 156,
          column: 58
        }
      },
      "129": {
        start: {
          line: 157,
          column: 8
        },
        end: {
          line: 158,
          column: 12
        }
      },
      "130": {
        start: {
          line: 163,
          column: 4
        },
        end: {
          line: 213,
          column: 6
        }
      },
      "131": {
        start: {
          line: 164,
          column: 18
        },
        end: {
          line: 164,
          column: 28
        }
      },
      "132": {
        start: {
          line: 165,
          column: 24
        },
        end: {
          line: 169,
          column: 10
        }
      },
      "133": {
        start: {
          line: 166,
          column: 23
        },
        end: {
          line: 166,
          column: 36
        }
      },
      "134": {
        start: {
          line: 167,
          column: 12
        },
        end: {
          line: 167,
          column: 45
        }
      },
      "135": {
        start: {
          line: 168,
          column: 12
        },
        end: {
          line: 168,
          column: 52
        }
      },
      "136": {
        start: {
          line: 170,
          column: 28
        },
        end: {
          line: 170,
          column: 30
        }
      },
      "137": {
        start: {
          line: 171,
          column: 33
        },
        end: {
          line: 171,
          column: 35
        }
      },
      "138": {
        start: {
          line: 172,
          column: 27
        },
        end: {
          line: 172,
          column: 29
        }
      },
      "139": {
        start: {
          line: 173,
          column: 28
        },
        end: {
          line: 173,
          column: 30
        }
      },
      "140": {
        start: {
          line: 174,
          column: 26
        },
        end: {
          line: 174,
          column: 47
        }
      },
      "141": {
        start: {
          line: 176,
          column: 8
        },
        end: {
          line: 178,
          column: 11
        }
      },
      "142": {
        start: {
          line: 177,
          column: 12
        },
        end: {
          line: 177,
          column: 36
        }
      },
      "143": {
        start: {
          line: 180,
          column: 8
        },
        end: {
          line: 199,
          column: 11
        }
      },
      "144": {
        start: {
          line: 181,
          column: 23
        },
        end: {
          line: 181,
          column: 77
        }
      },
      "145": {
        start: {
          line: 182,
          column: 23
        },
        end: {
          line: 182,
          column: 60
        }
      },
      "146": {
        start: {
          line: 184,
          column: 12
        },
        end: {
          line: 186,
          column: 13
        }
      },
      "147": {
        start: {
          line: 185,
          column: 16
        },
        end: {
          line: 185,
          column: 38
        }
      },
      "148": {
        start: {
          line: 188,
          column: 12
        },
        end: {
          line: 188,
          column: 99
        }
      },
      "149": {
        start: {
          line: 190,
          column: 12
        },
        end: {
          line: 192,
          column: 13
        }
      },
      "150": {
        start: {
          line: 191,
          column: 16
        },
        end: {
          line: 191,
          column: 85
        }
      },
      "151": {
        start: {
          line: 194,
          column: 12
        },
        end: {
          line: 196,
          column: 13
        }
      },
      "152": {
        start: {
          line: 195,
          column: 16
        },
        end: {
          line: 195,
          column: 85
        }
      },
      "153": {
        start: {
          line: 198,
          column: 12
        },
        end: {
          line: 198,
          column: 32
        }
      },
      "154": {
        start: {
          line: 201,
          column: 29
        },
        end: {
          line: 205,
          column: 55
        }
      },
      "155": {
        start: {
          line: 202,
          column: 42
        },
        end: {
          line: 202,
          column: 80
        }
      },
      "156": {
        start: {
          line: 203,
          column: 36
        },
        end: {
          line: 203,
          column: 61
        }
      },
      "157": {
        start: {
          line: 205,
          column: 35
        },
        end: {
          line: 205,
          column: 52
        }
      },
      "158": {
        start: {
          line: 206,
          column: 8
        },
        end: {
          line: 212,
          column: 10
        }
      },
      "159": {
        start: {
          line: 217,
          column: 4
        },
        end: {
          line: 296,
          column: 6
        }
      },
      "160": {
        start: {
          line: 218,
          column: 29
        },
        end: {
          line: 218,
          column: 38
        }
      },
      "161": {
        start: {
          line: 220,
          column: 8
        },
        end: {
          line: 244,
          column: 11
        }
      },
      "162": {
        start: {
          line: 221,
          column: 12
        },
        end: {
          line: 230,
          column: 13
        }
      },
      "163": {
        start: {
          line: 222,
          column: 16
        },
        end: {
          line: 229,
          column: 19
        }
      },
      "164": {
        start: {
          line: 231,
          column: 24
        },
        end: {
          line: 231,
          column: 60
        }
      },
      "165": {
        start: {
          line: 232,
          column: 12
        },
        end: {
          line: 232,
          column: 51
        }
      },
      "166": {
        start: {
          line: 233,
          column: 12
        },
        end: {
          line: 233,
          column: 26
        }
      },
      "167": {
        start: {
          line: 234,
          column: 12
        },
        end: {
          line: 236,
          column: 13
        }
      },
      "168": {
        start: {
          line: 235,
          column: 16
        },
        end: {
          line: 235,
          column: 31
        }
      },
      "169": {
        start: {
          line: 237,
          column: 12
        },
        end: {
          line: 243,
          column: 13
        }
      },
      "170": {
        start: {
          line: 238,
          column: 16
        },
        end: {
          line: 238,
          column: 34
        }
      },
      "171": {
        start: {
          line: 239,
          column: 16
        },
        end: {
          line: 239,
          column: 58
        }
      },
      "172": {
        start: {
          line: 242,
          column: 16
        },
        end: {
          line: 242,
          column: 59
        }
      },
      "173": {
        start: {
          line: 246,
          column: 32
        },
        end: {
          line: 255,
          column: 24
        }
      },
      "174": {
        start: {
          line: 248,
          column: 28
        },
        end: {
          line: 248,
          column: 33
        }
      },
      "175": {
        start: {
          line: 248,
          column: 43
        },
        end: {
          line: 248,
          column: 48
        }
      },
      "176": {
        start: {
          line: 249,
          column: 12
        },
        end: {
          line: 252,
          column: 15
        }
      },
      "177": {
        start: {
          line: 254,
          column: 36
        },
        end: {
          line: 254,
          column: 81
        }
      },
      "178": {
        start: {
          line: 256,
          column: 35
        },
        end: {
          line: 265,
          column: 72
        }
      },
      "179": {
        start: {
          line: 258,
          column: 28
        },
        end: {
          line: 258,
          column: 33
        }
      },
      "180": {
        start: {
          line: 258,
          column: 43
        },
        end: {
          line: 258,
          column: 48
        }
      },
      "181": {
        start: {
          line: 259,
          column: 12
        },
        end: {
          line: 262,
          column: 15
        }
      },
      "182": {
        start: {
          line: 264,
          column: 38
        },
        end: {
          line: 264,
          column: 64
        }
      },
      "183": {
        start: {
          line: 265,
          column: 36
        },
        end: {
          line: 265,
          column: 69
        }
      },
      "184": {
        start: {
          line: 267,
          column: 29
        },
        end: {
          line: 267,
          column: 131
        }
      },
      "185": {
        start: {
          line: 267,
          column: 96
        },
        end: {
          line: 267,
          column: 125
        }
      },
      "186": {
        start: {
          line: 268,
          column: 30
        },
        end: {
          line: 268,
          column: 60
        }
      },
      "187": {
        start: {
          line: 269,
          column: 32
        },
        end: {
          line: 269,
          column: 137
        }
      },
      "188": {
        start: {
          line: 269,
          column: 99
        },
        end: {
          line: 269,
          column: 131
        }
      },
      "189": {
        start: {
          line: 270,
          column: 33
        },
        end: {
          line: 270,
          column: 139
        }
      },
      "190": {
        start: {
          line: 270,
          column: 100
        },
        end: {
          line: 270,
          column: 133
        }
      },
      "191": {
        start: {
          line: 271,
          column: 33
        },
        end: {
          line: 275,
          column: 9
        }
      },
      "192": {
        start: {
          line: 277,
          column: 30
        },
        end: {
          line: 277,
          column: 32
        }
      },
      "193": {
        start: {
          line: 278,
          column: 8
        },
        end: {
          line: 280,
          column: 9
        }
      },
      "194": {
        start: {
          line: 279,
          column: 12
        },
        end: {
          line: 279,
          column: 92
        }
      },
      "195": {
        start: {
          line: 281,
          column: 8
        },
        end: {
          line: 283,
          column: 9
        }
      },
      "196": {
        start: {
          line: 282,
          column: 12
        },
        end: {
          line: 282,
          column: 88
        }
      },
      "197": {
        start: {
          line: 284,
          column: 8
        },
        end: {
          line: 286,
          column: 9
        }
      },
      "198": {
        start: {
          line: 285,
          column: 12
        },
        end: {
          line: 285,
          column: 76
        }
      },
      "199": {
        start: {
          line: 287,
          column: 8
        },
        end: {
          line: 289,
          column: 9
        }
      },
      "200": {
        start: {
          line: 288,
          column: 12
        },
        end: {
          line: 288,
          column: 100
        }
      },
      "201": {
        start: {
          line: 290,
          column: 8
        },
        end: {
          line: 295,
          column: 10
        }
      },
      "202": {
        start: {
          line: 300,
          column: 4
        },
        end: {
          line: 312,
          column: 6
        }
      },
      "203": {
        start: {
          line: 301,
          column: 20
        },
        end: {
          line: 301,
          column: 24
        }
      },
      "204": {
        start: {
          line: 302,
          column: 8
        },
        end: {
          line: 311,
          column: 43
        }
      },
      "205": {
        start: {
          line: 302,
          column: 58
        },
        end: {
          line: 311,
          column: 11
        }
      },
      "206": {
        start: {
          line: 303,
          column: 12
        },
        end: {
          line: 310,
          column: 15
        }
      },
      "207": {
        start: {
          line: 304,
          column: 16
        },
        end: {
          line: 309,
          column: 17
        }
      },
      "208": {
        start: {
          line: 305,
          column: 28
        },
        end: {
          line: 305,
          column: 73
        }
      },
      "209": {
        start: {
          line: 307,
          column: 24
        },
        end: {
          line: 307,
          column: 34
        }
      },
      "210": {
        start: {
          line: 308,
          column: 24
        },
        end: {
          line: 308,
          column: 46
        }
      },
      "211": {
        start: {
          line: 316,
          column: 4
        },
        end: {
          line: 320,
          column: 6
        }
      },
      "212": {
        start: {
          line: 317,
          column: 8
        },
        end: {
          line: 319,
          column: 9
        }
      },
      "213": {
        start: {
          line: 318,
          column: 12
        },
        end: {
          line: 318,
          column: 49
        }
      },
      "214": {
        start: {
          line: 324,
          column: 4
        },
        end: {
          line: 338,
          column: 6
        }
      },
      "215": {
        start: {
          line: 325,
          column: 8
        },
        end: {
          line: 325,
          column: 37
        }
      },
      "216": {
        start: {
          line: 326,
          column: 8
        },
        end: {
          line: 331,
          column: 9
        }
      },
      "217": {
        start: {
          line: 327,
          column: 12
        },
        end: {
          line: 327,
          column: 46
        }
      },
      "218": {
        start: {
          line: 330,
          column: 12
        },
        end: {
          line: 330,
          column: 42
        }
      },
      "219": {
        start: {
          line: 333,
          column: 32
        },
        end: {
          line: 333,
          column: 121
        }
      },
      "220": {
        start: {
          line: 334,
          column: 8
        },
        end: {
          line: 334,
          column: 90
        }
      },
      "221": {
        start: {
          line: 336,
          column: 24
        },
        end: {
          line: 336,
          column: 98
        }
      },
      "222": {
        start: {
          line: 336,
          column: 70
        },
        end: {
          line: 336,
          column: 88
        }
      },
      "223": {
        start: {
          line: 337,
          column: 8
        },
        end: {
          line: 337,
          column: 83
        }
      },
      "224": {
        start: {
          line: 342,
          column: 4
        },
        end: {
          line: 346,
          column: 6
        }
      },
      "225": {
        start: {
          line: 343,
          column: 8
        },
        end: {
          line: 345,
          column: 9
        }
      },
      "226": {
        start: {
          line: 344,
          column: 12
        },
        end: {
          line: 344,
          column: 92
        }
      },
      "227": {
        start: {
          line: 350,
          column: 4
        },
        end: {
          line: 354,
          column: 6
        }
      },
      "228": {
        start: {
          line: 351,
          column: 8
        },
        end: {
          line: 353,
          column: 9
        }
      },
      "229": {
        start: {
          line: 352,
          column: 12
        },
        end: {
          line: 352,
          column: 64
        }
      },
      "230": {
        start: {
          line: 358,
          column: 4
        },
        end: {
          line: 372,
          column: 6
        }
      },
      "231": {
        start: {
          line: 359,
          column: 8
        },
        end: {
          line: 371,
          column: 11
        }
      },
      "232": {
        start: {
          line: 360,
          column: 12
        },
        end: {
          line: 370,
          column: 15
        }
      },
      "233": {
        start: {
          line: 361,
          column: 16
        },
        end: {
          line: 368,
          column: 17
        }
      },
      "234": {
        start: {
          line: 364,
          column: 20
        },
        end: {
          line: 364,
          column: 48
        }
      },
      "235": {
        start: {
          line: 367,
          column: 20
        },
        end: {
          line: 367,
          column: 49
        }
      },
      "236": {
        start: {
          line: 369,
          column: 16
        },
        end: {
          line: 369,
          column: 38
        }
      },
      "237": {
        start: {
          line: 376,
          column: 4
        },
        end: {
          line: 389,
          column: 6
        }
      },
      "238": {
        start: {
          line: 377,
          column: 8
        },
        end: {
          line: 388,
          column: 11
        }
      },
      "239": {
        start: {
          line: 378,
          column: 12
        },
        end: {
          line: 387,
          column: 15
        }
      },
      "240": {
        start: {
          line: 379,
          column: 16
        },
        end: {
          line: 385,
          column: 17
        }
      },
      "241": {
        start: {
          line: 381,
          column: 20
        },
        end: {
          line: 381,
          column: 48
        }
      },
      "242": {
        start: {
          line: 384,
          column: 20
        },
        end: {
          line: 384,
          column: 49
        }
      },
      "243": {
        start: {
          line: 386,
          column: 16
        },
        end: {
          line: 386,
          column: 38
        }
      },
      "244": {
        start: {
          line: 393,
          column: 4
        },
        end: {
          line: 406,
          column: 6
        }
      },
      "245": {
        start: {
          line: 394,
          column: 8
        },
        end: {
          line: 405,
          column: 11
        }
      },
      "246": {
        start: {
          line: 395,
          column: 12
        },
        end: {
          line: 404,
          column: 15
        }
      },
      "247": {
        start: {
          line: 396,
          column: 16
        },
        end: {
          line: 402,
          column: 17
        }
      },
      "248": {
        start: {
          line: 398,
          column: 20
        },
        end: {
          line: 398,
          column: 48
        }
      },
      "249": {
        start: {
          line: 401,
          column: 20
        },
        end: {
          line: 401,
          column: 49
        }
      },
      "250": {
        start: {
          line: 403,
          column: 16
        },
        end: {
          line: 403,
          column: 38
        }
      },
      "251": {
        start: {
          line: 410,
          column: 4
        },
        end: {
          line: 421,
          column: 6
        }
      },
      "252": {
        start: {
          line: 411,
          column: 22
        },
        end: {
          line: 411,
          column: 39
        }
      },
      "253": {
        start: {
          line: 412,
          column: 24
        },
        end: {
          line: 412,
          column: 48
        }
      },
      "254": {
        start: {
          line: 413,
          column: 23
        },
        end: {
          line: 413,
          column: 52
        }
      },
      "255": {
        start: {
          line: 414,
          column: 8
        },
        end: {
          line: 420,
          column: 20
        }
      },
      "256": {
        start: {
          line: 425,
          column: 4
        },
        end: {
          line: 439,
          column: 6
        }
      },
      "257": {
        start: {
          line: 426,
          column: 8
        },
        end: {
          line: 435,
          column: 10
        }
      },
      "258": {
        start: {
          line: 436,
          column: 8
        },
        end: {
          line: 436,
          column: 37
        }
      },
      "259": {
        start: {
          line: 437,
          column: 8
        },
        end: {
          line: 437,
          column: 32
        }
      },
      "260": {
        start: {
          line: 438,
          column: 8
        },
        end: {
          line: 438,
          column: 36
        }
      },
      "261": {
        start: {
          line: 440,
          column: 4
        },
        end: {
          line: 440,
          column: 28
        }
      },
      "262": {
        start: {
          line: 442,
          column: 0
        },
        end: {
          line: 442,
          column: 44
        }
      },
      "263": {
        start: {
          line: 444,
          column: 0
        },
        end: {
          line: 444,
          column: 58
        }
      },
      "264": {
        start: {
          line: 445,
          column: 0
        },
        end: {
          line: 445,
          column: 43
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 42
          },
          end: {
            line: 6,
            column: 43
          }
        },
        loc: {
          start: {
            line: 6,
            column: 54
          },
          end: {
            line: 16,
            column: 1
          }
        },
        line: 6
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 33
          }
        },
        loc: {
          start: {
            line: 7,
            column: 44
          },
          end: {
            line: 14,
            column: 5
          }
        },
        line: 7
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 17,
            column: 44
          },
          end: {
            line: 17,
            column: 45
          }
        },
        loc: {
          start: {
            line: 17,
            column: 89
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 17
      },
      "3": {
        name: "adopt",
        decl: {
          start: {
            line: 18,
            column: 13
          },
          end: {
            line: 18,
            column: 18
          }
        },
        loc: {
          start: {
            line: 18,
            column: 26
          },
          end: {
            line: 18,
            column: 112
          }
        },
        line: 18
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 18,
            column: 70
          },
          end: {
            line: 18,
            column: 71
          }
        },
        loc: {
          start: {
            line: 18,
            column: 89
          },
          end: {
            line: 18,
            column: 108
          }
        },
        line: 18
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 19,
            column: 36
          },
          end: {
            line: 19,
            column: 37
          }
        },
        loc: {
          start: {
            line: 19,
            column: 63
          },
          end: {
            line: 24,
            column: 5
          }
        },
        line: 19
      },
      "6": {
        name: "fulfilled",
        decl: {
          start: {
            line: 20,
            column: 17
          },
          end: {
            line: 20,
            column: 26
          }
        },
        loc: {
          start: {
            line: 20,
            column: 34
          },
          end: {
            line: 20,
            column: 99
          }
        },
        line: 20
      },
      "7": {
        name: "rejected",
        decl: {
          start: {
            line: 21,
            column: 17
          },
          end: {
            line: 21,
            column: 25
          }
        },
        loc: {
          start: {
            line: 21,
            column: 33
          },
          end: {
            line: 21,
            column: 102
          }
        },
        line: 21
      },
      "8": {
        name: "step",
        decl: {
          start: {
            line: 22,
            column: 17
          },
          end: {
            line: 22,
            column: 21
          }
        },
        loc: {
          start: {
            line: 22,
            column: 30
          },
          end: {
            line: 22,
            column: 118
          }
        },
        line: 22
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 26,
            column: 48
          },
          end: {
            line: 26,
            column: 49
          }
        },
        loc: {
          start: {
            line: 26,
            column: 73
          },
          end: {
            line: 52,
            column: 1
          }
        },
        line: 26
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 27,
            column: 30
          },
          end: {
            line: 27,
            column: 31
          }
        },
        loc: {
          start: {
            line: 27,
            column: 41
          },
          end: {
            line: 27,
            column: 83
          }
        },
        line: 27
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 28,
            column: 128
          },
          end: {
            line: 28,
            column: 129
          }
        },
        loc: {
          start: {
            line: 28,
            column: 139
          },
          end: {
            line: 28,
            column: 155
          }
        },
        line: 28
      },
      "12": {
        name: "verb",
        decl: {
          start: {
            line: 29,
            column: 13
          },
          end: {
            line: 29,
            column: 17
          }
        },
        loc: {
          start: {
            line: 29,
            column: 21
          },
          end: {
            line: 29,
            column: 70
          }
        },
        line: 29
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 29,
            column: 30
          },
          end: {
            line: 29,
            column: 31
          }
        },
        loc: {
          start: {
            line: 29,
            column: 43
          },
          end: {
            line: 29,
            column: 67
          }
        },
        line: 29
      },
      "14": {
        name: "step",
        decl: {
          start: {
            line: 30,
            column: 13
          },
          end: {
            line: 30,
            column: 17
          }
        },
        loc: {
          start: {
            line: 30,
            column: 22
          },
          end: {
            line: 51,
            column: 5
          }
        },
        line: 30
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 55,
            column: 38
          },
          end: {
            line: 55,
            column: 39
          }
        },
        loc: {
          start: {
            line: 55,
            column: 50
          },
          end: {
            line: 441,
            column: 1
          }
        },
        line: 55
      },
      "16": {
        name: "AIServiceMonitor",
        decl: {
          start: {
            line: 56,
            column: 13
          },
          end: {
            line: 56,
            column: 29
          }
        },
        loc: {
          start: {
            line: 56,
            column: 32
          },
          end: {
            line: 73,
            column: 5
          }
        },
        line: 56
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 74,
            column: 35
          },
          end: {
            line: 74,
            column: 36
          }
        },
        loc: {
          start: {
            line: 74,
            column: 47
          },
          end: {
            line: 79,
            column: 5
          }
        },
        line: 74
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 83,
            column: 49
          },
          end: {
            line: 83,
            column: 50
          }
        },
        loc: {
          start: {
            line: 83,
            column: 118
          },
          end: {
            line: 97,
            column: 5
          }
        },
        line: 83
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 101,
            column: 52
          },
          end: {
            line: 101,
            column: 53
          }
        },
        loc: {
          start: {
            line: 101,
            column: 70
          },
          end: {
            line: 104,
            column: 5
          }
        },
        line: 101
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 108,
            column: 49
          },
          end: {
            line: 108,
            column: 50
          }
        },
        loc: {
          start: {
            line: 108,
            column: 61
          },
          end: {
            line: 151,
            column: 5
          }
        },
        line: 108
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 109,
            column: 48
          },
          end: {
            line: 109,
            column: 49
          }
        },
        loc: {
          start: {
            line: 109,
            column: 60
          },
          end: {
            line: 150,
            column: 9
          }
        },
        line: 109
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 111,
            column: 37
          },
          end: {
            line: 111,
            column: 38
          }
        },
        loc: {
          start: {
            line: 111,
            column: 51
          },
          end: {
            line: 149,
            column: 13
          }
        },
        line: 111
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 155,
            column: 44
          },
          end: {
            line: 155,
            column: 45
          }
        },
        loc: {
          start: {
            line: 155,
            column: 56
          },
          end: {
            line: 159,
            column: 5
          }
        },
        line: 155
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 163,
            column: 51
          },
          end: {
            line: 163,
            column: 52
          }
        },
        loc: {
          start: {
            line: 163,
            column: 63
          },
          end: {
            line: 213,
            column: 5
          }
        },
        line: 163
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 165,
            column: 50
          },
          end: {
            line: 165,
            column: 51
          }
        },
        loc: {
          start: {
            line: 165,
            column: 66
          },
          end: {
            line: 169,
            column: 9
          }
        },
        line: 165
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 176,
            column: 26
          },
          end: {
            line: 176,
            column: 27
          }
        },
        loc: {
          start: {
            line: 176,
            column: 42
          },
          end: {
            line: 178,
            column: 9
          }
        },
        line: 176
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 180,
            column: 40
          },
          end: {
            line: 180,
            column: 41
          }
        },
        loc: {
          start: {
            line: 180,
            column: 58
          },
          end: {
            line: 199,
            column: 9
          }
        },
        line: 180
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 202,
            column: 17
          },
          end: {
            line: 202,
            column: 18
          }
        },
        loc: {
          start: {
            line: 202,
            column: 40
          },
          end: {
            line: 202,
            column: 82
          }
        },
        line: 202
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 203,
            column: 18
          },
          end: {
            line: 203,
            column: 19
          }
        },
        loc: {
          start: {
            line: 203,
            column: 34
          },
          end: {
            line: 203,
            column: 63
          }
        },
        line: 203
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 205,
            column: 17
          },
          end: {
            line: 205,
            column: 18
          }
        },
        loc: {
          start: {
            line: 205,
            column: 33
          },
          end: {
            line: 205,
            column: 54
          }
        },
        line: 205
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 217,
            column: 56
          },
          end: {
            line: 217,
            column: 57
          }
        },
        loc: {
          start: {
            line: 217,
            column: 68
          },
          end: {
            line: 296,
            column: 5
          }
        },
        line: 217
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 220,
            column: 40
          },
          end: {
            line: 220,
            column: 41
          }
        },
        loc: {
          start: {
            line: 220,
            column: 58
          },
          end: {
            line: 244,
            column: 9
          }
        },
        line: 220
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 247,
            column: 17
          },
          end: {
            line: 247,
            column: 18
          }
        },
        loc: {
          start: {
            line: 247,
            column: 31
          },
          end: {
            line: 253,
            column: 9
          }
        },
        line: 247
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 254,
            column: 18
          },
          end: {
            line: 254,
            column: 19
          }
        },
        loc: {
          start: {
            line: 254,
            column: 34
          },
          end: {
            line: 254,
            column: 83
          }
        },
        line: 254
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 257,
            column: 17
          },
          end: {
            line: 257,
            column: 18
          }
        },
        loc: {
          start: {
            line: 257,
            column: 31
          },
          end: {
            line: 263,
            column: 9
          }
        },
        line: 257
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 264,
            column: 20
          },
          end: {
            line: 264,
            column: 21
          }
        },
        loc: {
          start: {
            line: 264,
            column: 36
          },
          end: {
            line: 264,
            column: 66
          }
        },
        line: 264
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 265,
            column: 18
          },
          end: {
            line: 265,
            column: 19
          }
        },
        loc: {
          start: {
            line: 265,
            column: 34
          },
          end: {
            line: 265,
            column: 71
          }
        },
        line: 265
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 267,
            column: 72
          },
          end: {
            line: 267,
            column: 73
          }
        },
        loc: {
          start: {
            line: 267,
            column: 94
          },
          end: {
            line: 267,
            column: 127
          }
        },
        line: 267
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 269,
            column: 75
          },
          end: {
            line: 269,
            column: 76
          }
        },
        loc: {
          start: {
            line: 269,
            column: 97
          },
          end: {
            line: 269,
            column: 133
          }
        },
        line: 269
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 270,
            column: 76
          },
          end: {
            line: 270,
            column: 77
          }
        },
        loc: {
          start: {
            line: 270,
            column: 98
          },
          end: {
            line: 270,
            column: 135
          }
        },
        line: 270
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 300,
            column: 53
          },
          end: {
            line: 300,
            column: 54
          }
        },
        loc: {
          start: {
            line: 300,
            column: 65
          },
          end: {
            line: 312,
            column: 5
          }
        },
        line: 300
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 302,
            column: 44
          },
          end: {
            line: 302,
            column: 45
          }
        },
        loc: {
          start: {
            line: 302,
            column: 56
          },
          end: {
            line: 311,
            column: 13
          }
        },
        line: 302
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 302,
            column: 98
          },
          end: {
            line: 302,
            column: 99
          }
        },
        loc: {
          start: {
            line: 302,
            column: 110
          },
          end: {
            line: 311,
            column: 9
          }
        },
        line: 302
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 303,
            column: 37
          },
          end: {
            line: 303,
            column: 38
          }
        },
        loc: {
          start: {
            line: 303,
            column: 51
          },
          end: {
            line: 310,
            column: 13
          }
        },
        line: 303
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 316,
            column: 52
          },
          end: {
            line: 316,
            column: 53
          }
        },
        loc: {
          start: {
            line: 316,
            column: 64
          },
          end: {
            line: 320,
            column: 5
          }
        },
        line: 316
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 324,
            column: 47
          },
          end: {
            line: 324,
            column: 48
          }
        },
        loc: {
          start: {
            line: 324,
            column: 65
          },
          end: {
            line: 338,
            column: 5
          }
        },
        line: 324
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 336,
            column: 55
          },
          end: {
            line: 336,
            column: 56
          }
        },
        loc: {
          start: {
            line: 336,
            column: 68
          },
          end: {
            line: 336,
            column: 90
          }
        },
        line: 336
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 342,
            column: 45
          },
          end: {
            line: 342,
            column: 46
          }
        },
        loc: {
          start: {
            line: 342,
            column: 57
          },
          end: {
            line: 346,
            column: 5
          }
        },
        line: 342
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 350,
            column: 51
          },
          end: {
            line: 350,
            column: 52
          }
        },
        loc: {
          start: {
            line: 350,
            column: 63
          },
          end: {
            line: 354,
            column: 5
          }
        },
        line: 350
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 358,
            column: 54
          },
          end: {
            line: 358,
            column: 55
          }
        },
        loc: {
          start: {
            line: 358,
            column: 66
          },
          end: {
            line: 372,
            column: 5
          }
        },
        line: 358
      },
      "51": {
        name: "(anonymous_51)",
        decl: {
          start: {
            line: 359,
            column: 48
          },
          end: {
            line: 359,
            column: 49
          }
        },
        loc: {
          start: {
            line: 359,
            column: 60
          },
          end: {
            line: 371,
            column: 9
          }
        },
        line: 359
      },
      "52": {
        name: "(anonymous_52)",
        decl: {
          start: {
            line: 360,
            column: 37
          },
          end: {
            line: 360,
            column: 38
          }
        },
        loc: {
          start: {
            line: 360,
            column: 51
          },
          end: {
            line: 370,
            column: 13
          }
        },
        line: 360
      },
      "53": {
        name: "(anonymous_53)",
        decl: {
          start: {
            line: 376,
            column: 50
          },
          end: {
            line: 376,
            column: 51
          }
        },
        loc: {
          start: {
            line: 376,
            column: 62
          },
          end: {
            line: 389,
            column: 5
          }
        },
        line: 376
      },
      "54": {
        name: "(anonymous_54)",
        decl: {
          start: {
            line: 377,
            column: 48
          },
          end: {
            line: 377,
            column: 49
          }
        },
        loc: {
          start: {
            line: 377,
            column: 60
          },
          end: {
            line: 388,
            column: 9
          }
        },
        line: 377
      },
      "55": {
        name: "(anonymous_55)",
        decl: {
          start: {
            line: 378,
            column: 37
          },
          end: {
            line: 378,
            column: 38
          }
        },
        loc: {
          start: {
            line: 378,
            column: 51
          },
          end: {
            line: 387,
            column: 13
          }
        },
        line: 378
      },
      "56": {
        name: "(anonymous_56)",
        decl: {
          start: {
            line: 393,
            column: 53
          },
          end: {
            line: 393,
            column: 54
          }
        },
        loc: {
          start: {
            line: 393,
            column: 65
          },
          end: {
            line: 406,
            column: 5
          }
        },
        line: 393
      },
      "57": {
        name: "(anonymous_57)",
        decl: {
          start: {
            line: 394,
            column: 48
          },
          end: {
            line: 394,
            column: 49
          }
        },
        loc: {
          start: {
            line: 394,
            column: 60
          },
          end: {
            line: 405,
            column: 9
          }
        },
        line: 394
      },
      "58": {
        name: "(anonymous_58)",
        decl: {
          start: {
            line: 395,
            column: 37
          },
          end: {
            line: 395,
            column: 38
          }
        },
        loc: {
          start: {
            line: 395,
            column: 51
          },
          end: {
            line: 404,
            column: 13
          }
        },
        line: 395
      },
      "59": {
        name: "(anonymous_59)",
        decl: {
          start: {
            line: 410,
            column: 47
          },
          end: {
            line: 410,
            column: 48
          }
        },
        loc: {
          start: {
            line: 410,
            column: 59
          },
          end: {
            line: 421,
            column: 5
          }
        },
        line: 410
      },
      "60": {
        name: "(anonymous_60)",
        decl: {
          start: {
            line: 425,
            column: 39
          },
          end: {
            line: 425,
            column: 40
          }
        },
        loc: {
          start: {
            line: 425,
            column: 51
          },
          end: {
            line: 439,
            column: 5
          }
        },
        line: 425
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 15
          },
          end: {
            line: 16,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 16
          },
          end: {
            line: 6,
            column: 20
          }
        }, {
          start: {
            line: 6,
            column: 24
          },
          end: {
            line: 6,
            column: 37
          }
        }, {
          start: {
            line: 6,
            column: 42
          },
          end: {
            line: 16,
            column: 1
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 14,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 14,
            column: 5
          }
        }],
        line: 7
      },
      "2": {
        loc: {
          start: {
            line: 10,
            column: 29
          },
          end: {
            line: 11,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 10,
            column: 29
          },
          end: {
            line: 11,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 10
      },
      "3": {
        loc: {
          start: {
            line: 17,
            column: 16
          },
          end: {
            line: 25,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 17
          },
          end: {
            line: 17,
            column: 21
          }
        }, {
          start: {
            line: 17,
            column: 25
          },
          end: {
            line: 17,
            column: 39
          }
        }, {
          start: {
            line: 17,
            column: 44
          },
          end: {
            line: 25,
            column: 1
          }
        }],
        line: 17
      },
      "4": {
        loc: {
          start: {
            line: 18,
            column: 35
          },
          end: {
            line: 18,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 56
          },
          end: {
            line: 18,
            column: 61
          }
        }, {
          start: {
            line: 18,
            column: 64
          },
          end: {
            line: 18,
            column: 109
          }
        }],
        line: 18
      },
      "5": {
        loc: {
          start: {
            line: 19,
            column: 16
          },
          end: {
            line: 19,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 16
          },
          end: {
            line: 19,
            column: 17
          }
        }, {
          start: {
            line: 19,
            column: 22
          },
          end: {
            line: 19,
            column: 33
          }
        }],
        line: 19
      },
      "6": {
        loc: {
          start: {
            line: 22,
            column: 32
          },
          end: {
            line: 22,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 46
          },
          end: {
            line: 22,
            column: 67
          }
        }, {
          start: {
            line: 22,
            column: 70
          },
          end: {
            line: 22,
            column: 115
          }
        }],
        line: 22
      },
      "7": {
        loc: {
          start: {
            line: 23,
            column: 51
          },
          end: {
            line: 23,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 23,
            column: 51
          },
          end: {
            line: 23,
            column: 61
          }
        }, {
          start: {
            line: 23,
            column: 65
          },
          end: {
            line: 23,
            column: 67
          }
        }],
        line: 23
      },
      "8": {
        loc: {
          start: {
            line: 26,
            column: 18
          },
          end: {
            line: 52,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 19
          },
          end: {
            line: 26,
            column: 23
          }
        }, {
          start: {
            line: 26,
            column: 27
          },
          end: {
            line: 26,
            column: 43
          }
        }, {
          start: {
            line: 26,
            column: 48
          },
          end: {
            line: 52,
            column: 1
          }
        }],
        line: 26
      },
      "9": {
        loc: {
          start: {
            line: 27,
            column: 43
          },
          end: {
            line: 27,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 43
          },
          end: {
            line: 27,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "10": {
        loc: {
          start: {
            line: 27,
            column: 134
          },
          end: {
            line: 27,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 27,
            column: 167
          },
          end: {
            line: 27,
            column: 175
          }
        }, {
          start: {
            line: 27,
            column: 178
          },
          end: {
            line: 27,
            column: 184
          }
        }],
        line: 27
      },
      "11": {
        loc: {
          start: {
            line: 28,
            column: 74
          },
          end: {
            line: 28,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 74
          },
          end: {
            line: 28,
            column: 102
          }
        }, {
          start: {
            line: 28,
            column: 107
          },
          end: {
            line: 28,
            column: 155
          }
        }],
        line: 28
      },
      "12": {
        loc: {
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 31,
            column: 8
          },
          end: {
            line: 31,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 31
      },
      "13": {
        loc: {
          start: {
            line: 32,
            column: 15
          },
          end: {
            line: 32,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 15
          },
          end: {
            line: 32,
            column: 16
          }
        }, {
          start: {
            line: 32,
            column: 21
          },
          end: {
            line: 32,
            column: 44
          }
        }],
        line: 32
      },
      "14": {
        loc: {
          start: {
            line: 32,
            column: 28
          },
          end: {
            line: 32,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 32,
            column: 28
          },
          end: {
            line: 32,
            column: 33
          }
        }, {
          start: {
            line: 32,
            column: 38
          },
          end: {
            line: 32,
            column: 43
          }
        }],
        line: 32
      },
      "15": {
        loc: {
          start: {
            line: 33,
            column: 12
          },
          end: {
            line: 33,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 12
          },
          end: {
            line: 33,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "16": {
        loc: {
          start: {
            line: 33,
            column: 23
          },
          end: {
            line: 33,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 23
          },
          end: {
            line: 33,
            column: 24
          }
        }, {
          start: {
            line: 33,
            column: 29
          },
          end: {
            line: 33,
            column: 125
          }
        }, {
          start: {
            line: 33,
            column: 130
          },
          end: {
            line: 33,
            column: 158
          }
        }],
        line: 33
      },
      "17": {
        loc: {
          start: {
            line: 33,
            column: 33
          },
          end: {
            line: 33,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 33,
            column: 45
          },
          end: {
            line: 33,
            column: 56
          }
        }, {
          start: {
            line: 33,
            column: 59
          },
          end: {
            line: 33,
            column: 125
          }
        }],
        line: 33
      },
      "18": {
        loc: {
          start: {
            line: 33,
            column: 59
          },
          end: {
            line: 33,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 33,
            column: 67
          },
          end: {
            line: 33,
            column: 116
          }
        }, {
          start: {
            line: 33,
            column: 119
          },
          end: {
            line: 33,
            column: 125
          }
        }],
        line: 33
      },
      "19": {
        loc: {
          start: {
            line: 33,
            column: 67
          },
          end: {
            line: 33,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 67
          },
          end: {
            line: 33,
            column: 77
          }
        }, {
          start: {
            line: 33,
            column: 82
          },
          end: {
            line: 33,
            column: 115
          }
        }],
        line: 33
      },
      "20": {
        loc: {
          start: {
            line: 33,
            column: 82
          },
          end: {
            line: 33,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 83
          },
          end: {
            line: 33,
            column: 98
          }
        }, {
          start: {
            line: 33,
            column: 103
          },
          end: {
            line: 33,
            column: 112
          }
        }],
        line: 33
      },
      "21": {
        loc: {
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 34,
            column: 12
          },
          end: {
            line: 34,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 34
      },
      "22": {
        loc: {
          start: {
            line: 35,
            column: 12
          },
          end: {
            line: 47,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 36,
            column: 23
          }
        }, {
          start: {
            line: 36,
            column: 24
          },
          end: {
            line: 36,
            column: 46
          }
        }, {
          start: {
            line: 37,
            column: 16
          },
          end: {
            line: 37,
            column: 72
          }
        }, {
          start: {
            line: 38,
            column: 16
          },
          end: {
            line: 38,
            column: 65
          }
        }, {
          start: {
            line: 39,
            column: 16
          },
          end: {
            line: 39,
            column: 65
          }
        }, {
          start: {
            line: 40,
            column: 16
          },
          end: {
            line: 46,
            column: 43
          }
        }],
        line: 35
      },
      "23": {
        loc: {
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 20
          },
          end: {
            line: 41,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "24": {
        loc: {
          start: {
            line: 41,
            column: 24
          },
          end: {
            line: 41,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 41,
            column: 24
          },
          end: {
            line: 41,
            column: 74
          }
        }, {
          start: {
            line: 41,
            column: 79
          },
          end: {
            line: 41,
            column: 90
          }
        }, {
          start: {
            line: 41,
            column: 94
          },
          end: {
            line: 41,
            column: 105
          }
        }],
        line: 41
      },
      "25": {
        loc: {
          start: {
            line: 41,
            column: 42
          },
          end: {
            line: 41,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 41,
            column: 42
          },
          end: {
            line: 41,
            column: 54
          }
        }, {
          start: {
            line: 41,
            column: 58
          },
          end: {
            line: 41,
            column: 73
          }
        }],
        line: 41
      },
      "26": {
        loc: {
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 20
          },
          end: {
            line: 42,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "27": {
        loc: {
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 35
          }
        }, {
          start: {
            line: 42,
            column: 40
          },
          end: {
            line: 42,
            column: 42
          }
        }, {
          start: {
            line: 42,
            column: 47
          },
          end: {
            line: 42,
            column: 59
          }
        }, {
          start: {
            line: 42,
            column: 63
          },
          end: {
            line: 42,
            column: 75
          }
        }],
        line: 42
      },
      "28": {
        loc: {
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 43,
            column: 20
          },
          end: {
            line: 43,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 43
      },
      "29": {
        loc: {
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 43,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 43,
            column: 24
          },
          end: {
            line: 43,
            column: 35
          }
        }, {
          start: {
            line: 43,
            column: 39
          },
          end: {
            line: 43,
            column: 53
          }
        }],
        line: 43
      },
      "30": {
        loc: {
          start: {
            line: 44,
            column: 20
          },
          end: {
            line: 44,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 20
          },
          end: {
            line: 44,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "31": {
        loc: {
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 25
          }
        }, {
          start: {
            line: 44,
            column: 29
          },
          end: {
            line: 44,
            column: 43
          }
        }],
        line: 44
      },
      "32": {
        loc: {
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 45,
            column: 20
          },
          end: {
            line: 45,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 45
      },
      "33": {
        loc: {
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 8
          },
          end: {
            line: 50,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "34": {
        loc: {
          start: {
            line: 50,
            column: 52
          },
          end: {
            line: 50,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 50,
            column: 60
          },
          end: {
            line: 50,
            column: 65
          }
        }, {
          start: {
            line: 50,
            column: 68
          },
          end: {
            line: 50,
            column: 74
          }
        }],
        line: 50
      },
      "35": {
        loc: {
          start: {
            line: 75,
            column: 8
          },
          end: {
            line: 77,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 75,
            column: 8
          },
          end: {
            line: 77,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 75
      },
      "36": {
        loc: {
          start: {
            line: 84,
            column: 8
          },
          end: {
            line: 84,
            column: 54
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 84,
            column: 8
          },
          end: {
            line: 84,
            column: 54
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 84
      },
      "37": {
        loc: {
          start: {
            line: 112,
            column: 16
          },
          end: {
            line: 148,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 113,
            column: 20
          },
          end: {
            line: 116,
            column: 74
          }
        }, {
          start: {
            line: 117,
            column: 20
          },
          end: {
            line: 119,
            column: 70
          }
        }, {
          start: {
            line: 120,
            column: 20
          },
          end: {
            line: 122,
            column: 73
          }
        }, {
          start: {
            line: 123,
            column: 20
          },
          end: {
            line: 135,
            column: 54
          }
        }, {
          start: {
            line: 136,
            column: 20
          },
          end: {
            line: 146,
            column: 54
          }
        }, {
          start: {
            line: 147,
            column: 20
          },
          end: {
            line: 147,
            column: 50
          }
        }],
        line: 112
      },
      "38": {
        loc: {
          start: {
            line: 126,
            column: 37
          },
          end: {
            line: 126,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 126,
            column: 37
          },
          end: {
            line: 126,
            column: 46
          }
        }, {
          start: {
            line: 126,
            column: 50
          },
          end: {
            line: 126,
            column: 62
          }
        }, {
          start: {
            line: 126,
            column: 66
          },
          end: {
            line: 126,
            column: 75
          }
        }],
        line: 126
      },
      "39": {
        loc: {
          start: {
            line: 128,
            column: 36
          },
          end: {
            line: 128,
            column: 71
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 128,
            column: 49
          },
          end: {
            line: 128,
            column: 58
          }
        }, {
          start: {
            line: 128,
            column: 61
          },
          end: {
            line: 128,
            column: 71
          }
        }],
        line: 128
      },
      "40": {
        loc: {
          start: {
            line: 142,
            column: 35
          },
          end: {
            line: 142,
            column: 95
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 142,
            column: 62
          },
          end: {
            line: 142,
            column: 77
          }
        }, {
          start: {
            line: 142,
            column: 80
          },
          end: {
            line: 142,
            column: 95
          }
        }],
        line: 142
      },
      "41": {
        loc: {
          start: {
            line: 184,
            column: 12
          },
          end: {
            line: 186,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 184,
            column: 12
          },
          end: {
            line: 186,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 184
      },
      "42": {
        loc: {
          start: {
            line: 188,
            column: 52
          },
          end: {
            line: 188,
            column: 93
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 188,
            column: 52
          },
          end: {
            line: 188,
            column: 88
          }
        }, {
          start: {
            line: 188,
            column: 92
          },
          end: {
            line: 188,
            column: 93
          }
        }],
        line: 188
      },
      "43": {
        loc: {
          start: {
            line: 190,
            column: 12
          },
          end: {
            line: 192,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 190,
            column: 12
          },
          end: {
            line: 192,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 190
      },
      "44": {
        loc: {
          start: {
            line: 191,
            column: 47
          },
          end: {
            line: 191,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 191,
            column: 47
          },
          end: {
            line: 191,
            column: 74
          }
        }, {
          start: {
            line: 191,
            column: 78
          },
          end: {
            line: 191,
            column: 79
          }
        }],
        line: 191
      },
      "45": {
        loc: {
          start: {
            line: 194,
            column: 12
          },
          end: {
            line: 196,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 194,
            column: 12
          },
          end: {
            line: 196,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 194
      },
      "46": {
        loc: {
          start: {
            line: 194,
            column: 16
          },
          end: {
            line: 194,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 194,
            column: 16
          },
          end: {
            line: 194,
            column: 31
          }
        }, {
          start: {
            line: 194,
            column: 35
          },
          end: {
            line: 194,
            column: 47
          }
        }],
        line: 194
      },
      "47": {
        loc: {
          start: {
            line: 195,
            column: 47
          },
          end: {
            line: 195,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 195,
            column: 47
          },
          end: {
            line: 195,
            column: 74
          }
        }, {
          start: {
            line: 195,
            column: 78
          },
          end: {
            line: 195,
            column: 79
          }
        }],
        line: 195
      },
      "48": {
        loc: {
          start: {
            line: 221,
            column: 12
          },
          end: {
            line: 230,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 221,
            column: 12
          },
          end: {
            line: 230,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 221
      },
      "49": {
        loc: {
          start: {
            line: 234,
            column: 12
          },
          end: {
            line: 236,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 234,
            column: 12
          },
          end: {
            line: 236,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 234
      },
      "50": {
        loc: {
          start: {
            line: 237,
            column: 12
          },
          end: {
            line: 243,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 237,
            column: 12
          },
          end: {
            line: 243,
            column: 13
          }
        }, {
          start: {
            line: 241,
            column: 17
          },
          end: {
            line: 243,
            column: 13
          }
        }],
        line: 237
      },
      "51": {
        loc: {
          start: {
            line: 272,
            column: 21
          },
          end: {
            line: 272,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 272,
            column: 43
          },
          end: {
            line: 272,
            column: 83
          }
        }, {
          start: {
            line: 272,
            column: 86
          },
          end: {
            line: 272,
            column: 87
          }
        }],
        line: 272
      },
      "52": {
        loc: {
          start: {
            line: 273,
            column: 38
          },
          end: {
            line: 273,
            column: 97
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 273,
            column: 59
          },
          end: {
            line: 273,
            column: 93
          }
        }, {
          start: {
            line: 273,
            column: 96
          },
          end: {
            line: 273,
            column: 97
          }
        }],
        line: 273
      },
      "53": {
        loc: {
          start: {
            line: 274,
            column: 41
          },
          end: {
            line: 274,
            column: 141
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 274,
            column: 82
          },
          end: {
            line: 274,
            column: 137
          }
        }, {
          start: {
            line: 274,
            column: 140
          },
          end: {
            line: 274,
            column: 141
          }
        }],
        line: 274
      },
      "54": {
        loc: {
          start: {
            line: 278,
            column: 8
          },
          end: {
            line: 280,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 278,
            column: 8
          },
          end: {
            line: 280,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 278
      },
      "55": {
        loc: {
          start: {
            line: 281,
            column: 8
          },
          end: {
            line: 283,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 281,
            column: 8
          },
          end: {
            line: 283,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 281
      },
      "56": {
        loc: {
          start: {
            line: 284,
            column: 8
          },
          end: {
            line: 286,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 284,
            column: 8
          },
          end: {
            line: 286,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 284
      },
      "57": {
        loc: {
          start: {
            line: 287,
            column: 8
          },
          end: {
            line: 289,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 287,
            column: 8
          },
          end: {
            line: 289,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 287
      },
      "58": {
        loc: {
          start: {
            line: 304,
            column: 16
          },
          end: {
            line: 309,
            column: 17
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 305,
            column: 20
          },
          end: {
            line: 305,
            column: 73
          }
        }, {
          start: {
            line: 306,
            column: 20
          },
          end: {
            line: 308,
            column: 46
          }
        }],
        line: 304
      },
      "59": {
        loc: {
          start: {
            line: 317,
            column: 8
          },
          end: {
            line: 319,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 317,
            column: 8
          },
          end: {
            line: 319,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 317
      },
      "60": {
        loc: {
          start: {
            line: 326,
            column: 8
          },
          end: {
            line: 331,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 326,
            column: 8
          },
          end: {
            line: 331,
            column: 9
          }
        }, {
          start: {
            line: 329,
            column: 13
          },
          end: {
            line: 331,
            column: 9
          }
        }],
        line: 326
      },
      "61": {
        loc: {
          start: {
            line: 343,
            column: 8
          },
          end: {
            line: 345,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 343,
            column: 8
          },
          end: {
            line: 345,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 343
      },
      "62": {
        loc: {
          start: {
            line: 351,
            column: 8
          },
          end: {
            line: 353,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 351,
            column: 8
          },
          end: {
            line: 353,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 351
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0, 0, 0, 0, 0],
      "23": [0, 0],
      "24": [0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0, 0, 0, 0, 0],
      "38": [0, 0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/ai-service-monitor.ts",
      mappings: ";AAAA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsCH;IAUE;QAPQ,uBAAkB,GAAwB,EAAE,CAAC;QAC7C,kBAAa,GAAmB,EAAE,CAAC;QAE1B,qBAAgB,GAAG,IAAI,CAAC;QACxB,0BAAqB,GAAG,KAAK,CAAC,CAAC,aAAa;QAI3D,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC5B,IAAI,CAAC,OAAO,GAAG;YACb,aAAa,EAAE,CAAC;YAChB,kBAAkB,EAAE,CAAC;YACrB,cAAc,EAAE,CAAC;YACjB,mBAAmB,EAAE,CAAC;YACtB,YAAY,EAAE,CAAC;YACf,aAAa,EAAE,CAAC;YAChB,eAAe,EAAE,CAAC;YAClB,MAAM,EAAE,CAAC;SACV,CAAC;QAEF,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAEM,4BAAW,GAAlB;QACE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC;YAC/B,gBAAgB,CAAC,QAAQ,GAAG,IAAI,gBAAgB,EAAE,CAAC;QACrD,CAAC;QACD,OAAO,gBAAgB,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,0CAAe,GAAf,UACE,SAAiB,EACjB,YAAoB,EACpB,OAAgB,EAChB,QAAyB,EACzB,MAAe,EACf,KAAc;QAFd,yBAAA,EAAA,gBAAyB;QAIzB,IAAM,MAAM,GAAsB;YAChC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,SAAS,WAAA;YACT,YAAY,cAAA;YACZ,OAAO,SAAA;YACP,QAAQ,UAAA;YACR,MAAM,QAAA;YACN,KAAK,OAAA;SACN,CAAC;QAEF,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrC,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,6CAAkB,GAAlB,UAAmB,MAAc;QAC/B,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAC7B,OAAO,CAAC,IAAI,CAAC,gDAAyC,MAAM,CAAE,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACG,0CAAe,GAArB;uCAAyB,OAAO;;;;;;wBAEtB,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;wBAGX,qBAAM,IAAI,CAAC,oBAAoB,EAAE,EAAA;;wBAA7C,SAAS,GAAG,SAAiC;wBAC9B,qBAAM,IAAI,CAAC,gBAAgB,EAAE,EAAA;;wBAA5C,YAAY,GAAG,SAA6B;wBAChC,qBAAM,IAAI,CAAC,mBAAmB,EAAE,EAAA;;wBAA5C,SAAS,GAAG,SAAgC;wBAE5C,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;wBACtC,UAAU,GAAG,SAAS,IAAI,YAAY,IAAI,SAAS,CAAC;wBAEpD,MAAM,GAAiB;4BAC3B,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,UAAU;4BAC3C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;4BACrB,YAAY,cAAA;yBACb,CAAC;wBAEF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAChC,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBACzB,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC;wBAEhD,sBAAO,MAAM,EAAC;;;wBAER,MAAM,GAAiB;4BAC3B,MAAM,EAAE,WAAW;4BACnB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;4BACrB,YAAY,EAAE,CAAC;4BACf,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;yBAChE,CAAC;wBAEF,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;wBAChC,IAAI,CAAC,iBAAiB,EAAE,CAAC;wBAEzB,sBAAO,MAAM,EAAC;;;;;KAEjB;IAED;;OAEG;IACH,qCAAU,GAAV;QAIE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;QAElD,6BACK,IAAI,CAAC,OAAO,KACf,iBAAiB,EAAE,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EACrD,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,wBAAwB;YACnE;IACJ,CAAC;IAED;;OAEG;IACH,4CAAiB,GAAjB;QACE,IAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,IAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,UAAC,CAAC,EAAE,CAAC;YAC/C,IAAM,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YACjC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,IAAM,aAAa,GAA2B,EAAE,CAAC;QACjD,IAAM,kBAAkB,GAA2B,EAAE,CAAC;QACtD,IAAM,YAAY,GAA2B,EAAE,CAAC;QAChD,IAAM,aAAa,GAA2B,EAAE,CAAC;QACjD,IAAM,WAAW,GAAG,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAE1C,4BAA4B;QAC5B,SAAS,CAAC,OAAO,CAAC,UAAA,IAAI;YACpB,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC;QAEH,8BAA8B;QAC9B,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAA,MAAM;YACpC,IAAM,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACpE,IAAM,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;YAEnD,iBAAiB;YACjB,IAAI,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YACxB,CAAC;YAED,sBAAsB;YACtB,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAEvF,gBAAgB;YAChB,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBAClB,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACvE,CAAC;YAED,iBAAiB;YACjB,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBACpC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACvE,CAAC;YAED,eAAe;YACf,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QACtB,CAAC,CAAC,CAAC;QAEH,wBAAwB;QACxB,IAAM,cAAc,GAAG,WAAW;aAC/B,GAAG,CAAC,UAAC,KAAK,EAAE,IAAI,IAAK,OAAA,CAAC,EAAE,IAAI,MAAA,EAAE,KAAK,OAAA,EAAE,CAAC,EAAjB,CAAiB,CAAC;aACvC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAjB,CAAiB,CAAC;aACjC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;aACX,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,IAAI,EAAT,CAAS,CAAC,CAAC;QAE1B,OAAO;YACL,aAAa,eAAA;YACb,kBAAkB,oBAAA;YAClB,YAAY,cAAA;YACZ,aAAa,eAAA;YACb,cAAc,gBAAA;SACf,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,iDAAsB,GAAtB;QAME,IAAM,cAAc,GAAG,IAAI,GAAG,EAO1B,CAAC;QAEL,2BAA2B;QAC3B,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAA,MAAM;YACpC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC1C,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE;oBACnC,SAAS,EAAE,CAAC;oBACZ,KAAK,EAAE,CAAC;oBACR,MAAM,EAAE,CAAC;oBACT,SAAS,EAAE,CAAC;oBACZ,YAAY,EAAE,CAAC;oBACf,aAAa,EAAE,CAAC;iBACjB,CAAC,CAAC;YACL,CAAC;YAED,IAAM,KAAK,GAAG,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAE,CAAC;YACpD,KAAK,CAAC,SAAS,IAAI,MAAM,CAAC,YAAY,CAAC;YACvC,KAAK,CAAC,KAAK,EAAE,CAAC;YAEd,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACpB,KAAK,CAAC,MAAM,EAAE,CAAC;YACjB,CAAC;YAED,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;gBACpB,KAAK,CAAC,SAAS,EAAE,CAAC;gBAClB,KAAK,CAAC,YAAY,IAAI,MAAM,CAAC,YAAY,CAAC;YAC5C,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,aAAa,IAAI,MAAM,CAAC,YAAY,CAAC;YAC7C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,qBAAqB;QACrB,IAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;aAC3D,GAAG,CAAC,UAAC,EAAkB;gBAAjB,SAAS,QAAA,EAAE,KAAK,QAAA;YAAM,OAAA,CAAC;gBAC5B,SAAS,WAAA;gBACT,eAAe,EAAE,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK;aAC/C,CAAC;QAH2B,CAG3B,CAAC;aACF,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,eAAe,GAAG,CAAC,CAAC,eAAe,EAArC,CAAqC,CAAC;aACrD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAEf,IAAM,oBAAoB,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;aAC9D,GAAG,CAAC,UAAC,EAAkB;gBAAjB,SAAS,QAAA,EAAE,KAAK,QAAA;YAAM,OAAA,CAAC;gBAC5B,SAAS,WAAA;gBACT,SAAS,EAAE,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG;aAC9C,CAAC;QAH2B,CAG3B,CAAC;aACF,MAAM,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,CAAC,SAAS,GAAG,CAAC,EAAlB,CAAkB,CAAC;aAClC,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAzB,CAAyB,CAAC,CAAC;QAE7C,sBAAsB;QACtB,IAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,KAAK,IAAK,OAAA,GAAG,GAAG,KAAK,CAAC,SAAS,EAArB,CAAqB,EAAE,CAAC,CAAC,CAAC;QAC5G,IAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;QACvD,IAAM,iBAAiB,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,KAAK,IAAK,OAAA,GAAG,GAAG,KAAK,CAAC,YAAY,EAAxB,CAAwB,EAAE,CAAC,CAAC,CAAC;QAClH,IAAM,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,KAAK,IAAK,OAAA,GAAG,GAAG,KAAK,CAAC,aAAa,EAAzB,CAAyB,EAAE,CAAC,CAAC,CAAC;QAEpH,IAAM,kBAAkB,GAAG;YACzB,OAAO,EAAE,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,cAAc,GAAG,eAAe,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;YAC3E,wBAAwB,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YACrF,2BAA2B,EAAE,CAAC,eAAe,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,GAAG,CAAC,eAAe,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;SAClI,CAAC;QAEF,2BAA2B;QAC3B,IAAM,eAAe,GAAa,EAAE,CAAC;QAErC,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,IAAI,EAAE,CAAC;YAC5C,eAAe,CAAC,IAAI,CAAC,yDAAyD,CAAC,CAAC;QAClF,CAAC;QAED,IAAI,kBAAkB,CAAC,OAAO,GAAG,EAAE,EAAE,CAAC;YACpC,eAAe,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,EAAE,CAAC;YACpE,eAAe,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAClE,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,EAAE,EAAE,CAAC;YACpC,eAAe,CAAC,IAAI,CAAC,iEAAiE,CAAC,CAAC;QAC1F,CAAC;QAED,OAAO;YACL,iBAAiB,mBAAA;YACjB,oBAAoB,sBAAA;YACpB,kBAAkB,oBAAA;YAClB,eAAe,iBAAA;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,8CAAmB,GAA3B;QAAA,iBAIC;QAHC,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC;;;4BAClC,qBAAM,IAAI,CAAC,eAAe,EAAE,EAAA;;wBAA5B,SAA4B,CAAC;;;;aAC9B,EAAE,IAAI,CAAC,qBAAqB,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,6CAAkB,GAAlB;QACE,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,wCAAa,GAArB,UAAsB,MAAyB;QAC7C,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QAE7B,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,IAAI,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAChC,CAAC;QAED,+BAA+B;QAC/B,IAAM,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,YAAY,CAAC;QACpH,IAAI,CAAC,OAAO,CAAC,mBAAmB,GAAG,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;QAElF,wBAAwB;QACxB,IAAM,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,EAAV,CAAU,CAAC,CAAC,MAAM,CAAC;QACzE,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,GAAG,GAAG,CAAC;IAC7E,CAAC;IAED;;OAEG;IACK,sCAAW,GAAnB;QACE,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC3D,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAClF,CAAC;IACH,CAAC;IAED;;OAEG;IACK,4CAAiB,GAAzB;QACE,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;YACpC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED;;OAEG;IACW,+CAAoB,GAAlC;uCAAsC,OAAO;;gBAC3C,IAAI,CAAC;oBACH,oEAAoE;oBACpE,yCAAyC;oBACzC,sBAAO,IAAI,EAAC;gBACd,CAAC;gBAAC,WAAM,CAAC;oBACP,sBAAO,KAAK,EAAC;gBACf,CAAC;;;;KACF;IAED;;OAEG;IACW,2CAAgB,GAA9B;uCAAkC,OAAO;;gBACvC,IAAI,CAAC;oBACH,+BAA+B;oBAC/B,sBAAO,IAAI,EAAC;gBACd,CAAC;gBAAC,WAAM,CAAC;oBACP,sBAAO,KAAK,EAAC;gBACf,CAAC;;;;KACF;IAED;;OAEG;IACW,8CAAmB,GAAjC;uCAAqC,OAAO;;gBAC1C,IAAI,CAAC;oBACH,kCAAkC;oBAClC,sBAAO,IAAI,EAAC;gBACd,CAAC;gBAAC,WAAM,CAAC;oBACP,sBAAO,KAAK,EAAC;gBACf,CAAC;;;;KACF;IAED;;OAEG;IACH,wCAAa,GAAb;QACE,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAClC,IAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC3C,IAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE/C,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;YACrB,OAAO,EAAE,YAAY;YACrB,OAAO,SAAA;YACP,SAAS,WAAA;YACT,QAAQ,UAAA;SACT,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACd,CAAC;IAED;;OAEG;IACH,gCAAK,GAAL;QACE,IAAI,CAAC,OAAO,GAAG;YACb,aAAa,EAAE,CAAC;YAChB,kBAAkB,EAAE,CAAC;YACrB,cAAc,EAAE,CAAC;YACjB,mBAAmB,EAAE,CAAC;YACtB,YAAY,EAAE,CAAC;YACf,aAAa,EAAE,CAAC;YAChB,eAAe,EAAE,CAAC;YAClB,MAAM,EAAE,CAAC;SACV,CAAC;QACF,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC9B,CAAC;IACH,uBAAC;AAAD,CAAC,AAraD,IAqaC;AAraY,4CAAgB;AAua7B,4BAA4B;AACf,QAAA,gBAAgB,GAAG,gBAAgB,CAAC,WAAW,EAAE,CAAC;AAC/D,kBAAe,wBAAgB,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/ai-service-monitor.ts"],
      sourcesContent: ["/**\n * AI Service Health Monitoring and Analytics\n * Provides real-time monitoring, performance metrics, and usage analytics\n */\n\ninterface HealthStatus {\n  status: 'healthy' | 'degraded' | 'unhealthy';\n  timestamp: number;\n  responseTime: number;\n  error?: string;\n}\n\ninterface ServiceMetrics {\n  totalRequests: number;\n  successfulRequests: number;\n  failedRequests: number;\n  averageResponseTime: number;\n  cacheHitRate: number;\n  rateLimitHits: number;\n  lastHealthCheck: number;\n  uptime: number;\n}\n\ninterface PerformanceMetric {\n  timestamp: number;\n  operation: string;\n  responseTime: number;\n  success: boolean;\n  cacheHit: boolean;\n  userId?: string;\n  error?: string;\n}\n\ninterface UsageAnalytics {\n  dailyRequests: Record<string, number>;\n  operationBreakdown: Record<string, number>;\n  userActivity: Record<string, number>;\n  errorPatterns: Record<string, number>;\n  peakUsageHours: number[];\n}\n\nexport class AIServiceMonitor {\n  private static instance: AIServiceMonitor;\n  private metrics: ServiceMetrics;\n  private performanceHistory: PerformanceMetric[] = [];\n  private healthHistory: HealthStatus[] = [];\n  private startTime: number;\n  private readonly MAX_HISTORY_SIZE = 1000;\n  private readonly HEALTH_CHECK_INTERVAL = 30000; // 30 seconds\n  private healthCheckTimer?: NodeJS.Timeout;\n\n  private constructor() {\n    this.startTime = Date.now();\n    this.metrics = {\n      totalRequests: 0,\n      successfulRequests: 0,\n      failedRequests: 0,\n      averageResponseTime: 0,\n      cacheHitRate: 0,\n      rateLimitHits: 0,\n      lastHealthCheck: 0,\n      uptime: 0\n    };\n    \n    this.startHealthChecking();\n  }\n\n  static getInstance(): AIServiceMonitor {\n    if (!AIServiceMonitor.instance) {\n      AIServiceMonitor.instance = new AIServiceMonitor();\n    }\n    return AIServiceMonitor.instance;\n  }\n\n  /**\n   * Record a service operation\n   */\n  recordOperation(\n    operation: string,\n    responseTime: number,\n    success: boolean,\n    cacheHit: boolean = false,\n    userId?: string,\n    error?: string\n  ): void {\n    const metric: PerformanceMetric = {\n      timestamp: Date.now(),\n      operation,\n      responseTime,\n      success,\n      cacheHit,\n      userId,\n      error\n    };\n\n    this.performanceHistory.push(metric);\n    this.trimHistory();\n    this.updateMetrics(metric);\n  }\n\n  /**\n   * Record rate limit hit\n   */\n  recordRateLimitHit(userId: string): void {\n    this.metrics.rateLimitHits++;\n    console.warn(`[AI-Monitor] Rate limit hit for user: ${userId}`);\n  }\n\n  /**\n   * Get current service health status\n   */\n  async getHealthStatus(): Promise<HealthStatus> {\n    try {\n      const startTime = Date.now();\n      \n      // Perform health checks on all components\n      const aiHealthy = await this.checkAIServiceHealth();\n      const cacheHealthy = await this.checkCacheHealth();\n      const dbHealthy = await this.checkDatabaseHealth();\n      \n      const responseTime = Date.now() - startTime;\n      const allHealthy = aiHealthy && cacheHealthy && dbHealthy;\n      \n      const status: HealthStatus = {\n        status: allHealthy ? 'healthy' : 'degraded',\n        timestamp: Date.now(),\n        responseTime\n      };\n\n      this.healthHistory.push(status);\n      this.trimHealthHistory();\n      this.metrics.lastHealthCheck = status.timestamp;\n\n      return status;\n    } catch (error) {\n      const status: HealthStatus = {\n        status: 'unhealthy',\n        timestamp: Date.now(),\n        responseTime: 0,\n        error: error instanceof Error ? error.message : 'Unknown error'\n      };\n\n      this.healthHistory.push(status);\n      this.trimHealthHistory();\n      \n      return status;\n    }\n  }\n\n  /**\n   * Get comprehensive service metrics\n   */\n  getMetrics(): ServiceMetrics & { \n    recentPerformance: PerformanceMetric[];\n    healthTrend: HealthStatus[];\n  } {\n    this.metrics.uptime = Date.now() - this.startTime;\n    \n    return {\n      ...this.metrics,\n      recentPerformance: this.performanceHistory.slice(-50), // Last 50 operations\n      healthTrend: this.healthHistory.slice(-20) // Last 20 health checks\n    };\n  }\n\n  /**\n   * Get usage analytics\n   */\n  getUsageAnalytics(): UsageAnalytics {\n    const now = new Date();\n    const last7Days = Array.from({ length: 7 }, (_, i) => {\n      const date = new Date(now);\n      date.setDate(date.getDate() - i);\n      return date.toISOString().split('T')[0];\n    });\n\n    const dailyRequests: Record<string, number> = {};\n    const operationBreakdown: Record<string, number> = {};\n    const userActivity: Record<string, number> = {};\n    const errorPatterns: Record<string, number> = {};\n    const hourlyUsage = new Array(24).fill(0);\n\n    // Initialize daily requests\n    last7Days.forEach(date => {\n      dailyRequests[date] = 0;\n    });\n\n    // Analyze performance history\n    this.performanceHistory.forEach(metric => {\n      const date = new Date(metric.timestamp).toISOString().split('T')[0];\n      const hour = new Date(metric.timestamp).getHours();\n      \n      // Daily requests\n      if (dailyRequests.hasOwnProperty(date)) {\n        dailyRequests[date]++;\n      }\n      \n      // Operation breakdown\n      operationBreakdown[metric.operation] = (operationBreakdown[metric.operation] || 0) + 1;\n      \n      // User activity\n      if (metric.userId) {\n        userActivity[metric.userId] = (userActivity[metric.userId] || 0) + 1;\n      }\n      \n      // Error patterns\n      if (!metric.success && metric.error) {\n        errorPatterns[metric.error] = (errorPatterns[metric.error] || 0) + 1;\n      }\n      \n      // Hourly usage\n      hourlyUsage[hour]++;\n    });\n\n    // Find peak usage hours\n    const peakUsageHours = hourlyUsage\n      .map((count, hour) => ({ hour, count }))\n      .sort((a, b) => b.count - a.count)\n      .slice(0, 3)\n      .map(item => item.hour);\n\n    return {\n      dailyRequests,\n      operationBreakdown,\n      userActivity,\n      errorPatterns,\n      peakUsageHours\n    };\n  }\n\n  /**\n   * Get performance insights\n   */\n  getPerformanceInsights(): {\n    slowestOperations: Array<{ operation: string; avgResponseTime: number }>;\n    errorRateByOperation: Array<{ operation: string; errorRate: number }>;\n    cacheEffectiveness: { hitRate: number; avgResponseTimeWithCache: number; avgResponseTimeWithoutCache: number };\n    recommendations: string[];\n  } {\n    const operationStats = new Map<string, { \n      totalTime: number; \n      count: number; \n      errors: number; \n      cacheHits: number;\n      cacheHitTime: number;\n      cacheMissTime: number;\n    }>();\n\n    // Analyze performance data\n    this.performanceHistory.forEach(metric => {\n      if (!operationStats.has(metric.operation)) {\n        operationStats.set(metric.operation, {\n          totalTime: 0,\n          count: 0,\n          errors: 0,\n          cacheHits: 0,\n          cacheHitTime: 0,\n          cacheMissTime: 0\n        });\n      }\n\n      const stats = operationStats.get(metric.operation)!;\n      stats.totalTime += metric.responseTime;\n      stats.count++;\n      \n      if (!metric.success) {\n        stats.errors++;\n      }\n      \n      if (metric.cacheHit) {\n        stats.cacheHits++;\n        stats.cacheHitTime += metric.responseTime;\n      } else {\n        stats.cacheMissTime += metric.responseTime;\n      }\n    });\n\n    // Calculate insights\n    const slowestOperations = Array.from(operationStats.entries())\n      .map(([operation, stats]) => ({\n        operation,\n        avgResponseTime: stats.totalTime / stats.count\n      }))\n      .sort((a, b) => b.avgResponseTime - a.avgResponseTime)\n      .slice(0, 5);\n\n    const errorRateByOperation = Array.from(operationStats.entries())\n      .map(([operation, stats]) => ({\n        operation,\n        errorRate: (stats.errors / stats.count) * 100\n      }))\n      .filter(item => item.errorRate > 0)\n      .sort((a, b) => b.errorRate - a.errorRate);\n\n    // Cache effectiveness\n    const totalCacheHits = Array.from(operationStats.values()).reduce((sum, stats) => sum + stats.cacheHits, 0);\n    const totalOperations = this.performanceHistory.length;\n    const totalCacheHitTime = Array.from(operationStats.values()).reduce((sum, stats) => sum + stats.cacheHitTime, 0);\n    const totalCacheMissTime = Array.from(operationStats.values()).reduce((sum, stats) => sum + stats.cacheMissTime, 0);\n\n    const cacheEffectiveness = {\n      hitRate: totalOperations > 0 ? (totalCacheHits / totalOperations) * 100 : 0,\n      avgResponseTimeWithCache: totalCacheHits > 0 ? totalCacheHitTime / totalCacheHits : 0,\n      avgResponseTimeWithoutCache: (totalOperations - totalCacheHits) > 0 ? totalCacheMissTime / (totalOperations - totalCacheHits) : 0\n    };\n\n    // Generate recommendations\n    const recommendations: string[] = [];\n    \n    if (this.metrics.averageResponseTime > 5000) {\n      recommendations.push('Consider optimizing AI prompts to reduce response times');\n    }\n    \n    if (cacheEffectiveness.hitRate < 30) {\n      recommendations.push('Improve caching strategy to increase cache hit rate');\n    }\n    \n    if (this.metrics.failedRequests / this.metrics.totalRequests > 0.05) {\n      recommendations.push('Investigate and address high error rate');\n    }\n    \n    if (this.metrics.rateLimitHits > 10) {\n      recommendations.push('Consider implementing request queuing to reduce rate limit hits');\n    }\n\n    return {\n      slowestOperations,\n      errorRateByOperation,\n      cacheEffectiveness,\n      recommendations\n    };\n  }\n\n  /**\n   * Start automatic health checking\n   */\n  private startHealthChecking(): void {\n    this.healthCheckTimer = setInterval(async () => {\n      await this.getHealthStatus();\n    }, this.HEALTH_CHECK_INTERVAL);\n  }\n\n  /**\n   * Stop health checking\n   */\n  stopHealthChecking(): void {\n    if (this.healthCheckTimer) {\n      clearInterval(this.healthCheckTimer);\n    }\n  }\n\n  /**\n   * Update metrics based on new performance data\n   */\n  private updateMetrics(metric: PerformanceMetric): void {\n    this.metrics.totalRequests++;\n    \n    if (metric.success) {\n      this.metrics.successfulRequests++;\n    } else {\n      this.metrics.failedRequests++;\n    }\n\n    // Update average response time\n    const totalResponseTime = this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + metric.responseTime;\n    this.metrics.averageResponseTime = totalResponseTime / this.metrics.totalRequests;\n\n    // Update cache hit rate\n    const cacheHits = this.performanceHistory.filter(m => m.cacheHit).length;\n    this.metrics.cacheHitRate = (cacheHits / this.metrics.totalRequests) * 100;\n  }\n\n  /**\n   * Trim performance history to prevent memory issues\n   */\n  private trimHistory(): void {\n    if (this.performanceHistory.length > this.MAX_HISTORY_SIZE) {\n      this.performanceHistory = this.performanceHistory.slice(-this.MAX_HISTORY_SIZE);\n    }\n  }\n\n  /**\n   * Trim health history\n   */\n  private trimHealthHistory(): void {\n    if (this.healthHistory.length > 100) {\n      this.healthHistory = this.healthHistory.slice(-100);\n    }\n  }\n\n  /**\n   * Check AI service health\n   */\n  private async checkAIServiceHealth(): Promise<boolean> {\n    try {\n      // This would typically make a lightweight request to the AI service\n      // For now, we'll simulate a health check\n      return true;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * Check cache health\n   */\n  private async checkCacheHealth(): Promise<boolean> {\n    try {\n      // Check if cache is responsive\n      return true;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * Check database health\n   */\n  private async checkDatabaseHealth(): Promise<boolean> {\n    try {\n      // Check if database is responsive\n      return true;\n    } catch {\n      return false;\n    }\n  }\n\n  /**\n   * Export metrics for external monitoring systems\n   */\n  exportMetrics(): string {\n    const metrics = this.getMetrics();\n    const analytics = this.getUsageAnalytics();\n    const insights = this.getPerformanceInsights();\n\n    return JSON.stringify({\n      timestamp: Date.now(),\n      service: 'ai-service',\n      metrics,\n      analytics,\n      insights\n    }, null, 2);\n  }\n\n  /**\n   * Reset all metrics (useful for testing)\n   */\n  reset(): void {\n    this.metrics = {\n      totalRequests: 0,\n      successfulRequests: 0,\n      failedRequests: 0,\n      averageResponseTime: 0,\n      cacheHitRate: 0,\n      rateLimitHits: 0,\n      lastHealthCheck: 0,\n      uptime: 0\n    };\n    this.performanceHistory = [];\n    this.healthHistory = [];\n    this.startTime = Date.now();\n  }\n}\n\n// Export singleton instance\nexport const aiServiceMonitor = AIServiceMonitor.getInstance();\nexport default aiServiceMonitor;\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0a0eb807da97771bbcb918343d1cfdd7f072d5b2"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_2df7457914 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2df7457914();
var __assign =
/* istanbul ignore next */
(cov_2df7457914().s[0]++,
/* istanbul ignore next */
(cov_2df7457914().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_2df7457914().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_2df7457914().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_2df7457914().f[0]++;
  cov_2df7457914().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_2df7457914().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_2df7457914().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_2df7457914().f[1]++;
    cov_2df7457914().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_2df7457914().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_2df7457914().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_2df7457914().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_2df7457914().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_2df7457914().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_2df7457914().b[2][0]++;
          cov_2df7457914().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_2df7457914().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_2df7457914().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_2df7457914().s[10]++;
  return __assign.apply(this, arguments);
}));
var __awaiter =
/* istanbul ignore next */
(cov_2df7457914().s[11]++,
/* istanbul ignore next */
(cov_2df7457914().b[3][0]++, this) &&
/* istanbul ignore next */
(cov_2df7457914().b[3][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_2df7457914().b[3][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_2df7457914().f[2]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_2df7457914().f[3]++;
    cov_2df7457914().s[12]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_2df7457914().b[4][0]++, value) :
    /* istanbul ignore next */
    (cov_2df7457914().b[4][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_2df7457914().f[4]++;
      cov_2df7457914().s[13]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_2df7457914().s[14]++;
  return new (
  /* istanbul ignore next */
  (cov_2df7457914().b[5][0]++, P) ||
  /* istanbul ignore next */
  (cov_2df7457914().b[5][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_2df7457914().f[5]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_2df7457914().f[6]++;
      cov_2df7457914().s[15]++;
      try {
        /* istanbul ignore next */
        cov_2df7457914().s[16]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2df7457914().s[17]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_2df7457914().f[7]++;
      cov_2df7457914().s[18]++;
      try {
        /* istanbul ignore next */
        cov_2df7457914().s[19]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_2df7457914().s[20]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_2df7457914().f[8]++;
      cov_2df7457914().s[21]++;
      result.done ?
      /* istanbul ignore next */
      (cov_2df7457914().b[6][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_2df7457914().b[6][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_2df7457914().s[22]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_2df7457914().b[7][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_2df7457914().b[7][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_2df7457914().s[23]++,
/* istanbul ignore next */
(cov_2df7457914().b[8][0]++, this) &&
/* istanbul ignore next */
(cov_2df7457914().b[8][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_2df7457914().b[8][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_2df7457914().f[9]++;
  var _ =
    /* istanbul ignore next */
    (cov_2df7457914().s[24]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_2df7457914().f[10]++;
        cov_2df7457914().s[25]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_2df7457914().b[9][0]++;
          cov_2df7457914().s[26]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_2df7457914().b[9][1]++;
        }
        cov_2df7457914().s[27]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_2df7457914().s[28]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_2df7457914().b[10][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_2df7457914().b[10][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_2df7457914().s[29]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_2df7457914().b[11][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_2df7457914().b[11][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_2df7457914().f[11]++;
    cov_2df7457914().s[30]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_2df7457914().f[12]++;
    cov_2df7457914().s[31]++;
    return function (v) {
      /* istanbul ignore next */
      cov_2df7457914().f[13]++;
      cov_2df7457914().s[32]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_2df7457914().f[14]++;
    cov_2df7457914().s[33]++;
    if (f) {
      /* istanbul ignore next */
      cov_2df7457914().b[12][0]++;
      cov_2df7457914().s[34]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_2df7457914().b[12][1]++;
    }
    cov_2df7457914().s[35]++;
    while (
    /* istanbul ignore next */
    (cov_2df7457914().b[13][0]++, g) &&
    /* istanbul ignore next */
    (cov_2df7457914().b[13][1]++, g = 0,
    /* istanbul ignore next */
    (cov_2df7457914().b[14][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_2df7457914().b[14][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_2df7457914().s[36]++;
      try {
        /* istanbul ignore next */
        cov_2df7457914().s[37]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_2df7457914().b[16][0]++, y) &&
        /* istanbul ignore next */
        (cov_2df7457914().b[16][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_2df7457914().b[17][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_2df7457914().b[17][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_2df7457914().b[18][0]++,
        /* istanbul ignore next */
        (cov_2df7457914().b[19][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_2df7457914().b[19][1]++,
        /* istanbul ignore next */
        (cov_2df7457914().b[20][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_2df7457914().b[20][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_2df7457914().b[18][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_2df7457914().b[16][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_2df7457914().b[15][0]++;
          cov_2df7457914().s[38]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_2df7457914().b[15][1]++;
        }
        cov_2df7457914().s[39]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_2df7457914().b[21][0]++;
          cov_2df7457914().s[40]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_2df7457914().b[21][1]++;
        }
        cov_2df7457914().s[41]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_2df7457914().b[22][0]++;
          case 1:
            /* istanbul ignore next */
            cov_2df7457914().b[22][1]++;
            cov_2df7457914().s[42]++;
            t = op;
            /* istanbul ignore next */
            cov_2df7457914().s[43]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_2df7457914().b[22][2]++;
            cov_2df7457914().s[44]++;
            _.label++;
            /* istanbul ignore next */
            cov_2df7457914().s[45]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_2df7457914().b[22][3]++;
            cov_2df7457914().s[46]++;
            _.label++;
            /* istanbul ignore next */
            cov_2df7457914().s[47]++;
            y = op[1];
            /* istanbul ignore next */
            cov_2df7457914().s[48]++;
            op = [0];
            /* istanbul ignore next */
            cov_2df7457914().s[49]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_2df7457914().b[22][4]++;
            cov_2df7457914().s[50]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_2df7457914().s[51]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2df7457914().s[52]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_2df7457914().b[22][5]++;
            cov_2df7457914().s[53]++;
            if (
            /* istanbul ignore next */
            (cov_2df7457914().b[24][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_2df7457914().b[25][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_2df7457914().b[25][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_2df7457914().b[24][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_2df7457914().b[24][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_2df7457914().b[23][0]++;
              cov_2df7457914().s[54]++;
              _ = 0;
              /* istanbul ignore next */
              cov_2df7457914().s[55]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_2df7457914().b[23][1]++;
            }
            cov_2df7457914().s[56]++;
            if (
            /* istanbul ignore next */
            (cov_2df7457914().b[27][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_2df7457914().b[27][1]++, !t) ||
            /* istanbul ignore next */
            (cov_2df7457914().b[27][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_2df7457914().b[27][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_2df7457914().b[26][0]++;
              cov_2df7457914().s[57]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_2df7457914().s[58]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2df7457914().b[26][1]++;
            }
            cov_2df7457914().s[59]++;
            if (
            /* istanbul ignore next */
            (cov_2df7457914().b[29][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_2df7457914().b[29][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_2df7457914().b[28][0]++;
              cov_2df7457914().s[60]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_2df7457914().s[61]++;
              t = op;
              /* istanbul ignore next */
              cov_2df7457914().s[62]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2df7457914().b[28][1]++;
            }
            cov_2df7457914().s[63]++;
            if (
            /* istanbul ignore next */
            (cov_2df7457914().b[31][0]++, t) &&
            /* istanbul ignore next */
            (cov_2df7457914().b[31][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_2df7457914().b[30][0]++;
              cov_2df7457914().s[64]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_2df7457914().s[65]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_2df7457914().s[66]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_2df7457914().b[30][1]++;
            }
            cov_2df7457914().s[67]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_2df7457914().b[32][0]++;
              cov_2df7457914().s[68]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_2df7457914().b[32][1]++;
            }
            cov_2df7457914().s[69]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_2df7457914().s[70]++;
            continue;
        }
        /* istanbul ignore next */
        cov_2df7457914().s[71]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_2df7457914().s[72]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_2df7457914().s[73]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_2df7457914().s[74]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_2df7457914().s[75]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_2df7457914().b[33][0]++;
      cov_2df7457914().s[76]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_2df7457914().b[33][1]++;
    }
    cov_2df7457914().s[77]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_2df7457914().b[34][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_2df7457914().b[34][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_2df7457914().s[78]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_2df7457914().s[79]++;
exports.aiServiceMonitor = exports.AIServiceMonitor = void 0;
var AIServiceMonitor =
/* istanbul ignore next */
(/** @class */cov_2df7457914().s[80]++, function () {
  /* istanbul ignore next */
  cov_2df7457914().f[15]++;
  function AIServiceMonitor() {
    /* istanbul ignore next */
    cov_2df7457914().f[16]++;
    cov_2df7457914().s[81]++;
    this.performanceHistory = [];
    /* istanbul ignore next */
    cov_2df7457914().s[82]++;
    this.healthHistory = [];
    /* istanbul ignore next */
    cov_2df7457914().s[83]++;
    this.MAX_HISTORY_SIZE = 1000;
    /* istanbul ignore next */
    cov_2df7457914().s[84]++;
    this.HEALTH_CHECK_INTERVAL = 30000; // 30 seconds
    /* istanbul ignore next */
    cov_2df7457914().s[85]++;
    this.startTime = Date.now();
    /* istanbul ignore next */
    cov_2df7457914().s[86]++;
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      cacheHitRate: 0,
      rateLimitHits: 0,
      lastHealthCheck: 0,
      uptime: 0
    };
    /* istanbul ignore next */
    cov_2df7457914().s[87]++;
    this.startHealthChecking();
  }
  /* istanbul ignore next */
  cov_2df7457914().s[88]++;
  AIServiceMonitor.getInstance = function () {
    /* istanbul ignore next */
    cov_2df7457914().f[17]++;
    cov_2df7457914().s[89]++;
    if (!AIServiceMonitor.instance) {
      /* istanbul ignore next */
      cov_2df7457914().b[35][0]++;
      cov_2df7457914().s[90]++;
      AIServiceMonitor.instance = new AIServiceMonitor();
    } else
    /* istanbul ignore next */
    {
      cov_2df7457914().b[35][1]++;
    }
    cov_2df7457914().s[91]++;
    return AIServiceMonitor.instance;
  };
  /**
   * Record a service operation
   */
  /* istanbul ignore next */
  cov_2df7457914().s[92]++;
  AIServiceMonitor.prototype.recordOperation = function (operation, responseTime, success, cacheHit, userId, error) {
    /* istanbul ignore next */
    cov_2df7457914().f[18]++;
    cov_2df7457914().s[93]++;
    if (cacheHit === void 0) {
      /* istanbul ignore next */
      cov_2df7457914().b[36][0]++;
      cov_2df7457914().s[94]++;
      cacheHit = false;
    } else
    /* istanbul ignore next */
    {
      cov_2df7457914().b[36][1]++;
    }
    var metric =
    /* istanbul ignore next */
    (cov_2df7457914().s[95]++, {
      timestamp: Date.now(),
      operation: operation,
      responseTime: responseTime,
      success: success,
      cacheHit: cacheHit,
      userId: userId,
      error: error
    });
    /* istanbul ignore next */
    cov_2df7457914().s[96]++;
    this.performanceHistory.push(metric);
    /* istanbul ignore next */
    cov_2df7457914().s[97]++;
    this.trimHistory();
    /* istanbul ignore next */
    cov_2df7457914().s[98]++;
    this.updateMetrics(metric);
  };
  /**
   * Record rate limit hit
   */
  /* istanbul ignore next */
  cov_2df7457914().s[99]++;
  AIServiceMonitor.prototype.recordRateLimitHit = function (userId) {
    /* istanbul ignore next */
    cov_2df7457914().f[19]++;
    cov_2df7457914().s[100]++;
    this.metrics.rateLimitHits++;
    /* istanbul ignore next */
    cov_2df7457914().s[101]++;
    console.warn("[AI-Monitor] Rate limit hit for user: ".concat(userId));
  };
  /**
   * Get current service health status
   */
  /* istanbul ignore next */
  cov_2df7457914().s[102]++;
  AIServiceMonitor.prototype.getHealthStatus = function () {
    /* istanbul ignore next */
    cov_2df7457914().f[20]++;
    cov_2df7457914().s[103]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_2df7457914().f[21]++;
      var startTime, aiHealthy, cacheHealthy, dbHealthy, responseTime, allHealthy, status, error_1, status;
      /* istanbul ignore next */
      cov_2df7457914().s[104]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_2df7457914().f[22]++;
        cov_2df7457914().s[105]++;
        switch (_a.label) {
          case 0:
            /* istanbul ignore next */
            cov_2df7457914().b[37][0]++;
            cov_2df7457914().s[106]++;
            _a.trys.push([0, 4,, 5]);
            /* istanbul ignore next */
            cov_2df7457914().s[107]++;
            startTime = Date.now();
            /* istanbul ignore next */
            cov_2df7457914().s[108]++;
            return [4 /*yield*/, this.checkAIServiceHealth()];
          case 1:
            /* istanbul ignore next */
            cov_2df7457914().b[37][1]++;
            cov_2df7457914().s[109]++;
            aiHealthy = _a.sent();
            /* istanbul ignore next */
            cov_2df7457914().s[110]++;
            return [4 /*yield*/, this.checkCacheHealth()];
          case 2:
            /* istanbul ignore next */
            cov_2df7457914().b[37][2]++;
            cov_2df7457914().s[111]++;
            cacheHealthy = _a.sent();
            /* istanbul ignore next */
            cov_2df7457914().s[112]++;
            return [4 /*yield*/, this.checkDatabaseHealth()];
          case 3:
            /* istanbul ignore next */
            cov_2df7457914().b[37][3]++;
            cov_2df7457914().s[113]++;
            dbHealthy = _a.sent();
            /* istanbul ignore next */
            cov_2df7457914().s[114]++;
            responseTime = Date.now() - startTime;
            /* istanbul ignore next */
            cov_2df7457914().s[115]++;
            allHealthy =
            /* istanbul ignore next */
            (cov_2df7457914().b[38][0]++, aiHealthy) &&
            /* istanbul ignore next */
            (cov_2df7457914().b[38][1]++, cacheHealthy) &&
            /* istanbul ignore next */
            (cov_2df7457914().b[38][2]++, dbHealthy);
            /* istanbul ignore next */
            cov_2df7457914().s[116]++;
            status = {
              status: allHealthy ?
              /* istanbul ignore next */
              (cov_2df7457914().b[39][0]++, 'healthy') :
              /* istanbul ignore next */
              (cov_2df7457914().b[39][1]++, 'degraded'),
              timestamp: Date.now(),
              responseTime: responseTime
            };
            /* istanbul ignore next */
            cov_2df7457914().s[117]++;
            this.healthHistory.push(status);
            /* istanbul ignore next */
            cov_2df7457914().s[118]++;
            this.trimHealthHistory();
            /* istanbul ignore next */
            cov_2df7457914().s[119]++;
            this.metrics.lastHealthCheck = status.timestamp;
            /* istanbul ignore next */
            cov_2df7457914().s[120]++;
            return [2 /*return*/, status];
          case 4:
            /* istanbul ignore next */
            cov_2df7457914().b[37][4]++;
            cov_2df7457914().s[121]++;
            error_1 = _a.sent();
            /* istanbul ignore next */
            cov_2df7457914().s[122]++;
            status = {
              status: 'unhealthy',
              timestamp: Date.now(),
              responseTime: 0,
              error: error_1 instanceof Error ?
              /* istanbul ignore next */
              (cov_2df7457914().b[40][0]++, error_1.message) :
              /* istanbul ignore next */
              (cov_2df7457914().b[40][1]++, 'Unknown error')
            };
            /* istanbul ignore next */
            cov_2df7457914().s[123]++;
            this.healthHistory.push(status);
            /* istanbul ignore next */
            cov_2df7457914().s[124]++;
            this.trimHealthHistory();
            /* istanbul ignore next */
            cov_2df7457914().s[125]++;
            return [2 /*return*/, status];
          case 5:
            /* istanbul ignore next */
            cov_2df7457914().b[37][5]++;
            cov_2df7457914().s[126]++;
            return [2 /*return*/];
        }
      });
    });
  };
  /**
   * Get comprehensive service metrics
   */
  /* istanbul ignore next */
  cov_2df7457914().s[127]++;
  AIServiceMonitor.prototype.getMetrics = function () {
    /* istanbul ignore next */
    cov_2df7457914().f[23]++;
    cov_2df7457914().s[128]++;
    this.metrics.uptime = Date.now() - this.startTime;
    /* istanbul ignore next */
    cov_2df7457914().s[129]++;
    return __assign(__assign({}, this.metrics), {
      recentPerformance: this.performanceHistory.slice(-50),
      healthTrend: this.healthHistory.slice(-20) // Last 20 health checks
    });
  };
  /**
   * Get usage analytics
   */
  /* istanbul ignore next */
  cov_2df7457914().s[130]++;
  AIServiceMonitor.prototype.getUsageAnalytics = function () {
    /* istanbul ignore next */
    cov_2df7457914().f[24]++;
    var now =
    /* istanbul ignore next */
    (cov_2df7457914().s[131]++, new Date());
    var last7Days =
    /* istanbul ignore next */
    (cov_2df7457914().s[132]++, Array.from({
      length: 7
    }, function (_, i) {
      /* istanbul ignore next */
      cov_2df7457914().f[25]++;
      var date =
      /* istanbul ignore next */
      (cov_2df7457914().s[133]++, new Date(now));
      /* istanbul ignore next */
      cov_2df7457914().s[134]++;
      date.setDate(date.getDate() - i);
      /* istanbul ignore next */
      cov_2df7457914().s[135]++;
      return date.toISOString().split('T')[0];
    }));
    var dailyRequests =
    /* istanbul ignore next */
    (cov_2df7457914().s[136]++, {});
    var operationBreakdown =
    /* istanbul ignore next */
    (cov_2df7457914().s[137]++, {});
    var userActivity =
    /* istanbul ignore next */
    (cov_2df7457914().s[138]++, {});
    var errorPatterns =
    /* istanbul ignore next */
    (cov_2df7457914().s[139]++, {});
    var hourlyUsage =
    /* istanbul ignore next */
    (cov_2df7457914().s[140]++, new Array(24).fill(0));
    // Initialize daily requests
    /* istanbul ignore next */
    cov_2df7457914().s[141]++;
    last7Days.forEach(function (date) {
      /* istanbul ignore next */
      cov_2df7457914().f[26]++;
      cov_2df7457914().s[142]++;
      dailyRequests[date] = 0;
    });
    // Analyze performance history
    /* istanbul ignore next */
    cov_2df7457914().s[143]++;
    this.performanceHistory.forEach(function (metric) {
      /* istanbul ignore next */
      cov_2df7457914().f[27]++;
      var date =
      /* istanbul ignore next */
      (cov_2df7457914().s[144]++, new Date(metric.timestamp).toISOString().split('T')[0]);
      var hour =
      /* istanbul ignore next */
      (cov_2df7457914().s[145]++, new Date(metric.timestamp).getHours());
      // Daily requests
      /* istanbul ignore next */
      cov_2df7457914().s[146]++;
      if (dailyRequests.hasOwnProperty(date)) {
        /* istanbul ignore next */
        cov_2df7457914().b[41][0]++;
        cov_2df7457914().s[147]++;
        dailyRequests[date]++;
      } else
      /* istanbul ignore next */
      {
        cov_2df7457914().b[41][1]++;
      }
      // Operation breakdown
      cov_2df7457914().s[148]++;
      operationBreakdown[metric.operation] = (
      /* istanbul ignore next */
      (cov_2df7457914().b[42][0]++, operationBreakdown[metric.operation]) ||
      /* istanbul ignore next */
      (cov_2df7457914().b[42][1]++, 0)) + 1;
      // User activity
      /* istanbul ignore next */
      cov_2df7457914().s[149]++;
      if (metric.userId) {
        /* istanbul ignore next */
        cov_2df7457914().b[43][0]++;
        cov_2df7457914().s[150]++;
        userActivity[metric.userId] = (
        /* istanbul ignore next */
        (cov_2df7457914().b[44][0]++, userActivity[metric.userId]) ||
        /* istanbul ignore next */
        (cov_2df7457914().b[44][1]++, 0)) + 1;
      } else
      /* istanbul ignore next */
      {
        cov_2df7457914().b[43][1]++;
      }
      // Error patterns
      cov_2df7457914().s[151]++;
      if (
      /* istanbul ignore next */
      (cov_2df7457914().b[46][0]++, !metric.success) &&
      /* istanbul ignore next */
      (cov_2df7457914().b[46][1]++, metric.error)) {
        /* istanbul ignore next */
        cov_2df7457914().b[45][0]++;
        cov_2df7457914().s[152]++;
        errorPatterns[metric.error] = (
        /* istanbul ignore next */
        (cov_2df7457914().b[47][0]++, errorPatterns[metric.error]) ||
        /* istanbul ignore next */
        (cov_2df7457914().b[47][1]++, 0)) + 1;
      } else
      /* istanbul ignore next */
      {
        cov_2df7457914().b[45][1]++;
      }
      // Hourly usage
      cov_2df7457914().s[153]++;
      hourlyUsage[hour]++;
    });
    // Find peak usage hours
    var peakUsageHours =
    /* istanbul ignore next */
    (cov_2df7457914().s[154]++, hourlyUsage.map(function (count, hour) {
      /* istanbul ignore next */
      cov_2df7457914().f[28]++;
      cov_2df7457914().s[155]++;
      return {
        hour: hour,
        count: count
      };
    }).sort(function (a, b) {
      /* istanbul ignore next */
      cov_2df7457914().f[29]++;
      cov_2df7457914().s[156]++;
      return b.count - a.count;
    }).slice(0, 3).map(function (item) {
      /* istanbul ignore next */
      cov_2df7457914().f[30]++;
      cov_2df7457914().s[157]++;
      return item.hour;
    }));
    /* istanbul ignore next */
    cov_2df7457914().s[158]++;
    return {
      dailyRequests: dailyRequests,
      operationBreakdown: operationBreakdown,
      userActivity: userActivity,
      errorPatterns: errorPatterns,
      peakUsageHours: peakUsageHours
    };
  };
  /**
   * Get performance insights
   */
  /* istanbul ignore next */
  cov_2df7457914().s[159]++;
  AIServiceMonitor.prototype.getPerformanceInsights = function () {
    /* istanbul ignore next */
    cov_2df7457914().f[31]++;
    var operationStats =
    /* istanbul ignore next */
    (cov_2df7457914().s[160]++, new Map());
    // Analyze performance data
    /* istanbul ignore next */
    cov_2df7457914().s[161]++;
    this.performanceHistory.forEach(function (metric) {
      /* istanbul ignore next */
      cov_2df7457914().f[32]++;
      cov_2df7457914().s[162]++;
      if (!operationStats.has(metric.operation)) {
        /* istanbul ignore next */
        cov_2df7457914().b[48][0]++;
        cov_2df7457914().s[163]++;
        operationStats.set(metric.operation, {
          totalTime: 0,
          count: 0,
          errors: 0,
          cacheHits: 0,
          cacheHitTime: 0,
          cacheMissTime: 0
        });
      } else
      /* istanbul ignore next */
      {
        cov_2df7457914().b[48][1]++;
      }
      var stats =
      /* istanbul ignore next */
      (cov_2df7457914().s[164]++, operationStats.get(metric.operation));
      /* istanbul ignore next */
      cov_2df7457914().s[165]++;
      stats.totalTime += metric.responseTime;
      /* istanbul ignore next */
      cov_2df7457914().s[166]++;
      stats.count++;
      /* istanbul ignore next */
      cov_2df7457914().s[167]++;
      if (!metric.success) {
        /* istanbul ignore next */
        cov_2df7457914().b[49][0]++;
        cov_2df7457914().s[168]++;
        stats.errors++;
      } else
      /* istanbul ignore next */
      {
        cov_2df7457914().b[49][1]++;
      }
      cov_2df7457914().s[169]++;
      if (metric.cacheHit) {
        /* istanbul ignore next */
        cov_2df7457914().b[50][0]++;
        cov_2df7457914().s[170]++;
        stats.cacheHits++;
        /* istanbul ignore next */
        cov_2df7457914().s[171]++;
        stats.cacheHitTime += metric.responseTime;
      } else {
        /* istanbul ignore next */
        cov_2df7457914().b[50][1]++;
        cov_2df7457914().s[172]++;
        stats.cacheMissTime += metric.responseTime;
      }
    });
    // Calculate insights
    var slowestOperations =
    /* istanbul ignore next */
    (cov_2df7457914().s[173]++, Array.from(operationStats.entries()).map(function (_a) {
      /* istanbul ignore next */
      cov_2df7457914().f[33]++;
      var operation =
        /* istanbul ignore next */
        (cov_2df7457914().s[174]++, _a[0]),
        stats =
        /* istanbul ignore next */
        (cov_2df7457914().s[175]++, _a[1]);
      /* istanbul ignore next */
      cov_2df7457914().s[176]++;
      return {
        operation: operation,
        avgResponseTime: stats.totalTime / stats.count
      };
    }).sort(function (a, b) {
      /* istanbul ignore next */
      cov_2df7457914().f[34]++;
      cov_2df7457914().s[177]++;
      return b.avgResponseTime - a.avgResponseTime;
    }).slice(0, 5));
    var errorRateByOperation =
    /* istanbul ignore next */
    (cov_2df7457914().s[178]++, Array.from(operationStats.entries()).map(function (_a) {
      /* istanbul ignore next */
      cov_2df7457914().f[35]++;
      var operation =
        /* istanbul ignore next */
        (cov_2df7457914().s[179]++, _a[0]),
        stats =
        /* istanbul ignore next */
        (cov_2df7457914().s[180]++, _a[1]);
      /* istanbul ignore next */
      cov_2df7457914().s[181]++;
      return {
        operation: operation,
        errorRate: stats.errors / stats.count * 100
      };
    }).filter(function (item) {
      /* istanbul ignore next */
      cov_2df7457914().f[36]++;
      cov_2df7457914().s[182]++;
      return item.errorRate > 0;
    }).sort(function (a, b) {
      /* istanbul ignore next */
      cov_2df7457914().f[37]++;
      cov_2df7457914().s[183]++;
      return b.errorRate - a.errorRate;
    }));
    // Cache effectiveness
    var totalCacheHits =
    /* istanbul ignore next */
    (cov_2df7457914().s[184]++, Array.from(operationStats.values()).reduce(function (sum, stats) {
      /* istanbul ignore next */
      cov_2df7457914().f[38]++;
      cov_2df7457914().s[185]++;
      return sum + stats.cacheHits;
    }, 0));
    var totalOperations =
    /* istanbul ignore next */
    (cov_2df7457914().s[186]++, this.performanceHistory.length);
    var totalCacheHitTime =
    /* istanbul ignore next */
    (cov_2df7457914().s[187]++, Array.from(operationStats.values()).reduce(function (sum, stats) {
      /* istanbul ignore next */
      cov_2df7457914().f[39]++;
      cov_2df7457914().s[188]++;
      return sum + stats.cacheHitTime;
    }, 0));
    var totalCacheMissTime =
    /* istanbul ignore next */
    (cov_2df7457914().s[189]++, Array.from(operationStats.values()).reduce(function (sum, stats) {
      /* istanbul ignore next */
      cov_2df7457914().f[40]++;
      cov_2df7457914().s[190]++;
      return sum + stats.cacheMissTime;
    }, 0));
    var cacheEffectiveness =
    /* istanbul ignore next */
    (cov_2df7457914().s[191]++, {
      hitRate: totalOperations > 0 ?
      /* istanbul ignore next */
      (cov_2df7457914().b[51][0]++, totalCacheHits / totalOperations * 100) :
      /* istanbul ignore next */
      (cov_2df7457914().b[51][1]++, 0),
      avgResponseTimeWithCache: totalCacheHits > 0 ?
      /* istanbul ignore next */
      (cov_2df7457914().b[52][0]++, totalCacheHitTime / totalCacheHits) :
      /* istanbul ignore next */
      (cov_2df7457914().b[52][1]++, 0),
      avgResponseTimeWithoutCache: totalOperations - totalCacheHits > 0 ?
      /* istanbul ignore next */
      (cov_2df7457914().b[53][0]++, totalCacheMissTime / (totalOperations - totalCacheHits)) :
      /* istanbul ignore next */
      (cov_2df7457914().b[53][1]++, 0)
    });
    // Generate recommendations
    var recommendations =
    /* istanbul ignore next */
    (cov_2df7457914().s[192]++, []);
    /* istanbul ignore next */
    cov_2df7457914().s[193]++;
    if (this.metrics.averageResponseTime > 5000) {
      /* istanbul ignore next */
      cov_2df7457914().b[54][0]++;
      cov_2df7457914().s[194]++;
      recommendations.push('Consider optimizing AI prompts to reduce response times');
    } else
    /* istanbul ignore next */
    {
      cov_2df7457914().b[54][1]++;
    }
    cov_2df7457914().s[195]++;
    if (cacheEffectiveness.hitRate < 30) {
      /* istanbul ignore next */
      cov_2df7457914().b[55][0]++;
      cov_2df7457914().s[196]++;
      recommendations.push('Improve caching strategy to increase cache hit rate');
    } else
    /* istanbul ignore next */
    {
      cov_2df7457914().b[55][1]++;
    }
    cov_2df7457914().s[197]++;
    if (this.metrics.failedRequests / this.metrics.totalRequests > 0.05) {
      /* istanbul ignore next */
      cov_2df7457914().b[56][0]++;
      cov_2df7457914().s[198]++;
      recommendations.push('Investigate and address high error rate');
    } else
    /* istanbul ignore next */
    {
      cov_2df7457914().b[56][1]++;
    }
    cov_2df7457914().s[199]++;
    if (this.metrics.rateLimitHits > 10) {
      /* istanbul ignore next */
      cov_2df7457914().b[57][0]++;
      cov_2df7457914().s[200]++;
      recommendations.push('Consider implementing request queuing to reduce rate limit hits');
    } else
    /* istanbul ignore next */
    {
      cov_2df7457914().b[57][1]++;
    }
    cov_2df7457914().s[201]++;
    return {
      slowestOperations: slowestOperations,
      errorRateByOperation: errorRateByOperation,
      cacheEffectiveness: cacheEffectiveness,
      recommendations: recommendations
    };
  };
  /**
   * Start automatic health checking
   */
  /* istanbul ignore next */
  cov_2df7457914().s[202]++;
  AIServiceMonitor.prototype.startHealthChecking = function () {
    /* istanbul ignore next */
    cov_2df7457914().f[41]++;
    var _this =
    /* istanbul ignore next */
    (cov_2df7457914().s[203]++, this);
    /* istanbul ignore next */
    cov_2df7457914().s[204]++;
    this.healthCheckTimer = setInterval(function () {
      /* istanbul ignore next */
      cov_2df7457914().f[42]++;
      cov_2df7457914().s[205]++;
      return __awaiter(_this, void 0, void 0, function () {
        /* istanbul ignore next */
        cov_2df7457914().f[43]++;
        cov_2df7457914().s[206]++;
        return __generator(this, function (_a) {
          /* istanbul ignore next */
          cov_2df7457914().f[44]++;
          cov_2df7457914().s[207]++;
          switch (_a.label) {
            case 0:
              /* istanbul ignore next */
              cov_2df7457914().b[58][0]++;
              cov_2df7457914().s[208]++;
              return [4 /*yield*/, this.getHealthStatus()];
            case 1:
              /* istanbul ignore next */
              cov_2df7457914().b[58][1]++;
              cov_2df7457914().s[209]++;
              _a.sent();
              /* istanbul ignore next */
              cov_2df7457914().s[210]++;
              return [2 /*return*/];
          }
        });
      });
    }, this.HEALTH_CHECK_INTERVAL);
  };
  /**
   * Stop health checking
   */
  /* istanbul ignore next */
  cov_2df7457914().s[211]++;
  AIServiceMonitor.prototype.stopHealthChecking = function () {
    /* istanbul ignore next */
    cov_2df7457914().f[45]++;
    cov_2df7457914().s[212]++;
    if (this.healthCheckTimer) {
      /* istanbul ignore next */
      cov_2df7457914().b[59][0]++;
      cov_2df7457914().s[213]++;
      clearInterval(this.healthCheckTimer);
    } else
    /* istanbul ignore next */
    {
      cov_2df7457914().b[59][1]++;
    }
  };
  /**
   * Update metrics based on new performance data
   */
  /* istanbul ignore next */
  cov_2df7457914().s[214]++;
  AIServiceMonitor.prototype.updateMetrics = function (metric) {
    /* istanbul ignore next */
    cov_2df7457914().f[46]++;
    cov_2df7457914().s[215]++;
    this.metrics.totalRequests++;
    /* istanbul ignore next */
    cov_2df7457914().s[216]++;
    if (metric.success) {
      /* istanbul ignore next */
      cov_2df7457914().b[60][0]++;
      cov_2df7457914().s[217]++;
      this.metrics.successfulRequests++;
    } else {
      /* istanbul ignore next */
      cov_2df7457914().b[60][1]++;
      cov_2df7457914().s[218]++;
      this.metrics.failedRequests++;
    }
    // Update average response time
    var totalResponseTime =
    /* istanbul ignore next */
    (cov_2df7457914().s[219]++, this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + metric.responseTime);
    /* istanbul ignore next */
    cov_2df7457914().s[220]++;
    this.metrics.averageResponseTime = totalResponseTime / this.metrics.totalRequests;
    // Update cache hit rate
    var cacheHits =
    /* istanbul ignore next */
    (cov_2df7457914().s[221]++, this.performanceHistory.filter(function (m) {
      /* istanbul ignore next */
      cov_2df7457914().f[47]++;
      cov_2df7457914().s[222]++;
      return m.cacheHit;
    }).length);
    /* istanbul ignore next */
    cov_2df7457914().s[223]++;
    this.metrics.cacheHitRate = cacheHits / this.metrics.totalRequests * 100;
  };
  /**
   * Trim performance history to prevent memory issues
   */
  /* istanbul ignore next */
  cov_2df7457914().s[224]++;
  AIServiceMonitor.prototype.trimHistory = function () {
    /* istanbul ignore next */
    cov_2df7457914().f[48]++;
    cov_2df7457914().s[225]++;
    if (this.performanceHistory.length > this.MAX_HISTORY_SIZE) {
      /* istanbul ignore next */
      cov_2df7457914().b[61][0]++;
      cov_2df7457914().s[226]++;
      this.performanceHistory = this.performanceHistory.slice(-this.MAX_HISTORY_SIZE);
    } else
    /* istanbul ignore next */
    {
      cov_2df7457914().b[61][1]++;
    }
  };
  /**
   * Trim health history
   */
  /* istanbul ignore next */
  cov_2df7457914().s[227]++;
  AIServiceMonitor.prototype.trimHealthHistory = function () {
    /* istanbul ignore next */
    cov_2df7457914().f[49]++;
    cov_2df7457914().s[228]++;
    if (this.healthHistory.length > 100) {
      /* istanbul ignore next */
      cov_2df7457914().b[62][0]++;
      cov_2df7457914().s[229]++;
      this.healthHistory = this.healthHistory.slice(-100);
    } else
    /* istanbul ignore next */
    {
      cov_2df7457914().b[62][1]++;
    }
  };
  /**
   * Check AI service health
   */
  /* istanbul ignore next */
  cov_2df7457914().s[230]++;
  AIServiceMonitor.prototype.checkAIServiceHealth = function () {
    /* istanbul ignore next */
    cov_2df7457914().f[50]++;
    cov_2df7457914().s[231]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_2df7457914().f[51]++;
      cov_2df7457914().s[232]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_2df7457914().f[52]++;
        cov_2df7457914().s[233]++;
        try {
          /* istanbul ignore next */
          cov_2df7457914().s[234]++;
          // This would typically make a lightweight request to the AI service
          // For now, we'll simulate a health check
          return [2 /*return*/, true];
        } catch (_b) {
          /* istanbul ignore next */
          cov_2df7457914().s[235]++;
          return [2 /*return*/, false];
        }
        /* istanbul ignore next */
        cov_2df7457914().s[236]++;
        return [2 /*return*/];
      });
    });
  };
  /**
   * Check cache health
   */
  /* istanbul ignore next */
  cov_2df7457914().s[237]++;
  AIServiceMonitor.prototype.checkCacheHealth = function () {
    /* istanbul ignore next */
    cov_2df7457914().f[53]++;
    cov_2df7457914().s[238]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_2df7457914().f[54]++;
      cov_2df7457914().s[239]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_2df7457914().f[55]++;
        cov_2df7457914().s[240]++;
        try {
          /* istanbul ignore next */
          cov_2df7457914().s[241]++;
          // Check if cache is responsive
          return [2 /*return*/, true];
        } catch (_b) {
          /* istanbul ignore next */
          cov_2df7457914().s[242]++;
          return [2 /*return*/, false];
        }
        /* istanbul ignore next */
        cov_2df7457914().s[243]++;
        return [2 /*return*/];
      });
    });
  };
  /**
   * Check database health
   */
  /* istanbul ignore next */
  cov_2df7457914().s[244]++;
  AIServiceMonitor.prototype.checkDatabaseHealth = function () {
    /* istanbul ignore next */
    cov_2df7457914().f[56]++;
    cov_2df7457914().s[245]++;
    return __awaiter(this, void 0, Promise, function () {
      /* istanbul ignore next */
      cov_2df7457914().f[57]++;
      cov_2df7457914().s[246]++;
      return __generator(this, function (_a) {
        /* istanbul ignore next */
        cov_2df7457914().f[58]++;
        cov_2df7457914().s[247]++;
        try {
          /* istanbul ignore next */
          cov_2df7457914().s[248]++;
          // Check if database is responsive
          return [2 /*return*/, true];
        } catch (_b) {
          /* istanbul ignore next */
          cov_2df7457914().s[249]++;
          return [2 /*return*/, false];
        }
        /* istanbul ignore next */
        cov_2df7457914().s[250]++;
        return [2 /*return*/];
      });
    });
  };
  /**
   * Export metrics for external monitoring systems
   */
  /* istanbul ignore next */
  cov_2df7457914().s[251]++;
  AIServiceMonitor.prototype.exportMetrics = function () {
    /* istanbul ignore next */
    cov_2df7457914().f[59]++;
    var metrics =
    /* istanbul ignore next */
    (cov_2df7457914().s[252]++, this.getMetrics());
    var analytics =
    /* istanbul ignore next */
    (cov_2df7457914().s[253]++, this.getUsageAnalytics());
    var insights =
    /* istanbul ignore next */
    (cov_2df7457914().s[254]++, this.getPerformanceInsights());
    /* istanbul ignore next */
    cov_2df7457914().s[255]++;
    return JSON.stringify({
      timestamp: Date.now(),
      service: 'ai-service',
      metrics: metrics,
      analytics: analytics,
      insights: insights
    }, null, 2);
  };
  /**
   * Reset all metrics (useful for testing)
   */
  /* istanbul ignore next */
  cov_2df7457914().s[256]++;
  AIServiceMonitor.prototype.reset = function () {
    /* istanbul ignore next */
    cov_2df7457914().f[60]++;
    cov_2df7457914().s[257]++;
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      cacheHitRate: 0,
      rateLimitHits: 0,
      lastHealthCheck: 0,
      uptime: 0
    };
    /* istanbul ignore next */
    cov_2df7457914().s[258]++;
    this.performanceHistory = [];
    /* istanbul ignore next */
    cov_2df7457914().s[259]++;
    this.healthHistory = [];
    /* istanbul ignore next */
    cov_2df7457914().s[260]++;
    this.startTime = Date.now();
  };
  /* istanbul ignore next */
  cov_2df7457914().s[261]++;
  return AIServiceMonitor;
}());
/* istanbul ignore next */
cov_2df7457914().s[262]++;
exports.AIServiceMonitor = AIServiceMonitor;
// Export singleton instance
/* istanbul ignore next */
cov_2df7457914().s[263]++;
exports.aiServiceMonitor = AIServiceMonitor.getInstance();
/* istanbul ignore next */
cov_2df7457914().s[264]++;
exports.default = exports.aiServiceMonitor;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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