e3c68aa4de6e1daaed005cf75706e62f
"use strict";

/* istanbul ignore next */
function cov_9of6p9z1() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/config.ts";
  var hash = "d0a09cd5d4405431520a9cd477fa160926077cef";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/config.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 0
        },
        end: {
          line: 2,
          column: 62
        }
      },
      "1": {
        start: {
          line: 3,
          column: 0
        },
        end: {
          line: 3,
          column: 38
        }
      },
      "2": {
        start: {
          line: 4,
          column: 0
        },
        end: {
          line: 4,
          column: 50
        }
      },
      "3": {
        start: {
          line: 6,
          column: 0
        },
        end: {
          line: 46,
          column: 2
        }
      },
      "4": {
        start: {
          line: 49,
          column: 19
        },
        end: {
          line: 53,
          column: 5
        }
      },
      "5": {
        start: {
          line: 54,
          column: 18
        },
        end: {
          line: 54,
          column: 79
        }
      },
      "6": {
        start: {
          line: 54,
          column: 51
        },
        end: {
          line: 54,
          column: 76
        }
      },
      "7": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 57,
          column: 5
        }
      },
      "8": {
        start: {
          line: 56,
          column: 8
        },
        end: {
          line: 56,
          column: 95
        }
      },
      "9": {
        start: {
          line: 59,
          column: 30
        },
        end: {
          line: 66,
          column: 5
        }
      },
      "10": {
        start: {
          line: 67,
          column: 25
        },
        end: {
          line: 67,
          column: 27
        }
      },
      "11": {
        start: {
          line: 69,
          column: 25
        },
        end: {
          line: 69,
          column: 52
        }
      },
      "12": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 77,
          column: 5
        }
      },
      "13": {
        start: {
          line: 71,
          column: 8
        },
        end: {
          line: 73,
          column: 9
        }
      },
      "14": {
        start: {
          line: 72,
          column: 12
        },
        end: {
          line: 72,
          column: 87
        }
      },
      "15": {
        start: {
          line: 74,
          column: 8
        },
        end: {
          line: 76,
          column: 9
        }
      },
      "16": {
        start: {
          line: 74,
          column: 58
        },
        end: {
          line: 74,
          column: 112
        }
      },
      "17": {
        start: {
          line: 75,
          column: 12
        },
        end: {
          line: 75,
          column: 87
        }
      },
      "18": {
        start: {
          line: 79,
          column: 22
        },
        end: {
          line: 79,
          column: 46
        }
      },
      "19": {
        start: {
          line: 80,
          column: 4
        },
        end: {
          line: 82,
          column: 5
        }
      },
      "20": {
        start: {
          line: 80,
          column: 69
        },
        end: {
          line: 80,
          column: 120
        }
      },
      "21": {
        start: {
          line: 81,
          column: 8
        },
        end: {
          line: 81,
          column: 80
        }
      },
      "22": {
        start: {
          line: 84,
          column: 18
        },
        end: {
          line: 84,
          column: 82
        }
      },
      "23": {
        start: {
          line: 85,
          column: 4
        },
        end: {
          line: 90,
          column: 7
        }
      },
      "24": {
        start: {
          line: 86,
          column: 20
        },
        end: {
          line: 86,
          column: 36
        }
      },
      "25": {
        start: {
          line: 87,
          column: 8
        },
        end: {
          line: 89,
          column: 9
        }
      },
      "26": {
        start: {
          line: 87,
          column: 67
        },
        end: {
          line: 87,
          column: 112
        }
      },
      "27": {
        start: {
          line: 88,
          column: 12
        },
        end: {
          line: 88,
          column: 88
        }
      },
      "28": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 93,
          column: 5
        }
      },
      "29": {
        start: {
          line: 92,
          column: 8
        },
        end: {
          line: 92,
          column: 90
        }
      },
      "30": {
        start: {
          line: 94,
          column: 4
        },
        end: {
          line: 96,
          column: 5
        }
      },
      "31": {
        start: {
          line: 95,
          column: 8
        },
        end: {
          line: 95,
          column: 75
        }
      },
      "32": {
        start: {
          line: 99,
          column: 0
        },
        end: {
          line: 106,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "validateEnvironment",
        decl: {
          start: {
            line: 48,
            column: 9
          },
          end: {
            line: 48,
            column: 28
          }
        },
        loc: {
          start: {
            line: 48,
            column: 31
          },
          end: {
            line: 97,
            column: 1
          }
        },
        line: 48
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 54,
            column: 34
          },
          end: {
            line: 54,
            column: 35
          }
        },
        loc: {
          start: {
            line: 54,
            column: 49
          },
          end: {
            line: 54,
            column: 78
          }
        },
        line: 54
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 74,
            column: 37
          },
          end: {
            line: 74,
            column: 38
          }
        },
        loc: {
          start: {
            line: 74,
            column: 56
          },
          end: {
            line: 74,
            column: 114
          }
        },
        line: 74
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 80,
            column: 48
          },
          end: {
            line: 80,
            column: 49
          }
        },
        loc: {
          start: {
            line: 80,
            column: 67
          },
          end: {
            line: 80,
            column: 122
          }
        },
        line: 80
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 85,
            column: 20
          },
          end: {
            line: 85,
            column: 21
          }
        },
        loc: {
          start: {
            line: 85,
            column: 35
          },
          end: {
            line: 90,
            column: 5
          }
        },
        line: 85
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 87,
            column: 46
          },
          end: {
            line: 87,
            column: 47
          }
        },
        loc: {
          start: {
            line: 87,
            column: 65
          },
          end: {
            line: 87,
            column: 114
          }
        },
        line: 87
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 33,
            column: 22
          },
          end: {
            line: 33,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 22
          },
          end: {
            line: 33,
            column: 44
          }
        }, {
          start: {
            line: 33,
            column: 48
          },
          end: {
            line: 33,
            column: 72
          }
        }],
        line: 33
      },
      "1": {
        loc: {
          start: {
            line: 55,
            column: 4
          },
          end: {
            line: 57,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 55,
            column: 4
          },
          end: {
            line: 57,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 55
      },
      "2": {
        loc: {
          start: {
            line: 70,
            column: 4
          },
          end: {
            line: 77,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 70,
            column: 4
          },
          end: {
            line: 77,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 70
      },
      "3": {
        loc: {
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 73,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 73,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 71
      },
      "4": {
        loc: {
          start: {
            line: 74,
            column: 8
          },
          end: {
            line: 76,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 8
          },
          end: {
            line: 76,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "5": {
        loc: {
          start: {
            line: 80,
            column: 4
          },
          end: {
            line: 82,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 80,
            column: 4
          },
          end: {
            line: 82,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 80
      },
      "6": {
        loc: {
          start: {
            line: 80,
            column: 8
          },
          end: {
            line: 80,
            column: 123
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 80,
            column: 8
          },
          end: {
            line: 80,
            column: 19
          }
        }, {
          start: {
            line: 80,
            column: 23
          },
          end: {
            line: 80,
            column: 123
          }
        }],
        line: 80
      },
      "7": {
        loc: {
          start: {
            line: 87,
            column: 8
          },
          end: {
            line: 89,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 87,
            column: 8
          },
          end: {
            line: 89,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 87
      },
      "8": {
        loc: {
          start: {
            line: 87,
            column: 12
          },
          end: {
            line: 87,
            column: 115
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 87,
            column: 12
          },
          end: {
            line: 87,
            column: 17
          }
        }, {
          start: {
            line: 87,
            column: 21
          },
          end: {
            line: 87,
            column: 115
          }
        }],
        line: 87
      },
      "9": {
        loc: {
          start: {
            line: 91,
            column: 4
          },
          end: {
            line: 93,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 91,
            column: 4
          },
          end: {
            line: 93,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 91
      },
      "10": {
        loc: {
          start: {
            line: 91,
            column: 8
          },
          end: {
            line: 91,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 91,
            column: 8
          },
          end: {
            line: 91,
            column: 33
          }
        }, {
          start: {
            line: 91,
            column: 37
          },
          end: {
            line: 91,
            column: 74
          }
        }],
        line: 91
      },
      "11": {
        loc: {
          start: {
            line: 94,
            column: 4
          },
          end: {
            line: 96,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 94,
            column: 4
          },
          end: {
            line: 96,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 94
      },
      "12": {
        loc: {
          start: {
            line: 94,
            column: 8
          },
          end: {
            line: 94,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 94,
            column: 8
          },
          end: {
            line: 94,
            column: 33
          }
        }, {
          start: {
            line: 94,
            column: 37
          },
          end: {
            line: 94,
            column: 68
          }
        }],
        line: 94
      },
      "13": {
        loc: {
          start: {
            line: 105,
            column: 14
          },
          end: {
            line: 105,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 105,
            column: 14
          },
          end: {
            line: 105,
            column: 34
          }
        }, {
          start: {
            line: 105,
            column: 38
          },
          end: {
            line: 105,
            column: 51
          }
        }],
        line: 105
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/config.ts",
      mappings: ";;;AAkDA,kDA0DC;AA5GD,sCAAsC;AACzB,QAAA,MAAM,GAAG;IACpB,iBAAiB;IACjB,IAAI,EAAE;QACJ,mBAAmB,EAAE,CAAC;QACtB,mBAAmB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;QAClD,wBAAwB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,SAAS;KACpD;IAED,oBAAoB;IACpB,QAAQ,EAAE;QACR,mBAAmB,EAAE,CAAC;QACtB,eAAe,EAAE,EAAE;QACnB,eAAe,EAAE,CAAC;KACnB;IAED,aAAa;IACb,GAAG,EAAE;QACH,iBAAiB,EAAE,EAAE;QACrB,aAAa,EAAE,GAAG;QAClB,mBAAmB,EAAE,GAAG;QACxB,oBAAoB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,aAAa;KACpD;IAED,aAAa;IACb,UAAU,EAAE;QACV,SAAS,EAAE,EAAE;QACb,qBAAqB,EAAE,EAAE,GAAG,IAAI,EAAE,aAAa;KAChD;IAED,QAAQ;IACR,KAAK,EAAE;QACL,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,wBAAwB;QAChE,yBAAyB,EAAE,EAAE;KAC9B;IAED,WAAW;IACX,QAAQ,EAAE;QACR,qBAAqB,EAAE,KAAK;QAC5B,gBAAgB,EAAE,IAAI;KACvB;IAED,WAAW;IACX,QAAQ,EAAE;QACR,aAAa,EAAE,EAAE;QACjB,eAAe,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,UAAU;KAC/C;CACO,CAAC;AAEX,8CAA8C;AAC9C,SAAgB,mBAAmB;IACjC,IAAM,QAAQ,GAAG;QACf,cAAc;QACd,iBAAiB;QACjB,cAAc;KACf,CAAC;IAEF,IAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,UAAA,GAAG,IAAI,OAAA,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAjB,CAAiB,CAAC,CAAC;IAE1D,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACvB,MAAM,IAAI,KAAK,CAAC,kDAA2C,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CAAC;IACnF,CAAC;IAED,qDAAqD;IACrD,IAAM,mBAAmB,GAAG;QAC1B,OAAO;QACP,aAAa;QACb,SAAS;QACT,aAAa;QACb,WAAW;QACX,YAAY;KACb,CAAC;IAEF,IAAM,cAAc,GAAa,EAAE,CAAC;IAEpC,wBAAwB;IACxB,IAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;IACnD,IAAI,cAAc,EAAE,CAAC;QACnB,IAAI,cAAc,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YAC/B,cAAc,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QAC7E,CAAC;QACD,IAAI,mBAAmB,CAAC,IAAI,CAAC,UAAA,OAAO,IAAI,OAAA,cAAc,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAA9C,CAA8C,CAAC,EAAE,CAAC;YACxF,cAAc,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAED,qBAAqB;IACrB,IAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;IAC7C,IAAI,WAAW,IAAI,mBAAmB,CAAC,IAAI,CAAC,UAAA,OAAO,IAAI,OAAA,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAA3C,CAA2C,CAAC,EAAE,CAAC;QACpG,cAAc,CAAC,IAAI,CAAC,kDAAkD,CAAC,CAAC;IAC1E,CAAC;IAED,iBAAiB;IACjB,IAAM,OAAO,GAAG,CAAC,gBAAgB,EAAE,uBAAuB,EAAE,mBAAmB,CAAC,CAAC;IACjF,OAAO,CAAC,OAAO,CAAC,UAAA,GAAG;QACjB,IAAM,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAI,KAAK,IAAI,mBAAmB,CAAC,IAAI,CAAC,UAAA,OAAO,IAAI,OAAA,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,EAArC,CAAqC,CAAC,EAAE,CAAC;YACxF,cAAc,CAAC,IAAI,CAAC,UAAG,GAAG,yCAAsC,CAAC,CAAC;QACpE,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;QACvE,MAAM,IAAI,KAAK,CAAC,sCAA+B,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC,CAAC;IAC9E,CAAC;IAED,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM,EAAE,CAAC;QACjE,OAAO,CAAC,IAAI,CAAC,oCAAoC,EAAE,cAAc,CAAC,CAAC;IACrE,CAAC;AACH,CAAC;AAED,+BAA+B;AAClB,QAAA,GAAG,GAAG;IACjB,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,YAAa;IACvC,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,eAAgB;IAC7C,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,YAAa;IACvC,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;IAC1C,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU;IAClC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa;CACvC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/config.ts"],
      sourcesContent: ["// Application configuration constants\nexport const CONFIG = {\n  // Authentication\n  AUTH: {\n    MAX_FAILED_ATTEMPTS: 5,\n    LOCKOUT_DURATION_MS: 15 * 60 * 1000, // 15 minutes\n    PASSWORD_RESET_EXPIRY_MS: 60 * 60 * 1000, // 1 hour\n  },\n  \n  // Progress tracking\n  PROGRESS: {\n    DEFAULT_WEEKLY_GOAL: 3,\n    MAX_WEEKLY_GOAL: 50,\n    MIN_WEEKLY_GOAL: 1,\n  },\n  \n  // API limits\n  API: {\n    DEFAULT_PAGE_SIZE: 10,\n    MAX_PAGE_SIZE: 100,\n    RATE_LIMIT_REQUESTS: 100,\n    RATE_LIMIT_WINDOW_MS: 15 * 60 * 1000, // 15 minutes\n  },\n  \n  // Assessment\n  ASSESSMENT: {\n    MAX_STEPS: 10,\n    AUTO_SAVE_INTERVAL_MS: 30 * 1000, // 30 seconds\n  },\n  \n  // Email\n  EMAIL: {\n    FROM_ADDRESS: process.env.EMAIL_FROM || '<EMAIL>',\n    VERIFICATION_EXPIRY_HOURS: 24,\n  },\n  \n  // Database\n  DATABASE: {\n    CONNECTION_TIMEOUT_MS: 10000,\n    QUERY_TIMEOUT_MS: 5000,\n  },\n  \n  // Security\n  SECURITY: {\n    BCRYPT_ROUNDS: 12,\n    SESSION_MAX_AGE: 30 * 24 * 60 * 60, // 30 days\n  },\n} as const;\n\n// Environment validation with security checks\nexport function validateEnvironment() {\n  const required = [\n    'DATABASE_URL',\n    'NEXTAUTH_SECRET',\n    'NEXTAUTH_URL',\n  ];\n\n  const missing = required.filter(key => !process.env[key]);\n\n  if (missing.length > 0) {\n    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);\n  }\n\n  // Security validation - check for placeholder values\n  const placeholderPatterns = [\n    'your-',\n    'placeholder',\n    'example',\n    'test-secret',\n    'change-me',\n    'replace-me'\n  ];\n\n  const securityIssues: string[] = [];\n\n  // Check NEXTAUTH_SECRET\n  const nextAuthSecret = process.env.NEXTAUTH_SECRET;\n  if (nextAuthSecret) {\n    if (nextAuthSecret.length < 32) {\n      securityIssues.push('NEXTAUTH_SECRET must be at least 32 characters long');\n    }\n    if (placeholderPatterns.some(pattern => nextAuthSecret.toLowerCase().includes(pattern))) {\n      securityIssues.push('NEXTAUTH_SECRET appears to contain placeholder text');\n    }\n  }\n\n  // Check DATABASE_URL\n  const databaseUrl = process.env.DATABASE_URL;\n  if (databaseUrl && placeholderPatterns.some(pattern => databaseUrl.toLowerCase().includes(pattern))) {\n    securityIssues.push('DATABASE_URL appears to contain placeholder text');\n  }\n\n  // Check API keys\n  const apiKeys = ['RESEND_API_KEY', 'GOOGLE_GEMINI_API_KEY', 'SENTRY_AUTH_TOKEN'];\n  apiKeys.forEach(key => {\n    const value = process.env[key];\n    if (value && placeholderPatterns.some(pattern => value.toLowerCase().includes(pattern))) {\n      securityIssues.push(`${key} appears to contain placeholder text`);\n    }\n  });\n\n  if (securityIssues.length > 0 && process.env.NODE_ENV === 'production') {\n    throw new Error(`Security validation failed: ${securityIssues.join(', ')}`);\n  }\n\n  if (securityIssues.length > 0 && process.env.NODE_ENV !== 'test') {\n    console.warn('\u26A0\uFE0F  Environment security warnings:', securityIssues);\n  }\n}\n\n// Type-safe environment access\nexport const ENV = {\n  DATABASE_URL: process.env.DATABASE_URL!,\n  NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET!,\n  NEXTAUTH_URL: process.env.NEXTAUTH_URL!,\n  RESEND_API_KEY: process.env.RESEND_API_KEY,\n  EMAIL_FROM: process.env.EMAIL_FROM,\n  NODE_ENV: process.env.NODE_ENV || 'development',\n} as const;\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d0a09cd5d4405431520a9cd477fa160926077cef"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_9of6p9z1 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_9of6p9z1();
cov_9of6p9z1().s[0]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_9of6p9z1().s[1]++;
exports.ENV = exports.CONFIG = void 0;
/* istanbul ignore next */
cov_9of6p9z1().s[2]++;
exports.validateEnvironment = validateEnvironment;
// Application configuration constants
/* istanbul ignore next */
cov_9of6p9z1().s[3]++;
exports.CONFIG = {
  // Authentication
  AUTH: {
    MAX_FAILED_ATTEMPTS: 5,
    LOCKOUT_DURATION_MS: 15 * 60 * 1000,
    // 15 minutes
    PASSWORD_RESET_EXPIRY_MS: 60 * 60 * 1000 // 1 hour
  },
  // Progress tracking
  PROGRESS: {
    DEFAULT_WEEKLY_GOAL: 3,
    MAX_WEEKLY_GOAL: 50,
    MIN_WEEKLY_GOAL: 1
  },
  // API limits
  API: {
    DEFAULT_PAGE_SIZE: 10,
    MAX_PAGE_SIZE: 100,
    RATE_LIMIT_REQUESTS: 100,
    RATE_LIMIT_WINDOW_MS: 15 * 60 * 1000 // 15 minutes
  },
  // Assessment
  ASSESSMENT: {
    MAX_STEPS: 10,
    AUTO_SAVE_INTERVAL_MS: 30 * 1000 // 30 seconds
  },
  // Email
  EMAIL: {
    FROM_ADDRESS:
    /* istanbul ignore next */
    (cov_9of6p9z1().b[0][0]++, process.env.EMAIL_FROM) ||
    /* istanbul ignore next */
    (cov_9of6p9z1().b[0][1]++, '<EMAIL>'),
    VERIFICATION_EXPIRY_HOURS: 24
  },
  // Database
  DATABASE: {
    CONNECTION_TIMEOUT_MS: 10000,
    QUERY_TIMEOUT_MS: 5000
  },
  // Security
  SECURITY: {
    BCRYPT_ROUNDS: 12,
    SESSION_MAX_AGE: 30 * 24 * 60 * 60 // 30 days
  }
};
// Environment validation with security checks
function validateEnvironment() {
  /* istanbul ignore next */
  cov_9of6p9z1().f[0]++;
  var required =
  /* istanbul ignore next */
  (cov_9of6p9z1().s[4]++, ['DATABASE_URL', 'NEXTAUTH_SECRET', 'NEXTAUTH_URL']);
  var missing =
  /* istanbul ignore next */
  (cov_9of6p9z1().s[5]++, required.filter(function (key) {
    /* istanbul ignore next */
    cov_9of6p9z1().f[1]++;
    cov_9of6p9z1().s[6]++;
    return !process.env[key];
  }));
  /* istanbul ignore next */
  cov_9of6p9z1().s[7]++;
  if (missing.length > 0) {
    /* istanbul ignore next */
    cov_9of6p9z1().b[1][0]++;
    cov_9of6p9z1().s[8]++;
    throw new Error("Missing required environment variables: ".concat(missing.join(', ')));
  } else
  /* istanbul ignore next */
  {
    cov_9of6p9z1().b[1][1]++;
  }
  // Security validation - check for placeholder values
  var placeholderPatterns =
  /* istanbul ignore next */
  (cov_9of6p9z1().s[9]++, ['your-', 'placeholder', 'example', 'test-secret', 'change-me', 'replace-me']);
  var securityIssues =
  /* istanbul ignore next */
  (cov_9of6p9z1().s[10]++, []);
  // Check NEXTAUTH_SECRET
  var nextAuthSecret =
  /* istanbul ignore next */
  (cov_9of6p9z1().s[11]++, process.env.NEXTAUTH_SECRET);
  /* istanbul ignore next */
  cov_9of6p9z1().s[12]++;
  if (nextAuthSecret) {
    /* istanbul ignore next */
    cov_9of6p9z1().b[2][0]++;
    cov_9of6p9z1().s[13]++;
    if (nextAuthSecret.length < 32) {
      /* istanbul ignore next */
      cov_9of6p9z1().b[3][0]++;
      cov_9of6p9z1().s[14]++;
      securityIssues.push('NEXTAUTH_SECRET must be at least 32 characters long');
    } else
    /* istanbul ignore next */
    {
      cov_9of6p9z1().b[3][1]++;
    }
    cov_9of6p9z1().s[15]++;
    if (placeholderPatterns.some(function (pattern) {
      /* istanbul ignore next */
      cov_9of6p9z1().f[2]++;
      cov_9of6p9z1().s[16]++;
      return nextAuthSecret.toLowerCase().includes(pattern);
    })) {
      /* istanbul ignore next */
      cov_9of6p9z1().b[4][0]++;
      cov_9of6p9z1().s[17]++;
      securityIssues.push('NEXTAUTH_SECRET appears to contain placeholder text');
    } else
    /* istanbul ignore next */
    {
      cov_9of6p9z1().b[4][1]++;
    }
  } else
  /* istanbul ignore next */
  {
    cov_9of6p9z1().b[2][1]++;
  }
  // Check DATABASE_URL
  var databaseUrl =
  /* istanbul ignore next */
  (cov_9of6p9z1().s[18]++, process.env.DATABASE_URL);
  /* istanbul ignore next */
  cov_9of6p9z1().s[19]++;
  if (
  /* istanbul ignore next */
  (cov_9of6p9z1().b[6][0]++, databaseUrl) &&
  /* istanbul ignore next */
  (cov_9of6p9z1().b[6][1]++, placeholderPatterns.some(function (pattern) {
    /* istanbul ignore next */
    cov_9of6p9z1().f[3]++;
    cov_9of6p9z1().s[20]++;
    return databaseUrl.toLowerCase().includes(pattern);
  }))) {
    /* istanbul ignore next */
    cov_9of6p9z1().b[5][0]++;
    cov_9of6p9z1().s[21]++;
    securityIssues.push('DATABASE_URL appears to contain placeholder text');
  } else
  /* istanbul ignore next */
  {
    cov_9of6p9z1().b[5][1]++;
  }
  // Check API keys
  var apiKeys =
  /* istanbul ignore next */
  (cov_9of6p9z1().s[22]++, ['RESEND_API_KEY', 'GOOGLE_GEMINI_API_KEY', 'SENTRY_AUTH_TOKEN']);
  /* istanbul ignore next */
  cov_9of6p9z1().s[23]++;
  apiKeys.forEach(function (key) {
    /* istanbul ignore next */
    cov_9of6p9z1().f[4]++;
    var value =
    /* istanbul ignore next */
    (cov_9of6p9z1().s[24]++, process.env[key]);
    /* istanbul ignore next */
    cov_9of6p9z1().s[25]++;
    if (
    /* istanbul ignore next */
    (cov_9of6p9z1().b[8][0]++, value) &&
    /* istanbul ignore next */
    (cov_9of6p9z1().b[8][1]++, placeholderPatterns.some(function (pattern) {
      /* istanbul ignore next */
      cov_9of6p9z1().f[5]++;
      cov_9of6p9z1().s[26]++;
      return value.toLowerCase().includes(pattern);
    }))) {
      /* istanbul ignore next */
      cov_9of6p9z1().b[7][0]++;
      cov_9of6p9z1().s[27]++;
      securityIssues.push("".concat(key, " appears to contain placeholder text"));
    } else
    /* istanbul ignore next */
    {
      cov_9of6p9z1().b[7][1]++;
    }
  });
  /* istanbul ignore next */
  cov_9of6p9z1().s[28]++;
  if (
  /* istanbul ignore next */
  (cov_9of6p9z1().b[10][0]++, securityIssues.length > 0) &&
  /* istanbul ignore next */
  (cov_9of6p9z1().b[10][1]++, process.env.NODE_ENV === 'production')) {
    /* istanbul ignore next */
    cov_9of6p9z1().b[9][0]++;
    cov_9of6p9z1().s[29]++;
    throw new Error("Security validation failed: ".concat(securityIssues.join(', ')));
  } else
  /* istanbul ignore next */
  {
    cov_9of6p9z1().b[9][1]++;
  }
  cov_9of6p9z1().s[30]++;
  if (
  /* istanbul ignore next */
  (cov_9of6p9z1().b[12][0]++, securityIssues.length > 0) &&
  /* istanbul ignore next */
  (cov_9of6p9z1().b[12][1]++, process.env.NODE_ENV !== 'test')) {
    /* istanbul ignore next */
    cov_9of6p9z1().b[11][0]++;
    cov_9of6p9z1().s[31]++;
    console.warn('⚠️  Environment security warnings:', securityIssues);
  } else
  /* istanbul ignore next */
  {
    cov_9of6p9z1().b[11][1]++;
  }
}
// Type-safe environment access
/* istanbul ignore next */
cov_9of6p9z1().s[32]++;
exports.ENV = {
  DATABASE_URL: process.env.DATABASE_URL,
  NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
  NEXTAUTH_URL: process.env.NEXTAUTH_URL,
  RESEND_API_KEY: process.env.RESEND_API_KEY,
  EMAIL_FROM: process.env.EMAIL_FROM,
  NODE_ENV:
  /* istanbul ignore next */
  (cov_9of6p9z1().b[13][0]++, process.env.NODE_ENV) ||
  /* istanbul ignore next */
  (cov_9of6p9z1().b[13][1]++, 'development')
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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