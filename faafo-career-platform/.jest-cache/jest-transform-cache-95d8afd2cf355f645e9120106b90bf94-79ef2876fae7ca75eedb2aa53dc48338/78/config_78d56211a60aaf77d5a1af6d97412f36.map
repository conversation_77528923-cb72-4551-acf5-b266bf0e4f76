{"version": 3, "names": ["cov_9of6p9z1", "actualCoverage", "s", "exports", "validateEnvironment", "CONFIG", "AUTH", "MAX_FAILED_ATTEMPTS", "LOCKOUT_DURATION_MS", "PASSWORD_RESET_EXPIRY_MS", "PROGRESS", "DEFAULT_WEEKLY_GOAL", "MAX_WEEKLY_GOAL", "MIN_WEEKLY_GOAL", "API", "DEFAULT_PAGE_SIZE", "MAX_PAGE_SIZE", "RATE_LIMIT_REQUESTS", "RATE_LIMIT_WINDOW_MS", "ASSESSMENT", "MAX_STEPS", "AUTO_SAVE_INTERVAL_MS", "EMAIL", "FROM_ADDRESS", "b", "process", "env", "EMAIL_FROM", "VERIFICATION_EXPIRY_HOURS", "DATABASE", "CONNECTION_TIMEOUT_MS", "QUERY_TIMEOUT_MS", "SECURITY", "BCRYPT_ROUNDS", "SESSION_MAX_AGE", "f", "required", "missing", "filter", "key", "length", "Error", "concat", "join", "placeholder<PERSON><PERSON>erns", "securityIssues", "nextAuthSecret", "NEXTAUTH_SECRET", "push", "some", "pattern", "toLowerCase", "includes", "databaseUrl", "DATABASE_URL", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "value", "NODE_ENV", "console", "warn", "ENV", "NEXTAUTH_URL", "RESEND_API_KEY"], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/config.ts"], "sourcesContent": ["// Application configuration constants\nexport const CONFIG = {\n  // Authentication\n  AUTH: {\n    MAX_FAILED_ATTEMPTS: 5,\n    LOCKOUT_DURATION_MS: 15 * 60 * 1000, // 15 minutes\n    PASSWORD_RESET_EXPIRY_MS: 60 * 60 * 1000, // 1 hour\n  },\n  \n  // Progress tracking\n  PROGRESS: {\n    DEFAULT_WEEKLY_GOAL: 3,\n    MAX_WEEKLY_GOAL: 50,\n    MIN_WEEKLY_GOAL: 1,\n  },\n  \n  // API limits\n  API: {\n    DEFAULT_PAGE_SIZE: 10,\n    MAX_PAGE_SIZE: 100,\n    RATE_LIMIT_REQUESTS: 100,\n    RATE_LIMIT_WINDOW_MS: 15 * 60 * 1000, // 15 minutes\n  },\n  \n  // Assessment\n  ASSESSMENT: {\n    MAX_STEPS: 10,\n    AUTO_SAVE_INTERVAL_MS: 30 * 1000, // 30 seconds\n  },\n  \n  // Email\n  EMAIL: {\n    FROM_ADDRESS: process.env.EMAIL_FROM || '<EMAIL>',\n    VERIFICATION_EXPIRY_HOURS: 24,\n  },\n  \n  // Database\n  DATABASE: {\n    CONNECTION_TIMEOUT_MS: 10000,\n    QUERY_TIMEOUT_MS: 5000,\n  },\n  \n  // Security\n  SECURITY: {\n    BCRYPT_ROUNDS: 12,\n    SESSION_MAX_AGE: 30 * 24 * 60 * 60, // 30 days\n  },\n} as const;\n\n// Environment validation with security checks\nexport function validateEnvironment() {\n  const required = [\n    'DATABASE_URL',\n    'NEXTAUTH_SECRET',\n    'NEXTAUTH_URL',\n  ];\n\n  const missing = required.filter(key => !process.env[key]);\n\n  if (missing.length > 0) {\n    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);\n  }\n\n  // Security validation - check for placeholder values\n  const placeholderPatterns = [\n    'your-',\n    'placeholder',\n    'example',\n    'test-secret',\n    'change-me',\n    'replace-me'\n  ];\n\n  const securityIssues: string[] = [];\n\n  // Check NEXTAUTH_SECRET\n  const nextAuthSecret = process.env.NEXTAUTH_SECRET;\n  if (nextAuthSecret) {\n    if (nextAuthSecret.length < 32) {\n      securityIssues.push('NEXTAUTH_SECRET must be at least 32 characters long');\n    }\n    if (placeholderPatterns.some(pattern => nextAuthSecret.toLowerCase().includes(pattern))) {\n      securityIssues.push('NEXTAUTH_SECRET appears to contain placeholder text');\n    }\n  }\n\n  // Check DATABASE_URL\n  const databaseUrl = process.env.DATABASE_URL;\n  if (databaseUrl && placeholderPatterns.some(pattern => databaseUrl.toLowerCase().includes(pattern))) {\n    securityIssues.push('DATABASE_URL appears to contain placeholder text');\n  }\n\n  // Check API keys\n  const apiKeys = ['RESEND_API_KEY', 'GOOGLE_GEMINI_API_KEY', 'SENTRY_AUTH_TOKEN'];\n  apiKeys.forEach(key => {\n    const value = process.env[key];\n    if (value && placeholderPatterns.some(pattern => value.toLowerCase().includes(pattern))) {\n      securityIssues.push(`${key} appears to contain placeholder text`);\n    }\n  });\n\n  if (securityIssues.length > 0 && process.env.NODE_ENV === 'production') {\n    throw new Error(`Security validation failed: ${securityIssues.join(', ')}`);\n  }\n\n  if (securityIssues.length > 0 && process.env.NODE_ENV !== 'test') {\n    console.warn('⚠️  Environment security warnings:', securityIssues);\n  }\n}\n\n// Type-safe environment access\nexport const ENV = {\n  DATABASE_URL: process.env.DATABASE_URL!,\n  NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET!,\n  NEXTAUTH_URL: process.env.NEXTAUTH_URL!,\n  RESEND_API_KEY: process.env.RESEND_API_KEY,\n  EMAIL_FROM: process.env.EMAIL_FROM,\n  NODE_ENV: process.env.NODE_ENV || 'development',\n} as const;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAYI;IAAAA,YAAA,YAAAA,CAAA;MAAA,OAAAC,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAD,YAAA;AAAAA,YAAA,GAAAE,CAAA;;;;;;;;;AAsCJC,OAAA,CAAAC,mBAAA,GAAAA,mBAAA;AAlDA;AAAA;AAAAJ,YAAA,GAAAE,CAAA;AACaC,OAAA,CAAAE,MAAM,GAAG;EACpB;EACAC,IAAI,EAAE;IACJC,mBAAmB,EAAE,CAAC;IACtBC,mBAAmB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IAAE;IACrCC,wBAAwB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAE;GAC3C;EAED;EACAC,QAAQ,EAAE;IACRC,mBAAmB,EAAE,CAAC;IACtBC,eAAe,EAAE,EAAE;IACnBC,eAAe,EAAE;GAClB;EAED;EACAC,GAAG,EAAE;IACHC,iBAAiB,EAAE,EAAE;IACrBC,aAAa,EAAE,GAAG;IAClBC,mBAAmB,EAAE,GAAG;IACxBC,oBAAoB,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAE;GACvC;EAED;EACAC,UAAU,EAAE;IACVC,SAAS,EAAE,EAAE;IACbC,qBAAqB,EAAE,EAAE,GAAG,IAAI,CAAE;GACnC;EAED;EACAC,KAAK,EAAE;IACLC,YAAY;IAAE;IAAA,CAAAvB,YAAA,GAAAwB,CAAA,UAAAC,OAAO,CAACC,GAAG,CAACC,UAAU;IAAA;IAAA,CAAA3B,YAAA,GAAAwB,CAAA,UAAI,wBAAwB;IAChEI,yBAAyB,EAAE;GAC5B;EAED;EACAC,QAAQ,EAAE;IACRC,qBAAqB,EAAE,KAAK;IAC5BC,gBAAgB,EAAE;GACnB;EAED;EACAC,QAAQ,EAAE;IACRC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAE;;CAE9B;AAEV;AACA,SAAgB9B,mBAAmBA,CAAA;EAAA;EAAAJ,YAAA,GAAAmC,CAAA;EACjC,IAAMC,QAAQ;EAAA;EAAA,CAAApC,YAAA,GAAAE,CAAA,OAAG,CACf,cAAc,EACd,iBAAiB,EACjB,cAAc,CACf;EAED,IAAMmC,OAAO;EAAA;EAAA,CAAArC,YAAA,GAAAE,CAAA,OAAGkC,QAAQ,CAACE,MAAM,CAAC,UAAAC,GAAG;IAAA;IAAAvC,YAAA,GAAAmC,CAAA;IAAAnC,YAAA,GAAAE,CAAA;IAAI,QAACuB,OAAO,CAACC,GAAG,CAACa,GAAG,CAAC;EAAjB,CAAiB,CAAC;EAAC;EAAAvC,YAAA,GAAAE,CAAA;EAE1D,IAAImC,OAAO,CAACG,MAAM,GAAG,CAAC,EAAE;IAAA;IAAAxC,YAAA,GAAAwB,CAAA;IAAAxB,YAAA,GAAAE,CAAA;IACtB,MAAM,IAAIuC,KAAK,CAAC,2CAAAC,MAAA,CAA2CL,OAAO,CAACM,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC;EAClF,CAAC;EAAA;EAAA;IAAA3C,YAAA,GAAAwB,CAAA;EAAA;EAED;EACA,IAAMoB,mBAAmB;EAAA;EAAA,CAAA5C,YAAA,GAAAE,CAAA,OAAG,CAC1B,OAAO,EACP,aAAa,EACb,SAAS,EACT,aAAa,EACb,WAAW,EACX,YAAY,CACb;EAED,IAAM2C,cAAc;EAAA;EAAA,CAAA7C,YAAA,GAAAE,CAAA,QAAa,EAAE;EAEnC;EACA,IAAM4C,cAAc;EAAA;EAAA,CAAA9C,YAAA,GAAAE,CAAA,QAAGuB,OAAO,CAACC,GAAG,CAACqB,eAAe;EAAC;EAAA/C,YAAA,GAAAE,CAAA;EACnD,IAAI4C,cAAc,EAAE;IAAA;IAAA9C,YAAA,GAAAwB,CAAA;IAAAxB,YAAA,GAAAE,CAAA;IAClB,IAAI4C,cAAc,CAACN,MAAM,GAAG,EAAE,EAAE;MAAA;MAAAxC,YAAA,GAAAwB,CAAA;MAAAxB,YAAA,GAAAE,CAAA;MAC9B2C,cAAc,CAACG,IAAI,CAAC,qDAAqD,CAAC;IAC5E,CAAC;IAAA;IAAA;MAAAhD,YAAA,GAAAwB,CAAA;IAAA;IAAAxB,YAAA,GAAAE,CAAA;IACD,IAAI0C,mBAAmB,CAACK,IAAI,CAAC,UAAAC,OAAO;MAAA;MAAAlD,YAAA,GAAAmC,CAAA;MAAAnC,YAAA,GAAAE,CAAA;MAAI,OAAA4C,cAAc,CAACK,WAAW,EAAE,CAACC,QAAQ,CAACF,OAAO,CAAC;IAA9C,CAA8C,CAAC,EAAE;MAAA;MAAAlD,YAAA,GAAAwB,CAAA;MAAAxB,YAAA,GAAAE,CAAA;MACvF2C,cAAc,CAACG,IAAI,CAAC,qDAAqD,CAAC;IAC5E,CAAC;IAAA;IAAA;MAAAhD,YAAA,GAAAwB,CAAA;IAAA;EACH,CAAC;EAAA;EAAA;IAAAxB,YAAA,GAAAwB,CAAA;EAAA;EAED;EACA,IAAM6B,WAAW;EAAA;EAAA,CAAArD,YAAA,GAAAE,CAAA,QAAGuB,OAAO,CAACC,GAAG,CAAC4B,YAAY;EAAC;EAAAtD,YAAA,GAAAE,CAAA;EAC7C;EAAI;EAAA,CAAAF,YAAA,GAAAwB,CAAA,UAAA6B,WAAW;EAAA;EAAA,CAAArD,YAAA,GAAAwB,CAAA,UAAIoB,mBAAmB,CAACK,IAAI,CAAC,UAAAC,OAAO;IAAA;IAAAlD,YAAA,GAAAmC,CAAA;IAAAnC,YAAA,GAAAE,CAAA;IAAI,OAAAmD,WAAW,CAACF,WAAW,EAAE,CAACC,QAAQ,CAACF,OAAO,CAAC;EAA3C,CAA2C,CAAC,GAAE;IAAA;IAAAlD,YAAA,GAAAwB,CAAA;IAAAxB,YAAA,GAAAE,CAAA;IACnG2C,cAAc,CAACG,IAAI,CAAC,kDAAkD,CAAC;EACzE,CAAC;EAAA;EAAA;IAAAhD,YAAA,GAAAwB,CAAA;EAAA;EAED;EACA,IAAM+B,OAAO;EAAA;EAAA,CAAAvD,YAAA,GAAAE,CAAA,QAAG,CAAC,gBAAgB,EAAE,uBAAuB,EAAE,mBAAmB,CAAC;EAAC;EAAAF,YAAA,GAAAE,CAAA;EACjFqD,OAAO,CAACC,OAAO,CAAC,UAAAjB,GAAG;IAAA;IAAAvC,YAAA,GAAAmC,CAAA;IACjB,IAAMsB,KAAK;IAAA;IAAA,CAAAzD,YAAA,GAAAE,CAAA,QAAGuB,OAAO,CAACC,GAAG,CAACa,GAAG,CAAC;IAAC;IAAAvC,YAAA,GAAAE,CAAA;IAC/B;IAAI;IAAA,CAAAF,YAAA,GAAAwB,CAAA,UAAAiC,KAAK;IAAA;IAAA,CAAAzD,YAAA,GAAAwB,CAAA,UAAIoB,mBAAmB,CAACK,IAAI,CAAC,UAAAC,OAAO;MAAA;MAAAlD,YAAA,GAAAmC,CAAA;MAAAnC,YAAA,GAAAE,CAAA;MAAI,OAAAuD,KAAK,CAACN,WAAW,EAAE,CAACC,QAAQ,CAACF,OAAO,CAAC;IAArC,CAAqC,CAAC,GAAE;MAAA;MAAAlD,YAAA,GAAAwB,CAAA;MAAAxB,YAAA,GAAAE,CAAA;MACvF2C,cAAc,CAACG,IAAI,CAAC,GAAAN,MAAA,CAAGH,GAAG,yCAAsC,CAAC;IACnE,CAAC;IAAA;IAAA;MAAAvC,YAAA,GAAAwB,CAAA;IAAA;EACH,CAAC,CAAC;EAAC;EAAAxB,YAAA,GAAAE,CAAA;EAEH;EAAI;EAAA,CAAAF,YAAA,GAAAwB,CAAA,WAAAqB,cAAc,CAACL,MAAM,GAAG,CAAC;EAAA;EAAA,CAAAxC,YAAA,GAAAwB,CAAA,WAAIC,OAAO,CAACC,GAAG,CAACgC,QAAQ,KAAK,YAAY,GAAE;IAAA;IAAA1D,YAAA,GAAAwB,CAAA;IAAAxB,YAAA,GAAAE,CAAA;IACtE,MAAM,IAAIuC,KAAK,CAAC,+BAAAC,MAAA,CAA+BG,cAAc,CAACF,IAAI,CAAC,IAAI,CAAC,CAAE,CAAC;EAC7E,CAAC;EAAA;EAAA;IAAA3C,YAAA,GAAAwB,CAAA;EAAA;EAAAxB,YAAA,GAAAE,CAAA;EAED;EAAI;EAAA,CAAAF,YAAA,GAAAwB,CAAA,WAAAqB,cAAc,CAACL,MAAM,GAAG,CAAC;EAAA;EAAA,CAAAxC,YAAA,GAAAwB,CAAA,WAAIC,OAAO,CAACC,GAAG,CAACgC,QAAQ,KAAK,MAAM,GAAE;IAAA;IAAA1D,YAAA,GAAAwB,CAAA;IAAAxB,YAAA,GAAAE,CAAA;IAChEyD,OAAO,CAACC,IAAI,CAAC,oCAAoC,EAAEf,cAAc,CAAC;EACpE,CAAC;EAAA;EAAA;IAAA7C,YAAA,GAAAwB,CAAA;EAAA;AACH;AAEA;AAAA;AAAAxB,YAAA,GAAAE,CAAA;AACaC,OAAA,CAAA0D,GAAG,GAAG;EACjBP,YAAY,EAAE7B,OAAO,CAACC,GAAG,CAAC4B,YAAa;EACvCP,eAAe,EAAEtB,OAAO,CAACC,GAAG,CAACqB,eAAgB;EAC7Ce,YAAY,EAAErC,OAAO,CAACC,GAAG,CAACoC,YAAa;EACvCC,cAAc,EAAEtC,OAAO,CAACC,GAAG,CAACqC,cAAc;EAC1CpC,UAAU,EAAEF,OAAO,CAACC,GAAG,CAACC,UAAU;EAClC+B,QAAQ;EAAE;EAAA,CAAA1D,YAAA,GAAAwB,CAAA,WAAAC,OAAO,CAACC,GAAG,CAACgC,QAAQ;EAAA;EAAA,CAAA1D,YAAA,GAAAwB,CAAA,WAAI,aAAa;CACvC", "ignoreList": []}