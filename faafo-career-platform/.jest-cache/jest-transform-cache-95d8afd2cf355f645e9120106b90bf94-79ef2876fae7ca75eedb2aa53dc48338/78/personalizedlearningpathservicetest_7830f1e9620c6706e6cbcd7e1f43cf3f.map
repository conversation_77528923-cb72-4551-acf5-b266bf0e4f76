{"file": "/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/personalized-learning-path-service.test.ts", "mappings": ";AAAA;;;;;;;GAOG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,gGAA+F;AAE/F,QAAQ,CAAC,iCAAiC,EAAE;IAC1C,IAAI,OAAwC,CAAC;IAE7C,UAAU,CAAC;QACT,OAAO,GAAG,IAAI,iEAA+B,EAAE,CAAC;IAClD,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,0BAA0B,EAAE;QACnC,EAAE,CAAC,8CAA8C,EAAE;;;;;wBAC3C,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE;gCACb,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE;gCAChD,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE;gCAC1C,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE;6BAC1C;4BACD,UAAU,EAAE,sBAAsB;4BAClC,SAAS,EAAE,CAAC,EAAE,SAAS;4BACvB,aAAa,EAAE,UAAmB;4BAClC,YAAY,EAAE,EAAE,EAAE,iBAAiB;4BACnC,MAAM,EAAE,GAAG,EAAE,UAAU;4BACvB,WAAW,EAAE;gCACX,gBAAgB,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC;gCAC1C,UAAU,EAAE,cAAc;gCAC1B,qBAAqB,EAAE,IAAI;6BAC5B;yBACF,CAAC;wBAEmB,qBAAM,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAA;;wBAA1D,YAAY,GAAG,SAA2C;wBAEhE,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;wBACnC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;wBACtC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBAC7C,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;wBAC7D,MAAM,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,CAAC;wBACrD,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;wBACxD,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC1C,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBACtD,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC7C,MAAM,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aACpD,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE;;;;;wBACzC,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BAC3D,UAAU,EAAE,iBAAiB;4BAC7B,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,QAAiB;4BAChC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,IAAI;yBACb,CAAC;wBAEmB,qBAAM,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAA;;wBAA1D,YAAY,GAAG,SAA2C;wBAEhE,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC7C,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAEnD,QAAQ,GAAG,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,KAAK,KAAK,OAAO,EAArB,CAAqB,CAAC,CAAC;wBAC3E,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC/B,MAAM,CAAC,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,YAAY,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBACvC,MAAM,CAAC,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,WAAW,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBACjD,MAAM,CAAC,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC1C,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE;;;;;wBAC7C,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BAC3D,UAAU,EAAE,oBAAoB;4BAChC,SAAS,EAAE,EAAE;4BACb,aAAa,EAAE,YAAqB;4BACpC,YAAY,EAAE,CAAC;4BACf,MAAM,EAAE,IAAI;yBACb,CAAC;wBAEmB,qBAAM,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAA;;wBAA1D,YAAY,GAAG,SAA2C;wBAEhE,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAEtD,6CAA6C;wBAC7C,KAAS,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;4BAC9C,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;4BACtC,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;4BAEjD,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC,sBAAsB,CAC7D,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,CAChC,CAAC;wBACJ,CAAC;;;;aACF,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE;;;;;wBAChC,gBAAgB,GAAG;4BACvB,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BACjE,UAAU,EAAE,sBAAsB;4BAClC,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,YAAqB;4BACpC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG,EAAE,aAAa;yBAC3B,CAAC;wBAEmB,qBAAM,OAAO,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,EAAA;;wBAAnE,YAAY,GAAG,SAAoD;wBAEzE,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;wBACxD,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,KAAK,CAAC,EAAZ,CAAY,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gCAAgC;;;;aACpG,CAAC,CAAC;QAEH,EAAE,CAAC,4CAA4C,EAAE;;;;;wBACzC,oBAAoB,GAAG;4BAC3B,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BACjE,UAAU,EAAE,oBAAoB;4BAChC,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,QAAiB;4BAChC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG;yBACZ,CAAC;wBAEmB,qBAAM,OAAO,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,EAAA;;wBAAvE,YAAY,GAAG,SAAwD;wBAEvE,eAAe,GAAG,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,UAAA,CAAC;4BACrD,OAAA,CAAC,CAAC,MAAM,KAAK,OAAO,IAAI,CAAC,CAAC,MAAM,KAAK,aAAa;wBAAlD,CAAkD,CACnD,CAAC;wBAEF,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;;;;aACnD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,4BAA4B,EAAE;QACrC,EAAE,CAAC,oDAAoD,EAAE;;;;;wBACjD,sBAAsB,GAAG;4BAC7B,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BACjE,UAAU,EAAE,iBAAiB;4BAC7B,SAAS,EAAE,CAAC,EAAE,uBAAuB;4BACrC,aAAa,EAAE,WAAoB;4BACnC,YAAY,EAAE,EAAE,EAAE,oBAAoB;4BACtC,MAAM,EAAE,IAAI;yBACb,CAAC;wBAEmB,qBAAM,OAAO,CAAC,oBAAoB,CAAC,sBAAsB,CAAC,EAAA;;wBAAzE,YAAY,GAAG,SAA0D;wBAE/E,MAAM,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,oBAAoB;wBACvF,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,SAAS,KAAK,MAAM,EAA1B,CAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aACnF,CAAC,CAAC;QAEH,EAAE,CAAC,mEAAmE,EAAE;;;;;wBAChE,kBAAkB,GAAG;4BACzB,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BAC3D,UAAU,EAAE,6BAA6B;4BACzC,SAAS,EAAE,CAAC,EAAE,wBAAwB;4BACtC,aAAa,EAAE,QAAiB;4BAChC,YAAY,EAAE,CAAC,EAAE,wBAAwB;4BACzC,MAAM,EAAE,EAAE,EAAE,kBAAkB;yBAC/B,CAAC;wBAEmB,qBAAM,OAAO,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,EAAA;;wBAArE,YAAY,GAAG,SAAsD;wBAE3E,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;wBAChD,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC5D,MAAM,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,kBAAkB;;;;aAC5E,CAAC,CAAC;QAEH,EAAE,CAAC,uEAAuE,EAAE;;;;;wBACpE,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BACjE,UAAU,EAAE,sBAAsB;4BAClC,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,UAAmB;4BAClC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG;yBACZ,CAAC;wBAEmB,qBAAM,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAA;;wBAA1D,YAAY,GAAG,SAA2C;wBAE1D,kBAAkB,GAAG,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,QAAQ,KAAK,MAAM,IAAI,GAAG,CAAC,QAAQ,KAAK,UAAU,EAAtD,CAAsD,CAAC,CAAC;wBACxH,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAG/C,gBAAgB,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;wBACjD,2BAA2B,GAAG,gBAAgB,CAAC,IAAI,CAAC,UAAA,KAAK;4BAC7D,OAAA,kBAAkB,CAAC,IAAI,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,EAAxB,CAAwB,CAAC;wBAAxD,CAAwD,CACzD,CAAC;wBACF,MAAM,CAAC,2BAA2B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aAChD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE;QACxC,EAAE,CAAC,IAAI,CAAC,4DAA4D,EAAE;;;;4BAC/C,qBAAM,OAAO,CAAC,oBAAoB,CAAC;4BACtD,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BACjE,UAAU,EAAE,iBAAiB;4BAC7B,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,YAAqB;4BACpC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG;yBACZ,CAAC,EAAA;;wBARI,YAAY,GAAG,SAQnB;wBAEF,4CAA4C;wBAC5C,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;wBACvE,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,EAAJ,CAAI,CAAC,CAAC,CAAC;wBAEpE,mDAAmD;wBACnD,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAEnD,cAAc,GAAG;4BACrB,MAAM,EAAE,UAAU;4BAClB,MAAM,EAAE,YAAY,CAAC,EAAE;4BACvB,kBAAkB,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,iCAAiC;4BACrF,YAAY,EAAE;gCACZ,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE;6BAC/C;4BACD,SAAS,EAAE,EAAE,EAAE,QAAQ;yBACxB,CAAC;wBAEkB,qBAAM,OAAO,CAAC,cAAc,CAAC,cAAc,CAAC,EAAA;;wBAA1D,WAAW,GAAG,SAA4C;wBAEhE,qCAAqC;wBACrC,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;wBAC7E,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;wBAC9D,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,WAAW,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;wBAEjF,MAAM,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;wBAClC,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBACrE,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;wBAChD,MAAM,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,YAAY,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;;;;aACzF,CAAC,CAAC;QAEH,EAAE,CAAC,mDAAmD,EAAE;;;;4BACjC,qBAAM,OAAO,CAAC,oBAAoB,CAAC;4BACtD,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BACjE,UAAU,EAAE,oBAAoB;4BAChC,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,UAAmB;4BAClC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG;yBACZ,CAAC,EAAA;;wBARI,YAAY,GAAG,SAQnB;wBAGI,cAAc,GAAG;4BACrB,MAAM,EAAE,UAAU;4BAClB,MAAM,EAAE,YAAY,CAAC,EAAE;4BACvB,kBAAkB,EAAE,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,EAAE,EAAJ,CAAI,CAAC;4BACrE,YAAY,EAAE;gCACZ,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE;6BAC/C;4BACD,SAAS,EAAE,EAAE,EAAE,2BAA2B;yBAC3C,CAAC;wBAEkB,qBAAM,OAAO,CAAC,cAAc,CAAC,cAAc,CAAC,EAAA;;wBAA1D,WAAW,GAAG,SAA4C;wBAEhE,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC9C,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAC1D,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,IAAI,KAAK,YAAY,EAAzB,CAAyB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aACnF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE;QAClC,EAAE,CAAC,6CAA6C,EAAE;;;;4BAC3B,qBAAM,OAAO,CAAC,oBAAoB,CAAC;4BACtD,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BACjE,UAAU,EAAE,sBAAsB;4BAClC,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,OAAgB;4BAC/B,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,IAAI;yBACb,CAAC,EAAA;;wBARI,YAAY,GAAG,SAQnB;wBAEI,aAAa,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,EAAR,CAAQ,CAAC,CAAC,CAAC;wBACzE,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,+BAA+B;wBAExE,UAAU,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAA5B,CAA4B,CAAC,CAAC,CAAC;wBAC1F,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gCAAgC;;;;aAC5E,CAAC,CAAC;QAEH,EAAE,CAAC,gDAAgD,EAAE;;;;;wBAC7C,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BAC3D,UAAU,EAAE,oBAAoB;4BAChC,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,UAAmB;4BAClC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG;4BACX,WAAW,EAAE;gCACX,gBAAgB,EAAE,CAAC,aAAa,EAAE,SAAS,CAAC;gCAC5C,UAAU,EAAE,cAAc;gCAC1B,qBAAqB,EAAE,IAAI;6BAC5B;yBACF,CAAC;wBAEmB,qBAAM,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAA;;wBAA1D,YAAY,GAAG,SAA2C;wBAE1D,oBAAoB,GAAG,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,UAAA,CAAC;4BAC1D,OAAA,CAAC,CAAC,MAAM,KAAK,aAAa,IAAI,CAAC,CAAC,MAAM,KAAK,SAAS;wBAApD,CAAoD,CACrD,CAAC;wBACF,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAEjD,sBAAsB,GAAG,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,qBAAqB,EAAvB,CAAuB,CAAC,CAAC;wBAC3F,MAAM,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;;;;aAC1D,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kCAAkC,EAAE;QAC3C,EAAE,CAAC,kDAAkD,EAAE;;;;4BAChC,qBAAM,OAAO,CAAC,oBAAoB,CAAC;4BACtD,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BACjE,UAAU,EAAE,iBAAiB;4BAC7B,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,eAAwB;4BACvC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG;yBACZ,CAAC,EAAA;;wBARI,YAAY,GAAG,SAQnB;wBAEF,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC9C,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBAE1D,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,UAAA,SAAS;4BACvC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE,CAAC;4BACnC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;4BACtC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;4BAC5C,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;4BAClD,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;4BACzC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBACvD,CAAC,CAAC,CAAC;;;;aACJ,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE;;;;4BACjB,qBAAM,OAAO,CAAC,oBAAoB,CAAC;4BACtD,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BACjE,UAAU,EAAE,oBAAoB;4BAChC,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,YAAqB;4BACpC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG;yBACZ,CAAC,EAAA;;wBARI,YAAY,GAAG,SAQnB;wBAEI,mBAAmB,GAAG;4BAC1B,MAAM,EAAE,UAAU;4BAClB,MAAM,EAAE,YAAY,CAAC,EAAE;4BACvB,WAAW,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;4BAC1C,iBAAiB,EAAE,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;4BAClE,QAAQ,EAAE,CAAC,uBAAuB,EAAE,qBAAqB,CAAC;yBAC3D,CAAC;wBAEa,qBAAM,OAAO,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,EAAA;;wBAA7D,MAAM,GAAG,SAAoD;wBAEnE,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC9C,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;wBAC5D,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;;;;aAC3C,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE;QAC5B,EAAE,CAAC,8CAA8C,EAAE;;;;4BAC5B,qBAAM,OAAO,CAAC,oBAAoB,CAAC;4BACtD,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BACjE,UAAU,EAAE,sBAAsB;4BAClC,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,UAAmB;4BAClC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG;yBACZ,CAAC,EAAA;;wBARI,YAAY,GAAG,SAQnB;wBAGI,kBAAkB,GAAG;4BACzB,MAAM,EAAE,UAAU;4BAClB,MAAM,EAAE,YAAY,CAAC,EAAE;4BACvB,kBAAkB,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;4BAClD,YAAY,EAAE;gCACZ,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,sBAAsB;6BACvE;4BACD,SAAS,EAAE,EAAE,EAAE,2BAA2B;yBAC3C,CAAC;wBAEkB,qBAAM,OAAO,CAAC,cAAc,CAAC,kBAAkB,CAAC,EAAA;;wBAA9D,WAAW,GAAG,SAAgD;wBAEpE,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC9C,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,IAAI,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,IAAI,KAAK,UAAU,EAAvB,CAAuB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAChF,MAAM,CAAC,WAAW,CAAC,sBAAsB,CAAC,CAAC,eAAe,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;;;;aAC5F,CAAC,CAAC;QAEH,EAAE,CAAC,qDAAqD,EAAE;;;;4BACnC,qBAAM,OAAO,CAAC,oBAAoB,CAAC;4BACtD,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BAC3D,UAAU,EAAE,oBAAoB;4BAChC,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,YAAqB;4BACpC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG;yBACZ,CAAC,EAAA;;wBARI,YAAY,GAAG,SAQnB;wBAEI,gBAAgB,GAAG;4BACvB,MAAM,EAAE,UAAU;4BAClB,MAAM,EAAE,YAAY,CAAC,EAAE;4BACvB,kBAAkB,EAAE,EAAE;4BACtB,YAAY,EAAE;gCACZ,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,EAAE,iBAAiB;6BAChE;4BACD,SAAS,EAAE,EAAE;4BACb,YAAY,EAAE,CAAC,aAAa,EAAE,UAAU,CAAC;yBAC1C,CAAC;wBAEoB,qBAAM,OAAO,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAAA;;wBAA9D,aAAa,GAAG,SAA8C;wBAEpE,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC1C,mBAAmB,GAAG,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,UAAA,GAAG,IAAI,OAAA,GAAG,CAAC,IAAI,KAAK,cAAc,EAA3B,CAA2B,CAAC,CAAC;wBACjG,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;;;;aACvD,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,oCAAoC,EAAE;QAC7C,EAAE,CAAC,uDAAuD,EAAE;;;;4BACrC,qBAAM,OAAO,CAAC,oBAAoB,CAAC;4BACtD,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BACjE,UAAU,EAAE,sBAAsB;4BAClC,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,eAAwB;4BACvC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG;yBACZ,CAAC,EAAA;;wBARI,YAAY,GAAG,SAQnB;wBAEF,MAAM,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC,WAAW,EAAE,CAAC;wBACnD,MAAM,CAAC,YAAY,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBACpE,MAAM,CAAC,YAAY,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;wBAChE,MAAM,CAAC,YAAY,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;;;;aAC1E,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE;;;;;wBAC/B,gBAAgB,GAAG;4BACvB,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BAC3D,UAAU,EAAE,6BAA6B;4BACzC,SAAS,EAAE,CAAC,EAAE,iBAAiB;4BAC/B,aAAa,EAAE,WAAoB;4BACnC,YAAY,EAAE,EAAE,EAAE,YAAY;4BAC9B,MAAM,EAAE,IAAI;yBACb,CAAC;wBAEmB,qBAAM,OAAO,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,EAAA;;wBAAnE,YAAY,GAAG,SAAoD;wBAEzE,MAAM,CAAC,YAAY,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;wBACvD,MAAM,CAAC,YAAY,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,6BAA6B;wBACtG,MAAM,CAAC,YAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC7D,MAAM,CAAC,YAAY,CAAC,mBAAmB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;;;;aAC1E,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,+BAA+B,EAAE;QACxC,EAAE,CAAC,wCAAwC,EAAE;;;;;wBACrC,cAAc,GAAG;4BACrB,MAAM,EAAE,EAAE;4BACV,aAAa,EAAE,EAAE;4BACjB,UAAU,EAAE,EAAE;4BACd,SAAS,EAAE,CAAC,CAAC;4BACb,aAAa,EAAE,SAAgB;4BAC/B,YAAY,EAAE,CAAC,CAAC;4BAChB,MAAM,EAAE,CAAC,GAAG;yBACb,CAAC;wBAEF,qBAAM,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,cAAc,CAAC,CAAC;iCACvD,OAAO,CAAC,OAAO,CAAC,+BAA+B,CAAC,EAAA;;wBADnD,SACmD,CAAC;;;;aACrD,CAAC,CAAC;QAEH,EAAE,CAAC,oCAAoC,EAAE;;;;;wBACjC,kBAAkB,GAAG;4BACzB,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BACjE,UAAU,EAAE,mBAAmB,EAAE,oBAAoB;4BACrD,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,YAAqB;4BACpC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG;yBACZ,CAAC;wBAEmB,qBAAM,OAAO,CAAC,oBAAoB,CAAC,kBAAkB,CAAC,EAAA;;wBAArE,YAAY,GAAG,SAAsD;wBAE3E,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;wBACnC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;wBAC5C,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,EAA1B,CAA0B,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;;aAChF,CAAC,CAAC;QAEH,EAAE,CAAC,iDAAiD,EAAE;;;;;wBAE9C,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC;wBACnC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC;wBAEvE,OAAO,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,aAAa,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;4BACjE,UAAU,EAAE,oBAAoB;4BAChC,SAAS,EAAE,CAAC;4BACZ,aAAa,EAAE,YAAqB;4BACpC,YAAY,EAAE,EAAE;4BAChB,MAAM,EAAE,GAAG;yBACZ,CAAC;wBAEmB,qBAAM,OAAO,CAAC,oBAAoB,CAAC,OAAO,CAAC,EAAA;;wBAA1D,YAAY,GAAG,SAA2C;wBAEhE,MAAM,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;wBACnC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAC1C,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,sCAAsC;wBAEhG,MAAM,CAAC,KAAK,GAAG,aAAa,CAAC;;;;aAC9B,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC", "names": [], "sources": ["/Users/<USER>/faafo/faafo/faafo-career-platform/__tests__/lib/skills/personalized-learning-path-service.test.ts"], "sourcesContent": ["/**\n * Personalized Learning Path Service Tests\n * \n * Tests Personalized Learning Path Service functionality, business logic, and edge cases.\n * \n * @category unit\n * @requires Unit testing utilities, mocking\n */\n\nimport { PersonalizedLearningPathService } from '@/lib/skills/PersonalizedLearningPathService';\n\ndescribe('PersonalizedLearningPathService', () => {\n  let service: PersonalizedLearningPathService;\n\n  beforeEach(() => {\n    service = new PersonalizedLearningPathService();\n  });\n\n  describe('Learning Path Generation', () => {\n    it('should generate a personalized learning path', async () => {\n      const request = {\n        userId: 'user-123',\n        currentSkills: [\n          { skill: 'javascript', level: 7, confidence: 8 },\n          { skill: 'html', level: 9, confidence: 9 },\n          { skill: 'css', level: 8, confidence: 7 },\n        ],\n        targetRole: 'Full Stack Developer',\n        timeframe: 6, // months\n        learningStyle: 'hands-on' as const,\n        availability: 10, // hours per week\n        budget: 500, // dollars\n        preferences: {\n          preferredFormats: ['video', 'interactive'],\n          difficulty: 'intermediate',\n          certificationRequired: true,\n        },\n      };\n\n      const learningPath = await service.generateLearningPath(request);\n\n      expect(learningPath).toBeDefined();\n      expect(learningPath.id).toBeDefined();\n      expect(learningPath.userId).toBe('user-123');\n      expect(learningPath.targetRole).toBe('Full Stack Developer');\n      expect(learningPath.estimatedDuration).toBeDefined();\n      expect(learningPath.totalCost).toBeLessThanOrEqual(500);\n      expect(learningPath.phases).toBeDefined();\n      expect(learningPath.phases.length).toBeGreaterThan(0);\n      expect(learningPath.skillGaps).toBeDefined();\n      expect(learningPath.recommendations).toBeDefined();\n    });\n\n    it('should identify skill gaps for target role', async () => {\n      const request = {\n        userId: 'user-123',\n        currentSkills: [{ skill: 'html', level: 5, confidence: 6 }],\n        targetRole: 'React Developer',\n        timeframe: 3,\n        learningStyle: 'visual' as const,\n        availability: 15,\n        budget: 1000,\n      };\n\n      const learningPath = await service.generateLearningPath(request);\n\n      expect(learningPath.skillGaps).toBeDefined();\n      expect(learningPath.skillGaps.length).toBeGreaterThan(0);\n      \n      const reactGap = learningPath.skillGaps.find(gap => gap.skill === 'react');\n      expect(reactGap).toBeDefined();\n      expect(reactGap?.currentLevel).toBe(0);\n      expect(reactGap?.targetLevel).toBeGreaterThan(0);\n      expect(reactGap?.priority).toBeDefined();\n    });\n\n    it('should create learning phases in logical order', async () => {\n      const request = {\n        userId: 'user-123',\n        currentSkills: [{ skill: 'html', level: 3, confidence: 4 }],\n        targetRole: 'Frontend Developer',\n        timeframe: 12,\n        learningStyle: 'structured' as const,\n        availability: 8,\n        budget: 2000,\n      };\n\n      const learningPath = await service.generateLearningPath(request);\n\n      expect(learningPath.phases.length).toBeGreaterThan(1);\n      \n      // Check that phases have proper dependencies\n      for (let i = 1; i < learningPath.phases.length; i++) {\n        const currentPhase = learningPath.phases[i];\n        const previousPhase = learningPath.phases[i - 1];\n        \n        expect(currentPhase.startDate.getTime()).toBeGreaterThanOrEqual(\n          previousPhase.endDate.getTime()\n        );\n      }\n    });\n\n    it('should respect budget constraints', async () => {\n      const lowBudgetRequest = {\n        userId: 'user-123',\n        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],\n        targetRole: 'Full Stack Developer',\n        timeframe: 6,\n        learningStyle: 'self-paced' as const,\n        availability: 10,\n        budget: 100, // Low budget\n      };\n\n      const learningPath = await service.generateLearningPath(lowBudgetRequest);\n\n      expect(learningPath.totalCost).toBeLessThanOrEqual(100);\n      expect(learningPath.resources.some(r => r.cost === 0)).toBe(true); // Should include free resources\n    });\n\n    it('should adapt to learning style preferences', async () => {\n      const visualLearnerRequest = {\n        userId: 'user-123',\n        currentSkills: [{ skill: 'javascript', level: 6, confidence: 7 }],\n        targetRole: 'Frontend Developer',\n        timeframe: 4,\n        learningStyle: 'visual' as const,\n        availability: 12,\n        budget: 500,\n      };\n\n      const learningPath = await service.generateLearningPath(visualLearnerRequest);\n\n      const visualResources = learningPath.resources.filter(r => \n        r.format === 'video' || r.format === 'interactive'\n      );\n      \n      expect(visualResources.length).toBeGreaterThan(0);\n    });\n  });\n\n  describe('Learning Path Optimization', () => {\n    it('should optimize learning path for time constraints', async () => {\n      const timeConstrainedRequest = {\n        userId: 'user-123',\n        currentSkills: [{ skill: 'javascript', level: 7, confidence: 8 }],\n        targetRole: 'React Developer',\n        timeframe: 2, // Very short timeframe\n        learningStyle: 'intensive' as const,\n        availability: 20, // High availability\n        budget: 1000,\n      };\n\n      const learningPath = await service.generateLearningPath(timeConstrainedRequest);\n\n      expect(learningPath.estimatedDuration).toBeLessThanOrEqual(2 * 4); // 2 months in weeks\n      expect(learningPath.phases.every(phase => phase.intensity === 'high')).toBe(true);\n    });\n\n    it('should suggest alternative paths when constraints are unrealistic', async () => {\n      const unrealisticRequest = {\n        userId: 'user-123',\n        currentSkills: [{ skill: 'html', level: 2, confidence: 3 }],\n        targetRole: 'Senior Full Stack Developer',\n        timeframe: 1, // Unrealistic timeframe\n        learningStyle: 'casual' as const,\n        availability: 2, // Very low availability\n        budget: 50, // Very low budget\n      };\n\n      const learningPath = await service.generateLearningPath(unrealisticRequest);\n\n      expect(learningPath.alternatives).toBeDefined();\n      expect(learningPath.alternatives.length).toBeGreaterThan(0);\n      expect(learningPath.feasibilityScore).toBeLessThan(0.7); // Low feasibility\n    });\n\n    it('should prioritize skills based on market demand and role requirements', async () => {\n      const request = {\n        userId: 'user-123',\n        currentSkills: [{ skill: 'javascript', level: 6, confidence: 7 }],\n        targetRole: 'Full Stack Developer',\n        timeframe: 8,\n        learningStyle: 'balanced' as const,\n        availability: 12,\n        budget: 800,\n      };\n\n      const learningPath = await service.generateLearningPath(request);\n\n      const highPrioritySkills = learningPath.skillGaps.filter(gap => gap.priority === 'high' || gap.priority === 'critical');\n      expect(highPrioritySkills.length).toBeGreaterThan(0);\n      \n      // High priority skills should appear in earlier phases\n      const firstPhaseSkills = learningPath.phases[0].skills;\n      const hasHighPriorityInFirstPhase = firstPhaseSkills.some(skill => \n        highPrioritySkills.some(gap => gap.skill === skill.name)\n      );\n      expect(hasHighPriorityInFirstPhase).toBe(true);\n    });\n  });\n\n  describe('Progress Tracking Integration', () => {\n    it.skip('should track learning progress and update path accordingly', async () => {\n      const learningPath = await service.generateLearningPath({\n        userId: 'user-123',\n        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],\n        targetRole: 'React Developer',\n        timeframe: 6,\n        learningStyle: 'structured' as const,\n        availability: 10,\n        budget: 600,\n      });\n\n      // Debug: Check what resources are available\n      console.log('Learning path resources:', learningPath.resources.length);\n      console.log('Resource IDs:', learningPath.resources.map(r => r.id));\n\n      // Ensure we have at least one resource to complete\n      expect(learningPath.resources.length).toBeGreaterThan(0);\n\n      const progressUpdate = {\n        userId: 'user-123',\n        pathId: learningPath.id,\n        completedResources: [learningPath.resources[0].id], // Complete at least one resource\n        skillUpdates: [\n          { skill: 'react', newLevel: 4, confidence: 6 },\n        ],\n        timeSpent: 20, // hours\n      };\n\n      const updatedPath = await service.updateProgress(progressUpdate);\n\n      // Debug: Check progress after update\n      console.log('Completed resources:', updatedPath.progress.completedResources);\n      console.log('Total resources:', updatedPath.resources.length);\n      console.log('Completion percentage:', updatedPath.progress.completionPercentage);\n\n      expect(updatedPath).toBeDefined();\n      expect(updatedPath.progress.completionPercentage).toBeGreaterThan(0);\n      expect(updatedPath.progress.timeSpent).toBe(20);\n      expect(updatedPath.estimatedTimeRemaining).toBeLessThan(learningPath.estimatedDuration);\n    });\n\n    it('should suggest path adjustments based on progress', async () => {\n      const learningPath = await service.generateLearningPath({\n        userId: 'user-123',\n        currentSkills: [{ skill: 'javascript', level: 6, confidence: 7 }],\n        targetRole: 'Frontend Developer',\n        timeframe: 4,\n        learningStyle: 'adaptive' as const,\n        availability: 15,\n        budget: 500,\n      });\n\n      // Simulate faster than expected progress\n      const progressUpdate = {\n        userId: 'user-123',\n        pathId: learningPath.id,\n        completedResources: learningPath.resources.slice(0, 3).map(r => r.id),\n        skillUpdates: [\n          { skill: 'react', newLevel: 7, confidence: 8 },\n        ],\n        timeSpent: 15, // Less time than estimated\n      };\n\n      const updatedPath = await service.updateProgress(progressUpdate);\n\n      expect(updatedPath.adjustments).toBeDefined();\n      expect(updatedPath.adjustments.length).toBeGreaterThan(0);\n      expect(updatedPath.adjustments.some(adj => adj.type === 'accelerate')).toBe(true);\n    });\n  });\n\n  describe('Resource Recommendation', () => {\n    it('should recommend diverse learning resources', async () => {\n      const learningPath = await service.generateLearningPath({\n        userId: 'user-123',\n        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],\n        targetRole: 'Full Stack Developer',\n        timeframe: 8,\n        learningStyle: 'mixed' as const,\n        availability: 12,\n        budget: 1000,\n      });\n\n      const resourceTypes = new Set(learningPath.resources.map(r => r.format));\n      expect(resourceTypes.size).toBeGreaterThan(2); // Should have multiple formats\n\n      const costLevels = new Set(learningPath.resources.map(r => r.cost > 0 ? 'paid' : 'free'));\n      expect(costLevels.has('free')).toBe(true); // Should include free resources\n    });\n\n    it('should match resources to learning preferences', async () => {\n      const request = {\n        userId: 'user-123',\n        currentSkills: [{ skill: 'html', level: 6, confidence: 7 }],\n        targetRole: 'Frontend Developer',\n        timeframe: 6,\n        learningStyle: 'hands-on' as const,\n        availability: 10,\n        budget: 500,\n        preferences: {\n          preferredFormats: ['interactive', 'project'],\n          difficulty: 'intermediate',\n          certificationRequired: true,\n        },\n      };\n\n      const learningPath = await service.generateLearningPath(request);\n\n      const interactiveResources = learningPath.resources.filter(r => \n        r.format === 'interactive' || r.format === 'project'\n      );\n      expect(interactiveResources.length).toBeGreaterThan(0);\n\n      const certificationResources = learningPath.resources.filter(r => r.providesCertification);\n      expect(certificationResources.length).toBeGreaterThan(0);\n    });\n  });\n\n  describe('Milestone and Achievement System', () => {\n    it('should define clear milestones for learning path', async () => {\n      const learningPath = await service.generateLearningPath({\n        userId: 'user-123',\n        currentSkills: [{ skill: 'javascript', level: 4, confidence: 5 }],\n        targetRole: 'React Developer',\n        timeframe: 5,\n        learningStyle: 'goal-oriented' as const,\n        availability: 12,\n        budget: 600,\n      });\n\n      expect(learningPath.milestones).toBeDefined();\n      expect(learningPath.milestones.length).toBeGreaterThan(0);\n      \n      learningPath.milestones.forEach(milestone => {\n        expect(milestone.id).toBeDefined();\n        expect(milestone.title).toBeDefined();\n        expect(milestone.description).toBeDefined();\n        expect(milestone.targetDate).toBeInstanceOf(Date);\n        expect(milestone.criteria).toBeDefined();\n        expect(milestone.criteria.length).toBeGreaterThan(0);\n      });\n    });\n\n    it('should track milestone completion', async () => {\n      const learningPath = await service.generateLearningPath({\n        userId: 'user-123',\n        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],\n        targetRole: 'Frontend Developer',\n        timeframe: 4,\n        learningStyle: 'structured' as const,\n        availability: 15,\n        budget: 500,\n      });\n\n      const milestoneCompletion = {\n        userId: 'user-123',\n        pathId: learningPath.id,\n        milestoneId: learningPath.milestones[0].id,\n        completedCriteria: learningPath.milestones[0].criteria.slice(0, 2),\n        evidence: ['completed-project-url', 'assessment-score-85'],\n      };\n\n      const result = await service.completeMilestone(milestoneCompletion);\n\n      expect(result.success).toBe(true);\n      expect(result.milestone.completed).toBe(true);\n      expect(result.milestone.completedDate).toBeInstanceOf(Date);\n      expect(result.achievements).toBeDefined();\n    });\n  });\n\n  describe('Adaptive Learning', () => {\n    it('should adapt path based on learning velocity', async () => {\n      const learningPath = await service.generateLearningPath({\n        userId: 'user-123',\n        currentSkills: [{ skill: 'javascript', level: 6, confidence: 7 }],\n        targetRole: 'Full Stack Developer',\n        timeframe: 6,\n        learningStyle: 'adaptive' as const,\n        availability: 12,\n        budget: 800,\n      });\n\n      // Simulate slow progress\n      const slowProgressUpdate = {\n        userId: 'user-123',\n        pathId: learningPath.id,\n        completedResources: [learningPath.resources[0].id],\n        skillUpdates: [\n          { skill: 'react', newLevel: 2, confidence: 4 }, // Lower than expected\n        ],\n        timeSpent: 40, // More time than estimated\n      };\n\n      const adaptedPath = await service.updateProgress(slowProgressUpdate);\n\n      expect(adaptedPath.adjustments).toBeDefined();\n      expect(adaptedPath.adjustments.some(adj => adj.type === 'simplify')).toBe(true);\n      expect(adaptedPath.estimatedTimeRemaining).toBeGreaterThan(learningPath.estimatedDuration);\n    });\n\n    it('should suggest additional resources when struggling', async () => {\n      const learningPath = await service.generateLearningPath({\n        userId: 'user-123',\n        currentSkills: [{ skill: 'html', level: 4, confidence: 5 }],\n        targetRole: 'Frontend Developer',\n        timeframe: 8,\n        learningStyle: 'supportive' as const,\n        availability: 10,\n        budget: 600,\n      });\n\n      const strugglingUpdate = {\n        userId: 'user-123',\n        pathId: learningPath.id,\n        completedResources: [],\n        skillUpdates: [\n          { skill: 'css', newLevel: 3, confidence: 2 }, // Low confidence\n        ],\n        timeSpent: 30,\n        difficulties: ['css-flexbox', 'css-grid'],\n      };\n\n      const supportedPath = await service.updateProgress(strugglingUpdate);\n\n      expect(supportedPath.adjustments).toBeDefined();\n      const additionalResources = supportedPath.adjustments.filter(adj => adj.type === 'add_resource');\n      expect(additionalResources.length).toBeGreaterThan(0);\n    });\n  });\n\n  describe('Integration with External Services', () => {\n    it('should integrate with skill market data for relevance', async () => {\n      const learningPath = await service.generateLearningPath({\n        userId: 'user-123',\n        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],\n        targetRole: 'Full Stack Developer',\n        timeframe: 6,\n        learningStyle: 'market-driven' as const,\n        availability: 12,\n        budget: 800,\n      });\n\n      expect(learningPath.marketRelevance).toBeDefined();\n      expect(learningPath.marketRelevance.demandScore).toBeGreaterThan(0);\n      expect(learningPath.marketRelevance.salaryImpact).toBeDefined();\n      expect(learningPath.marketRelevance.jobOpportunities).toBeGreaterThan(0);\n    });\n\n    it('should validate path feasibility', async () => {\n      const ambitiousRequest = {\n        userId: 'user-123',\n        currentSkills: [{ skill: 'html', level: 3, confidence: 4 }],\n        targetRole: 'Senior Full Stack Developer',\n        timeframe: 3, // Very ambitious\n        learningStyle: 'intensive' as const,\n        availability: 40, // Full time\n        budget: 2000,\n      };\n\n      const learningPath = await service.generateLearningPath(ambitiousRequest);\n\n      expect(learningPath.feasibilityAnalysis).toBeDefined();\n      expect(learningPath.feasibilityAnalysis.overallScore).toBeLessThan(0.8); // Should flag as challenging\n      expect(learningPath.feasibilityAnalysis.risks).toBeDefined();\n      expect(learningPath.feasibilityAnalysis.risks.length).toBeGreaterThan(0);\n    });\n  });\n\n  describe('Error Handling and Edge Cases', () => {\n    it('should handle invalid input gracefully', async () => {\n      const invalidRequest = {\n        userId: '',\n        currentSkills: [],\n        targetRole: '',\n        timeframe: -1,\n        learningStyle: 'invalid' as any,\n        availability: -5,\n        budget: -100,\n      };\n\n      await expect(service.generateLearningPath(invalidRequest))\n        .rejects.toThrow('Invalid learning path request');\n    });\n\n    it('should handle unknown target roles', async () => {\n      const unknownRoleRequest = {\n        userId: 'user-123',\n        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],\n        targetRole: 'Quantum Developer', // Non-existent role\n        timeframe: 6,\n        learningStyle: 'structured' as const,\n        availability: 10,\n        budget: 500,\n      };\n\n      const learningPath = await service.generateLearningPath(unknownRoleRequest);\n\n      expect(learningPath).toBeDefined();\n      expect(learningPath.warnings).toBeDefined();\n      expect(learningPath.warnings.some(w => w.includes('unknown role'))).toBe(true);\n    });\n\n    it('should handle service unavailability gracefully', async () => {\n      // Mock service failure\n      const originalFetch = global.fetch;\n      global.fetch = jest.fn().mockRejectedValue(new Error('Service unavailable'));\n\n      const request = {\n        userId: 'user-123',\n        currentSkills: [{ skill: 'javascript', level: 5, confidence: 6 }],\n        targetRole: 'Frontend Developer',\n        timeframe: 6,\n        learningStyle: 'structured' as const,\n        availability: 10,\n        budget: 500,\n      };\n\n      const learningPath = await service.generateLearningPath(request);\n\n      expect(learningPath).toBeDefined();\n      expect(learningPath.isOffline).toBe(true);\n      expect(learningPath.resources.length).toBeGreaterThan(0); // Should use cached/default resources\n\n      global.fetch = originalFetch;\n    });\n  });\n});\n"], "version": 3}