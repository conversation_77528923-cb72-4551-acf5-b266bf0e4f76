e70aa94d3c4a9eaa9b16a041122bcfd0
"use strict";

/* istanbul ignore next */
function cov_1pdg1trbnj() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/reactions/batch/route.ts";
  var hash = "699bceb9fc0c1332ea7bda3d27d32912ffe0b32f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/reactions/batch/route.ts",
    statementMap: {
      "0": {
        start: {
          line: 2,
          column: 16
        },
        end: {
          line: 10,
          column: 1
        }
      },
      "1": {
        start: {
          line: 3,
          column: 28
        },
        end: {
          line: 3,
          column: 110
        }
      },
      "2": {
        start: {
          line: 3,
          column: 91
        },
        end: {
          line: 3,
          column: 106
        }
      },
      "3": {
        start: {
          line: 4,
          column: 4
        },
        end: {
          line: 9,
          column: 7
        }
      },
      "4": {
        start: {
          line: 5,
          column: 36
        },
        end: {
          line: 5,
          column: 97
        }
      },
      "5": {
        start: {
          line: 5,
          column: 42
        },
        end: {
          line: 5,
          column: 70
        }
      },
      "6": {
        start: {
          line: 5,
          column: 85
        },
        end: {
          line: 5,
          column: 95
        }
      },
      "7": {
        start: {
          line: 6,
          column: 35
        },
        end: {
          line: 6,
          column: 100
        }
      },
      "8": {
        start: {
          line: 6,
          column: 41
        },
        end: {
          line: 6,
          column: 73
        }
      },
      "9": {
        start: {
          line: 6,
          column: 88
        },
        end: {
          line: 6,
          column: 98
        }
      },
      "10": {
        start: {
          line: 7,
          column: 32
        },
        end: {
          line: 7,
          column: 116
        }
      },
      "11": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 8,
          column: 78
        }
      },
      "12": {
        start: {
          line: 11,
          column: 18
        },
        end: {
          line: 37,
          column: 1
        }
      },
      "13": {
        start: {
          line: 12,
          column: 12
        },
        end: {
          line: 12,
          column: 104
        }
      },
      "14": {
        start: {
          line: 12,
          column: 43
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "15": {
        start: {
          line: 12,
          column: 57
        },
        end: {
          line: 12,
          column: 68
        }
      },
      "16": {
        start: {
          line: 12,
          column: 69
        },
        end: {
          line: 12,
          column: 81
        }
      },
      "17": {
        start: {
          line: 12,
          column: 119
        },
        end: {
          line: 12,
          column: 196
        }
      },
      "18": {
        start: {
          line: 13,
          column: 4
        },
        end: {
          line: 13,
          column: 160
        }
      },
      "19": {
        start: {
          line: 13,
          column: 141
        },
        end: {
          line: 13,
          column: 153
        }
      },
      "20": {
        start: {
          line: 14,
          column: 23
        },
        end: {
          line: 14,
          column: 68
        }
      },
      "21": {
        start: {
          line: 14,
          column: 45
        },
        end: {
          line: 14,
          column: 65
        }
      },
      "22": {
        start: {
          line: 16,
          column: 8
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "23": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 16,
          column: 70
        }
      },
      "24": {
        start: {
          line: 17,
          column: 8
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "25": {
        start: {
          line: 17,
          column: 50
        },
        end: {
          line: 34,
          column: 66
        }
      },
      "26": {
        start: {
          line: 18,
          column: 12
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "27": {
        start: {
          line: 18,
          column: 160
        },
        end: {
          line: 18,
          column: 169
        }
      },
      "28": {
        start: {
          line: 19,
          column: 12
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "29": {
        start: {
          line: 19,
          column: 26
        },
        end: {
          line: 19,
          column: 52
        }
      },
      "30": {
        start: {
          line: 20,
          column: 12
        },
        end: {
          line: 32,
          column: 13
        }
      },
      "31": {
        start: {
          line: 21,
          column: 32
        },
        end: {
          line: 21,
          column: 39
        }
      },
      "32": {
        start: {
          line: 21,
          column: 40
        },
        end: {
          line: 21,
          column: 46
        }
      },
      "33": {
        start: {
          line: 22,
          column: 24
        },
        end: {
          line: 22,
          column: 34
        }
      },
      "34": {
        start: {
          line: 22,
          column: 35
        },
        end: {
          line: 22,
          column: 72
        }
      },
      "35": {
        start: {
          line: 23,
          column: 24
        },
        end: {
          line: 23,
          column: 34
        }
      },
      "36": {
        start: {
          line: 23,
          column: 35
        },
        end: {
          line: 23,
          column: 45
        }
      },
      "37": {
        start: {
          line: 23,
          column: 46
        },
        end: {
          line: 23,
          column: 55
        }
      },
      "38": {
        start: {
          line: 23,
          column: 56
        },
        end: {
          line: 23,
          column: 65
        }
      },
      "39": {
        start: {
          line: 24,
          column: 24
        },
        end: {
          line: 24,
          column: 41
        }
      },
      "40": {
        start: {
          line: 24,
          column: 42
        },
        end: {
          line: 24,
          column: 55
        }
      },
      "41": {
        start: {
          line: 24,
          column: 56
        },
        end: {
          line: 24,
          column: 65
        }
      },
      "42": {
        start: {
          line: 26,
          column: 20
        },
        end: {
          line: 26,
          column: 128
        }
      },
      "43": {
        start: {
          line: 26,
          column: 110
        },
        end: {
          line: 26,
          column: 116
        }
      },
      "44": {
        start: {
          line: 26,
          column: 117
        },
        end: {
          line: 26,
          column: 126
        }
      },
      "45": {
        start: {
          line: 27,
          column: 20
        },
        end: {
          line: 27,
          column: 106
        }
      },
      "46": {
        start: {
          line: 27,
          column: 81
        },
        end: {
          line: 27,
          column: 97
        }
      },
      "47": {
        start: {
          line: 27,
          column: 98
        },
        end: {
          line: 27,
          column: 104
        }
      },
      "48": {
        start: {
          line: 28,
          column: 20
        },
        end: {
          line: 28,
          column: 89
        }
      },
      "49": {
        start: {
          line: 28,
          column: 57
        },
        end: {
          line: 28,
          column: 72
        }
      },
      "50": {
        start: {
          line: 28,
          column: 73
        },
        end: {
          line: 28,
          column: 80
        }
      },
      "51": {
        start: {
          line: 28,
          column: 81
        },
        end: {
          line: 28,
          column: 87
        }
      },
      "52": {
        start: {
          line: 29,
          column: 20
        },
        end: {
          line: 29,
          column: 87
        }
      },
      "53": {
        start: {
          line: 29,
          column: 47
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "54": {
        start: {
          line: 29,
          column: 63
        },
        end: {
          line: 29,
          column: 78
        }
      },
      "55": {
        start: {
          line: 29,
          column: 79
        },
        end: {
          line: 29,
          column: 85
        }
      },
      "56": {
        start: {
          line: 30,
          column: 20
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "57": {
        start: {
          line: 30,
          column: 30
        },
        end: {
          line: 30,
          column: 42
        }
      },
      "58": {
        start: {
          line: 31,
          column: 20
        },
        end: {
          line: 31,
          column: 33
        }
      },
      "59": {
        start: {
          line: 31,
          column: 34
        },
        end: {
          line: 31,
          column: 43
        }
      },
      "60": {
        start: {
          line: 33,
          column: 12
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "61": {
        start: {
          line: 34,
          column: 22
        },
        end: {
          line: 34,
          column: 34
        }
      },
      "62": {
        start: {
          line: 34,
          column: 35
        },
        end: {
          line: 34,
          column: 41
        }
      },
      "63": {
        start: {
          line: 34,
          column: 54
        },
        end: {
          line: 34,
          column: 64
        }
      },
      "64": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "65": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "66": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 89
        }
      },
      "67": {
        start: {
          line: 38,
          column: 0
        },
        end: {
          line: 38,
          column: 62
        }
      },
      "68": {
        start: {
          line: 39,
          column: 0
        },
        end: {
          line: 39,
          column: 36
        }
      },
      "69": {
        start: {
          line: 40,
          column: 15
        },
        end: {
          line: 40,
          column: 37
        }
      },
      "70": {
        start: {
          line: 41,
          column: 18
        },
        end: {
          line: 41,
          column: 38
        }
      },
      "71": {
        start: {
          line: 42,
          column: 13
        },
        end: {
          line: 42,
          column: 34
        }
      },
      "72": {
        start: {
          line: 43,
          column: 15
        },
        end: {
          line: 43,
          column: 38
        }
      },
      "73": {
        start: {
          line: 44,
          column: 34
        },
        end: {
          line: 44,
          column: 76
        }
      },
      "74": {
        start: {
          line: 45,
          column: 18
        },
        end: {
          line: 45,
          column: 44
        }
      },
      "75": {
        start: {
          line: 47,
          column: 0
        },
        end: {
          line: 172,
          column: 7
        }
      },
      "76": {
        start: {
          line: 47,
          column: 93
        },
        end: {
          line: 172,
          column: 3
        }
      },
      "77": {
        start: {
          line: 48,
          column: 4
        },
        end: {
          line: 171,
          column: 7
        }
      },
      "78": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 170,
          column: 20
        }
      },
      "79": {
        start: {
          line: 49,
          column: 132
        },
        end: {
          line: 170,
          column: 15
        }
      },
      "80": {
        start: {
          line: 52,
          column: 16
        },
        end: {
          line: 169,
          column: 19
        }
      },
      "81": {
        start: {
          line: 53,
          column: 20
        },
        end: {
          line: 168,
          column: 21
        }
      },
      "82": {
        start: {
          line: 55,
          column: 28
        },
        end: {
          line: 55,
          column: 77
        }
      },
      "83": {
        start: {
          line: 56,
          column: 28
        },
        end: {
          line: 56,
          column: 148
        }
      },
      "84": {
        start: {
          line: 57,
          column: 28
        },
        end: {
          line: 57,
          column: 150
        }
      },
      "85": {
        start: {
          line: 58,
          column: 28
        },
        end: {
          line: 58,
          column: 104
        }
      },
      "86": {
        start: {
          line: 60,
          column: 28
        },
        end: {
          line: 60,
          column: 48
        }
      },
      "87": {
        start: {
          line: 61,
          column: 28
        },
        end: {
          line: 61,
          column: 150
        }
      },
      "88": {
        start: {
          line: 62,
          column: 28
        },
        end: {
          line: 67,
          column: 29
        }
      },
      "89": {
        start: {
          line: 63,
          column: 32
        },
        end: {
          line: 66,
          column: 57
        }
      },
      "90": {
        start: {
          line: 69,
          column: 28
        },
        end: {
          line: 74,
          column: 29
        }
      },
      "91": {
        start: {
          line: 70,
          column: 32
        },
        end: {
          line: 73,
          column: 57
        }
      },
      "92": {
        start: {
          line: 75,
          column: 28
        },
        end: {
          line: 78,
          column: 30
        }
      },
      "93": {
        start: {
          line: 79,
          column: 28
        },
        end: {
          line: 79,
          column: 79
        }
      },
      "94": {
        start: {
          line: 79,
          column: 55
        },
        end: {
          line: 79,
          column: 79
        }
      },
      "95": {
        start: {
          line: 80,
          column: 28
        },
        end: {
          line: 92,
          column: 36
        }
      },
      "96": {
        start: {
          line: 94,
          column: 28
        },
        end: {
          line: 94,
          column: 54
        }
      },
      "97": {
        start: {
          line: 95,
          column: 28
        },
        end: {
          line: 101,
          column: 35
        }
      },
      "98": {
        start: {
          line: 96,
          column: 32
        },
        end: {
          line: 98,
          column: 33
        }
      },
      "99": {
        start: {
          line: 97,
          column: 36
        },
        end: {
          line: 97,
          column: 62
        }
      },
      "100": {
        start: {
          line: 99,
          column: 32
        },
        end: {
          line: 99,
          column: 68
        }
      },
      "101": {
        start: {
          line: 100,
          column: 32
        },
        end: {
          line: 100,
          column: 43
        }
      },
      "102": {
        start: {
          line: 103,
          column: 28
        },
        end: {
          line: 119,
          column: 29
        }
      },
      "103": {
        start: {
          line: 104,
          column: 32
        },
        end: {
          line: 104,
          column: 55
        }
      },
      "104": {
        start: {
          line: 105,
          column: 32
        },
        end: {
          line: 105,
          column: 77
        }
      },
      "105": {
        start: {
          line: 106,
          column: 32
        },
        end: {
          line: 109,
          column: 39
        }
      },
      "106": {
        start: {
          line: 107,
          column: 36
        },
        end: {
          line: 107,
          column: 87
        }
      },
      "107": {
        start: {
          line: 108,
          column: 36
        },
        end: {
          line: 108,
          column: 47
        }
      },
      "108": {
        start: {
          line: 110,
          column: 32
        },
        end: {
          line: 112,
          column: 48
        }
      },
      "109": {
        start: {
          line: 111,
          column: 74
        },
        end: {
          line: 111,
          column: 101
        }
      },
      "110": {
        start: {
          line: 113,
          column: 32
        },
        end: {
          line: 118,
          column: 34
        }
      },
      "111": {
        start: {
          line: 114,
          column: 76
        },
        end: {
          line: 114,
          column: 120
        }
      },
      "112": {
        start: {
          line: 120,
          column: 28
        },
        end: {
          line: 120,
          column: 41
        }
      },
      "113": {
        start: {
          line: 122,
          column: 28
        },
        end: {
          line: 122,
          column: 80
        }
      },
      "114": {
        start: {
          line: 122,
          column: 56
        },
        end: {
          line: 122,
          column: 80
        }
      },
      "115": {
        start: {
          line: 123,
          column: 28
        },
        end: {
          line: 135,
          column: 36
        }
      },
      "116": {
        start: {
          line: 137,
          column: 28
        },
        end: {
          line: 137,
          column: 55
        }
      },
      "117": {
        start: {
          line: 138,
          column: 28
        },
        end: {
          line: 144,
          column: 35
        }
      },
      "118": {
        start: {
          line: 139,
          column: 32
        },
        end: {
          line: 141,
          column: 33
        }
      },
      "119": {
        start: {
          line: 140,
          column: 36
        },
        end: {
          line: 140,
          column: 63
        }
      },
      "120": {
        start: {
          line: 142,
          column: 32
        },
        end: {
          line: 142,
          column: 69
        }
      },
      "121": {
        start: {
          line: 143,
          column: 32
        },
        end: {
          line: 143,
          column: 43
        }
      },
      "122": {
        start: {
          line: 146,
          column: 28
        },
        end: {
          line: 162,
          column: 29
        }
      },
      "123": {
        start: {
          line: 147,
          column: 32
        },
        end: {
          line: 147,
          column: 57
        }
      },
      "124": {
        start: {
          line: 148,
          column: 32
        },
        end: {
          line: 148,
          column: 79
        }
      },
      "125": {
        start: {
          line: 149,
          column: 32
        },
        end: {
          line: 152,
          column: 39
        }
      },
      "126": {
        start: {
          line: 150,
          column: 36
        },
        end: {
          line: 150,
          column: 87
        }
      },
      "127": {
        start: {
          line: 151,
          column: 36
        },
        end: {
          line: 151,
          column: 47
        }
      },
      "128": {
        start: {
          line: 153,
          column: 32
        },
        end: {
          line: 155,
          column: 48
        }
      },
      "129": {
        start: {
          line: 154,
          column: 74
        },
        end: {
          line: 154,
          column: 101
        }
      },
      "130": {
        start: {
          line: 156,
          column: 32
        },
        end: {
          line: 161,
          column: 34
        }
      },
      "131": {
        start: {
          line: 157,
          column: 76
        },
        end: {
          line: 157,
          column: 120
        }
      },
      "132": {
        start: {
          line: 163,
          column: 28
        },
        end: {
          line: 163,
          column: 41
        }
      },
      "133": {
        start: {
          line: 164,
          column: 32
        },
        end: {
          line: 167,
          column: 32
        }
      },
      "134": {
        start: {
          line: 174,
          column: 0
        },
        end: {
          line: 328,
          column: 7
        }
      },
      "135": {
        start: {
          line: 174,
          column: 94
        },
        end: {
          line: 328,
          column: 3
        }
      },
      "136": {
        start: {
          line: 175,
          column: 4
        },
        end: {
          line: 327,
          column: 7
        }
      },
      "137": {
        start: {
          line: 176,
          column: 8
        },
        end: {
          line: 326,
          column: 20
        }
      },
      "138": {
        start: {
          line: 176,
          column: 131
        },
        end: {
          line: 326,
          column: 15
        }
      },
      "139": {
        start: {
          line: 179,
          column: 16
        },
        end: {
          line: 325,
          column: 19
        }
      },
      "140": {
        start: {
          line: 180,
          column: 20
        },
        end: {
          line: 324,
          column: 21
        }
      },
      "141": {
        start: {
          line: 181,
          column: 32
        },
        end: {
          line: 181,
          column: 108
        }
      },
      "142": {
        start: {
          line: 183,
          column: 28
        },
        end: {
          line: 183,
          column: 48
        }
      },
      "143": {
        start: {
          line: 184,
          column: 28
        },
        end: {
          line: 189,
          column: 29
        }
      },
      "144": {
        start: {
          line: 185,
          column: 32
        },
        end: {
          line: 188,
          column: 57
        }
      },
      "145": {
        start: {
          line: 190,
          column: 28
        },
        end: {
          line: 190,
          column: 65
        }
      },
      "146": {
        start: {
          line: 192,
          column: 28
        },
        end: {
          line: 192,
          column: 45
        }
      },
      "147": {
        start: {
          line: 193,
          column: 28
        },
        end: {
          line: 193,
          column: 57
        }
      },
      "148": {
        start: {
          line: 194,
          column: 28
        },
        end: {
          line: 199,
          column: 29
        }
      },
      "149": {
        start: {
          line: 195,
          column: 32
        },
        end: {
          line: 198,
          column: 57
        }
      },
      "150": {
        start: {
          line: 201,
          column: 28
        },
        end: {
          line: 206,
          column: 29
        }
      },
      "151": {
        start: {
          line: 202,
          column: 32
        },
        end: {
          line: 205,
          column: 57
        }
      },
      "152": {
        start: {
          line: 207,
          column: 28
        },
        end: {
          line: 207,
          column: 41
        }
      },
      "153": {
        start: {
          line: 209,
          column: 28
        },
        end: {
          line: 315,
          column: 40
        }
      },
      "154": {
        start: {
          line: 209,
          column: 94
        },
        end: {
          line: 315,
          column: 35
        }
      },
      "155": {
        start: {
          line: 211,
          column: 36
        },
        end: {
          line: 314,
          column: 39
        }
      },
      "156": {
        start: {
          line: 212,
          column: 40
        },
        end: {
          line: 313,
          column: 41
        }
      },
      "157": {
        start: {
          line: 214,
          column: 48
        },
        end: {
          line: 214,
          column: 82
        }
      },
      "158": {
        start: {
          line: 215,
          column: 48
        },
        end: {
          line: 215,
          column: 61
        }
      },
      "159": {
        start: {
          line: 217,
          column: 48
        },
        end: {
          line: 217,
          column: 106
        }
      },
      "160": {
        start: {
          line: 217,
          column: 81
        },
        end: {
          line: 217,
          column: 106
        }
      },
      "161": {
        start: {
          line: 218,
          column: 48
        },
        end: {
          line: 218,
          column: 70
        }
      },
      "162": {
        start: {
          line: 219,
          column: 48
        },
        end: {
          line: 219,
          column: 157
        }
      },
      "163": {
        start: {
          line: 220,
          column: 48
        },
        end: {
          line: 223,
          column: 49
        }
      },
      "164": {
        start: {
          line: 221,
          column: 52
        },
        end: {
          line: 221,
          column: 109
        }
      },
      "165": {
        start: {
          line: 222,
          column: 52
        },
        end: {
          line: 222,
          column: 77
        }
      },
      "166": {
        start: {
          line: 224,
          column: 48
        },
        end: {
          line: 227,
          column: 49
        }
      },
      "167": {
        start: {
          line: 225,
          column: 52
        },
        end: {
          line: 225,
          column: 107
        }
      },
      "168": {
        start: {
          line: 226,
          column: 52
        },
        end: {
          line: 226,
          column: 77
        }
      },
      "169": {
        start: {
          line: 228,
          column: 48
        },
        end: {
          line: 231,
          column: 49
        }
      },
      "170": {
        start: {
          line: 229,
          column: 52
        },
        end: {
          line: 229,
          column: 116
        }
      },
      "171": {
        start: {
          line: 230,
          column: 52
        },
        end: {
          line: 230,
          column: 77
        }
      },
      "172": {
        start: {
          line: 232,
          column: 48
        },
        end: {
          line: 232,
          column: 61
        }
      },
      "173": {
        start: {
          line: 234,
          column: 48
        },
        end: {
          line: 234,
          column: 76
        }
      },
      "174": {
        start: {
          line: 235,
          column: 48
        },
        end: {
          line: 235,
          column: 106
        }
      },
      "175": {
        start: {
          line: 235,
          column: 82
        },
        end: {
          line: 235,
          column: 106
        }
      },
      "176": {
        start: {
          line: 236,
          column: 48
        },
        end: {
          line: 236,
          column: 97
        }
      },
      "177": {
        start: {
          line: 236,
          column: 73
        },
        end: {
          line: 236,
          column: 97
        }
      },
      "178": {
        start: {
          line: 238,
          column: 48
        },
        end: {
          line: 243,
          column: 56
        }
      },
      "179": {
        start: {
          line: 246,
          column: 48
        },
        end: {
          line: 246,
          column: 58
        }
      },
      "180": {
        start: {
          line: 247,
          column: 48
        },
        end: {
          line: 253,
          column: 56
        }
      },
      "181": {
        start: {
          line: 255,
          column: 48
        },
        end: {
          line: 255,
          column: 58
        }
      },
      "182": {
        start: {
          line: 256,
          column: 48
        },
        end: {
          line: 256,
          column: 72
        }
      },
      "183": {
        start: {
          line: 257,
          column: 52
        },
        end: {
          line: 263,
          column: 52
        }
      },
      "184": {
        start: {
          line: 265,
          column: 48
        },
        end: {
          line: 265,
          column: 58
        }
      },
      "185": {
        start: {
          line: 266,
          column: 48
        },
        end: {
          line: 266,
          column: 61
        }
      },
      "186": {
        start: {
          line: 267,
          column: 52
        },
        end: {
          line: 267,
          column: 77
        }
      },
      "187": {
        start: {
          line: 269,
          column: 48
        },
        end: {
          line: 269,
          column: 109
        }
      },
      "188": {
        start: {
          line: 269,
          column: 84
        },
        end: {
          line: 269,
          column: 109
        }
      },
      "189": {
        start: {
          line: 270,
          column: 48
        },
        end: {
          line: 270,
          column: 98
        }
      },
      "190": {
        start: {
          line: 270,
          column: 73
        },
        end: {
          line: 270,
          column: 98
        }
      },
      "191": {
        start: {
          line: 272,
          column: 48
        },
        end: {
          line: 277,
          column: 56
        }
      },
      "192": {
        start: {
          line: 280,
          column: 48
        },
        end: {
          line: 280,
          column: 58
        }
      },
      "193": {
        start: {
          line: 281,
          column: 48
        },
        end: {
          line: 287,
          column: 56
        }
      },
      "194": {
        start: {
          line: 289,
          column: 48
        },
        end: {
          line: 289,
          column: 58
        }
      },
      "195": {
        start: {
          line: 290,
          column: 48
        },
        end: {
          line: 290,
          column: 73
        }
      },
      "196": {
        start: {
          line: 291,
          column: 53
        },
        end: {
          line: 297,
          column: 52
        }
      },
      "197": {
        start: {
          line: 299,
          column: 48
        },
        end: {
          line: 299,
          column: 58
        }
      },
      "198": {
        start: {
          line: 300,
          column: 48
        },
        end: {
          line: 300,
          column: 62
        }
      },
      "199": {
        start: {
          line: 302,
          column: 48
        },
        end: {
          line: 302,
          column: 95
        }
      },
      "200": {
        start: {
          line: 303,
          column: 48
        },
        end: {
          line: 303,
          column: 73
        }
      },
      "201": {
        start: {
          line: 305,
          column: 48
        },
        end: {
          line: 305,
          column: 68
        }
      },
      "202": {
        start: {
          line: 306,
          column: 48
        },
        end: {
          line: 306,
          column: 107
        }
      },
      "203": {
        start: {
          line: 307,
          column: 48
        },
        end: {
          line: 307,
          column: 107
        }
      },
      "204": {
        start: {
          line: 308,
          column: 48
        },
        end: {
          line: 308,
          column: 73
        }
      },
      "205": {
        start: {
          line: 310,
          column: 48
        },
        end: {
          line: 310,
          column: 53
        }
      },
      "206": {
        start: {
          line: 311,
          column: 48
        },
        end: {
          line: 311,
          column: 72
        }
      },
      "207": {
        start: {
          line: 312,
          column: 53
        },
        end: {
          line: 312,
          column: 75
        }
      },
      "208": {
        start: {
          line: 318,
          column: 28
        },
        end: {
          line: 318,
          column: 38
        }
      },
      "209": {
        start: {
          line: 319,
          column: 28
        },
        end: {
          line: 323,
          column: 36
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 2,
            column: 45
          }
        },
        loc: {
          start: {
            line: 2,
            column: 89
          },
          end: {
            line: 10,
            column: 1
          }
        },
        line: 2
      },
      "1": {
        name: "adopt",
        decl: {
          start: {
            line: 3,
            column: 13
          },
          end: {
            line: 3,
            column: 18
          }
        },
        loc: {
          start: {
            line: 3,
            column: 26
          },
          end: {
            line: 3,
            column: 112
          }
        },
        line: 3
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 3,
            column: 70
          },
          end: {
            line: 3,
            column: 71
          }
        },
        loc: {
          start: {
            line: 3,
            column: 89
          },
          end: {
            line: 3,
            column: 108
          }
        },
        line: 3
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 4,
            column: 36
          },
          end: {
            line: 4,
            column: 37
          }
        },
        loc: {
          start: {
            line: 4,
            column: 63
          },
          end: {
            line: 9,
            column: 5
          }
        },
        line: 4
      },
      "4": {
        name: "fulfilled",
        decl: {
          start: {
            line: 5,
            column: 17
          },
          end: {
            line: 5,
            column: 26
          }
        },
        loc: {
          start: {
            line: 5,
            column: 34
          },
          end: {
            line: 5,
            column: 99
          }
        },
        line: 5
      },
      "5": {
        name: "rejected",
        decl: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 25
          }
        },
        loc: {
          start: {
            line: 6,
            column: 33
          },
          end: {
            line: 6,
            column: 102
          }
        },
        line: 6
      },
      "6": {
        name: "step",
        decl: {
          start: {
            line: 7,
            column: 17
          },
          end: {
            line: 7,
            column: 21
          }
        },
        loc: {
          start: {
            line: 7,
            column: 30
          },
          end: {
            line: 7,
            column: 118
          }
        },
        line: 7
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 11,
            column: 49
          }
        },
        loc: {
          start: {
            line: 11,
            column: 73
          },
          end: {
            line: 37,
            column: 1
          }
        },
        line: 11
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 12,
            column: 30
          },
          end: {
            line: 12,
            column: 31
          }
        },
        loc: {
          start: {
            line: 12,
            column: 41
          },
          end: {
            line: 12,
            column: 83
          }
        },
        line: 12
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 13,
            column: 128
          },
          end: {
            line: 13,
            column: 129
          }
        },
        loc: {
          start: {
            line: 13,
            column: 139
          },
          end: {
            line: 13,
            column: 155
          }
        },
        line: 13
      },
      "10": {
        name: "verb",
        decl: {
          start: {
            line: 14,
            column: 13
          },
          end: {
            line: 14,
            column: 17
          }
        },
        loc: {
          start: {
            line: 14,
            column: 21
          },
          end: {
            line: 14,
            column: 70
          }
        },
        line: 14
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 14,
            column: 30
          },
          end: {
            line: 14,
            column: 31
          }
        },
        loc: {
          start: {
            line: 14,
            column: 43
          },
          end: {
            line: 14,
            column: 67
          }
        },
        line: 14
      },
      "12": {
        name: "step",
        decl: {
          start: {
            line: 15,
            column: 13
          },
          end: {
            line: 15,
            column: 17
          }
        },
        loc: {
          start: {
            line: 15,
            column: 22
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 15
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 47,
            column: 72
          },
          end: {
            line: 47,
            column: 73
          }
        },
        loc: {
          start: {
            line: 47,
            column: 91
          },
          end: {
            line: 172,
            column: 5
          }
        },
        line: 47
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 47,
            column: 134
          },
          end: {
            line: 47,
            column: 135
          }
        },
        loc: {
          start: {
            line: 47,
            column: 146
          },
          end: {
            line: 172,
            column: 1
          }
        },
        line: 47
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 48,
            column: 29
          },
          end: {
            line: 48,
            column: 30
          }
        },
        loc: {
          start: {
            line: 48,
            column: 43
          },
          end: {
            line: 171,
            column: 5
          }
        },
        line: 48
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 49,
            column: 118
          },
          end: {
            line: 49,
            column: 119
          }
        },
        loc: {
          start: {
            line: 49,
            column: 130
          },
          end: {
            line: 170,
            column: 17
          }
        },
        line: 49
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 49,
            column: 173
          },
          end: {
            line: 49,
            column: 174
          }
        },
        loc: {
          start: {
            line: 49,
            column: 185
          },
          end: {
            line: 170,
            column: 13
          }
        },
        line: 49
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 52,
            column: 41
          },
          end: {
            line: 52,
            column: 42
          }
        },
        loc: {
          start: {
            line: 52,
            column: 55
          },
          end: {
            line: 169,
            column: 17
          }
        },
        line: 52
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 95,
            column: 70
          },
          end: {
            line: 95,
            column: 71
          }
        },
        loc: {
          start: {
            line: 95,
            column: 95
          },
          end: {
            line: 101,
            column: 29
          }
        },
        line: 95
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 106,
            column: 58
          },
          end: {
            line: 106,
            column: 59
          }
        },
        loc: {
          start: {
            line: 106,
            column: 83
          },
          end: {
            line: 109,
            column: 33
          }
        },
        line: 106
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 111,
            column: 59
          },
          end: {
            line: 111,
            column: 60
          }
        },
        loc: {
          start: {
            line: 111,
            column: 72
          },
          end: {
            line: 111,
            column: 103
          }
        },
        line: 111
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 114,
            column: 61
          },
          end: {
            line: 114,
            column: 62
          }
        },
        loc: {
          start: {
            line: 114,
            column: 74
          },
          end: {
            line: 114,
            column: 122
          }
        },
        line: 114
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 138,
            column: 72
          },
          end: {
            line: 138,
            column: 73
          }
        },
        loc: {
          start: {
            line: 138,
            column: 97
          },
          end: {
            line: 144,
            column: 29
          }
        },
        line: 138
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 149,
            column: 58
          },
          end: {
            line: 149,
            column: 59
          }
        },
        loc: {
          start: {
            line: 149,
            column: 83
          },
          end: {
            line: 152,
            column: 33
          }
        },
        line: 149
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 154,
            column: 59
          },
          end: {
            line: 154,
            column: 60
          }
        },
        loc: {
          start: {
            line: 154,
            column: 72
          },
          end: {
            line: 154,
            column: 103
          }
        },
        line: 154
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 157,
            column: 61
          },
          end: {
            line: 157,
            column: 62
          }
        },
        loc: {
          start: {
            line: 157,
            column: 74
          },
          end: {
            line: 157,
            column: 122
          }
        },
        line: 157
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 174,
            column: 73
          },
          end: {
            line: 174,
            column: 74
          }
        },
        loc: {
          start: {
            line: 174,
            column: 92
          },
          end: {
            line: 328,
            column: 5
          }
        },
        line: 174
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 174,
            column: 135
          },
          end: {
            line: 174,
            column: 136
          }
        },
        loc: {
          start: {
            line: 174,
            column: 147
          },
          end: {
            line: 328,
            column: 1
          }
        },
        line: 174
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 175,
            column: 29
          },
          end: {
            line: 175,
            column: 30
          }
        },
        loc: {
          start: {
            line: 175,
            column: 43
          },
          end: {
            line: 327,
            column: 5
          }
        },
        line: 175
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 176,
            column: 117
          },
          end: {
            line: 176,
            column: 118
          }
        },
        loc: {
          start: {
            line: 176,
            column: 129
          },
          end: {
            line: 326,
            column: 17
          }
        },
        line: 176
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 176,
            column: 172
          },
          end: {
            line: 176,
            column: 173
          }
        },
        loc: {
          start: {
            line: 176,
            column: 184
          },
          end: {
            line: 326,
            column: 13
          }
        },
        line: 176
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 179,
            column: 41
          },
          end: {
            line: 179,
            column: 42
          }
        },
        loc: {
          start: {
            line: 179,
            column: 55
          },
          end: {
            line: 325,
            column: 17
          }
        },
        line: 179
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 209,
            column: 78
          },
          end: {
            line: 209,
            column: 79
          }
        },
        loc: {
          start: {
            line: 209,
            column: 92
          },
          end: {
            line: 315,
            column: 37
          }
        },
        line: 209
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 209,
            column: 135
          },
          end: {
            line: 209,
            column: 136
          }
        },
        loc: {
          start: {
            line: 209,
            column: 147
          },
          end: {
            line: 315,
            column: 33
          }
        },
        line: 209
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 211,
            column: 61
          },
          end: {
            line: 211,
            column: 62
          }
        },
        loc: {
          start: {
            line: 211,
            column: 75
          },
          end: {
            line: 314,
            column: 37
          }
        },
        line: 211
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 2,
            column: 16
          },
          end: {
            line: 10,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 2,
            column: 17
          },
          end: {
            line: 2,
            column: 21
          }
        }, {
          start: {
            line: 2,
            column: 25
          },
          end: {
            line: 2,
            column: 39
          }
        }, {
          start: {
            line: 2,
            column: 44
          },
          end: {
            line: 10,
            column: 1
          }
        }],
        line: 2
      },
      "1": {
        loc: {
          start: {
            line: 3,
            column: 35
          },
          end: {
            line: 3,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 3,
            column: 56
          },
          end: {
            line: 3,
            column: 61
          }
        }, {
          start: {
            line: 3,
            column: 64
          },
          end: {
            line: 3,
            column: 109
          }
        }],
        line: 3
      },
      "2": {
        loc: {
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 4,
            column: 16
          },
          end: {
            line: 4,
            column: 17
          }
        }, {
          start: {
            line: 4,
            column: 22
          },
          end: {
            line: 4,
            column: 33
          }
        }],
        line: 4
      },
      "3": {
        loc: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 115
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 7,
            column: 46
          },
          end: {
            line: 7,
            column: 67
          }
        }, {
          start: {
            line: 7,
            column: 70
          },
          end: {
            line: 7,
            column: 115
          }
        }],
        line: 7
      },
      "4": {
        loc: {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 61
          }
        }, {
          start: {
            line: 8,
            column: 65
          },
          end: {
            line: 8,
            column: 67
          }
        }],
        line: 8
      },
      "5": {
        loc: {
          start: {
            line: 11,
            column: 18
          },
          end: {
            line: 37,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 11,
            column: 19
          },
          end: {
            line: 11,
            column: 23
          }
        }, {
          start: {
            line: 11,
            column: 27
          },
          end: {
            line: 11,
            column: 43
          }
        }, {
          start: {
            line: 11,
            column: 48
          },
          end: {
            line: 37,
            column: 1
          }
        }],
        line: 11
      },
      "6": {
        loc: {
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 12,
            column: 43
          },
          end: {
            line: 12,
            column: 68
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 12
      },
      "7": {
        loc: {
          start: {
            line: 12,
            column: 134
          },
          end: {
            line: 12,
            column: 184
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 12,
            column: 167
          },
          end: {
            line: 12,
            column: 175
          }
        }, {
          start: {
            line: 12,
            column: 178
          },
          end: {
            line: 12,
            column: 184
          }
        }],
        line: 12
      },
      "8": {
        loc: {
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 156
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 13,
            column: 74
          },
          end: {
            line: 13,
            column: 102
          }
        }, {
          start: {
            line: 13,
            column: 107
          },
          end: {
            line: 13,
            column: 155
          }
        }],
        line: 13
      },
      "9": {
        loc: {
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 16,
            column: 8
          },
          end: {
            line: 16,
            column: 70
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 16
      },
      "10": {
        loc: {
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 15
          },
          end: {
            line: 17,
            column: 16
          }
        }, {
          start: {
            line: 17,
            column: 21
          },
          end: {
            line: 17,
            column: 44
          }
        }],
        line: 17
      },
      "11": {
        loc: {
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 17,
            column: 28
          },
          end: {
            line: 17,
            column: 33
          }
        }, {
          start: {
            line: 17,
            column: 38
          },
          end: {
            line: 17,
            column: 43
          }
        }],
        line: 17
      },
      "12": {
        loc: {
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 18,
            column: 12
          },
          end: {
            line: 18,
            column: 169
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 18
      },
      "13": {
        loc: {
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 158
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 23
          },
          end: {
            line: 18,
            column: 24
          }
        }, {
          start: {
            line: 18,
            column: 29
          },
          end: {
            line: 18,
            column: 125
          }
        }, {
          start: {
            line: 18,
            column: 130
          },
          end: {
            line: 18,
            column: 158
          }
        }],
        line: 18
      },
      "14": {
        loc: {
          start: {
            line: 18,
            column: 33
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 45
          },
          end: {
            line: 18,
            column: 56
          }
        }, {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "15": {
        loc: {
          start: {
            line: 18,
            column: 59
          },
          end: {
            line: 18,
            column: 125
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        }, {
          start: {
            line: 18,
            column: 119
          },
          end: {
            line: 18,
            column: 125
          }
        }],
        line: 18
      },
      "16": {
        loc: {
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 67
          },
          end: {
            line: 18,
            column: 77
          }
        }, {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 115
          }
        }],
        line: 18
      },
      "17": {
        loc: {
          start: {
            line: 18,
            column: 82
          },
          end: {
            line: 18,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 18,
            column: 83
          },
          end: {
            line: 18,
            column: 98
          }
        }, {
          start: {
            line: 18,
            column: 103
          },
          end: {
            line: 18,
            column: 112
          }
        }],
        line: 18
      },
      "18": {
        loc: {
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 19,
            column: 12
          },
          end: {
            line: 19,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 19
      },
      "19": {
        loc: {
          start: {
            line: 20,
            column: 12
          },
          end: {
            line: 32,
            column: 13
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 21,
            column: 16
          },
          end: {
            line: 21,
            column: 23
          }
        }, {
          start: {
            line: 21,
            column: 24
          },
          end: {
            line: 21,
            column: 46
          }
        }, {
          start: {
            line: 22,
            column: 16
          },
          end: {
            line: 22,
            column: 72
          }
        }, {
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 65
          }
        }, {
          start: {
            line: 24,
            column: 16
          },
          end: {
            line: 24,
            column: 65
          }
        }, {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 31,
            column: 43
          }
        }],
        line: 20
      },
      "20": {
        loc: {
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 20
          },
          end: {
            line: 26,
            column: 128
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "21": {
        loc: {
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 24
          },
          end: {
            line: 26,
            column: 74
          }
        }, {
          start: {
            line: 26,
            column: 79
          },
          end: {
            line: 26,
            column: 90
          }
        }, {
          start: {
            line: 26,
            column: 94
          },
          end: {
            line: 26,
            column: 105
          }
        }],
        line: 26
      },
      "22": {
        loc: {
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 42
          },
          end: {
            line: 26,
            column: 54
          }
        }, {
          start: {
            line: 26,
            column: 58
          },
          end: {
            line: 26,
            column: 73
          }
        }],
        line: 26
      },
      "23": {
        loc: {
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 27,
            column: 20
          },
          end: {
            line: 27,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 27
      },
      "24": {
        loc: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 35
          }
        }, {
          start: {
            line: 27,
            column: 40
          },
          end: {
            line: 27,
            column: 42
          }
        }, {
          start: {
            line: 27,
            column: 47
          },
          end: {
            line: 27,
            column: 59
          }
        }, {
          start: {
            line: 27,
            column: 63
          },
          end: {
            line: 27,
            column: 75
          }
        }],
        line: 27
      },
      "25": {
        loc: {
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 28,
            column: 20
          },
          end: {
            line: 28,
            column: 89
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 28
      },
      "26": {
        loc: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 35
          }
        }, {
          start: {
            line: 28,
            column: 39
          },
          end: {
            line: 28,
            column: 53
          }
        }],
        line: 28
      },
      "27": {
        loc: {
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 20
          },
          end: {
            line: 29,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "28": {
        loc: {
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 24
          },
          end: {
            line: 29,
            column: 25
          }
        }, {
          start: {
            line: 29,
            column: 29
          },
          end: {
            line: 29,
            column: 43
          }
        }],
        line: 29
      },
      "29": {
        loc: {
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 20
          },
          end: {
            line: 30,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 30
      },
      "30": {
        loc: {
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 8
          },
          end: {
            line: 35,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "31": {
        loc: {
          start: {
            line: 35,
            column: 52
          },
          end: {
            line: 35,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 35,
            column: 60
          },
          end: {
            line: 35,
            column: 65
          }
        }, {
          start: {
            line: 35,
            column: 68
          },
          end: {
            line: 35,
            column: 74
          }
        }],
        line: 35
      },
      "32": {
        loc: {
          start: {
            line: 53,
            column: 20
          },
          end: {
            line: 168,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 54,
            column: 24
          },
          end: {
            line: 58,
            column: 104
          }
        }, {
          start: {
            line: 59,
            column: 24
          },
          end: {
            line: 92,
            column: 36
          }
        }, {
          start: {
            line: 93,
            column: 24
          },
          end: {
            line: 120,
            column: 41
          }
        }, {
          start: {
            line: 121,
            column: 24
          },
          end: {
            line: 135,
            column: 36
          }
        }, {
          start: {
            line: 136,
            column: 24
          },
          end: {
            line: 163,
            column: 41
          }
        }, {
          start: {
            line: 164,
            column: 24
          },
          end: {
            line: 167,
            column: 32
          }
        }],
        line: 53
      },
      "33": {
        loc: {
          start: {
            line: 56,
            column: 38
          },
          end: {
            line: 56,
            column: 147
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 56,
            column: 39
          },
          end: {
            line: 56,
            column: 140
          }
        }, {
          start: {
            line: 56,
            column: 145
          },
          end: {
            line: 56,
            column: 147
          }
        }],
        line: 56
      },
      "34": {
        loc: {
          start: {
            line: 56,
            column: 39
          },
          end: {
            line: 56,
            column: 140
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 56,
            column: 102
          },
          end: {
            line: 56,
            column: 108
          }
        }, {
          start: {
            line: 56,
            column: 111
          },
          end: {
            line: 56,
            column: 140
          }
        }],
        line: 56
      },
      "35": {
        loc: {
          start: {
            line: 56,
            column: 39
          },
          end: {
            line: 56,
            column: 99
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 56,
            column: 39
          },
          end: {
            line: 56,
            column: 82
          }
        }, {
          start: {
            line: 56,
            column: 86
          },
          end: {
            line: 56,
            column: 99
          }
        }],
        line: 56
      },
      "36": {
        loc: {
          start: {
            line: 57,
            column: 39
          },
          end: {
            line: 57,
            column: 149
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 57,
            column: 40
          },
          end: {
            line: 57,
            column: 142
          }
        }, {
          start: {
            line: 57,
            column: 147
          },
          end: {
            line: 57,
            column: 149
          }
        }],
        line: 57
      },
      "37": {
        loc: {
          start: {
            line: 57,
            column: 40
          },
          end: {
            line: 57,
            column: 142
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 57,
            column: 104
          },
          end: {
            line: 57,
            column: 110
          }
        }, {
          start: {
            line: 57,
            column: 113
          },
          end: {
            line: 57,
            column: 142
          }
        }],
        line: 57
      },
      "38": {
        loc: {
          start: {
            line: 57,
            column: 40
          },
          end: {
            line: 57,
            column: 101
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 57,
            column: 40
          },
          end: {
            line: 57,
            column: 84
          }
        }, {
          start: {
            line: 57,
            column: 88
          },
          end: {
            line: 57,
            column: 101
          }
        }],
        line: 57
      },
      "39": {
        loc: {
          start: {
            line: 61,
            column: 37
          },
          end: {
            line: 61,
            column: 149
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 61,
            column: 135
          },
          end: {
            line: 61,
            column: 141
          }
        }, {
          start: {
            line: 61,
            column: 144
          },
          end: {
            line: 61,
            column: 149
          }
        }],
        line: 61
      },
      "40": {
        loc: {
          start: {
            line: 61,
            column: 37
          },
          end: {
            line: 61,
            column: 132
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 61,
            column: 37
          },
          end: {
            line: 61,
            column: 115
          }
        }, {
          start: {
            line: 61,
            column: 119
          },
          end: {
            line: 61,
            column: 132
          }
        }],
        line: 61
      },
      "41": {
        loc: {
          start: {
            line: 61,
            column: 43
          },
          end: {
            line: 61,
            column: 105
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 61,
            column: 84
          },
          end: {
            line: 61,
            column: 90
          }
        }, {
          start: {
            line: 61,
            column: 93
          },
          end: {
            line: 61,
            column: 105
          }
        }],
        line: 61
      },
      "42": {
        loc: {
          start: {
            line: 61,
            column: 43
          },
          end: {
            line: 61,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 61,
            column: 43
          },
          end: {
            line: 61,
            column: 59
          }
        }, {
          start: {
            line: 61,
            column: 63
          },
          end: {
            line: 61,
            column: 81
          }
        }],
        line: 61
      },
      "43": {
        loc: {
          start: {
            line: 62,
            column: 28
          },
          end: {
            line: 67,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 62,
            column: 28
          },
          end: {
            line: 67,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 62
      },
      "44": {
        loc: {
          start: {
            line: 62,
            column: 32
          },
          end: {
            line: 62,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 62,
            column: 32
          },
          end: {
            line: 62,
            column: 52
          }
        }, {
          start: {
            line: 62,
            column: 56
          },
          end: {
            line: 62,
            column: 77
          }
        }],
        line: 62
      },
      "45": {
        loc: {
          start: {
            line: 69,
            column: 28
          },
          end: {
            line: 74,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 69,
            column: 28
          },
          end: {
            line: 74,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 69
      },
      "46": {
        loc: {
          start: {
            line: 69,
            column: 32
          },
          end: {
            line: 69,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 69,
            column: 32
          },
          end: {
            line: 69,
            column: 51
          }
        }, {
          start: {
            line: 69,
            column: 55
          },
          end: {
            line: 69,
            column: 75
          }
        }],
        line: 69
      },
      "47": {
        loc: {
          start: {
            line: 79,
            column: 28
          },
          end: {
            line: 79,
            column: 79
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 79,
            column: 28
          },
          end: {
            line: 79,
            column: 79
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 79
      },
      "48": {
        loc: {
          start: {
            line: 96,
            column: 32
          },
          end: {
            line: 98,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 96,
            column: 32
          },
          end: {
            line: 98,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 96
      },
      "49": {
        loc: {
          start: {
            line: 105,
            column: 44
          },
          end: {
            line: 105,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 105,
            column: 44
          },
          end: {
            line: 105,
            column: 70
          }
        }, {
          start: {
            line: 105,
            column: 74
          },
          end: {
            line: 105,
            column: 76
          }
        }],
        line: 105
      },
      "50": {
        loc: {
          start: {
            line: 107,
            column: 58
          },
          end: {
            line: 107,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 107,
            column: 58
          },
          end: {
            line: 107,
            column: 76
          }
        }, {
          start: {
            line: 107,
            column: 80
          },
          end: {
            line: 107,
            column: 81
          }
        }],
        line: 107
      },
      "51": {
        loc: {
          start: {
            line: 110,
            column: 47
          },
          end: {
            line: 112,
            column: 47
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 111,
            column: 38
          },
          end: {
            line: 111,
            column: 150
          }
        }, {
          start: {
            line: 112,
            column: 38
          },
          end: {
            line: 112,
            column: 47
          }
        }],
        line: 110
      },
      "52": {
        loc: {
          start: {
            line: 111,
            column: 38
          },
          end: {
            line: 111,
            column: 150
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 111,
            column: 134
          },
          end: {
            line: 111,
            column: 140
          }
        }, {
          start: {
            line: 111,
            column: 143
          },
          end: {
            line: 111,
            column: 150
          }
        }],
        line: 111
      },
      "53": {
        loc: {
          start: {
            line: 111,
            column: 38
          },
          end: {
            line: 111,
            column: 131
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 111,
            column: 38
          },
          end: {
            line: 111,
            column: 114
          }
        }, {
          start: {
            line: 111,
            column: 118
          },
          end: {
            line: 111,
            column: 131
          }
        }],
        line: 111
      },
      "54": {
        loc: {
          start: {
            line: 122,
            column: 28
          },
          end: {
            line: 122,
            column: 80
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 122,
            column: 28
          },
          end: {
            line: 122,
            column: 80
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 122
      },
      "55": {
        loc: {
          start: {
            line: 139,
            column: 32
          },
          end: {
            line: 141,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 139,
            column: 32
          },
          end: {
            line: 141,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 139
      },
      "56": {
        loc: {
          start: {
            line: 148,
            column: 44
          },
          end: {
            line: 148,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 148,
            column: 44
          },
          end: {
            line: 148,
            column: 72
          }
        }, {
          start: {
            line: 148,
            column: 76
          },
          end: {
            line: 148,
            column: 78
          }
        }],
        line: 148
      },
      "57": {
        loc: {
          start: {
            line: 150,
            column: 58
          },
          end: {
            line: 150,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 150,
            column: 58
          },
          end: {
            line: 150,
            column: 76
          }
        }, {
          start: {
            line: 150,
            column: 80
          },
          end: {
            line: 150,
            column: 81
          }
        }],
        line: 150
      },
      "58": {
        loc: {
          start: {
            line: 153,
            column: 47
          },
          end: {
            line: 155,
            column: 47
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 154,
            column: 38
          },
          end: {
            line: 154,
            column: 150
          }
        }, {
          start: {
            line: 155,
            column: 38
          },
          end: {
            line: 155,
            column: 47
          }
        }],
        line: 153
      },
      "59": {
        loc: {
          start: {
            line: 154,
            column: 38
          },
          end: {
            line: 154,
            column: 150
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 154,
            column: 134
          },
          end: {
            line: 154,
            column: 140
          }
        }, {
          start: {
            line: 154,
            column: 143
          },
          end: {
            line: 154,
            column: 150
          }
        }],
        line: 154
      },
      "60": {
        loc: {
          start: {
            line: 154,
            column: 38
          },
          end: {
            line: 154,
            column: 131
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 154,
            column: 38
          },
          end: {
            line: 154,
            column: 114
          }
        }, {
          start: {
            line: 154,
            column: 118
          },
          end: {
            line: 154,
            column: 131
          }
        }],
        line: 154
      },
      "61": {
        loc: {
          start: {
            line: 180,
            column: 20
          },
          end: {
            line: 324,
            column: 21
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 181,
            column: 24
          },
          end: {
            line: 181,
            column: 108
          }
        }, {
          start: {
            line: 182,
            column: 24
          },
          end: {
            line: 190,
            column: 65
          }
        }, {
          start: {
            line: 191,
            column: 24
          },
          end: {
            line: 315,
            column: 40
          }
        }, {
          start: {
            line: 316,
            column: 24
          },
          end: {
            line: 323,
            column: 36
          }
        }],
        line: 180
      },
      "62": {
        loc: {
          start: {
            line: 184,
            column: 28
          },
          end: {
            line: 189,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 184,
            column: 28
          },
          end: {
            line: 189,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 184
      },
      "63": {
        loc: {
          start: {
            line: 184,
            column: 34
          },
          end: {
            line: 184,
            column: 146
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 184,
            column: 132
          },
          end: {
            line: 184,
            column: 138
          }
        }, {
          start: {
            line: 184,
            column: 141
          },
          end: {
            line: 184,
            column: 146
          }
        }],
        line: 184
      },
      "64": {
        loc: {
          start: {
            line: 184,
            column: 34
          },
          end: {
            line: 184,
            column: 129
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 184,
            column: 34
          },
          end: {
            line: 184,
            column: 112
          }
        }, {
          start: {
            line: 184,
            column: 116
          },
          end: {
            line: 184,
            column: 129
          }
        }],
        line: 184
      },
      "65": {
        loc: {
          start: {
            line: 184,
            column: 40
          },
          end: {
            line: 184,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 184,
            column: 81
          },
          end: {
            line: 184,
            column: 87
          }
        }, {
          start: {
            line: 184,
            column: 90
          },
          end: {
            line: 184,
            column: 102
          }
        }],
        line: 184
      },
      "66": {
        loc: {
          start: {
            line: 184,
            column: 40
          },
          end: {
            line: 184,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 184,
            column: 40
          },
          end: {
            line: 184,
            column: 56
          }
        }, {
          start: {
            line: 184,
            column: 60
          },
          end: {
            line: 184,
            column: 78
          }
        }],
        line: 184
      },
      "67": {
        loc: {
          start: {
            line: 194,
            column: 28
          },
          end: {
            line: 199,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 194,
            column: 28
          },
          end: {
            line: 199,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 194
      },
      "68": {
        loc: {
          start: {
            line: 194,
            column: 32
          },
          end: {
            line: 194,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 194,
            column: 32
          },
          end: {
            line: 194,
            column: 58
          }
        }, {
          start: {
            line: 194,
            column: 62
          },
          end: {
            line: 194,
            column: 85
          }
        }],
        line: 194
      },
      "69": {
        loc: {
          start: {
            line: 201,
            column: 28
          },
          end: {
            line: 206,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 201,
            column: 28
          },
          end: {
            line: 206,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 201
      },
      "70": {
        loc: {
          start: {
            line: 212,
            column: 40
          },
          end: {
            line: 313,
            column: 41
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 213,
            column: 44
          },
          end: {
            line: 215,
            column: 61
          }
        }, {
          start: {
            line: 216,
            column: 44
          },
          end: {
            line: 232,
            column: 61
          }
        }, {
          start: {
            line: 233,
            column: 44
          },
          end: {
            line: 243,
            column: 56
          }
        }, {
          start: {
            line: 244,
            column: 44
          },
          end: {
            line: 253,
            column: 56
          }
        }, {
          start: {
            line: 254,
            column: 44
          },
          end: {
            line: 256,
            column: 72
          }
        }, {
          start: {
            line: 257,
            column: 44
          },
          end: {
            line: 263,
            column: 52
          }
        }, {
          start: {
            line: 264,
            column: 44
          },
          end: {
            line: 266,
            column: 61
          }
        }, {
          start: {
            line: 267,
            column: 44
          },
          end: {
            line: 267,
            column: 77
          }
        }, {
          start: {
            line: 268,
            column: 44
          },
          end: {
            line: 277,
            column: 56
          }
        }, {
          start: {
            line: 278,
            column: 44
          },
          end: {
            line: 287,
            column: 56
          }
        }, {
          start: {
            line: 288,
            column: 44
          },
          end: {
            line: 290,
            column: 73
          }
        }, {
          start: {
            line: 291,
            column: 44
          },
          end: {
            line: 297,
            column: 52
          }
        }, {
          start: {
            line: 298,
            column: 44
          },
          end: {
            line: 300,
            column: 62
          }
        }, {
          start: {
            line: 301,
            column: 44
          },
          end: {
            line: 303,
            column: 73
          }
        }, {
          start: {
            line: 304,
            column: 44
          },
          end: {
            line: 308,
            column: 73
          }
        }, {
          start: {
            line: 309,
            column: 44
          },
          end: {
            line: 311,
            column: 72
          }
        }, {
          start: {
            line: 312,
            column: 44
          },
          end: {
            line: 312,
            column: 75
          }
        }],
        line: 212
      },
      "71": {
        loc: {
          start: {
            line: 217,
            column: 48
          },
          end: {
            line: 217,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 217,
            column: 48
          },
          end: {
            line: 217,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 217
      },
      "72": {
        loc: {
          start: {
            line: 220,
            column: 48
          },
          end: {
            line: 223,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 220,
            column: 48
          },
          end: {
            line: 223,
            column: 49
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 220
      },
      "73": {
        loc: {
          start: {
            line: 224,
            column: 48
          },
          end: {
            line: 227,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 224,
            column: 48
          },
          end: {
            line: 227,
            column: 49
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 224
      },
      "74": {
        loc: {
          start: {
            line: 228,
            column: 48
          },
          end: {
            line: 231,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 228,
            column: 48
          },
          end: {
            line: 231,
            column: 49
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 228
      },
      "75": {
        loc: {
          start: {
            line: 228,
            column: 52
          },
          end: {
            line: 228,
            column: 145
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 228,
            column: 52
          },
          end: {
            line: 228,
            column: 65
          }
        }, {
          start: {
            line: 228,
            column: 69
          },
          end: {
            line: 228,
            column: 145
          }
        }],
        line: 228
      },
      "76": {
        loc: {
          start: {
            line: 235,
            column: 48
          },
          end: {
            line: 235,
            column: 106
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 235,
            column: 48
          },
          end: {
            line: 235,
            column: 106
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 235
      },
      "77": {
        loc: {
          start: {
            line: 235,
            column: 54
          },
          end: {
            line: 235,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 235,
            column: 54
          },
          end: {
            line: 235,
            column: 69
          }
        }, {
          start: {
            line: 235,
            column: 73
          },
          end: {
            line: 235,
            column: 79
          }
        }],
        line: 235
      },
      "78": {
        loc: {
          start: {
            line: 236,
            column: 48
          },
          end: {
            line: 236,
            column: 97
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 236,
            column: 48
          },
          end: {
            line: 236,
            column: 97
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 236
      },
      "79": {
        loc: {
          start: {
            line: 269,
            column: 48
          },
          end: {
            line: 269,
            column: 109
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 269,
            column: 48
          },
          end: {
            line: 269,
            column: 109
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 269
      },
      "80": {
        loc: {
          start: {
            line: 269,
            column: 54
          },
          end: {
            line: 269,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 269,
            column: 54
          },
          end: {
            line: 269,
            column: 70
          }
        }, {
          start: {
            line: 269,
            column: 74
          },
          end: {
            line: 269,
            column: 81
          }
        }],
        line: 269
      },
      "81": {
        loc: {
          start: {
            line: 270,
            column: 48
          },
          end: {
            line: 270,
            column: 98
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 270,
            column: 48
          },
          end: {
            line: 270,
            column: 98
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 270
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0, 0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0, 0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0, 0, 0, 0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0, 0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0, 0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0, 0],
      "70": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0],
      "78": [0, 0],
      "79": [0, 0],
      "80": [0, 0],
      "81": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/reactions/batch/route.ts",
      mappings: ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,sCAAwD;AACxD,uCAA6C;AAC7C,mCAAyC;AACzC,uCAAsC;AACtC,6EAA2E;AAC3E,6CAAgD;AAwBhD,+EAA+E;AAClE,QAAA,GAAG,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;QACrE,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,EAC9C;;;;;;4BACU,YAAY,GAAK,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,aAAzB,CAA0B;4BACxC,OAAO,GAAG,CAAA,MAAA,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,0CAAE,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,OAAO,CAAC,KAAI,EAAE,CAAC;4BACxE,QAAQ,GAAG,CAAA,MAAA,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,0CAAE,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,OAAO,CAAC,KAAI,EAAE,CAAC;4BAEhE,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BAC7C,MAAM,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAC;4BAEjC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gCAClD,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,4CAA4C;qCACpD,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAC;4BACtB,CAAC;4BAED,oCAAoC;4BACpC,IAAI,OAAO,CAAC,MAAM,GAAG,EAAE,IAAI,QAAQ,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;gCAChD,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,8CAA8C;qCACtD,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAC;4BACtB,CAAC;4BAEK,MAAM,GAAsB;gCAChC,aAAa,EAAE,EAAE;gCACjB,cAAc,EAAE,EAAE;6BACnB,CAAC;iCAGE,CAAA,OAAO,CAAC,MAAM,GAAG,CAAC,CAAA,EAAlB,wBAAkB;4BACE,qBAAM,eAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;oCAC5D,KAAK,EAAE;wCACL,MAAM,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;qCACxB;oCACD,MAAM,EAAE;wCACN,MAAM,EAAE,IAAI;wCACZ,IAAI,EAAE,IAAI;wCACV,MAAM,EAAE,IAAI;qCACb;oCACD,OAAO,EAAE;wCACP,SAAS,EAAE,MAAM;qCAClB;iCACF,CAAC,EAAA;;4BAZI,aAAa,GAAG,SAYpB;4BAGI,kBAAkB,GAAG,aAAa,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,QAAQ;gCAC5D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oCAC1B,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;gCAC5B,CAAC;gCACD,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gCACpC,OAAO,GAAG,CAAC;4BACb,CAAC,EAAE,EAA0C,CAAC,CAAC;4BAE/C,gCAAgC;4BAChC,WAA4B,EAAP,mBAAO,EAAP,qBAAO,EAAP,IAAO,EAAE,CAAC;gCAApB,MAAM;gCACT,SAAS,GAAG,kBAAkB,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;gCAG7C,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,QAAQ;oCAC5C,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;oCACnD,OAAO,GAAG,CAAC;gCACb,CAAC,EAAE,EAA4B,CAAC,CAAC;gCAG3B,YAAY,GAAG,MAAM;oCACzB,CAAC,CAAC,MAAA,SAAS,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,KAAK,MAAM,EAAnB,CAAmB,CAAC,0CAAE,IAAI;oCAChD,CAAC,CAAC,SAAS,CAAC;gCAEd,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG;oCAC7B,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,EAApC,CAAoC,CAAC;oCACnE,MAAM,QAAA;oCACN,KAAK,EAAE,SAAS,CAAC,MAAM;oCACvB,YAAY,cAAA;iCACb,CAAC;4BACJ,CAAC;;;iCAIC,CAAA,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAA,EAAnB,wBAAmB;4BACE,qBAAM,eAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC;oCAC9D,KAAK,EAAE;wCACL,OAAO,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE;qCAC1B;oCACD,MAAM,EAAE;wCACN,OAAO,EAAE,IAAI;wCACb,IAAI,EAAE,IAAI;wCACV,MAAM,EAAE,IAAI;qCACb;oCACD,OAAO,EAAE;wCACP,SAAS,EAAE,MAAM;qCAClB;iCACF,CAAC,EAAA;;4BAZI,cAAc,GAAG,SAYrB;4BAGI,mBAAmB,GAAG,cAAc,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,QAAQ;gCAC9D,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;oCAC3B,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;gCAC7B,CAAC;gCACD,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gCACrC,OAAO,GAAG,CAAC;4BACb,CAAC,EAAE,EAA2C,CAAC,CAAC;4BAEhD,iCAAiC;4BACjC,WAA8B,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ,EAAE,CAAC;gCAAtB,OAAO;gCACV,SAAS,GAAG,mBAAmB,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;gCAG/C,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,UAAC,GAAG,EAAE,QAAQ;oCAC5C,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;oCACnD,OAAO,GAAG,CAAC;gCACb,CAAC,EAAE,EAA4B,CAAC,CAAC;gCAG3B,YAAY,GAAG,MAAM;oCACzB,CAAC,CAAC,MAAA,SAAS,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,MAAM,KAAK,MAAM,EAAnB,CAAmB,CAAC,0CAAE,IAAI;oCAChD,CAAC,CAAC,SAAS,CAAC;gCAEd,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,GAAG;oCAC/B,SAAS,EAAE,SAAS,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,EAApC,CAAoC,CAAC;oCACnE,MAAM,QAAA;oCACN,KAAK,EAAE,SAAS,CAAC,MAAM;oCACvB,YAAY,cAAA;iCACb,CAAC;4BACJ,CAAC;;gCAGH,sBAAO,qBAAY,CAAC,IAAI,CAAC;gCACvB,OAAO,EAAE,IAAI;gCACb,IAAI,EAAE,MAAM;6BACb,CAAC,EAAC;;;iBACJ,CACF,EAAC;;KACH,CAAC,CAAC;AAEH,kEAAkE;AACrD,QAAA,IAAI,GAAG,IAAA,oDAAwB,EAAC,UAAO,OAAoB;;QACtE,sBAAO,IAAA,yBAAa,EAClB,OAAO,EACP,EAAE,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,EAC7C;;;;;gCACkB,qBAAM,IAAA,4BAAgB,EAAC,kBAAW,CAAC,EAAA;;4BAA7C,OAAO,GAAG,SAAmC;4BAEnD,IAAI,CAAC,CAAA,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,0CAAE,EAAE,CAAA,EAAE,CAAC;gCACvB,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,yBAAyB;qCACjC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAC;4BACtB,CAAC;4BAEY,qBAAM,OAAO,CAAC,IAAI,EAAE,EAAA;;4BAA3B,IAAI,GAAG,SAAoB;4BACzB,UAAU,GAAK,IAAI,WAAT,CAAU;4BAE5B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gCAC1D,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,8BAA8B;qCACtC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAC;4BACtB,CAAC;4BAED,mBAAmB;4BACnB,IAAI,UAAU,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;gCAC3B,sBAAO,qBAAY,CAAC,IAAI,CAAC;wCACvB,OAAO,EAAE,KAAK;wCACd,KAAK,EAAE,8BAA8B;qCACtC,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,EAAC;4BACtB,CAAC;4BAEK,OAAO,GAAU,EAAE,CAAC;4BAE1B,oDAAoD;4BACpD,qBAAM,eAAM,CAAC,YAAY,CAAC,UAAO,EAAE;;;;;sDACN,EAAV,yBAAU;;;qDAAV,CAAA,wBAAU,CAAA;gDAAhB,EAAE;gDACH,MAAM,GAA0C,EAAE,OAA5C,EAAE,IAAI,GAAoC,EAAE,KAAtC,EAAE,MAAM,GAA4B,EAAE,OAA9B,EAAE,OAAO,GAAmB,EAAE,QAArB,EAAE,YAAY,GAAK,EAAE,aAAP,CAAQ;gDAE3D,IAAI,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;oDACxC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,gBAAgB,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;oDACzD,yBAAS;gDACX,CAAC;gDAED,IAAI,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oDACtC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;oDACvD,yBAAS;gDACX,CAAC;gDAED,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;oDAClG,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;oDAChE,yBAAS;gDACX,CAAC;;;;qDAGK,CAAA,IAAI,KAAK,MAAM,IAAI,MAAM,CAAA,EAAzB,wBAAyB;qDACvB,CAAA,MAAM,KAAK,KAAK,CAAA,EAAhB,wBAAgB;gDAClB,mDAAmD;gDACnD,qBAAM,EAAE,CAAC,iBAAiB,CAAC,UAAU,CAAC;wDACpC,KAAK,EAAE;4DACL,MAAM,QAAA;4DACN,MAAM,EAAE,OAAO,CAAC,IAAK,CAAC,EAAG;yDAC1B;qDACF,CAAC,EAAA;;gDANF,mDAAmD;gDACnD,SAKE,CAAC;gDAEH,qBAAM,EAAE,CAAC,iBAAiB,CAAC,MAAM,CAAC;wDAChC,IAAI,EAAE;4DACJ,MAAM,QAAA;4DACN,MAAM,EAAE,OAAO,CAAC,IAAK,CAAC,EAAG;4DACzB,IAAI,EAAE,YAAY;yDACnB;qDACF,CAAC,EAAA;;gDANF,SAME,CAAC;;oDAEH,qBAAM,EAAE,CAAC,iBAAiB,CAAC,UAAU,CAAC;oDACpC,KAAK,EAAE;wDACL,MAAM,QAAA;wDACN,MAAM,EAAE,OAAO,CAAC,IAAK,CAAC,EAAG;wDACzB,IAAI,EAAE,YAAY;qDACnB;iDACF,CAAC,EAAA;;gDANF,SAME,CAAC;;;;qDAEI,CAAA,IAAI,KAAK,OAAO,IAAI,OAAO,CAAA,EAA3B,yBAA2B;qDAChC,CAAA,MAAM,KAAK,KAAK,CAAA,EAAhB,yBAAgB;gDAClB,mDAAmD;gDACnD,qBAAM,EAAE,CAAC,kBAAkB,CAAC,UAAU,CAAC;wDACrC,KAAK,EAAE;4DACL,OAAO,SAAA;4DACP,MAAM,EAAE,OAAO,CAAC,IAAK,CAAC,EAAG;yDAC1B;qDACF,CAAC,EAAA;;gDANF,mDAAmD;gDACnD,SAKE,CAAC;gDAEH,qBAAM,EAAE,CAAC,kBAAkB,CAAC,MAAM,CAAC;wDACjC,IAAI,EAAE;4DACJ,OAAO,SAAA;4DACP,MAAM,EAAE,OAAO,CAAC,IAAK,CAAC,EAAG;4DACzB,IAAI,EAAE,YAAY;yDACnB;qDACF,CAAC,EAAA;;gDANF,SAME,CAAC;;qDAEH,qBAAM,EAAE,CAAC,kBAAkB,CAAC,UAAU,CAAC;oDACrC,KAAK,EAAE;wDACL,OAAO,SAAA;wDACP,MAAM,EAAE,OAAO,CAAC,IAAK,CAAC,EAAG;wDACzB,IAAI,EAAE,YAAY;qDACnB;iDACF,CAAC,EAAA;;gDANF,SAME,CAAC;;;gDAIP,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;;;;gDAE/C,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,OAAK,CAAC,CAAC;gDACzD,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;;;gDA5E9C,IAAU,CAAA;;;;;qCA+E5B,CAAC,EAAA;;4BAjFF,oDAAoD;4BACpD,SAgFE,CAAC;4BAEH,sBAAO,qBAAY,CAAC,IAAI,CAAC;oCACvB,OAAO,EAAE,IAAI;oCACb,IAAI,EAAE,EAAE,OAAO,SAAA,EAAE;oCACjB,OAAO,EAAE,4BAA4B;iCACtC,CAAC,EAAC;;;iBACJ,CACF,EAAC;;KACH,CAAC,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/app/api/forum/reactions/batch/route.ts"],
      sourcesContent: ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport { prisma } from '@/lib/prisma';\nimport { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';\nimport { withRateLimit } from '@/lib/rateLimit';\n\ninterface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\ninterface BatchReactionData {\n  postReactions: Record<string, {\n    reactions: Array<{ type: string; userId: string }>;\n    counts: Record<string, number>;\n    total: number;\n    userReaction?: string;\n  }>;\n  replyReactions: Record<string, {\n    reactions: Array<{ type: string; userId: string }>;\n    counts: Record<string, number>;\n    total: number;\n    userReaction?: string;\n  }>;\n}\n\n// GET - Batch load reactions for multiple posts/replies (prevents N+1 queries)\nexport const GET = withUnifiedErrorHandling(async (request: NextRequest) => {\n  return withRateLimit(\n    request,\n    { windowMs: 15 * 60 * 1000, maxRequests: 100 },\n    async () => {\n      const { searchParams } = new URL(request.url);\n      const postIds = searchParams.get('postIds')?.split(',').filter(Boolean) || [];\n      const replyIds = searchParams.get('replyIds')?.split(',').filter(Boolean) || [];\n      \n      const session = await getServerSession(authOptions);\n      const userId = session?.user?.id;\n\n      if (postIds.length === 0 && replyIds.length === 0) {\n        return NextResponse.json({\n          success: false,\n          error: 'At least one postId or replyId is required'\n        }, { status: 400 });\n      }\n\n      // Limit batch size to prevent abuse\n      if (postIds.length > 50 || replyIds.length > 50) {\n        return NextResponse.json({\n          success: false,\n          error: 'Batch size too large (max 50 items per type)'\n        }, { status: 400 });\n      }\n\n      const result: BatchReactionData = {\n        postReactions: {},\n        replyReactions: {}\n      };\n\n      // Batch load post reactions\n      if (postIds.length > 0) {\n        const postReactions = await prisma.forumPostReaction.findMany({\n          where: { \n            postId: { in: postIds }\n          },\n          select: {\n            postId: true,\n            type: true,\n            userId: true,\n          },\n          orderBy: {\n            createdAt: 'desc',\n          },\n        });\n\n        // Group reactions by post\n        const postReactionGroups = postReactions.reduce((acc, reaction) => {\n          if (!acc[reaction.postId]) {\n            acc[reaction.postId] = [];\n          }\n          acc[reaction.postId].push(reaction);\n          return acc;\n        }, {} as Record<string, typeof postReactions>);\n\n        // Process each post's reactions\n        for (const postId of postIds) {\n          const reactions = postReactionGroups[postId] || [];\n          \n          // Count reactions by type\n          const counts = reactions.reduce((acc, reaction) => {\n            acc[reaction.type] = (acc[reaction.type] || 0) + 1;\n            return acc;\n          }, {} as Record<string, number>);\n\n          // Find user's reaction if authenticated\n          const userReaction = userId \n            ? reactions.find(r => r.userId === userId)?.type\n            : undefined;\n\n          result.postReactions[postId] = {\n            reactions: reactions.map(r => ({ type: r.type, userId: r.userId })),\n            counts,\n            total: reactions.length,\n            userReaction,\n          };\n        }\n      }\n\n      // Batch load reply reactions\n      if (replyIds.length > 0) {\n        const replyReactions = await prisma.forumReplyReaction.findMany({\n          where: { \n            replyId: { in: replyIds }\n          },\n          select: {\n            replyId: true,\n            type: true,\n            userId: true,\n          },\n          orderBy: {\n            createdAt: 'desc',\n          },\n        });\n\n        // Group reactions by reply\n        const replyReactionGroups = replyReactions.reduce((acc, reaction) => {\n          if (!acc[reaction.replyId]) {\n            acc[reaction.replyId] = [];\n          }\n          acc[reaction.replyId].push(reaction);\n          return acc;\n        }, {} as Record<string, typeof replyReactions>);\n\n        // Process each reply's reactions\n        for (const replyId of replyIds) {\n          const reactions = replyReactionGroups[replyId] || [];\n          \n          // Count reactions by type\n          const counts = reactions.reduce((acc, reaction) => {\n            acc[reaction.type] = (acc[reaction.type] || 0) + 1;\n            return acc;\n          }, {} as Record<string, number>);\n\n          // Find user's reaction if authenticated\n          const userReaction = userId \n            ? reactions.find(r => r.userId === userId)?.type\n            : undefined;\n\n          result.replyReactions[replyId] = {\n            reactions: reactions.map(r => ({ type: r.type, userId: r.userId })),\n            counts,\n            total: reactions.length,\n            userReaction,\n          };\n        }\n      }\n\n      return NextResponse.json({\n        success: true,\n        data: result\n      });\n    }\n  );\n});\n\n// POST - Batch add/remove reactions (prevents multiple API calls)\nexport const POST = withUnifiedErrorHandling(async (request: NextRequest) => {\n  return withRateLimit(\n    request,\n    { windowMs: 15 * 60 * 1000, maxRequests: 50 },\n    async () => {\n      const session = await getServerSession(authOptions);\n\n      if (!session?.user?.id) {\n        return NextResponse.json({\n          success: false,\n          error: 'Authentication required'\n        }, { status: 401 });\n      }\n\n      const body = await request.json();\n      const { operations } = body;\n\n      if (!Array.isArray(operations) || operations.length === 0) {\n        return NextResponse.json({\n          success: false,\n          error: 'Operations array is required'\n        }, { status: 400 });\n      }\n\n      // Limit batch size\n      if (operations.length > 20) {\n        return NextResponse.json({\n          success: false,\n          error: 'Too many operations (max 20)'\n        }, { status: 400 });\n      }\n\n      const results: any[] = [];\n\n      // Process operations in transaction for consistency\n      await prisma.$transaction(async (tx) => {\n        for (const op of operations) {\n          const { action, type, postId, replyId, reactionType } = op;\n\n          if (!['add', 'remove'].includes(action)) {\n            results.push({ error: 'Invalid action', operation: op });\n            continue;\n          }\n\n          if (!['post', 'reply'].includes(type)) {\n            results.push({ error: 'Invalid type', operation: op });\n            continue;\n          }\n\n          if (!reactionType || !['like', 'dislike', 'love', 'laugh', 'angry', 'sad'].includes(reactionType)) {\n            results.push({ error: 'Invalid reaction type', operation: op });\n            continue;\n          }\n\n          try {\n            if (type === 'post' && postId) {\n              if (action === 'add') {\n                // Remove existing reaction first, then add new one\n                await tx.forumPostReaction.deleteMany({\n                  where: {\n                    postId,\n                    userId: session.user!.id!,\n                  },\n                });\n\n                await tx.forumPostReaction.create({\n                  data: {\n                    postId,\n                    userId: session.user!.id!,\n                    type: reactionType,\n                  },\n                });\n              } else {\n                await tx.forumPostReaction.deleteMany({\n                  where: {\n                    postId,\n                    userId: session.user!.id!,\n                    type: reactionType,\n                  },\n                });\n              }\n            } else if (type === 'reply' && replyId) {\n              if (action === 'add') {\n                // Remove existing reaction first, then add new one\n                await tx.forumReplyReaction.deleteMany({\n                  where: {\n                    replyId,\n                    userId: session.user!.id!,\n                  },\n                });\n\n                await tx.forumReplyReaction.create({\n                  data: {\n                    replyId,\n                    userId: session.user!.id!,\n                    type: reactionType,\n                  },\n                });\n              } else {\n                await tx.forumReplyReaction.deleteMany({\n                  where: {\n                    replyId,\n                    userId: session.user!.id!,\n                    type: reactionType,\n                  },\n                });\n              }\n            }\n\n            results.push({ success: true, operation: op });\n          } catch (error) {\n            console.error('Batch reaction operation failed:', error);\n            results.push({ error: 'Operation failed', operation: op });\n          }\n        }\n      });\n\n      return NextResponse.json({\n        success: true,\n        data: { results },\n        message: 'Batch operations completed'\n      });\n    }\n  );\n});\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "699bceb9fc0c1332ea7bda3d27d32912ffe0b32f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_1pdg1trbnj = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1pdg1trbnj();
var __awaiter =
/* istanbul ignore next */
(cov_1pdg1trbnj().s[0]++,
/* istanbul ignore next */
(cov_1pdg1trbnj().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_1pdg1trbnj().b[0][1]++, this.__awaiter) ||
/* istanbul ignore next */
(cov_1pdg1trbnj().b[0][2]++, function (thisArg, _arguments, P, generator) {
  /* istanbul ignore next */
  cov_1pdg1trbnj().f[0]++;
  function adopt(value) {
    /* istanbul ignore next */
    cov_1pdg1trbnj().f[1]++;
    cov_1pdg1trbnj().s[1]++;
    return value instanceof P ?
    /* istanbul ignore next */
    (cov_1pdg1trbnj().b[1][0]++, value) :
    /* istanbul ignore next */
    (cov_1pdg1trbnj().b[1][1]++, new P(function (resolve) {
      /* istanbul ignore next */
      cov_1pdg1trbnj().f[2]++;
      cov_1pdg1trbnj().s[2]++;
      resolve(value);
    }));
  }
  /* istanbul ignore next */
  cov_1pdg1trbnj().s[3]++;
  return new (
  /* istanbul ignore next */
  (cov_1pdg1trbnj().b[2][0]++, P) ||
  /* istanbul ignore next */
  (cov_1pdg1trbnj().b[2][1]++, P = Promise))(function (resolve, reject) {
    /* istanbul ignore next */
    cov_1pdg1trbnj().f[3]++;
    function fulfilled(value) {
      /* istanbul ignore next */
      cov_1pdg1trbnj().f[4]++;
      cov_1pdg1trbnj().s[4]++;
      try {
        /* istanbul ignore next */
        cov_1pdg1trbnj().s[5]++;
        step(generator.next(value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1pdg1trbnj().s[6]++;
        reject(e);
      }
    }
    function rejected(value) {
      /* istanbul ignore next */
      cov_1pdg1trbnj().f[5]++;
      cov_1pdg1trbnj().s[7]++;
      try {
        /* istanbul ignore next */
        cov_1pdg1trbnj().s[8]++;
        step(generator["throw"](value));
      } catch (e) {
        /* istanbul ignore next */
        cov_1pdg1trbnj().s[9]++;
        reject(e);
      }
    }
    function step(result) {
      /* istanbul ignore next */
      cov_1pdg1trbnj().f[6]++;
      cov_1pdg1trbnj().s[10]++;
      result.done ?
      /* istanbul ignore next */
      (cov_1pdg1trbnj().b[3][0]++, resolve(result.value)) :
      /* istanbul ignore next */
      (cov_1pdg1trbnj().b[3][1]++, adopt(result.value).then(fulfilled, rejected));
    }
    /* istanbul ignore next */
    cov_1pdg1trbnj().s[11]++;
    step((generator = generator.apply(thisArg,
    /* istanbul ignore next */
    (cov_1pdg1trbnj().b[4][0]++, _arguments) ||
    /* istanbul ignore next */
    (cov_1pdg1trbnj().b[4][1]++, []))).next());
  });
}));
var __generator =
/* istanbul ignore next */
(cov_1pdg1trbnj().s[12]++,
/* istanbul ignore next */
(cov_1pdg1trbnj().b[5][0]++, this) &&
/* istanbul ignore next */
(cov_1pdg1trbnj().b[5][1]++, this.__generator) ||
/* istanbul ignore next */
(cov_1pdg1trbnj().b[5][2]++, function (thisArg, body) {
  /* istanbul ignore next */
  cov_1pdg1trbnj().f[7]++;
  var _ =
    /* istanbul ignore next */
    (cov_1pdg1trbnj().s[13]++, {
      label: 0,
      sent: function () {
        /* istanbul ignore next */
        cov_1pdg1trbnj().f[8]++;
        cov_1pdg1trbnj().s[14]++;
        if (t[0] & 1) {
          /* istanbul ignore next */
          cov_1pdg1trbnj().b[6][0]++;
          cov_1pdg1trbnj().s[15]++;
          throw t[1];
        } else
        /* istanbul ignore next */
        {
          cov_1pdg1trbnj().b[6][1]++;
        }
        cov_1pdg1trbnj().s[16]++;
        return t[1];
      },
      trys: [],
      ops: []
    }),
    f,
    y,
    t,
    g =
    /* istanbul ignore next */
    (cov_1pdg1trbnj().s[17]++, Object.create((typeof Iterator === "function" ?
    /* istanbul ignore next */
    (cov_1pdg1trbnj().b[7][0]++, Iterator) :
    /* istanbul ignore next */
    (cov_1pdg1trbnj().b[7][1]++, Object)).prototype));
  /* istanbul ignore next */
  cov_1pdg1trbnj().s[18]++;
  return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2),
  /* istanbul ignore next */
  (cov_1pdg1trbnj().b[8][0]++, typeof Symbol === "function") &&
  /* istanbul ignore next */
  (cov_1pdg1trbnj().b[8][1]++, g[Symbol.iterator] = function () {
    /* istanbul ignore next */
    cov_1pdg1trbnj().f[9]++;
    cov_1pdg1trbnj().s[19]++;
    return this;
  }), g;
  function verb(n) {
    /* istanbul ignore next */
    cov_1pdg1trbnj().f[10]++;
    cov_1pdg1trbnj().s[20]++;
    return function (v) {
      /* istanbul ignore next */
      cov_1pdg1trbnj().f[11]++;
      cov_1pdg1trbnj().s[21]++;
      return step([n, v]);
    };
  }
  function step(op) {
    /* istanbul ignore next */
    cov_1pdg1trbnj().f[12]++;
    cov_1pdg1trbnj().s[22]++;
    if (f) {
      /* istanbul ignore next */
      cov_1pdg1trbnj().b[9][0]++;
      cov_1pdg1trbnj().s[23]++;
      throw new TypeError("Generator is already executing.");
    } else
    /* istanbul ignore next */
    {
      cov_1pdg1trbnj().b[9][1]++;
    }
    cov_1pdg1trbnj().s[24]++;
    while (
    /* istanbul ignore next */
    (cov_1pdg1trbnj().b[10][0]++, g) &&
    /* istanbul ignore next */
    (cov_1pdg1trbnj().b[10][1]++, g = 0,
    /* istanbul ignore next */
    (cov_1pdg1trbnj().b[11][0]++, op[0]) &&
    /* istanbul ignore next */
    (cov_1pdg1trbnj().b[11][1]++, _ = 0)), _) {
      /* istanbul ignore next */
      cov_1pdg1trbnj().s[25]++;
      try {
        /* istanbul ignore next */
        cov_1pdg1trbnj().s[26]++;
        if (f = 1,
        /* istanbul ignore next */
        (cov_1pdg1trbnj().b[13][0]++, y) &&
        /* istanbul ignore next */
        (cov_1pdg1trbnj().b[13][1]++, t = op[0] & 2 ?
        /* istanbul ignore next */
        (cov_1pdg1trbnj().b[14][0]++, y["return"]) :
        /* istanbul ignore next */
        (cov_1pdg1trbnj().b[14][1]++, op[0] ?
        /* istanbul ignore next */
        (cov_1pdg1trbnj().b[15][0]++,
        /* istanbul ignore next */
        (cov_1pdg1trbnj().b[16][0]++, y["throw"]) ||
        /* istanbul ignore next */
        (cov_1pdg1trbnj().b[16][1]++,
        /* istanbul ignore next */
        (cov_1pdg1trbnj().b[17][0]++, t = y["return"]) &&
        /* istanbul ignore next */
        (cov_1pdg1trbnj().b[17][1]++, t.call(y)), 0)) :
        /* istanbul ignore next */
        (cov_1pdg1trbnj().b[15][1]++, y.next))) &&
        /* istanbul ignore next */
        (cov_1pdg1trbnj().b[13][2]++, !(t = t.call(y, op[1])).done)) {
          /* istanbul ignore next */
          cov_1pdg1trbnj().b[12][0]++;
          cov_1pdg1trbnj().s[27]++;
          return t;
        } else
        /* istanbul ignore next */
        {
          cov_1pdg1trbnj().b[12][1]++;
        }
        cov_1pdg1trbnj().s[28]++;
        if (y = 0, t) {
          /* istanbul ignore next */
          cov_1pdg1trbnj().b[18][0]++;
          cov_1pdg1trbnj().s[29]++;
          op = [op[0] & 2, t.value];
        } else
        /* istanbul ignore next */
        {
          cov_1pdg1trbnj().b[18][1]++;
        }
        cov_1pdg1trbnj().s[30]++;
        switch (op[0]) {
          case 0:
            /* istanbul ignore next */
            cov_1pdg1trbnj().b[19][0]++;
          case 1:
            /* istanbul ignore next */
            cov_1pdg1trbnj().b[19][1]++;
            cov_1pdg1trbnj().s[31]++;
            t = op;
            /* istanbul ignore next */
            cov_1pdg1trbnj().s[32]++;
            break;
          case 4:
            /* istanbul ignore next */
            cov_1pdg1trbnj().b[19][2]++;
            cov_1pdg1trbnj().s[33]++;
            _.label++;
            /* istanbul ignore next */
            cov_1pdg1trbnj().s[34]++;
            return {
              value: op[1],
              done: false
            };
          case 5:
            /* istanbul ignore next */
            cov_1pdg1trbnj().b[19][3]++;
            cov_1pdg1trbnj().s[35]++;
            _.label++;
            /* istanbul ignore next */
            cov_1pdg1trbnj().s[36]++;
            y = op[1];
            /* istanbul ignore next */
            cov_1pdg1trbnj().s[37]++;
            op = [0];
            /* istanbul ignore next */
            cov_1pdg1trbnj().s[38]++;
            continue;
          case 7:
            /* istanbul ignore next */
            cov_1pdg1trbnj().b[19][4]++;
            cov_1pdg1trbnj().s[39]++;
            op = _.ops.pop();
            /* istanbul ignore next */
            cov_1pdg1trbnj().s[40]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1pdg1trbnj().s[41]++;
            continue;
          default:
            /* istanbul ignore next */
            cov_1pdg1trbnj().b[19][5]++;
            cov_1pdg1trbnj().s[42]++;
            if (
            /* istanbul ignore next */
            (cov_1pdg1trbnj().b[21][0]++, !(t = _.trys, t =
            /* istanbul ignore next */
            (cov_1pdg1trbnj().b[22][0]++, t.length > 0) &&
            /* istanbul ignore next */
            (cov_1pdg1trbnj().b[22][1]++, t[t.length - 1]))) && (
            /* istanbul ignore next */
            (cov_1pdg1trbnj().b[21][1]++, op[0] === 6) ||
            /* istanbul ignore next */
            (cov_1pdg1trbnj().b[21][2]++, op[0] === 2))) {
              /* istanbul ignore next */
              cov_1pdg1trbnj().b[20][0]++;
              cov_1pdg1trbnj().s[43]++;
              _ = 0;
              /* istanbul ignore next */
              cov_1pdg1trbnj().s[44]++;
              continue;
            } else
            /* istanbul ignore next */
            {
              cov_1pdg1trbnj().b[20][1]++;
            }
            cov_1pdg1trbnj().s[45]++;
            if (
            /* istanbul ignore next */
            (cov_1pdg1trbnj().b[24][0]++, op[0] === 3) && (
            /* istanbul ignore next */
            (cov_1pdg1trbnj().b[24][1]++, !t) ||
            /* istanbul ignore next */
            (cov_1pdg1trbnj().b[24][2]++, op[1] > t[0]) &&
            /* istanbul ignore next */
            (cov_1pdg1trbnj().b[24][3]++, op[1] < t[3]))) {
              /* istanbul ignore next */
              cov_1pdg1trbnj().b[23][0]++;
              cov_1pdg1trbnj().s[46]++;
              _.label = op[1];
              /* istanbul ignore next */
              cov_1pdg1trbnj().s[47]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1pdg1trbnj().b[23][1]++;
            }
            cov_1pdg1trbnj().s[48]++;
            if (
            /* istanbul ignore next */
            (cov_1pdg1trbnj().b[26][0]++, op[0] === 6) &&
            /* istanbul ignore next */
            (cov_1pdg1trbnj().b[26][1]++, _.label < t[1])) {
              /* istanbul ignore next */
              cov_1pdg1trbnj().b[25][0]++;
              cov_1pdg1trbnj().s[49]++;
              _.label = t[1];
              /* istanbul ignore next */
              cov_1pdg1trbnj().s[50]++;
              t = op;
              /* istanbul ignore next */
              cov_1pdg1trbnj().s[51]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1pdg1trbnj().b[25][1]++;
            }
            cov_1pdg1trbnj().s[52]++;
            if (
            /* istanbul ignore next */
            (cov_1pdg1trbnj().b[28][0]++, t) &&
            /* istanbul ignore next */
            (cov_1pdg1trbnj().b[28][1]++, _.label < t[2])) {
              /* istanbul ignore next */
              cov_1pdg1trbnj().b[27][0]++;
              cov_1pdg1trbnj().s[53]++;
              _.label = t[2];
              /* istanbul ignore next */
              cov_1pdg1trbnj().s[54]++;
              _.ops.push(op);
              /* istanbul ignore next */
              cov_1pdg1trbnj().s[55]++;
              break;
            } else
            /* istanbul ignore next */
            {
              cov_1pdg1trbnj().b[27][1]++;
            }
            cov_1pdg1trbnj().s[56]++;
            if (t[2]) {
              /* istanbul ignore next */
              cov_1pdg1trbnj().b[29][0]++;
              cov_1pdg1trbnj().s[57]++;
              _.ops.pop();
            } else
            /* istanbul ignore next */
            {
              cov_1pdg1trbnj().b[29][1]++;
            }
            cov_1pdg1trbnj().s[58]++;
            _.trys.pop();
            /* istanbul ignore next */
            cov_1pdg1trbnj().s[59]++;
            continue;
        }
        /* istanbul ignore next */
        cov_1pdg1trbnj().s[60]++;
        op = body.call(thisArg, _);
      } catch (e) {
        /* istanbul ignore next */
        cov_1pdg1trbnj().s[61]++;
        op = [6, e];
        /* istanbul ignore next */
        cov_1pdg1trbnj().s[62]++;
        y = 0;
      } finally {
        /* istanbul ignore next */
        cov_1pdg1trbnj().s[63]++;
        f = t = 0;
      }
    }
    /* istanbul ignore next */
    cov_1pdg1trbnj().s[64]++;
    if (op[0] & 5) {
      /* istanbul ignore next */
      cov_1pdg1trbnj().b[30][0]++;
      cov_1pdg1trbnj().s[65]++;
      throw op[1];
    } else
    /* istanbul ignore next */
    {
      cov_1pdg1trbnj().b[30][1]++;
    }
    cov_1pdg1trbnj().s[66]++;
    return {
      value: op[0] ?
      /* istanbul ignore next */
      (cov_1pdg1trbnj().b[31][0]++, op[1]) :
      /* istanbul ignore next */
      (cov_1pdg1trbnj().b[31][1]++, void 0),
      done: true
    };
  }
}));
/* istanbul ignore next */
cov_1pdg1trbnj().s[67]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_1pdg1trbnj().s[68]++;
exports.POST = exports.GET = void 0;
var server_1 =
/* istanbul ignore next */
(cov_1pdg1trbnj().s[69]++, require("next/server"));
var next_auth_1 =
/* istanbul ignore next */
(cov_1pdg1trbnj().s[70]++, require("next-auth"));
var auth_1 =
/* istanbul ignore next */
(cov_1pdg1trbnj().s[71]++, require("@/lib/auth"));
var prisma_1 =
/* istanbul ignore next */
(cov_1pdg1trbnj().s[72]++, require("@/lib/prisma"));
var unified_api_error_handler_1 =
/* istanbul ignore next */
(cov_1pdg1trbnj().s[73]++, require("@/lib/unified-api-error-handler"));
var rateLimit_1 =
/* istanbul ignore next */
(cov_1pdg1trbnj().s[74]++, require("@/lib/rateLimit"));
// GET - Batch load reactions for multiple posts/replies (prevents N+1 queries)
/* istanbul ignore next */
cov_1pdg1trbnj().s[75]++;
exports.GET = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_1pdg1trbnj().f[13]++;
  cov_1pdg1trbnj().s[76]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_1pdg1trbnj().f[14]++;
    cov_1pdg1trbnj().s[77]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_1pdg1trbnj().f[15]++;
      cov_1pdg1trbnj().s[78]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: 100
      }, function () {
        /* istanbul ignore next */
        cov_1pdg1trbnj().f[16]++;
        cov_1pdg1trbnj().s[79]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_1pdg1trbnj().f[17]++;
          var searchParams, postIds, replyIds, session, userId, result, postReactions, postReactionGroups, _i, postIds_1, postId, reactions, counts, userReaction, replyReactions, replyReactionGroups, _a, replyIds_1, replyId, reactions, counts, userReaction;
          var _b, _c, _d, _e, _f;
          /* istanbul ignore next */
          cov_1pdg1trbnj().s[80]++;
          return __generator(this, function (_g) {
            /* istanbul ignore next */
            cov_1pdg1trbnj().f[18]++;
            cov_1pdg1trbnj().s[81]++;
            switch (_g.label) {
              case 0:
                /* istanbul ignore next */
                cov_1pdg1trbnj().b[32][0]++;
                cov_1pdg1trbnj().s[82]++;
                searchParams = new URL(request.url).searchParams;
                /* istanbul ignore next */
                cov_1pdg1trbnj().s[83]++;
                postIds =
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[33][0]++,
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[35][0]++, (_b = searchParams.get('postIds')) === null) ||
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[35][1]++, _b === void 0) ?
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[34][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[34][1]++, _b.split(',').filter(Boolean))) ||
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[33][1]++, []);
                /* istanbul ignore next */
                cov_1pdg1trbnj().s[84]++;
                replyIds =
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[36][0]++,
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[38][0]++, (_c = searchParams.get('replyIds')) === null) ||
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[38][1]++, _c === void 0) ?
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[37][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[37][1]++, _c.split(',').filter(Boolean))) ||
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[36][1]++, []);
                /* istanbul ignore next */
                cov_1pdg1trbnj().s[85]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_1pdg1trbnj().b[32][1]++;
                cov_1pdg1trbnj().s[86]++;
                session = _g.sent();
                /* istanbul ignore next */
                cov_1pdg1trbnj().s[87]++;
                userId =
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[40][0]++, (_d =
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[42][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[42][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[41][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[41][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[40][1]++, _d === void 0) ?
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[39][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[39][1]++, _d.id);
                /* istanbul ignore next */
                cov_1pdg1trbnj().s[88]++;
                if (
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[44][0]++, postIds.length === 0) &&
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[44][1]++, replyIds.length === 0)) {
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().b[43][0]++;
                  cov_1pdg1trbnj().s[89]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'At least one postId or replyId is required'
                  }, {
                    status: 400
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1pdg1trbnj().b[43][1]++;
                }
                // Limit batch size to prevent abuse
                cov_1pdg1trbnj().s[90]++;
                if (
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[46][0]++, postIds.length > 50) ||
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[46][1]++, replyIds.length > 50)) {
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().b[45][0]++;
                  cov_1pdg1trbnj().s[91]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Batch size too large (max 50 items per type)'
                  }, {
                    status: 400
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1pdg1trbnj().b[45][1]++;
                }
                cov_1pdg1trbnj().s[92]++;
                result = {
                  postReactions: {},
                  replyReactions: {}
                };
                /* istanbul ignore next */
                cov_1pdg1trbnj().s[93]++;
                if (!(postIds.length > 0)) {
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().b[47][0]++;
                  cov_1pdg1trbnj().s[94]++;
                  return [3 /*break*/, 3];
                } else
                /* istanbul ignore next */
                {
                  cov_1pdg1trbnj().b[47][1]++;
                }
                cov_1pdg1trbnj().s[95]++;
                return [4 /*yield*/, prisma_1.prisma.forumPostReaction.findMany({
                  where: {
                    postId: {
                      in: postIds
                    }
                  },
                  select: {
                    postId: true,
                    type: true,
                    userId: true
                  },
                  orderBy: {
                    createdAt: 'desc'
                  }
                })];
              case 2:
                /* istanbul ignore next */
                cov_1pdg1trbnj().b[32][2]++;
                cov_1pdg1trbnj().s[96]++;
                postReactions = _g.sent();
                /* istanbul ignore next */
                cov_1pdg1trbnj().s[97]++;
                postReactionGroups = postReactions.reduce(function (acc, reaction) {
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().f[19]++;
                  cov_1pdg1trbnj().s[98]++;
                  if (!acc[reaction.postId]) {
                    /* istanbul ignore next */
                    cov_1pdg1trbnj().b[48][0]++;
                    cov_1pdg1trbnj().s[99]++;
                    acc[reaction.postId] = [];
                  } else
                  /* istanbul ignore next */
                  {
                    cov_1pdg1trbnj().b[48][1]++;
                  }
                  cov_1pdg1trbnj().s[100]++;
                  acc[reaction.postId].push(reaction);
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().s[101]++;
                  return acc;
                }, {});
                // Process each post's reactions
                /* istanbul ignore next */
                cov_1pdg1trbnj().s[102]++;
                for (_i = 0, postIds_1 = postIds; _i < postIds_1.length; _i++) {
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().s[103]++;
                  postId = postIds_1[_i];
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().s[104]++;
                  reactions =
                  /* istanbul ignore next */
                  (cov_1pdg1trbnj().b[49][0]++, postReactionGroups[postId]) ||
                  /* istanbul ignore next */
                  (cov_1pdg1trbnj().b[49][1]++, []);
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().s[105]++;
                  counts = reactions.reduce(function (acc, reaction) {
                    /* istanbul ignore next */
                    cov_1pdg1trbnj().f[20]++;
                    cov_1pdg1trbnj().s[106]++;
                    acc[reaction.type] = (
                    /* istanbul ignore next */
                    (cov_1pdg1trbnj().b[50][0]++, acc[reaction.type]) ||
                    /* istanbul ignore next */
                    (cov_1pdg1trbnj().b[50][1]++, 0)) + 1;
                    /* istanbul ignore next */
                    cov_1pdg1trbnj().s[107]++;
                    return acc;
                  }, {});
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().s[108]++;
                  userReaction = userId ?
                  /* istanbul ignore next */
                  (cov_1pdg1trbnj().b[51][0]++,
                  /* istanbul ignore next */
                  (cov_1pdg1trbnj().b[53][0]++, (_e = reactions.find(function (r) {
                    /* istanbul ignore next */
                    cov_1pdg1trbnj().f[21]++;
                    cov_1pdg1trbnj().s[109]++;
                    return r.userId === userId;
                  })) === null) ||
                  /* istanbul ignore next */
                  (cov_1pdg1trbnj().b[53][1]++, _e === void 0) ?
                  /* istanbul ignore next */
                  (cov_1pdg1trbnj().b[52][0]++, void 0) :
                  /* istanbul ignore next */
                  (cov_1pdg1trbnj().b[52][1]++, _e.type)) :
                  /* istanbul ignore next */
                  (cov_1pdg1trbnj().b[51][1]++, undefined);
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().s[110]++;
                  result.postReactions[postId] = {
                    reactions: reactions.map(function (r) {
                      /* istanbul ignore next */
                      cov_1pdg1trbnj().f[22]++;
                      cov_1pdg1trbnj().s[111]++;
                      return {
                        type: r.type,
                        userId: r.userId
                      };
                    }),
                    counts: counts,
                    total: reactions.length,
                    userReaction: userReaction
                  };
                }
                /* istanbul ignore next */
                cov_1pdg1trbnj().s[112]++;
                _g.label = 3;
              case 3:
                /* istanbul ignore next */
                cov_1pdg1trbnj().b[32][3]++;
                cov_1pdg1trbnj().s[113]++;
                if (!(replyIds.length > 0)) {
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().b[54][0]++;
                  cov_1pdg1trbnj().s[114]++;
                  return [3 /*break*/, 5];
                } else
                /* istanbul ignore next */
                {
                  cov_1pdg1trbnj().b[54][1]++;
                }
                cov_1pdg1trbnj().s[115]++;
                return [4 /*yield*/, prisma_1.prisma.forumReplyReaction.findMany({
                  where: {
                    replyId: {
                      in: replyIds
                    }
                  },
                  select: {
                    replyId: true,
                    type: true,
                    userId: true
                  },
                  orderBy: {
                    createdAt: 'desc'
                  }
                })];
              case 4:
                /* istanbul ignore next */
                cov_1pdg1trbnj().b[32][4]++;
                cov_1pdg1trbnj().s[116]++;
                replyReactions = _g.sent();
                /* istanbul ignore next */
                cov_1pdg1trbnj().s[117]++;
                replyReactionGroups = replyReactions.reduce(function (acc, reaction) {
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().f[23]++;
                  cov_1pdg1trbnj().s[118]++;
                  if (!acc[reaction.replyId]) {
                    /* istanbul ignore next */
                    cov_1pdg1trbnj().b[55][0]++;
                    cov_1pdg1trbnj().s[119]++;
                    acc[reaction.replyId] = [];
                  } else
                  /* istanbul ignore next */
                  {
                    cov_1pdg1trbnj().b[55][1]++;
                  }
                  cov_1pdg1trbnj().s[120]++;
                  acc[reaction.replyId].push(reaction);
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().s[121]++;
                  return acc;
                }, {});
                // Process each reply's reactions
                /* istanbul ignore next */
                cov_1pdg1trbnj().s[122]++;
                for (_a = 0, replyIds_1 = replyIds; _a < replyIds_1.length; _a++) {
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().s[123]++;
                  replyId = replyIds_1[_a];
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().s[124]++;
                  reactions =
                  /* istanbul ignore next */
                  (cov_1pdg1trbnj().b[56][0]++, replyReactionGroups[replyId]) ||
                  /* istanbul ignore next */
                  (cov_1pdg1trbnj().b[56][1]++, []);
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().s[125]++;
                  counts = reactions.reduce(function (acc, reaction) {
                    /* istanbul ignore next */
                    cov_1pdg1trbnj().f[24]++;
                    cov_1pdg1trbnj().s[126]++;
                    acc[reaction.type] = (
                    /* istanbul ignore next */
                    (cov_1pdg1trbnj().b[57][0]++, acc[reaction.type]) ||
                    /* istanbul ignore next */
                    (cov_1pdg1trbnj().b[57][1]++, 0)) + 1;
                    /* istanbul ignore next */
                    cov_1pdg1trbnj().s[127]++;
                    return acc;
                  }, {});
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().s[128]++;
                  userReaction = userId ?
                  /* istanbul ignore next */
                  (cov_1pdg1trbnj().b[58][0]++,
                  /* istanbul ignore next */
                  (cov_1pdg1trbnj().b[60][0]++, (_f = reactions.find(function (r) {
                    /* istanbul ignore next */
                    cov_1pdg1trbnj().f[25]++;
                    cov_1pdg1trbnj().s[129]++;
                    return r.userId === userId;
                  })) === null) ||
                  /* istanbul ignore next */
                  (cov_1pdg1trbnj().b[60][1]++, _f === void 0) ?
                  /* istanbul ignore next */
                  (cov_1pdg1trbnj().b[59][0]++, void 0) :
                  /* istanbul ignore next */
                  (cov_1pdg1trbnj().b[59][1]++, _f.type)) :
                  /* istanbul ignore next */
                  (cov_1pdg1trbnj().b[58][1]++, undefined);
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().s[130]++;
                  result.replyReactions[replyId] = {
                    reactions: reactions.map(function (r) {
                      /* istanbul ignore next */
                      cov_1pdg1trbnj().f[26]++;
                      cov_1pdg1trbnj().s[131]++;
                      return {
                        type: r.type,
                        userId: r.userId
                      };
                    }),
                    counts: counts,
                    total: reactions.length,
                    userReaction: userReaction
                  };
                }
                /* istanbul ignore next */
                cov_1pdg1trbnj().s[132]++;
                _g.label = 5;
              case 5:
                /* istanbul ignore next */
                cov_1pdg1trbnj().b[32][5]++;
                cov_1pdg1trbnj().s[133]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: result
                })];
            }
          });
        });
      })];
    });
  });
});
// POST - Batch add/remove reactions (prevents multiple API calls)
/* istanbul ignore next */
cov_1pdg1trbnj().s[134]++;
exports.POST = (0, unified_api_error_handler_1.withUnifiedErrorHandling)(function (request) {
  /* istanbul ignore next */
  cov_1pdg1trbnj().f[27]++;
  cov_1pdg1trbnj().s[135]++;
  return __awaiter(void 0, void 0, void 0, function () {
    /* istanbul ignore next */
    cov_1pdg1trbnj().f[28]++;
    cov_1pdg1trbnj().s[136]++;
    return __generator(this, function (_a) {
      /* istanbul ignore next */
      cov_1pdg1trbnj().f[29]++;
      cov_1pdg1trbnj().s[137]++;
      return [2 /*return*/, (0, rateLimit_1.withRateLimit)(request, {
        windowMs: 15 * 60 * 1000,
        maxRequests: 50
      }, function () {
        /* istanbul ignore next */
        cov_1pdg1trbnj().f[30]++;
        cov_1pdg1trbnj().s[138]++;
        return __awaiter(void 0, void 0, void 0, function () {
          /* istanbul ignore next */
          cov_1pdg1trbnj().f[31]++;
          var session, body, operations, results;
          var _a;
          /* istanbul ignore next */
          cov_1pdg1trbnj().s[139]++;
          return __generator(this, function (_b) {
            /* istanbul ignore next */
            cov_1pdg1trbnj().f[32]++;
            cov_1pdg1trbnj().s[140]++;
            switch (_b.label) {
              case 0:
                /* istanbul ignore next */
                cov_1pdg1trbnj().b[61][0]++;
                cov_1pdg1trbnj().s[141]++;
                return [4 /*yield*/, (0, next_auth_1.getServerSession)(auth_1.authOptions)];
              case 1:
                /* istanbul ignore next */
                cov_1pdg1trbnj().b[61][1]++;
                cov_1pdg1trbnj().s[142]++;
                session = _b.sent();
                /* istanbul ignore next */
                cov_1pdg1trbnj().s[143]++;
                if (!(
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[64][0]++, (_a =
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[66][0]++, session === null) ||
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[66][1]++, session === void 0) ?
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[65][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[65][1]++, session.user)) === null) ||
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[64][1]++, _a === void 0) ?
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[63][0]++, void 0) :
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[63][1]++, _a.id))) {
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().b[62][0]++;
                  cov_1pdg1trbnj().s[144]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Authentication required'
                  }, {
                    status: 401
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1pdg1trbnj().b[62][1]++;
                }
                cov_1pdg1trbnj().s[145]++;
                return [4 /*yield*/, request.json()];
              case 2:
                /* istanbul ignore next */
                cov_1pdg1trbnj().b[61][2]++;
                cov_1pdg1trbnj().s[146]++;
                body = _b.sent();
                /* istanbul ignore next */
                cov_1pdg1trbnj().s[147]++;
                operations = body.operations;
                /* istanbul ignore next */
                cov_1pdg1trbnj().s[148]++;
                if (
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[68][0]++, !Array.isArray(operations)) ||
                /* istanbul ignore next */
                (cov_1pdg1trbnj().b[68][1]++, operations.length === 0)) {
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().b[67][0]++;
                  cov_1pdg1trbnj().s[149]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Operations array is required'
                  }, {
                    status: 400
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1pdg1trbnj().b[67][1]++;
                }
                // Limit batch size
                cov_1pdg1trbnj().s[150]++;
                if (operations.length > 20) {
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().b[69][0]++;
                  cov_1pdg1trbnj().s[151]++;
                  return [2 /*return*/, server_1.NextResponse.json({
                    success: false,
                    error: 'Too many operations (max 20)'
                  }, {
                    status: 400
                  })];
                } else
                /* istanbul ignore next */
                {
                  cov_1pdg1trbnj().b[69][1]++;
                }
                cov_1pdg1trbnj().s[152]++;
                results = [];
                // Process operations in transaction for consistency
                /* istanbul ignore next */
                cov_1pdg1trbnj().s[153]++;
                return [4 /*yield*/, prisma_1.prisma.$transaction(function (tx) {
                  /* istanbul ignore next */
                  cov_1pdg1trbnj().f[33]++;
                  cov_1pdg1trbnj().s[154]++;
                  return __awaiter(void 0, void 0, void 0, function () {
                    /* istanbul ignore next */
                    cov_1pdg1trbnj().f[34]++;
                    var _i, operations_1, op, action, type, postId, replyId, reactionType, error_1;
                    /* istanbul ignore next */
                    cov_1pdg1trbnj().s[155]++;
                    return __generator(this, function (_a) {
                      /* istanbul ignore next */
                      cov_1pdg1trbnj().f[35]++;
                      cov_1pdg1trbnj().s[156]++;
                      switch (_a.label) {
                        case 0:
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().b[70][0]++;
                          cov_1pdg1trbnj().s[157]++;
                          _i = 0, operations_1 = operations;
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().s[158]++;
                          _a.label = 1;
                        case 1:
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().b[70][1]++;
                          cov_1pdg1trbnj().s[159]++;
                          if (!(_i < operations_1.length)) {
                            /* istanbul ignore next */
                            cov_1pdg1trbnj().b[71][0]++;
                            cov_1pdg1trbnj().s[160]++;
                            return [3 /*break*/, 16];
                          } else
                          /* istanbul ignore next */
                          {
                            cov_1pdg1trbnj().b[71][1]++;
                          }
                          cov_1pdg1trbnj().s[161]++;
                          op = operations_1[_i];
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().s[162]++;
                          action = op.action, type = op.type, postId = op.postId, replyId = op.replyId, reactionType = op.reactionType;
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().s[163]++;
                          if (!['add', 'remove'].includes(action)) {
                            /* istanbul ignore next */
                            cov_1pdg1trbnj().b[72][0]++;
                            cov_1pdg1trbnj().s[164]++;
                            results.push({
                              error: 'Invalid action',
                              operation: op
                            });
                            /* istanbul ignore next */
                            cov_1pdg1trbnj().s[165]++;
                            return [3 /*break*/, 15];
                          } else
                          /* istanbul ignore next */
                          {
                            cov_1pdg1trbnj().b[72][1]++;
                          }
                          cov_1pdg1trbnj().s[166]++;
                          if (!['post', 'reply'].includes(type)) {
                            /* istanbul ignore next */
                            cov_1pdg1trbnj().b[73][0]++;
                            cov_1pdg1trbnj().s[167]++;
                            results.push({
                              error: 'Invalid type',
                              operation: op
                            });
                            /* istanbul ignore next */
                            cov_1pdg1trbnj().s[168]++;
                            return [3 /*break*/, 15];
                          } else
                          /* istanbul ignore next */
                          {
                            cov_1pdg1trbnj().b[73][1]++;
                          }
                          cov_1pdg1trbnj().s[169]++;
                          if (
                          /* istanbul ignore next */
                          (cov_1pdg1trbnj().b[75][0]++, !reactionType) ||
                          /* istanbul ignore next */
                          (cov_1pdg1trbnj().b[75][1]++, !['like', 'dislike', 'love', 'laugh', 'angry', 'sad'].includes(reactionType))) {
                            /* istanbul ignore next */
                            cov_1pdg1trbnj().b[74][0]++;
                            cov_1pdg1trbnj().s[170]++;
                            results.push({
                              error: 'Invalid reaction type',
                              operation: op
                            });
                            /* istanbul ignore next */
                            cov_1pdg1trbnj().s[171]++;
                            return [3 /*break*/, 15];
                          } else
                          /* istanbul ignore next */
                          {
                            cov_1pdg1trbnj().b[74][1]++;
                          }
                          cov_1pdg1trbnj().s[172]++;
                          _a.label = 2;
                        case 2:
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().b[70][2]++;
                          cov_1pdg1trbnj().s[173]++;
                          _a.trys.push([2, 14,, 15]);
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().s[174]++;
                          if (!(
                          /* istanbul ignore next */
                          (cov_1pdg1trbnj().b[77][0]++, type === 'post') &&
                          /* istanbul ignore next */
                          (cov_1pdg1trbnj().b[77][1]++, postId))) {
                            /* istanbul ignore next */
                            cov_1pdg1trbnj().b[76][0]++;
                            cov_1pdg1trbnj().s[175]++;
                            return [3 /*break*/, 8];
                          } else
                          /* istanbul ignore next */
                          {
                            cov_1pdg1trbnj().b[76][1]++;
                          }
                          cov_1pdg1trbnj().s[176]++;
                          if (!(action === 'add')) {
                            /* istanbul ignore next */
                            cov_1pdg1trbnj().b[78][0]++;
                            cov_1pdg1trbnj().s[177]++;
                            return [3 /*break*/, 5];
                          } else
                          /* istanbul ignore next */
                          {
                            cov_1pdg1trbnj().b[78][1]++;
                          }
                          // Remove existing reaction first, then add new one
                          cov_1pdg1trbnj().s[178]++;
                          return [4 /*yield*/, tx.forumPostReaction.deleteMany({
                            where: {
                              postId: postId,
                              userId: session.user.id
                            }
                          })];
                        case 3:
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().b[70][3]++;
                          cov_1pdg1trbnj().s[179]++;
                          // Remove existing reaction first, then add new one
                          _a.sent();
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().s[180]++;
                          return [4 /*yield*/, tx.forumPostReaction.create({
                            data: {
                              postId: postId,
                              userId: session.user.id,
                              type: reactionType
                            }
                          })];
                        case 4:
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().b[70][4]++;
                          cov_1pdg1trbnj().s[181]++;
                          _a.sent();
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().s[182]++;
                          return [3 /*break*/, 7];
                        case 5:
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().b[70][5]++;
                          cov_1pdg1trbnj().s[183]++;
                          return [4 /*yield*/, tx.forumPostReaction.deleteMany({
                            where: {
                              postId: postId,
                              userId: session.user.id,
                              type: reactionType
                            }
                          })];
                        case 6:
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().b[70][6]++;
                          cov_1pdg1trbnj().s[184]++;
                          _a.sent();
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().s[185]++;
                          _a.label = 7;
                        case 7:
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().b[70][7]++;
                          cov_1pdg1trbnj().s[186]++;
                          return [3 /*break*/, 13];
                        case 8:
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().b[70][8]++;
                          cov_1pdg1trbnj().s[187]++;
                          if (!(
                          /* istanbul ignore next */
                          (cov_1pdg1trbnj().b[80][0]++, type === 'reply') &&
                          /* istanbul ignore next */
                          (cov_1pdg1trbnj().b[80][1]++, replyId))) {
                            /* istanbul ignore next */
                            cov_1pdg1trbnj().b[79][0]++;
                            cov_1pdg1trbnj().s[188]++;
                            return [3 /*break*/, 13];
                          } else
                          /* istanbul ignore next */
                          {
                            cov_1pdg1trbnj().b[79][1]++;
                          }
                          cov_1pdg1trbnj().s[189]++;
                          if (!(action === 'add')) {
                            /* istanbul ignore next */
                            cov_1pdg1trbnj().b[81][0]++;
                            cov_1pdg1trbnj().s[190]++;
                            return [3 /*break*/, 11];
                          } else
                          /* istanbul ignore next */
                          {
                            cov_1pdg1trbnj().b[81][1]++;
                          }
                          // Remove existing reaction first, then add new one
                          cov_1pdg1trbnj().s[191]++;
                          return [4 /*yield*/, tx.forumReplyReaction.deleteMany({
                            where: {
                              replyId: replyId,
                              userId: session.user.id
                            }
                          })];
                        case 9:
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().b[70][9]++;
                          cov_1pdg1trbnj().s[192]++;
                          // Remove existing reaction first, then add new one
                          _a.sent();
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().s[193]++;
                          return [4 /*yield*/, tx.forumReplyReaction.create({
                            data: {
                              replyId: replyId,
                              userId: session.user.id,
                              type: reactionType
                            }
                          })];
                        case 10:
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().b[70][10]++;
                          cov_1pdg1trbnj().s[194]++;
                          _a.sent();
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().s[195]++;
                          return [3 /*break*/, 13];
                        case 11:
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().b[70][11]++;
                          cov_1pdg1trbnj().s[196]++;
                          return [4 /*yield*/, tx.forumReplyReaction.deleteMany({
                            where: {
                              replyId: replyId,
                              userId: session.user.id,
                              type: reactionType
                            }
                          })];
                        case 12:
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().b[70][12]++;
                          cov_1pdg1trbnj().s[197]++;
                          _a.sent();
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().s[198]++;
                          _a.label = 13;
                        case 13:
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().b[70][13]++;
                          cov_1pdg1trbnj().s[199]++;
                          results.push({
                            success: true,
                            operation: op
                          });
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().s[200]++;
                          return [3 /*break*/, 15];
                        case 14:
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().b[70][14]++;
                          cov_1pdg1trbnj().s[201]++;
                          error_1 = _a.sent();
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().s[202]++;
                          console.error('Batch reaction operation failed:', error_1);
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().s[203]++;
                          results.push({
                            error: 'Operation failed',
                            operation: op
                          });
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().s[204]++;
                          return [3 /*break*/, 15];
                        case 15:
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().b[70][15]++;
                          cov_1pdg1trbnj().s[205]++;
                          _i++;
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().s[206]++;
                          return [3 /*break*/, 1];
                        case 16:
                          /* istanbul ignore next */
                          cov_1pdg1trbnj().b[70][16]++;
                          cov_1pdg1trbnj().s[207]++;
                          return [2 /*return*/];
                      }
                    });
                  });
                })];
              case 3:
                /* istanbul ignore next */
                cov_1pdg1trbnj().b[61][3]++;
                cov_1pdg1trbnj().s[208]++;
                // Process operations in transaction for consistency
                _b.sent();
                /* istanbul ignore next */
                cov_1pdg1trbnj().s[209]++;
                return [2 /*return*/, server_1.NextResponse.json({
                  success: true,
                  data: {
                    results: results
                  },
                  message: 'Batch operations completed'
                })];
            }
          });
        });
      })];
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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