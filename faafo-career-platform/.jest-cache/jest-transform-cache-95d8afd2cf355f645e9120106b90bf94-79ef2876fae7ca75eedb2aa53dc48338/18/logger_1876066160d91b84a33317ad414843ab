0202a0045b225efe2e97feca7474eeb8
"use strict";

/**
 * Centralized Logging Service
 * Provides structured logging with different levels and contexts
 */
/* istanbul ignore next */
function cov_26ot6ei5w7() {
  var path = "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/logger.ts";
  var hash = "2d34b1f2b3a45e31a7a97dab446503d659d6406d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/logger.ts",
    statementMap: {
      "0": {
        start: {
          line: 6,
          column: 15
        },
        end: {
          line: 16,
          column: 1
        }
      },
      "1": {
        start: {
          line: 7,
          column: 4
        },
        end: {
          line: 14,
          column: 6
        }
      },
      "2": {
        start: {
          line: 8,
          column: 8
        },
        end: {
          line: 12,
          column: 9
        }
      },
      "3": {
        start: {
          line: 8,
          column: 24
        },
        end: {
          line: 8,
          column: 25
        }
      },
      "4": {
        start: {
          line: 8,
          column: 31
        },
        end: {
          line: 8,
          column: 47
        }
      },
      "5": {
        start: {
          line: 9,
          column: 12
        },
        end: {
          line: 9,
          column: 29
        }
      },
      "6": {
        start: {
          line: 10,
          column: 12
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "7": {
        start: {
          line: 10,
          column: 29
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "8": {
        start: {
          line: 11,
          column: 16
        },
        end: {
          line: 11,
          column: 28
        }
      },
      "9": {
        start: {
          line: 13,
          column: 8
        },
        end: {
          line: 13,
          column: 17
        }
      },
      "10": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 43
        }
      },
      "11": {
        start: {
          line: 17,
          column: 0
        },
        end: {
          line: 17,
          column: 62
        }
      },
      "12": {
        start: {
          line: 18,
          column: 0
        },
        end: {
          line: 18,
          column: 55
        }
      },
      "13": {
        start: {
          line: 19,
          column: 28
        },
        end: {
          line: 246,
          column: 3
        }
      },
      "14": {
        start: {
          line: 21,
          column: 8
        },
        end: {
          line: 21,
          column: 66
        }
      },
      "15": {
        start: {
          line: 22,
          column: 8
        },
        end: {
          line: 22,
          column: 88
        }
      },
      "16": {
        start: {
          line: 23,
          column: 8
        },
        end: {
          line: 23,
          column: 26
        }
      },
      "17": {
        start: {
          line: 24,
          column: 8
        },
        end: {
          line: 29,
          column: 10
        }
      },
      "18": {
        start: {
          line: 34,
          column: 4
        },
        end: {
          line: 36,
          column: 6
        }
      },
      "19": {
        start: {
          line: 35,
          column: 8
        },
        end: {
          line: 35,
          column: 69
        }
      },
      "20": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 42,
          column: 6
        }
      },
      "21": {
        start: {
          line: 41,
          column: 8
        },
        end: {
          line: 41,
          column: 26
        }
      },
      "22": {
        start: {
          line: 46,
          column: 4
        },
        end: {
          line: 52,
          column: 6
        }
      },
      "23": {
        start: {
          line: 47,
          column: 26
        },
        end: {
          line: 47,
          column: 38
        }
      },
      "24": {
        start: {
          line: 48,
          column: 8
        },
        end: {
          line: 48,
          column: 76
        }
      },
      "25": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 49,
          column: 45
        }
      },
      "26": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 50,
          column: 53
        }
      },
      "27": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 51,
          column: 27
        }
      },
      "28": {
        start: {
          line: 56,
          column: 4
        },
        end: {
          line: 58,
          column: 6
        }
      },
      "29": {
        start: {
          line: 57,
          column: 8
        },
        end: {
          line: 57,
          column: 78
        }
      },
      "30": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 77,
          column: 6
        }
      },
      "31": {
        start: {
          line: 63,
          column: 8
        },
        end: {
          line: 76,
          column: 9
        }
      },
      "32": {
        start: {
          line: 65,
          column: 12
        },
        end: {
          line: 65,
          column: 41
        }
      },
      "33": {
        start: {
          line: 69,
          column: 28
        },
        end: {
          line: 69,
          column: 74
        }
      },
      "34": {
        start: {
          line: 70,
          column: 24
        },
        end: {
          line: 70,
          column: 59
        }
      },
      "35": {
        start: {
          line: 71,
          column: 26
        },
        end: {
          line: 74,
          column: 36
        }
      },
      "36": {
        start: {
          line: 72,
          column: 24
        },
        end: {
          line: 72,
          column: 29
        }
      },
      "37": {
        start: {
          line: 72,
          column: 35
        },
        end: {
          line: 72,
          column: 40
        }
      },
      "38": {
        start: {
          line: 73,
          column: 16
        },
        end: {
          line: 73,
          column: 51
        }
      },
      "39": {
        start: {
          line: 75,
          column: 12
        },
        end: {
          line: 75,
          column: 102
        }
      },
      "40": {
        start: {
          line: 81,
          column: 4
        },
        end: {
          line: 101,
          column: 6
        }
      },
      "41": {
        start: {
          line: 82,
          column: 24
        },
        end: {
          line: 82,
          column: 50
        }
      },
      "42": {
        start: {
          line: 83,
          column: 8
        },
        end: {
          line: 96,
          column: 9
        }
      },
      "43": {
        start: {
          line: 85,
          column: 16
        },
        end: {
          line: 85,
          column: 41
        }
      },
      "44": {
        start: {
          line: 86,
          column: 16
        },
        end: {
          line: 86,
          column: 22
        }
      },
      "45": {
        start: {
          line: 88,
          column: 16
        },
        end: {
          line: 88,
          column: 40
        }
      },
      "46": {
        start: {
          line: 89,
          column: 16
        },
        end: {
          line: 89,
          column: 22
        }
      },
      "47": {
        start: {
          line: 91,
          column: 16
        },
        end: {
          line: 91,
          column: 40
        }
      },
      "48": {
        start: {
          line: 92,
          column: 16
        },
        end: {
          line: 92,
          column: 22
        }
      },
      "49": {
        start: {
          line: 94,
          column: 16
        },
        end: {
          line: 94,
          column: 41
        }
      },
      "50": {
        start: {
          line: 95,
          column: 16
        },
        end: {
          line: 95,
          column: 22
        }
      },
      "51": {
        start: {
          line: 98,
          column: 8
        },
        end: {
          line: 100,
          column: 9
        }
      },
      "52": {
        start: {
          line: 99,
          column: 12
        },
        end: {
          line: 99,
          column: 46
        }
      },
      "53": {
        start: {
          line: 105,
          column: 4
        },
        end: {
          line: 133,
          column: 6
        }
      },
      "54": {
        start: {
          line: 109,
          column: 8
        },
        end: {
          line: 132,
          column: 9
        }
      },
      "55": {
        start: {
          line: 110,
          column: 12
        },
        end: {
          line: 127,
          column: 13
        }
      },
      "56": {
        start: {
          line: 111,
          column: 28
        },
        end: {
          line: 111,
          column: 58
        }
      },
      "57": {
        start: {
          line: 112,
          column: 16
        },
        end: {
          line: 112,
          column: 46
        }
      },
      "58": {
        start: {
          line: 113,
          column: 16
        },
        end: {
          line: 113,
          column: 48
        }
      },
      "59": {
        start: {
          line: 115,
          column: 16
        },
        end: {
          line: 126,
          column: 17
        }
      },
      "60": {
        start: {
          line: 116,
          column: 20
        },
        end: {
          line: 125,
          column: 23
        }
      },
      "61": {
        start: {
          line: 131,
          column: 12
        },
        end: {
          line: 131,
          column: 76
        }
      },
      "62": {
        start: {
          line: 137,
          column: 4
        },
        end: {
          line: 146,
          column: 6
        }
      },
      "63": {
        start: {
          line: 138,
          column: 8
        },
        end: {
          line: 139,
          column: 19
        }
      },
      "64": {
        start: {
          line: 139,
          column: 12
        },
        end: {
          line: 139,
          column: 19
        }
      },
      "65": {
        start: {
          line: 140,
          column: 8
        },
        end: {
          line: 145,
          column: 11
        }
      },
      "66": {
        start: {
          line: 150,
          column: 4
        },
        end: {
          line: 159,
          column: 6
        }
      },
      "67": {
        start: {
          line: 151,
          column: 8
        },
        end: {
          line: 152,
          column: 19
        }
      },
      "68": {
        start: {
          line: 152,
          column: 12
        },
        end: {
          line: 152,
          column: 19
        }
      },
      "69": {
        start: {
          line: 153,
          column: 8
        },
        end: {
          line: 158,
          column: 11
        }
      },
      "70": {
        start: {
          line: 163,
          column: 4
        },
        end: {
          line: 172,
          column: 6
        }
      },
      "71": {
        start: {
          line: 164,
          column: 8
        },
        end: {
          line: 165,
          column: 19
        }
      },
      "72": {
        start: {
          line: 165,
          column: 12
        },
        end: {
          line: 165,
          column: 19
        }
      },
      "73": {
        start: {
          line: 166,
          column: 8
        },
        end: {
          line: 171,
          column: 11
        }
      },
      "74": {
        start: {
          line: 176,
          column: 4
        },
        end: {
          line: 190,
          column: 6
        }
      },
      "75": {
        start: {
          line: 177,
          column: 8
        },
        end: {
          line: 178,
          column: 19
        }
      },
      "76": {
        start: {
          line: 178,
          column: 12
        },
        end: {
          line: 178,
          column: 19
        }
      },
      "77": {
        start: {
          line: 179,
          column: 8
        },
        end: {
          line: 189,
          column: 11
        }
      },
      "78": {
        start: {
          line: 194,
          column: 4
        },
        end: {
          line: 207,
          column: 6
        }
      },
      "79": {
        start: {
          line: 195,
          column: 20
        },
        end: {
          line: 195,
          column: 85
        }
      },
      "80": {
        start: {
          line: 196,
          column: 8
        },
        end: {
          line: 206,
          column: 11
        }
      },
      "81": {
        start: {
          line: 211,
          column: 4
        },
        end: {
          line: 217,
          column: 6
        }
      },
      "82": {
        start: {
          line: 212,
          column: 8
        },
        end: {
          line: 216,
          column: 18
        }
      },
      "83": {
        start: {
          line: 221,
          column: 4
        },
        end: {
          line: 233,
          column: 6
        }
      },
      "84": {
        start: {
          line: 222,
          column: 8
        },
        end: {
          line: 222,
          column: 51
        }
      },
      "85": {
        start: {
          line: 222,
          column: 34
        },
        end: {
          line: 222,
          column: 49
        }
      },
      "86": {
        start: {
          line: 223,
          column: 20
        },
        end: {
          line: 223,
          column: 45
        }
      },
      "87": {
        start: {
          line: 224,
          column: 8
        },
        end: {
          line: 232,
          column: 11
        }
      },
      "88": {
        start: {
          line: 237,
          column: 4
        },
        end: {
          line: 244,
          column: 6
        }
      },
      "89": {
        start: {
          line: 238,
          column: 8
        },
        end: {
          line: 238,
          column: 45
        }
      },
      "90": {
        start: {
          line: 238,
          column: 31
        },
        end: {
          line: 238,
          column: 43
        }
      },
      "91": {
        start: {
          line: 239,
          column: 8
        },
        end: {
          line: 243,
          column: 18
        }
      },
      "92": {
        start: {
          line: 245,
          column: 4
        },
        end: {
          line: 245,
          column: 18
        }
      },
      "93": {
        start: {
          line: 247,
          column: 0
        },
        end: {
          line: 247,
          column: 24
        }
      },
      "94": {
        start: {
          line: 249,
          column: 13
        },
        end: {
          line: 249,
          column: 25
        }
      },
      "95": {
        start: {
          line: 250,
          column: 0
        },
        end: {
          line: 250,
          column: 24
        }
      },
      "96": {
        start: {
          line: 252,
          column: 0
        },
        end: {
          line: 269,
          column: 2
        }
      },
      "97": {
        start: {
          line: 253,
          column: 41
        },
        end: {
          line: 253,
          column: 79
        }
      },
      "98": {
        start: {
          line: 254,
          column: 40
        },
        end: {
          line: 254,
          column: 77
        }
      },
      "99": {
        start: {
          line: 255,
          column: 40
        },
        end: {
          line: 255,
          column: 77
        }
      },
      "100": {
        start: {
          line: 256,
          column: 48
        },
        end: {
          line: 256,
          column: 93
        }
      },
      "101": {
        start: {
          line: 258,
          column: 8
        },
        end: {
          line: 258,
          column: 70
        }
      },
      "102": {
        start: {
          line: 261,
          column: 8
        },
        end: {
          line: 261,
          column: 68
        }
      },
      "103": {
        start: {
          line: 264,
          column: 8
        },
        end: {
          line: 264,
          column: 60
        }
      },
      "104": {
        start: {
          line: 267,
          column: 8
        },
        end: {
          line: 267,
          column: 64
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 6,
            column: 42
          },
          end: {
            line: 6,
            column: 43
          }
        },
        loc: {
          start: {
            line: 6,
            column: 54
          },
          end: {
            line: 16,
            column: 1
          }
        },
        line: 6
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 7,
            column: 33
          }
        },
        loc: {
          start: {
            line: 7,
            column: 44
          },
          end: {
            line: 14,
            column: 5
          }
        },
        line: 7
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 19,
            column: 28
          },
          end: {
            line: 19,
            column: 29
          }
        },
        loc: {
          start: {
            line: 19,
            column: 40
          },
          end: {
            line: 246,
            column: 1
          }
        },
        line: 19
      },
      "3": {
        name: "Logger",
        decl: {
          start: {
            line: 20,
            column: 13
          },
          end: {
            line: 20,
            column: 19
          }
        },
        loc: {
          start: {
            line: 20,
            column: 22
          },
          end: {
            line: 30,
            column: 5
          }
        },
        line: 20
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 34,
            column: 34
          },
          end: {
            line: 34,
            column: 35
          }
        },
        loc: {
          start: {
            line: 34,
            column: 53
          },
          end: {
            line: 36,
            column: 5
          }
        },
        line: 34
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 40,
            column: 36
          },
          end: {
            line: 40,
            column: 37
          }
        },
        loc: {
          start: {
            line: 40,
            column: 48
          },
          end: {
            line: 42,
            column: 5
          }
        },
        line: 40
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 46,
            column: 29
          },
          end: {
            line: 46,
            column: 30
          }
        },
        loc: {
          start: {
            line: 46,
            column: 48
          },
          end: {
            line: 52,
            column: 5
          }
        },
        line: 46
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 56,
            column: 33
          },
          end: {
            line: 56,
            column: 34
          }
        },
        loc: {
          start: {
            line: 56,
            column: 50
          },
          end: {
            line: 58,
            column: 5
          }
        },
        line: 56
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 62,
            column: 38
          },
          end: {
            line: 62,
            column: 39
          }
        },
        loc: {
          start: {
            line: 62,
            column: 55
          },
          end: {
            line: 77,
            column: 5
          }
        },
        line: 62
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 71,
            column: 88
          },
          end: {
            line: 71,
            column: 89
          }
        },
        loc: {
          start: {
            line: 71,
            column: 102
          },
          end: {
            line: 74,
            column: 13
          }
        },
        line: 71
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 81,
            column: 32
          },
          end: {
            line: 81,
            column: 33
          }
        },
        loc: {
          start: {
            line: 81,
            column: 49
          },
          end: {
            line: 101,
            column: 5
          }
        },
        line: 81
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 105,
            column: 45
          },
          end: {
            line: 105,
            column: 46
          }
        },
        loc: {
          start: {
            line: 105,
            column: 62
          },
          end: {
            line: 133,
            column: 5
          }
        },
        line: 105
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 137,
            column: 29
          },
          end: {
            line: 137,
            column: 30
          }
        },
        loc: {
          start: {
            line: 137,
            column: 57
          },
          end: {
            line: 146,
            column: 5
          }
        },
        line: 137
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 150,
            column: 28
          },
          end: {
            line: 150,
            column: 29
          }
        },
        loc: {
          start: {
            line: 150,
            column: 56
          },
          end: {
            line: 159,
            column: 5
          }
        },
        line: 150
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 163,
            column: 28
          },
          end: {
            line: 163,
            column: 29
          }
        },
        loc: {
          start: {
            line: 163,
            column: 56
          },
          end: {
            line: 172,
            column: 5
          }
        },
        line: 163
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 176,
            column: 29
          },
          end: {
            line: 176,
            column: 30
          }
        },
        loc: {
          start: {
            line: 176,
            column: 64
          },
          end: {
            line: 190,
            column: 5
          }
        },
        line: 176
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 194,
            column: 27
          },
          end: {
            line: 194,
            column: 28
          }
        },
        loc: {
          start: {
            line: 194,
            column: 81
          },
          end: {
            line: 207,
            column: 5
          }
        },
        line: 194
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 211,
            column: 32
          },
          end: {
            line: 211,
            column: 33
          }
        },
        loc: {
          start: {
            line: 211,
            column: 79
          },
          end: {
            line: 217,
            column: 5
          }
        },
        line: 211
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 221,
            column: 28
          },
          end: {
            line: 221,
            column: 29
          }
        },
        loc: {
          start: {
            line: 221,
            column: 71
          },
          end: {
            line: 233,
            column: 5
          }
        },
        line: 221
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 237,
            column: 35
          },
          end: {
            line: 237,
            column: 36
          }
        },
        loc: {
          start: {
            line: 237,
            column: 75
          },
          end: {
            line: 244,
            column: 5
          }
        },
        line: 237
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 253,
            column: 11
          },
          end: {
            line: 253,
            column: 12
          }
        },
        loc: {
          start: {
            line: 253,
            column: 39
          },
          end: {
            line: 253,
            column: 81
          }
        },
        line: 253
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 254,
            column: 10
          },
          end: {
            line: 254,
            column: 11
          }
        },
        loc: {
          start: {
            line: 254,
            column: 38
          },
          end: {
            line: 254,
            column: 79
          }
        },
        line: 254
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 255,
            column: 10
          },
          end: {
            line: 255,
            column: 11
          }
        },
        loc: {
          start: {
            line: 255,
            column: 38
          },
          end: {
            line: 255,
            column: 79
          }
        },
        line: 255
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 256,
            column: 11
          },
          end: {
            line: 256,
            column: 12
          }
        },
        loc: {
          start: {
            line: 256,
            column: 46
          },
          end: {
            line: 256,
            column: 95
          }
        },
        line: 256
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 257,
            column: 9
          },
          end: {
            line: 257,
            column: 10
          }
        },
        loc: {
          start: {
            line: 257,
            column: 63
          },
          end: {
            line: 259,
            column: 5
          }
        },
        line: 257
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 260,
            column: 14
          },
          end: {
            line: 260,
            column: 15
          }
        },
        loc: {
          start: {
            line: 260,
            column: 61
          },
          end: {
            line: 262,
            column: 5
          }
        },
        line: 260
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 263,
            column: 10
          },
          end: {
            line: 263,
            column: 11
          }
        },
        loc: {
          start: {
            line: 263,
            column: 53
          },
          end: {
            line: 265,
            column: 5
          }
        },
        line: 263
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 266,
            column: 17
          },
          end: {
            line: 266,
            column: 18
          }
        },
        loc: {
          start: {
            line: 266,
            column: 57
          },
          end: {
            line: 268,
            column: 5
          }
        },
        line: 266
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 15
          },
          end: {
            line: 16,
            column: 1
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 16
          },
          end: {
            line: 6,
            column: 20
          }
        }, {
          start: {
            line: 6,
            column: 24
          },
          end: {
            line: 6,
            column: 37
          }
        }, {
          start: {
            line: 6,
            column: 42
          },
          end: {
            line: 16,
            column: 1
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 14,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 7,
            column: 15
          },
          end: {
            line: 7,
            column: 28
          }
        }, {
          start: {
            line: 7,
            column: 32
          },
          end: {
            line: 14,
            column: 5
          }
        }],
        line: 7
      },
      "2": {
        loc: {
          start: {
            line: 10,
            column: 29
          },
          end: {
            line: 11,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 10,
            column: 29
          },
          end: {
            line: 11,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 10
      },
      "3": {
        loc: {
          start: {
            line: 22,
            column: 24
          },
          end: {
            line: 22,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 22,
            column: 24
          },
          end: {
            line: 22,
            column: 45
          }
        }, {
          start: {
            line: 22,
            column: 50
          },
          end: {
            line: 22,
            column: 86
          }
        }],
        line: 22
      },
      "4": {
        loc: {
          start: {
            line: 22,
            column: 50
          },
          end: {
            line: 22,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 22,
            column: 70
          },
          end: {
            line: 22,
            column: 76
          }
        }, {
          start: {
            line: 22,
            column: 79
          },
          end: {
            line: 22,
            column: 86
          }
        }],
        line: 22
      },
      "5": {
        loc: {
          start: {
            line: 63,
            column: 8
          },
          end: {
            line: 76,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 63,
            column: 8
          },
          end: {
            line: 76,
            column: 9
          }
        }, {
          start: {
            line: 67,
            column: 13
          },
          end: {
            line: 76,
            column: 9
          }
        }],
        line: 63
      },
      "6": {
        loc: {
          start: {
            line: 71,
            column: 26
          },
          end: {
            line: 74,
            column: 36
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 71,
            column: 42
          },
          end: {
            line: 74,
            column: 31
          }
        }, {
          start: {
            line: 74,
            column: 34
          },
          end: {
            line: 74,
            column: 36
          }
        }],
        line: 71
      },
      "7": {
        loc: {
          start: {
            line: 83,
            column: 8
          },
          end: {
            line: 96,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 84,
            column: 12
          },
          end: {
            line: 86,
            column: 22
          }
        }, {
          start: {
            line: 87,
            column: 12
          },
          end: {
            line: 89,
            column: 22
          }
        }, {
          start: {
            line: 90,
            column: 12
          },
          end: {
            line: 92,
            column: 22
          }
        }, {
          start: {
            line: 93,
            column: 12
          },
          end: {
            line: 95,
            column: 22
          }
        }],
        line: 83
      },
      "8": {
        loc: {
          start: {
            line: 98,
            column: 8
          },
          end: {
            line: 100,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 98,
            column: 8
          },
          end: {
            line: 100,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 98
      },
      "9": {
        loc: {
          start: {
            line: 98,
            column: 12
          },
          end: {
            line: 98,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 98,
            column: 12
          },
          end: {
            line: 98,
            column: 29
          }
        }, {
          start: {
            line: 98,
            column: 33
          },
          end: {
            line: 98,
            column: 56
          }
        }],
        line: 98
      },
      "10": {
        loc: {
          start: {
            line: 110,
            column: 12
          },
          end: {
            line: 127,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 110,
            column: 12
          },
          end: {
            line: 127,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 110
      },
      "11": {
        loc: {
          start: {
            line: 115,
            column: 16
          },
          end: {
            line: 126,
            column: 17
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 115,
            column: 16
          },
          end: {
            line: 126,
            column: 17
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 115
      },
      "12": {
        loc: {
          start: {
            line: 115,
            column: 20
          },
          end: {
            line: 115,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 115,
            column: 20
          },
          end: {
            line: 115,
            column: 49
          }
        }, {
          start: {
            line: 115,
            column: 53
          },
          end: {
            line: 115,
            column: 66
          }
        }],
        line: 115
      },
      "13": {
        loc: {
          start: {
            line: 123,
            column: 39
          },
          end: {
            line: 123,
            column: 124
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 123,
            column: 40
          },
          end: {
            line: 123,
            column: 110
          }
        }, {
          start: {
            line: 123,
            column: 115
          },
          end: {
            line: 123,
            column: 124
          }
        }],
        line: 123
      },
      "14": {
        loc: {
          start: {
            line: 123,
            column: 40
          },
          end: {
            line: 123,
            column: 110
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 123,
            column: 89
          },
          end: {
            line: 123,
            column: 95
          }
        }, {
          start: {
            line: 123,
            column: 98
          },
          end: {
            line: 123,
            column: 110
          }
        }],
        line: 123
      },
      "15": {
        loc: {
          start: {
            line: 123,
            column: 40
          },
          end: {
            line: 123,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 123,
            column: 40
          },
          end: {
            line: 123,
            column: 69
          }
        }, {
          start: {
            line: 123,
            column: 73
          },
          end: {
            line: 123,
            column: 86
          }
        }],
        line: 123
      },
      "16": {
        loc: {
          start: {
            line: 138,
            column: 8
          },
          end: {
            line: 139,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 138,
            column: 8
          },
          end: {
            line: 139,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 138
      },
      "17": {
        loc: {
          start: {
            line: 151,
            column: 8
          },
          end: {
            line: 152,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 151,
            column: 8
          },
          end: {
            line: 152,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 151
      },
      "18": {
        loc: {
          start: {
            line: 164,
            column: 8
          },
          end: {
            line: 165,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 164,
            column: 8
          },
          end: {
            line: 165,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 164
      },
      "19": {
        loc: {
          start: {
            line: 177,
            column: 8
          },
          end: {
            line: 178,
            column: 19
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 177,
            column: 8
          },
          end: {
            line: 178,
            column: 19
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 177
      },
      "20": {
        loc: {
          start: {
            line: 184,
            column: 19
          },
          end: {
            line: 188,
            column: 25
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 184,
            column: 27
          },
          end: {
            line: 188,
            column: 13
          }
        }, {
          start: {
            line: 188,
            column: 16
          },
          end: {
            line: 188,
            column: 25
          }
        }],
        line: 184
      },
      "21": {
        loc: {
          start: {
            line: 195,
            column: 20
          },
          end: {
            line: 195,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 195,
            column: 40
          },
          end: {
            line: 195,
            column: 47
          }
        }, {
          start: {
            line: 195,
            column: 50
          },
          end: {
            line: 195,
            column: 85
          }
        }],
        line: 195
      },
      "22": {
        loc: {
          start: {
            line: 195,
            column: 50
          },
          end: {
            line: 195,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 195,
            column: 70
          },
          end: {
            line: 195,
            column: 76
          }
        }, {
          start: {
            line: 195,
            column: 79
          },
          end: {
            line: 195,
            column: 85
          }
        }],
        line: 195
      },
      "23": {
        loc: {
          start: {
            line: 222,
            column: 8
          },
          end: {
            line: 222,
            column: 51
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 222,
            column: 8
          },
          end: {
            line: 222,
            column: 51
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 222
      },
      "24": {
        loc: {
          start: {
            line: 223,
            column: 20
          },
          end: {
            line: 223,
            column: 45
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 223,
            column: 30
          },
          end: {
            line: 223,
            column: 36
          }
        }, {
          start: {
            line: 223,
            column: 39
          },
          end: {
            line: 223,
            column: 45
          }
        }],
        line: 223
      },
      "25": {
        loc: {
          start: {
            line: 227,
            column: 55
          },
          end: {
            line: 227,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 227,
            column: 65
          },
          end: {
            line: 227,
            column: 74
          }
        }, {
          start: {
            line: 227,
            column: 77
          },
          end: {
            line: 227,
            column: 85
          }
        }],
        line: 227
      },
      "26": {
        loc: {
          start: {
            line: 227,
            column: 94
          },
          end: {
            line: 227,
            column: 135
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 227,
            column: 103
          },
          end: {
            line: 227,
            column: 130
          }
        }, {
          start: {
            line: 227,
            column: 133
          },
          end: {
            line: 227,
            column: 135
          }
        }],
        line: 227
      },
      "27": {
        loc: {
          start: {
            line: 238,
            column: 8
          },
          end: {
            line: 238,
            column: 45
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 238,
            column: 8
          },
          end: {
            line: 238,
            column: 45
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 238
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0
    },
    b: {
      "0": [0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0, 0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0]
    },
    inputSourceMap: {
      file: "/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/logger.ts",
      mappings: ";AAAA;;;GAGG;;;;;;;;;;;;;;AAyBH;IAAA;QACU,iBAAY,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC;QACrD,aAAQ,GAAc,OAAO,CAAC,GAAG,CAAC,SAAsB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACnG,YAAO,GAAe,EAAE,CAAC;QAEzB,kBAAa,GAA6B;YAChD,KAAK,EAAE,CAAC;YACR,IAAI,EAAE,CAAC;YACP,IAAI,EAAE,CAAC;YACP,KAAK,EAAE,CAAC;SACT,CAAC;IAkPJ,CAAC;IAhPC;;OAEG;IACH,2BAAU,GAAV,UAAW,OAA4B;QACrC,IAAI,CAAC,OAAO,yBAAQ,IAAI,CAAC,OAAO,GAAK,OAAO,CAAE,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,6BAAY,GAAZ;QACE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,sBAAK,GAAL,UAAM,OAA4B;QAChC,IAAM,WAAW,GAAG,IAAI,MAAM,EAAE,CAAC;QACjC,WAAW,CAAC,OAAO,yBAAQ,IAAI,CAAC,OAAO,GAAK,OAAO,CAAE,CAAC;QACtD,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QACrC,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QAC7C,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,0BAAS,GAAjB,UAAkB,KAAe;QAC/B,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACK,+BAAc,GAAtB,UAAuB,KAAe;QACpC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACtB,yCAAyC;YACzC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,yCAAyC;YACzC,IAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,kBAAkB,EAAE,CAAC;YACjE,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAClD,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,YAAK,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,UAAC,EAAM;oBAAL,CAAC,QAAA,EAAE,CAAC,QAAA;gBAAM,OAAA,UAAG,CAAC,cAAI,CAAC,CAAE;YAAX,CAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAG,CAAC,CAAC,CAAC,EAAE,CAAC;YACnH,OAAO,UAAG,SAAS,cAAI,KAAK,cAAI,KAAK,CAAC,OAAO,SAAG,OAAO,CAAE,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,yBAAQ,GAAhB,UAAiB,KAAe;QAC9B,IAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;QAE7C,QAAQ,KAAK,CAAC,KAAK,EAAE,CAAC;YACpB,KAAK,OAAO;gBACV,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBACzB,MAAM;YACR,KAAK,MAAM;gBACT,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM;YACR,KAAK,MAAM;gBACT,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACxB,MAAM;YACR,KAAK,OAAO;gBACV,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;gBACzB,MAAM;QACV,CAAC;QAED,uDAAuD;QACvD,IAAI,IAAI,CAAC,YAAY,IAAI,KAAK,CAAC,KAAK,KAAK,OAAO,EAAE,CAAC;YACjD,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,sCAAqB,GAA7B,UAA8B,KAAe;;QAC3C,0DAA0D;QAC1D,uCAAuC;QACvC,IAAI,CAAC;YACH,IAAI,KAAK,CAAC,KAAK,EAAE,CAAC;gBAChB,IAAM,KAAK,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBAC7C,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC9B,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;gBAEhC,2CAA2C;gBAC3C,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;oBACnD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE;wBACpC,KAAK,EAAE;4BACL,QAAQ,EAAE,KAAK;4BACf,OAAO,EAAE,KAAK,CAAC,OAAO;yBACvB;wBACD,IAAI,EAAE;4BACJ,MAAM,EAAE,QAAQ;4BAChB,SAAS,EAAE,CAAA,MAAA,KAAK,CAAC,OAAO,0CAAE,SAAS,KAAI,SAAS;yBACjD;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,4DAA4D;YAC5D,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,sBAAK,GAAL,UAAM,OAAe,EAAE,OAA6B;QAClD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAAE,OAAO;QAErC,IAAI,CAAC,QAAQ,CAAC;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,KAAK,EAAE,OAAO;YACd,OAAO,SAAA;YACP,OAAO,wBAAO,IAAI,CAAC,OAAO,GAAK,OAAO,CAAE;SACzC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,qBAAI,GAAJ,UAAK,OAAe,EAAE,OAA6B;QACjD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;YAAE,OAAO;QAEpC,IAAI,CAAC,QAAQ,CAAC;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,KAAK,EAAE,MAAM;YACb,OAAO,SAAA;YACP,OAAO,wBAAO,IAAI,CAAC,OAAO,GAAK,OAAO,CAAE;SACzC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,qBAAI,GAAJ,UAAK,OAAe,EAAE,OAA6B;QACjD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;YAAE,OAAO;QAEpC,IAAI,CAAC,QAAQ,CAAC;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,KAAK,EAAE,MAAM;YACb,OAAO,SAAA;YACP,OAAO,wBAAO,IAAI,CAAC,OAAO,GAAK,OAAO,CAAE;SACzC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,sBAAK,GAAL,UAAM,OAAe,EAAE,KAAa,EAAE,OAA6B;QACjE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;YAAE,OAAO;QAErC,IAAI,CAAC,QAAQ,CAAC;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,KAAK,EAAE,OAAO;YACd,OAAO,SAAA;YACP,OAAO,wBAAO,IAAI,CAAC,OAAO,GAAK,OAAO,CAAE;YACxC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;gBACb,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC,CAAC,CAAC,SAAS;SACd,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,oBAAG,GAAH,UAAI,MAAc,EAAE,GAAW,EAAE,UAAkB,EAAE,QAAgB,EAAE,OAA6B;QAClG,IAAM,KAAK,GAAa,UAAU,IAAI,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;QAE1F,IAAI,CAAC,QAAQ,CAAC;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,KAAK,OAAA;YACL,OAAO,EAAE,UAAG,MAAM,cAAI,GAAG,cAAI,UAAU,cAAI,QAAQ,OAAI;YACvD,OAAO,iCACF,IAAI,CAAC,OAAO,GACZ,OAAO,KACV,QAAQ,EAAE;oBACR,MAAM,QAAA;oBACN,GAAG,KAAA;oBACH,UAAU,EAAE,UAAU,CAAC,QAAQ,EAAE;oBACjC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE;iBAC9B,GACF;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,yBAAQ,GAAR,UAAS,SAAiB,EAAE,KAAa,EAAE,QAAgB,EAAE,OAA6B;QACxF,IAAI,CAAC,KAAK,CAAC,aAAM,SAAS,cAAI,KAAK,cAAI,QAAQ,OAAI,wBAC9C,OAAO,KACV,QAAQ,EAAE;gBACR,SAAS,WAAA;gBACT,KAAK,OAAA;gBACL,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE;aAC9B,IACD,CAAC;IACL,CAAC;IAED;;OAEG;IACH,qBAAI,GAAJ,UAAK,KAAa,EAAE,MAAe,EAAE,OAAuB,EAAE,OAA6B;QAAtD,wBAAA,EAAA,cAAuB;QAC1D,IAAM,KAAK,GAAa,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC;QAElD,IAAI,CAAC,QAAQ,CAAC;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,KAAK,OAAA;YACL,OAAO,EAAE,eAAQ,KAAK,cAAI,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,SAAG,MAAM,CAAC,CAAC,CAAC,oBAAa,MAAM,CAAE,CAAC,CAAC,CAAC,EAAE,CAAE;YAChG,OAAO,iCACF,IAAI,CAAC,OAAO,GACZ,OAAO,KACV,MAAM,QAAA,EACN,QAAQ,EAAE;oBACR,KAAK,OAAA;oBACL,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;iBAC5B,GACF;SACF,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,4BAAW,GAAX,UAAY,MAAc,EAAE,KAAa,EAAE,IAAmB,EAAE,OAA6B;QAAlD,qBAAA,EAAA,WAAmB;QAC5D,IAAI,CAAC,IAAI,CAAC,sBAAe,MAAM,eAAK,KAAK,SAAG,IAAI,CAAE,wBAC7C,OAAO,KACV,QAAQ,EAAE;gBACR,MAAM,QAAA;gBACN,KAAK,EAAE,KAAK,CAAC,QAAQ,EAAE;gBACvB,IAAI,MAAA;aACL,IACD,CAAC;IACL,CAAC;IACH,aAAC;AAAD,CAAC,AA5PD,IA4PC;AAqBgB,wBAAM;AAnBvB,mCAAmC;AACnC,IAAM,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;AAkBnB,wBAAM;AAhBf,oDAAoD;AACvC,QAAA,GAAG,GAAG;IACjB,KAAK,EAAE,UAAC,OAAe,EAAE,OAA6B,IAAK,OAAA,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,EAA9B,CAA8B;IACzF,IAAI,EAAE,UAAC,OAAe,EAAE,OAA6B,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,EAA7B,CAA6B;IACvF,IAAI,EAAE,UAAC,OAAe,EAAE,OAA6B,IAAK,OAAA,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,EAA7B,CAA6B;IACvF,KAAK,EAAE,UAAC,OAAe,EAAE,KAAa,EAAE,OAA6B,IAAK,OAAA,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,CAAC,EAArC,CAAqC;IAC/G,GAAG,EAAE,UAAC,MAAc,EAAE,GAAW,EAAE,UAAkB,EAAE,QAAgB,EAAE,OAA6B;QACpG,OAAA,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,CAAC;IAAtD,CAAsD;IACxD,QAAQ,EAAE,UAAC,SAAiB,EAAE,KAAa,EAAE,QAAgB,EAAE,OAA6B;QAC1F,OAAA,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,CAAC;IAApD,CAAoD;IACtD,IAAI,EAAE,UAAC,KAAa,EAAE,MAAe,EAAE,OAAiB,EAAE,OAA6B;QACrF,OAAA,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;IAA5C,CAA4C;IAC9C,WAAW,EAAE,UAAC,MAAc,EAAE,KAAa,EAAE,IAAa,EAAE,OAA6B;QACvF,OAAA,MAAM,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,CAAC;IAAhD,CAAgD;CACnD,CAAC",
      names: [],
      sources: ["/Users/<USER>/faafo/faafo/faafo-career-platform/src/lib/logger.ts"],
      sourcesContent: ["/**\n * Centralized Logging Service\n * Provides structured logging with different levels and contexts\n */\n\ntype LogLevel = 'debug' | 'info' | 'warn' | 'error';\n\ninterface LogContext {\n  userId?: string;\n  sessionId?: string;\n  requestId?: string;\n  component?: string;\n  action?: string;\n  metadata?: Record<string, any>;\n}\n\ninterface LogEntry {\n  timestamp: string;\n  level: LogLevel;\n  message: string;\n  context?: LogContext;\n  error?: {\n    name: string;\n    message: string;\n    stack?: string;\n  };\n}\n\nclass Logger {\n  private isProduction = process.env.NODE_ENV === 'production';\n  private logLevel: LogLevel = (process.env.LOG_LEVEL as LogLevel) || (this.isProduction ? 'warn' : 'debug');\n  private context: LogContext = {};\n\n  private levelPriority: Record<LogLevel, number> = {\n    debug: 0,\n    info: 1,\n    warn: 2,\n    error: 3\n  };\n\n  /**\n   * Set global context for all log entries\n   */\n  setContext(context: Partial<LogContext>): void {\n    this.context = { ...this.context, ...context };\n  }\n\n  /**\n   * Clear global context\n   */\n  clearContext(): void {\n    this.context = {};\n  }\n\n  /**\n   * Create a child logger with additional context\n   */\n  child(context: Partial<LogContext>): Logger {\n    const childLogger = new Logger();\n    childLogger.context = { ...this.context, ...context };\n    childLogger.logLevel = this.logLevel;\n    childLogger.isProduction = this.isProduction;\n    return childLogger;\n  }\n\n  /**\n   * Check if a log level should be logged\n   */\n  private shouldLog(level: LogLevel): boolean {\n    return this.levelPriority[level] >= this.levelPriority[this.logLevel];\n  }\n\n  /**\n   * Format log entry for output\n   */\n  private formatLogEntry(entry: LogEntry): string {\n    if (this.isProduction) {\n      // Structured JSON logging for production\n      return JSON.stringify(entry);\n    } else {\n      // Human-readable logging for development\n      const timestamp = new Date(entry.timestamp).toLocaleTimeString();\n      const level = entry.level.toUpperCase().padEnd(5);\n      const context = entry.context ? ` [${Object.entries(entry.context).map(([k, v]) => `${k}=${v}`).join(', ')}]` : '';\n      return `${timestamp} ${level} ${entry.message}${context}`;\n    }\n  }\n\n  /**\n   * Write log entry to appropriate output\n   */\n  private writeLog(entry: LogEntry): void {\n    const formatted = this.formatLogEntry(entry);\n\n    switch (entry.level) {\n      case 'debug':\n        console.debug(formatted);\n        break;\n      case 'info':\n        console.info(formatted);\n        break;\n      case 'warn':\n        console.warn(formatted);\n        break;\n      case 'error':\n        console.error(formatted);\n        break;\n    }\n\n    // In production, also send to external logging service\n    if (this.isProduction && entry.level === 'error') {\n      this.sendToExternalService(entry);\n    }\n  }\n\n  /**\n   * Send error logs to external service (e.g., Sentry, DataDog)\n   */\n  private sendToExternalService(entry: LogEntry): void {\n    // This would integrate with your external logging service\n    // For now, we'll use the error tracker\n    try {\n      if (entry.error) {\n        const error = new Error(entry.error.message);\n        error.name = entry.error.name;\n        error.stack = entry.error.stack;\n\n        // Use the error tracker we created earlier\n        if (typeof window !== 'undefined' && window.Sentry) {\n          window.Sentry.captureException(error, {\n            extra: {\n              logEntry: entry,\n              context: entry.context\n            },\n            tags: {\n              source: 'logger',\n              component: entry.context?.component || 'unknown'\n            }\n          });\n        }\n      }\n    } catch (error) {\n      // Fallback - don't let logging errors break the application\n      console.error('Failed to send log to external service:', error);\n    }\n  }\n\n  /**\n   * Log debug message\n   */\n  debug(message: string, context?: Partial<LogContext>): void {\n    if (!this.shouldLog('debug')) return;\n\n    this.writeLog({\n      timestamp: new Date().toISOString(),\n      level: 'debug',\n      message,\n      context: { ...this.context, ...context }\n    });\n  }\n\n  /**\n   * Log info message\n   */\n  info(message: string, context?: Partial<LogContext>): void {\n    if (!this.shouldLog('info')) return;\n\n    this.writeLog({\n      timestamp: new Date().toISOString(),\n      level: 'info',\n      message,\n      context: { ...this.context, ...context }\n    });\n  }\n\n  /**\n   * Log warning message\n   */\n  warn(message: string, context?: Partial<LogContext>): void {\n    if (!this.shouldLog('warn')) return;\n\n    this.writeLog({\n      timestamp: new Date().toISOString(),\n      level: 'warn',\n      message,\n      context: { ...this.context, ...context }\n    });\n  }\n\n  /**\n   * Log error message\n   */\n  error(message: string, error?: Error, context?: Partial<LogContext>): void {\n    if (!this.shouldLog('error')) return;\n\n    this.writeLog({\n      timestamp: new Date().toISOString(),\n      level: 'error',\n      message,\n      context: { ...this.context, ...context },\n      error: error ? {\n        name: error.name,\n        message: error.message,\n        stack: error.stack\n      } : undefined\n    });\n  }\n\n  /**\n   * Log API request/response\n   */\n  api(method: string, url: string, statusCode: number, duration: number, context?: Partial<LogContext>): void {\n    const level: LogLevel = statusCode >= 500 ? 'error' : statusCode >= 400 ? 'warn' : 'info';\n    \n    this.writeLog({\n      timestamp: new Date().toISOString(),\n      level,\n      message: `${method} ${url} ${statusCode} ${duration}ms`,\n      context: {\n        ...this.context,\n        ...context,\n        metadata: {\n          method,\n          url,\n          statusCode: statusCode.toString(),\n          duration: duration.toString()\n        }\n      }\n    });\n  }\n\n  /**\n   * Log database operation\n   */\n  database(operation: string, table: string, duration: number, context?: Partial<LogContext>): void {\n    this.debug(`DB ${operation} ${table} ${duration}ms`, {\n      ...context,\n      metadata: {\n        operation,\n        table,\n        duration: duration.toString()\n      }\n    });\n  }\n\n  /**\n   * Log authentication event\n   */\n  auth(event: string, userId?: string, success: boolean = true, context?: Partial<LogContext>): void {\n    const level: LogLevel = success ? 'info' : 'warn';\n    \n    this.writeLog({\n      timestamp: new Date().toISOString(),\n      level,\n      message: `Auth ${event} ${success ? 'success' : 'failed'}${userId ? ` for user ${userId}` : ''}`,\n      context: {\n        ...this.context,\n        ...context,\n        userId,\n        metadata: {\n          event,\n          success: success.toString()\n        }\n      }\n    });\n  }\n\n  /**\n   * Log performance metric\n   */\n  performance(metric: string, value: number, unit: string = 'ms', context?: Partial<LogContext>): void {\n    this.info(`Performance ${metric}: ${value}${unit}`, {\n      ...context,\n      metadata: {\n        metric,\n        value: value.toString(),\n        unit\n      }\n    });\n  }\n}\n\n// Create singleton logger instance\nconst logger = new Logger();\n\n// Convenience functions for common logging patterns\nexport const log = {\n  debug: (message: string, context?: Partial<LogContext>) => logger.debug(message, context),\n  info: (message: string, context?: Partial<LogContext>) => logger.info(message, context),\n  warn: (message: string, context?: Partial<LogContext>) => logger.warn(message, context),\n  error: (message: string, error?: Error, context?: Partial<LogContext>) => logger.error(message, error, context),\n  api: (method: string, url: string, statusCode: number, duration: number, context?: Partial<LogContext>) => \n    logger.api(method, url, statusCode, duration, context),\n  database: (operation: string, table: string, duration: number, context?: Partial<LogContext>) => \n    logger.database(operation, table, duration, context),\n  auth: (event: string, userId?: string, success?: boolean, context?: Partial<LogContext>) => \n    logger.auth(event, userId, success, context),\n  performance: (metric: string, value: number, unit?: string, context?: Partial<LogContext>) => \n    logger.performance(metric, value, unit, context)\n};\n\nexport { logger, Logger };\nexport type { LogLevel, LogContext, LogEntry };\n"],
      version: 3
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "2d34b1f2b3a45e31a7a97dab446503d659d6406d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    // @ts-ignore
    cov_26ot6ei5w7 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_26ot6ei5w7();
var __assign =
/* istanbul ignore next */
(cov_26ot6ei5w7().s[0]++,
/* istanbul ignore next */
(cov_26ot6ei5w7().b[0][0]++, this) &&
/* istanbul ignore next */
(cov_26ot6ei5w7().b[0][1]++, this.__assign) ||
/* istanbul ignore next */
(cov_26ot6ei5w7().b[0][2]++, function () {
  /* istanbul ignore next */
  cov_26ot6ei5w7().f[0]++;
  cov_26ot6ei5w7().s[1]++;
  __assign =
  /* istanbul ignore next */
  (cov_26ot6ei5w7().b[1][0]++, Object.assign) ||
  /* istanbul ignore next */
  (cov_26ot6ei5w7().b[1][1]++, function (t) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[1]++;
    cov_26ot6ei5w7().s[2]++;
    for (var s, i =
      /* istanbul ignore next */
      (cov_26ot6ei5w7().s[3]++, 1), n =
      /* istanbul ignore next */
      (cov_26ot6ei5w7().s[4]++, arguments.length); i < n; i++) {
      /* istanbul ignore next */
      cov_26ot6ei5w7().s[5]++;
      s = arguments[i];
      /* istanbul ignore next */
      cov_26ot6ei5w7().s[6]++;
      for (var p in s) {
        /* istanbul ignore next */
        cov_26ot6ei5w7().s[7]++;
        if (Object.prototype.hasOwnProperty.call(s, p)) {
          /* istanbul ignore next */
          cov_26ot6ei5w7().b[2][0]++;
          cov_26ot6ei5w7().s[8]++;
          t[p] = s[p];
        } else
        /* istanbul ignore next */
        {
          cov_26ot6ei5w7().b[2][1]++;
        }
      }
    }
    /* istanbul ignore next */
    cov_26ot6ei5w7().s[9]++;
    return t;
  });
  /* istanbul ignore next */
  cov_26ot6ei5w7().s[10]++;
  return __assign.apply(this, arguments);
}));
/* istanbul ignore next */
cov_26ot6ei5w7().s[11]++;
Object.defineProperty(exports, "__esModule", {
  value: true
});
/* istanbul ignore next */
cov_26ot6ei5w7().s[12]++;
exports.Logger = exports.logger = exports.log = void 0;
var Logger =
/* istanbul ignore next */
(/** @class */cov_26ot6ei5w7().s[13]++, function () {
  /* istanbul ignore next */
  cov_26ot6ei5w7().f[2]++;
  function Logger() {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[3]++;
    cov_26ot6ei5w7().s[14]++;
    this.isProduction = process.env.NODE_ENV === 'production';
    /* istanbul ignore next */
    cov_26ot6ei5w7().s[15]++;
    this.logLevel =
    /* istanbul ignore next */
    (cov_26ot6ei5w7().b[3][0]++, process.env.LOG_LEVEL) ||
    /* istanbul ignore next */
    (cov_26ot6ei5w7().b[3][1]++, this.isProduction ?
    /* istanbul ignore next */
    (cov_26ot6ei5w7().b[4][0]++, 'warn') :
    /* istanbul ignore next */
    (cov_26ot6ei5w7().b[4][1]++, 'debug'));
    /* istanbul ignore next */
    cov_26ot6ei5w7().s[16]++;
    this.context = {};
    /* istanbul ignore next */
    cov_26ot6ei5w7().s[17]++;
    this.levelPriority = {
      debug: 0,
      info: 1,
      warn: 2,
      error: 3
    };
  }
  /**
   * Set global context for all log entries
   */
  /* istanbul ignore next */
  cov_26ot6ei5w7().s[18]++;
  Logger.prototype.setContext = function (context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[4]++;
    cov_26ot6ei5w7().s[19]++;
    this.context = __assign(__assign({}, this.context), context);
  };
  /**
   * Clear global context
   */
  /* istanbul ignore next */
  cov_26ot6ei5w7().s[20]++;
  Logger.prototype.clearContext = function () {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[5]++;
    cov_26ot6ei5w7().s[21]++;
    this.context = {};
  };
  /**
   * Create a child logger with additional context
   */
  /* istanbul ignore next */
  cov_26ot6ei5w7().s[22]++;
  Logger.prototype.child = function (context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[6]++;
    var childLogger =
    /* istanbul ignore next */
    (cov_26ot6ei5w7().s[23]++, new Logger());
    /* istanbul ignore next */
    cov_26ot6ei5w7().s[24]++;
    childLogger.context = __assign(__assign({}, this.context), context);
    /* istanbul ignore next */
    cov_26ot6ei5w7().s[25]++;
    childLogger.logLevel = this.logLevel;
    /* istanbul ignore next */
    cov_26ot6ei5w7().s[26]++;
    childLogger.isProduction = this.isProduction;
    /* istanbul ignore next */
    cov_26ot6ei5w7().s[27]++;
    return childLogger;
  };
  /**
   * Check if a log level should be logged
   */
  /* istanbul ignore next */
  cov_26ot6ei5w7().s[28]++;
  Logger.prototype.shouldLog = function (level) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[7]++;
    cov_26ot6ei5w7().s[29]++;
    return this.levelPriority[level] >= this.levelPriority[this.logLevel];
  };
  /**
   * Format log entry for output
   */
  /* istanbul ignore next */
  cov_26ot6ei5w7().s[30]++;
  Logger.prototype.formatLogEntry = function (entry) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[8]++;
    cov_26ot6ei5w7().s[31]++;
    if (this.isProduction) {
      /* istanbul ignore next */
      cov_26ot6ei5w7().b[5][0]++;
      cov_26ot6ei5w7().s[32]++;
      // Structured JSON logging for production
      return JSON.stringify(entry);
    } else {
      /* istanbul ignore next */
      cov_26ot6ei5w7().b[5][1]++;
      // Human-readable logging for development
      var timestamp =
      /* istanbul ignore next */
      (cov_26ot6ei5w7().s[33]++, new Date(entry.timestamp).toLocaleTimeString());
      var level =
      /* istanbul ignore next */
      (cov_26ot6ei5w7().s[34]++, entry.level.toUpperCase().padEnd(5));
      var context =
      /* istanbul ignore next */
      (cov_26ot6ei5w7().s[35]++, entry.context ?
      /* istanbul ignore next */
      (cov_26ot6ei5w7().b[6][0]++, " [".concat(Object.entries(entry.context).map(function (_a) {
        /* istanbul ignore next */
        cov_26ot6ei5w7().f[9]++;
        var k =
          /* istanbul ignore next */
          (cov_26ot6ei5w7().s[36]++, _a[0]),
          v =
          /* istanbul ignore next */
          (cov_26ot6ei5w7().s[37]++, _a[1]);
        /* istanbul ignore next */
        cov_26ot6ei5w7().s[38]++;
        return "".concat(k, "=").concat(v);
      }).join(', '), "]")) :
      /* istanbul ignore next */
      (cov_26ot6ei5w7().b[6][1]++, ''));
      /* istanbul ignore next */
      cov_26ot6ei5w7().s[39]++;
      return "".concat(timestamp, " ").concat(level, " ").concat(entry.message).concat(context);
    }
  };
  /**
   * Write log entry to appropriate output
   */
  /* istanbul ignore next */
  cov_26ot6ei5w7().s[40]++;
  Logger.prototype.writeLog = function (entry) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[10]++;
    var formatted =
    /* istanbul ignore next */
    (cov_26ot6ei5w7().s[41]++, this.formatLogEntry(entry));
    /* istanbul ignore next */
    cov_26ot6ei5w7().s[42]++;
    switch (entry.level) {
      case 'debug':
        /* istanbul ignore next */
        cov_26ot6ei5w7().b[7][0]++;
        cov_26ot6ei5w7().s[43]++;
        console.debug(formatted);
        /* istanbul ignore next */
        cov_26ot6ei5w7().s[44]++;
        break;
      case 'info':
        /* istanbul ignore next */
        cov_26ot6ei5w7().b[7][1]++;
        cov_26ot6ei5w7().s[45]++;
        console.info(formatted);
        /* istanbul ignore next */
        cov_26ot6ei5w7().s[46]++;
        break;
      case 'warn':
        /* istanbul ignore next */
        cov_26ot6ei5w7().b[7][2]++;
        cov_26ot6ei5w7().s[47]++;
        console.warn(formatted);
        /* istanbul ignore next */
        cov_26ot6ei5w7().s[48]++;
        break;
      case 'error':
        /* istanbul ignore next */
        cov_26ot6ei5w7().b[7][3]++;
        cov_26ot6ei5w7().s[49]++;
        console.error(formatted);
        /* istanbul ignore next */
        cov_26ot6ei5w7().s[50]++;
        break;
    }
    // In production, also send to external logging service
    /* istanbul ignore next */
    cov_26ot6ei5w7().s[51]++;
    if (
    /* istanbul ignore next */
    (cov_26ot6ei5w7().b[9][0]++, this.isProduction) &&
    /* istanbul ignore next */
    (cov_26ot6ei5w7().b[9][1]++, entry.level === 'error')) {
      /* istanbul ignore next */
      cov_26ot6ei5w7().b[8][0]++;
      cov_26ot6ei5w7().s[52]++;
      this.sendToExternalService(entry);
    } else
    /* istanbul ignore next */
    {
      cov_26ot6ei5w7().b[8][1]++;
    }
  };
  /**
   * Send error logs to external service (e.g., Sentry, DataDog)
   */
  /* istanbul ignore next */
  cov_26ot6ei5w7().s[53]++;
  Logger.prototype.sendToExternalService = function (entry) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[11]++;
    var _a;
    // This would integrate with your external logging service
    // For now, we'll use the error tracker
    /* istanbul ignore next */
    cov_26ot6ei5w7().s[54]++;
    try {
      /* istanbul ignore next */
      cov_26ot6ei5w7().s[55]++;
      if (entry.error) {
        /* istanbul ignore next */
        cov_26ot6ei5w7().b[10][0]++;
        var error =
        /* istanbul ignore next */
        (cov_26ot6ei5w7().s[56]++, new Error(entry.error.message));
        /* istanbul ignore next */
        cov_26ot6ei5w7().s[57]++;
        error.name = entry.error.name;
        /* istanbul ignore next */
        cov_26ot6ei5w7().s[58]++;
        error.stack = entry.error.stack;
        // Use the error tracker we created earlier
        /* istanbul ignore next */
        cov_26ot6ei5w7().s[59]++;
        if (
        /* istanbul ignore next */
        (cov_26ot6ei5w7().b[12][0]++, typeof window !== 'undefined') &&
        /* istanbul ignore next */
        (cov_26ot6ei5w7().b[12][1]++, window.Sentry)) {
          /* istanbul ignore next */
          cov_26ot6ei5w7().b[11][0]++;
          cov_26ot6ei5w7().s[60]++;
          window.Sentry.captureException(error, {
            extra: {
              logEntry: entry,
              context: entry.context
            },
            tags: {
              source: 'logger',
              component:
              /* istanbul ignore next */
              (cov_26ot6ei5w7().b[13][0]++,
              /* istanbul ignore next */
              (cov_26ot6ei5w7().b[15][0]++, (_a = entry.context) === null) ||
              /* istanbul ignore next */
              (cov_26ot6ei5w7().b[15][1]++, _a === void 0) ?
              /* istanbul ignore next */
              (cov_26ot6ei5w7().b[14][0]++, void 0) :
              /* istanbul ignore next */
              (cov_26ot6ei5w7().b[14][1]++, _a.component)) ||
              /* istanbul ignore next */
              (cov_26ot6ei5w7().b[13][1]++, 'unknown')
            }
          });
        } else
        /* istanbul ignore next */
        {
          cov_26ot6ei5w7().b[11][1]++;
        }
      } else
      /* istanbul ignore next */
      {
        cov_26ot6ei5w7().b[10][1]++;
      }
    } catch (error) {
      /* istanbul ignore next */
      cov_26ot6ei5w7().s[61]++;
      // Fallback - don't let logging errors break the application
      console.error('Failed to send log to external service:', error);
    }
  };
  /**
   * Log debug message
   */
  /* istanbul ignore next */
  cov_26ot6ei5w7().s[62]++;
  Logger.prototype.debug = function (message, context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[12]++;
    cov_26ot6ei5w7().s[63]++;
    if (!this.shouldLog('debug')) {
      /* istanbul ignore next */
      cov_26ot6ei5w7().b[16][0]++;
      cov_26ot6ei5w7().s[64]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_26ot6ei5w7().b[16][1]++;
    }
    cov_26ot6ei5w7().s[65]++;
    this.writeLog({
      timestamp: new Date().toISOString(),
      level: 'debug',
      message: message,
      context: __assign(__assign({}, this.context), context)
    });
  };
  /**
   * Log info message
   */
  /* istanbul ignore next */
  cov_26ot6ei5w7().s[66]++;
  Logger.prototype.info = function (message, context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[13]++;
    cov_26ot6ei5w7().s[67]++;
    if (!this.shouldLog('info')) {
      /* istanbul ignore next */
      cov_26ot6ei5w7().b[17][0]++;
      cov_26ot6ei5w7().s[68]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_26ot6ei5w7().b[17][1]++;
    }
    cov_26ot6ei5w7().s[69]++;
    this.writeLog({
      timestamp: new Date().toISOString(),
      level: 'info',
      message: message,
      context: __assign(__assign({}, this.context), context)
    });
  };
  /**
   * Log warning message
   */
  /* istanbul ignore next */
  cov_26ot6ei5w7().s[70]++;
  Logger.prototype.warn = function (message, context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[14]++;
    cov_26ot6ei5w7().s[71]++;
    if (!this.shouldLog('warn')) {
      /* istanbul ignore next */
      cov_26ot6ei5w7().b[18][0]++;
      cov_26ot6ei5w7().s[72]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_26ot6ei5w7().b[18][1]++;
    }
    cov_26ot6ei5w7().s[73]++;
    this.writeLog({
      timestamp: new Date().toISOString(),
      level: 'warn',
      message: message,
      context: __assign(__assign({}, this.context), context)
    });
  };
  /**
   * Log error message
   */
  /* istanbul ignore next */
  cov_26ot6ei5w7().s[74]++;
  Logger.prototype.error = function (message, error, context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[15]++;
    cov_26ot6ei5w7().s[75]++;
    if (!this.shouldLog('error')) {
      /* istanbul ignore next */
      cov_26ot6ei5w7().b[19][0]++;
      cov_26ot6ei5w7().s[76]++;
      return;
    } else
    /* istanbul ignore next */
    {
      cov_26ot6ei5w7().b[19][1]++;
    }
    cov_26ot6ei5w7().s[77]++;
    this.writeLog({
      timestamp: new Date().toISOString(),
      level: 'error',
      message: message,
      context: __assign(__assign({}, this.context), context),
      error: error ?
      /* istanbul ignore next */
      (cov_26ot6ei5w7().b[20][0]++, {
        name: error.name,
        message: error.message,
        stack: error.stack
      }) :
      /* istanbul ignore next */
      (cov_26ot6ei5w7().b[20][1]++, undefined)
    });
  };
  /**
   * Log API request/response
   */
  /* istanbul ignore next */
  cov_26ot6ei5w7().s[78]++;
  Logger.prototype.api = function (method, url, statusCode, duration, context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[16]++;
    var level =
    /* istanbul ignore next */
    (cov_26ot6ei5w7().s[79]++, statusCode >= 500 ?
    /* istanbul ignore next */
    (cov_26ot6ei5w7().b[21][0]++, 'error') :
    /* istanbul ignore next */
    (cov_26ot6ei5w7().b[21][1]++, statusCode >= 400 ?
    /* istanbul ignore next */
    (cov_26ot6ei5w7().b[22][0]++, 'warn') :
    /* istanbul ignore next */
    (cov_26ot6ei5w7().b[22][1]++, 'info')));
    /* istanbul ignore next */
    cov_26ot6ei5w7().s[80]++;
    this.writeLog({
      timestamp: new Date().toISOString(),
      level: level,
      message: "".concat(method, " ").concat(url, " ").concat(statusCode, " ").concat(duration, "ms"),
      context: __assign(__assign(__assign({}, this.context), context), {
        metadata: {
          method: method,
          url: url,
          statusCode: statusCode.toString(),
          duration: duration.toString()
        }
      })
    });
  };
  /**
   * Log database operation
   */
  /* istanbul ignore next */
  cov_26ot6ei5w7().s[81]++;
  Logger.prototype.database = function (operation, table, duration, context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[17]++;
    cov_26ot6ei5w7().s[82]++;
    this.debug("DB ".concat(operation, " ").concat(table, " ").concat(duration, "ms"), __assign(__assign({}, context), {
      metadata: {
        operation: operation,
        table: table,
        duration: duration.toString()
      }
    }));
  };
  /**
   * Log authentication event
   */
  /* istanbul ignore next */
  cov_26ot6ei5w7().s[83]++;
  Logger.prototype.auth = function (event, userId, success, context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[18]++;
    cov_26ot6ei5w7().s[84]++;
    if (success === void 0) {
      /* istanbul ignore next */
      cov_26ot6ei5w7().b[23][0]++;
      cov_26ot6ei5w7().s[85]++;
      success = true;
    } else
    /* istanbul ignore next */
    {
      cov_26ot6ei5w7().b[23][1]++;
    }
    var level =
    /* istanbul ignore next */
    (cov_26ot6ei5w7().s[86]++, success ?
    /* istanbul ignore next */
    (cov_26ot6ei5w7().b[24][0]++, 'info') :
    /* istanbul ignore next */
    (cov_26ot6ei5w7().b[24][1]++, 'warn'));
    /* istanbul ignore next */
    cov_26ot6ei5w7().s[87]++;
    this.writeLog({
      timestamp: new Date().toISOString(),
      level: level,
      message: "Auth ".concat(event, " ").concat(success ?
      /* istanbul ignore next */
      (cov_26ot6ei5w7().b[25][0]++, 'success') :
      /* istanbul ignore next */
      (cov_26ot6ei5w7().b[25][1]++, 'failed')).concat(userId ?
      /* istanbul ignore next */
      (cov_26ot6ei5w7().b[26][0]++, " for user ".concat(userId)) :
      /* istanbul ignore next */
      (cov_26ot6ei5w7().b[26][1]++, '')),
      context: __assign(__assign(__assign({}, this.context), context), {
        userId: userId,
        metadata: {
          event: event,
          success: success.toString()
        }
      })
    });
  };
  /**
   * Log performance metric
   */
  /* istanbul ignore next */
  cov_26ot6ei5w7().s[88]++;
  Logger.prototype.performance = function (metric, value, unit, context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[19]++;
    cov_26ot6ei5w7().s[89]++;
    if (unit === void 0) {
      /* istanbul ignore next */
      cov_26ot6ei5w7().b[27][0]++;
      cov_26ot6ei5w7().s[90]++;
      unit = 'ms';
    } else
    /* istanbul ignore next */
    {
      cov_26ot6ei5w7().b[27][1]++;
    }
    cov_26ot6ei5w7().s[91]++;
    this.info("Performance ".concat(metric, ": ").concat(value).concat(unit), __assign(__assign({}, context), {
      metadata: {
        metric: metric,
        value: value.toString(),
        unit: unit
      }
    }));
  };
  /* istanbul ignore next */
  cov_26ot6ei5w7().s[92]++;
  return Logger;
}());
/* istanbul ignore next */
cov_26ot6ei5w7().s[93]++;
exports.Logger = Logger;
// Create singleton logger instance
var logger =
/* istanbul ignore next */
(cov_26ot6ei5w7().s[94]++, new Logger());
/* istanbul ignore next */
cov_26ot6ei5w7().s[95]++;
exports.logger = logger;
// Convenience functions for common logging patterns
/* istanbul ignore next */
cov_26ot6ei5w7().s[96]++;
exports.log = {
  debug: function (message, context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[20]++;
    cov_26ot6ei5w7().s[97]++;
    return logger.debug(message, context);
  },
  info: function (message, context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[21]++;
    cov_26ot6ei5w7().s[98]++;
    return logger.info(message, context);
  },
  warn: function (message, context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[22]++;
    cov_26ot6ei5w7().s[99]++;
    return logger.warn(message, context);
  },
  error: function (message, error, context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[23]++;
    cov_26ot6ei5w7().s[100]++;
    return logger.error(message, error, context);
  },
  api: function (method, url, statusCode, duration, context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[24]++;
    cov_26ot6ei5w7().s[101]++;
    return logger.api(method, url, statusCode, duration, context);
  },
  database: function (operation, table, duration, context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[25]++;
    cov_26ot6ei5w7().s[102]++;
    return logger.database(operation, table, duration, context);
  },
  auth: function (event, userId, success, context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[26]++;
    cov_26ot6ei5w7().s[103]++;
    return logger.auth(event, userId, success, context);
  },
  performance: function (metric, value, unit, context) {
    /* istanbul ignore next */
    cov_26ot6ei5w7().f[27]++;
    cov_26ot6ei5w7().s[104]++;
    return logger.performance(metric, value, unit, context);
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjb3ZfMjZvdDZlaTV3NyIsInBhdGgiLCJoYXNoIiwiZ2xvYmFsIiwiRnVuY3Rpb24iLCJnY3YiLCJjb3ZlcmFnZURhdGEiLCJzdGF0ZW1lbnRNYXAiLCJzdGFydCIsImxpbmUiLCJjb2x1bW4iLCJlbmQiLCJmbk1hcCIsIm5hbWUiLCJkZWNsIiwibG9jIiwiYnJhbmNoTWFwIiwidHlwZSIsImxvY2F0aW9ucyIsInVuZGVmaW5lZCIsInMiLCJmIiwiYiIsImlucHV0U291cmNlTWFwIiwiZmlsZSIsIm1hcHBpbmdzIiwibmFtZXMiLCJzb3VyY2VzIiwic291cmNlc0NvbnRlbnQiLCJ2ZXJzaW9uIiwiX2NvdmVyYWdlU2NoZW1hIiwiY292ZXJhZ2UiLCJhY3R1YWxDb3ZlcmFnZSIsIkxvZ2dlciIsImlzUHJvZHVjdGlvbiIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsImxvZ0xldmVsIiwiTE9HX0xFVkVMIiwiY29udGV4dCIsImxldmVsUHJpb3JpdHkiLCJkZWJ1ZyIsImluZm8iLCJ3YXJuIiwiZXJyb3IiLCJwcm90b3R5cGUiLCJzZXRDb250ZXh0IiwiX19hc3NpZ24iLCJjbGVhckNvbnRleHQiLCJjaGlsZCIsImNoaWxkTG9nZ2VyIiwic2hvdWxkTG9nIiwibGV2ZWwiLCJmb3JtYXRMb2dFbnRyeSIsImVudHJ5IiwiSlNPTiIsInN0cmluZ2lmeSIsInRpbWVzdGFtcCIsIkRhdGUiLCJ0b0xvY2FsZVRpbWVTdHJpbmciLCJ0b1VwcGVyQ2FzZSIsInBhZEVuZCIsImNvbmNhdCIsIk9iamVjdCIsImVudHJpZXMiLCJtYXAiLCJfYSIsImsiLCJ2Iiwiam9pbiIsIm1lc3NhZ2UiLCJ3cml0ZUxvZyIsImZvcm1hdHRlZCIsImNvbnNvbGUiLCJzZW5kVG9FeHRlcm5hbFNlcnZpY2UiLCJFcnJvciIsInN0YWNrIiwid2luZG93IiwiU2VudHJ5IiwiY2FwdHVyZUV4Y2VwdGlvbiIsImV4dHJhIiwibG9nRW50cnkiLCJ0YWdzIiwic291cmNlIiwiY29tcG9uZW50IiwidG9JU09TdHJpbmciLCJhcGkiLCJtZXRob2QiLCJ1cmwiLCJzdGF0dXNDb2RlIiwiZHVyYXRpb24iLCJtZXRhZGF0YSIsInRvU3RyaW5nIiwiZGF0YWJhc2UiLCJvcGVyYXRpb24iLCJ0YWJsZSIsImF1dGgiLCJldmVudCIsInVzZXJJZCIsInN1Y2Nlc3MiLCJwZXJmb3JtYW5jZSIsIm1ldHJpYyIsInZhbHVlIiwidW5pdCIsImV4cG9ydHMiLCJsb2dnZXIiLCJsb2ciXSwic291cmNlcyI6WyIvVXNlcnMvZGQ2MC9mYWFmby9mYWFmby9mYWFmby1jYXJlZXItcGxhdGZvcm0vc3JjL2xpYi9sb2dnZXIudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDZW50cmFsaXplZCBMb2dnaW5nIFNlcnZpY2VcbiAqIFByb3ZpZGVzIHN0cnVjdHVyZWQgbG9nZ2luZyB3aXRoIGRpZmZlcmVudCBsZXZlbHMgYW5kIGNvbnRleHRzXG4gKi9cblxudHlwZSBMb2dMZXZlbCA9ICdkZWJ1ZycgfCAnaW5mbycgfCAnd2FybicgfCAnZXJyb3InO1xuXG5pbnRlcmZhY2UgTG9nQ29udGV4dCB7XG4gIHVzZXJJZD86IHN0cmluZztcbiAgc2Vzc2lvbklkPzogc3RyaW5nO1xuICByZXF1ZXN0SWQ/OiBzdHJpbmc7XG4gIGNvbXBvbmVudD86IHN0cmluZztcbiAgYWN0aW9uPzogc3RyaW5nO1xuICBtZXRhZGF0YT86IFJlY29yZDxzdHJpbmcsIGFueT47XG59XG5cbmludGVyZmFjZSBMb2dFbnRyeSB7XG4gIHRpbWVzdGFtcDogc3RyaW5nO1xuICBsZXZlbDogTG9nTGV2ZWw7XG4gIG1lc3NhZ2U6IHN0cmluZztcbiAgY29udGV4dD86IExvZ0NvbnRleHQ7XG4gIGVycm9yPzoge1xuICAgIG5hbWU6IHN0cmluZztcbiAgICBtZXNzYWdlOiBzdHJpbmc7XG4gICAgc3RhY2s/OiBzdHJpbmc7XG4gIH07XG59XG5cbmNsYXNzIExvZ2dlciB7XG4gIHByaXZhdGUgaXNQcm9kdWN0aW9uID0gcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJztcbiAgcHJpdmF0ZSBsb2dMZXZlbDogTG9nTGV2ZWwgPSAocHJvY2Vzcy5lbnYuTE9HX0xFVkVMIGFzIExvZ0xldmVsKSB8fCAodGhpcy5pc1Byb2R1Y3Rpb24gPyAnd2FybicgOiAnZGVidWcnKTtcbiAgcHJpdmF0ZSBjb250ZXh0OiBMb2dDb250ZXh0ID0ge307XG5cbiAgcHJpdmF0ZSBsZXZlbFByaW9yaXR5OiBSZWNvcmQ8TG9nTGV2ZWwsIG51bWJlcj4gPSB7XG4gICAgZGVidWc6IDAsXG4gICAgaW5mbzogMSxcbiAgICB3YXJuOiAyLFxuICAgIGVycm9yOiAzXG4gIH07XG5cbiAgLyoqXG4gICAqIFNldCBnbG9iYWwgY29udGV4dCBmb3IgYWxsIGxvZyBlbnRyaWVzXG4gICAqL1xuICBzZXRDb250ZXh0KGNvbnRleHQ6IFBhcnRpYWw8TG9nQ29udGV4dD4pOiB2b2lkIHtcbiAgICB0aGlzLmNvbnRleHQgPSB7IC4uLnRoaXMuY29udGV4dCwgLi4uY29udGV4dCB9O1xuICB9XG5cbiAgLyoqXG4gICAqIENsZWFyIGdsb2JhbCBjb250ZXh0XG4gICAqL1xuICBjbGVhckNvbnRleHQoKTogdm9pZCB7XG4gICAgdGhpcy5jb250ZXh0ID0ge307XG4gIH1cblxuICAvKipcbiAgICogQ3JlYXRlIGEgY2hpbGQgbG9nZ2VyIHdpdGggYWRkaXRpb25hbCBjb250ZXh0XG4gICAqL1xuICBjaGlsZChjb250ZXh0OiBQYXJ0aWFsPExvZ0NvbnRleHQ+KTogTG9nZ2VyIHtcbiAgICBjb25zdCBjaGlsZExvZ2dlciA9IG5ldyBMb2dnZXIoKTtcbiAgICBjaGlsZExvZ2dlci5jb250ZXh0ID0geyAuLi50aGlzLmNvbnRleHQsIC4uLmNvbnRleHQgfTtcbiAgICBjaGlsZExvZ2dlci5sb2dMZXZlbCA9IHRoaXMubG9nTGV2ZWw7XG4gICAgY2hpbGRMb2dnZXIuaXNQcm9kdWN0aW9uID0gdGhpcy5pc1Byb2R1Y3Rpb247XG4gICAgcmV0dXJuIGNoaWxkTG9nZ2VyO1xuICB9XG5cbiAgLyoqXG4gICAqIENoZWNrIGlmIGEgbG9nIGxldmVsIHNob3VsZCBiZSBsb2dnZWRcbiAgICovXG4gIHByaXZhdGUgc2hvdWxkTG9nKGxldmVsOiBMb2dMZXZlbCk6IGJvb2xlYW4ge1xuICAgIHJldHVybiB0aGlzLmxldmVsUHJpb3JpdHlbbGV2ZWxdID49IHRoaXMubGV2ZWxQcmlvcml0eVt0aGlzLmxvZ0xldmVsXTtcbiAgfVxuXG4gIC8qKlxuICAgKiBGb3JtYXQgbG9nIGVudHJ5IGZvciBvdXRwdXRcbiAgICovXG4gIHByaXZhdGUgZm9ybWF0TG9nRW50cnkoZW50cnk6IExvZ0VudHJ5KTogc3RyaW5nIHtcbiAgICBpZiAodGhpcy5pc1Byb2R1Y3Rpb24pIHtcbiAgICAgIC8vIFN0cnVjdHVyZWQgSlNPTiBsb2dnaW5nIGZvciBwcm9kdWN0aW9uXG4gICAgICByZXR1cm4gSlNPTi5zdHJpbmdpZnkoZW50cnkpO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBIdW1hbi1yZWFkYWJsZSBsb2dnaW5nIGZvciBkZXZlbG9wbWVudFxuICAgICAgY29uc3QgdGltZXN0YW1wID0gbmV3IERhdGUoZW50cnkudGltZXN0YW1wKS50b0xvY2FsZVRpbWVTdHJpbmcoKTtcbiAgICAgIGNvbnN0IGxldmVsID0gZW50cnkubGV2ZWwudG9VcHBlckNhc2UoKS5wYWRFbmQoNSk7XG4gICAgICBjb25zdCBjb250ZXh0ID0gZW50cnkuY29udGV4dCA/IGAgWyR7T2JqZWN0LmVudHJpZXMoZW50cnkuY29udGV4dCkubWFwKChbaywgdl0pID0+IGAke2t9PSR7dn1gKS5qb2luKCcsICcpfV1gIDogJyc7XG4gICAgICByZXR1cm4gYCR7dGltZXN0YW1wfSAke2xldmVsfSAke2VudHJ5Lm1lc3NhZ2V9JHtjb250ZXh0fWA7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFdyaXRlIGxvZyBlbnRyeSB0byBhcHByb3ByaWF0ZSBvdXRwdXRcbiAgICovXG4gIHByaXZhdGUgd3JpdGVMb2coZW50cnk6IExvZ0VudHJ5KTogdm9pZCB7XG4gICAgY29uc3QgZm9ybWF0dGVkID0gdGhpcy5mb3JtYXRMb2dFbnRyeShlbnRyeSk7XG5cbiAgICBzd2l0Y2ggKGVudHJ5LmxldmVsKSB7XG4gICAgICBjYXNlICdkZWJ1Zyc6XG4gICAgICAgIGNvbnNvbGUuZGVidWcoZm9ybWF0dGVkKTtcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdpbmZvJzpcbiAgICAgICAgY29uc29sZS5pbmZvKGZvcm1hdHRlZCk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAnd2Fybic6XG4gICAgICAgIGNvbnNvbGUud2Fybihmb3JtYXR0ZWQpO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgJ2Vycm9yJzpcbiAgICAgICAgY29uc29sZS5lcnJvcihmb3JtYXR0ZWQpO1xuICAgICAgICBicmVhaztcbiAgICB9XG5cbiAgICAvLyBJbiBwcm9kdWN0aW9uLCBhbHNvIHNlbmQgdG8gZXh0ZXJuYWwgbG9nZ2luZyBzZXJ2aWNlXG4gICAgaWYgKHRoaXMuaXNQcm9kdWN0aW9uICYmIGVudHJ5LmxldmVsID09PSAnZXJyb3InKSB7XG4gICAgICB0aGlzLnNlbmRUb0V4dGVybmFsU2VydmljZShlbnRyeSk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFNlbmQgZXJyb3IgbG9ncyB0byBleHRlcm5hbCBzZXJ2aWNlIChlLmcuLCBTZW50cnksIERhdGFEb2cpXG4gICAqL1xuICBwcml2YXRlIHNlbmRUb0V4dGVybmFsU2VydmljZShlbnRyeTogTG9nRW50cnkpOiB2b2lkIHtcbiAgICAvLyBUaGlzIHdvdWxkIGludGVncmF0ZSB3aXRoIHlvdXIgZXh0ZXJuYWwgbG9nZ2luZyBzZXJ2aWNlXG4gICAgLy8gRm9yIG5vdywgd2UnbGwgdXNlIHRoZSBlcnJvciB0cmFja2VyXG4gICAgdHJ5IHtcbiAgICAgIGlmIChlbnRyeS5lcnJvcikge1xuICAgICAgICBjb25zdCBlcnJvciA9IG5ldyBFcnJvcihlbnRyeS5lcnJvci5tZXNzYWdlKTtcbiAgICAgICAgZXJyb3IubmFtZSA9IGVudHJ5LmVycm9yLm5hbWU7XG4gICAgICAgIGVycm9yLnN0YWNrID0gZW50cnkuZXJyb3Iuc3RhY2s7XG5cbiAgICAgICAgLy8gVXNlIHRoZSBlcnJvciB0cmFja2VyIHdlIGNyZWF0ZWQgZWFybGllclxuICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93LlNlbnRyeSkge1xuICAgICAgICAgIHdpbmRvdy5TZW50cnkuY2FwdHVyZUV4Y2VwdGlvbihlcnJvciwge1xuICAgICAgICAgICAgZXh0cmE6IHtcbiAgICAgICAgICAgICAgbG9nRW50cnk6IGVudHJ5LFxuICAgICAgICAgICAgICBjb250ZXh0OiBlbnRyeS5jb250ZXh0XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgdGFnczoge1xuICAgICAgICAgICAgICBzb3VyY2U6ICdsb2dnZXInLFxuICAgICAgICAgICAgICBjb21wb25lbnQ6IGVudHJ5LmNvbnRleHQ/LmNvbXBvbmVudCB8fCAndW5rbm93bidcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAvLyBGYWxsYmFjayAtIGRvbid0IGxldCBsb2dnaW5nIGVycm9ycyBicmVhayB0aGUgYXBwbGljYXRpb25cbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBzZW5kIGxvZyB0byBleHRlcm5hbCBzZXJ2aWNlOicsIGVycm9yKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogTG9nIGRlYnVnIG1lc3NhZ2VcbiAgICovXG4gIGRlYnVnKG1lc3NhZ2U6IHN0cmluZywgY29udGV4dD86IFBhcnRpYWw8TG9nQ29udGV4dD4pOiB2b2lkIHtcbiAgICBpZiAoIXRoaXMuc2hvdWxkTG9nKCdkZWJ1ZycpKSByZXR1cm47XG5cbiAgICB0aGlzLndyaXRlTG9nKHtcbiAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgbGV2ZWw6ICdkZWJ1ZycsXG4gICAgICBtZXNzYWdlLFxuICAgICAgY29udGV4dDogeyAuLi50aGlzLmNvbnRleHQsIC4uLmNvbnRleHQgfVxuICAgIH0pO1xuICB9XG5cbiAgLyoqXG4gICAqIExvZyBpbmZvIG1lc3NhZ2VcbiAgICovXG4gIGluZm8obWVzc2FnZTogc3RyaW5nLCBjb250ZXh0PzogUGFydGlhbDxMb2dDb250ZXh0Pik6IHZvaWQge1xuICAgIGlmICghdGhpcy5zaG91bGRMb2coJ2luZm8nKSkgcmV0dXJuO1xuXG4gICAgdGhpcy53cml0ZUxvZyh7XG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIGxldmVsOiAnaW5mbycsXG4gICAgICBtZXNzYWdlLFxuICAgICAgY29udGV4dDogeyAuLi50aGlzLmNvbnRleHQsIC4uLmNvbnRleHQgfVxuICAgIH0pO1xuICB9XG5cbiAgLyoqXG4gICAqIExvZyB3YXJuaW5nIG1lc3NhZ2VcbiAgICovXG4gIHdhcm4obWVzc2FnZTogc3RyaW5nLCBjb250ZXh0PzogUGFydGlhbDxMb2dDb250ZXh0Pik6IHZvaWQge1xuICAgIGlmICghdGhpcy5zaG91bGRMb2coJ3dhcm4nKSkgcmV0dXJuO1xuXG4gICAgdGhpcy53cml0ZUxvZyh7XG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIGxldmVsOiAnd2FybicsXG4gICAgICBtZXNzYWdlLFxuICAgICAgY29udGV4dDogeyAuLi50aGlzLmNvbnRleHQsIC4uLmNvbnRleHQgfVxuICAgIH0pO1xuICB9XG5cbiAgLyoqXG4gICAqIExvZyBlcnJvciBtZXNzYWdlXG4gICAqL1xuICBlcnJvcihtZXNzYWdlOiBzdHJpbmcsIGVycm9yPzogRXJyb3IsIGNvbnRleHQ/OiBQYXJ0aWFsPExvZ0NvbnRleHQ+KTogdm9pZCB7XG4gICAgaWYgKCF0aGlzLnNob3VsZExvZygnZXJyb3InKSkgcmV0dXJuO1xuXG4gICAgdGhpcy53cml0ZUxvZyh7XG4gICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgIGxldmVsOiAnZXJyb3InLFxuICAgICAgbWVzc2FnZSxcbiAgICAgIGNvbnRleHQ6IHsgLi4udGhpcy5jb250ZXh0LCAuLi5jb250ZXh0IH0sXG4gICAgICBlcnJvcjogZXJyb3IgPyB7XG4gICAgICAgIG5hbWU6IGVycm9yLm5hbWUsXG4gICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsXG4gICAgICAgIHN0YWNrOiBlcnJvci5zdGFja1xuICAgICAgfSA6IHVuZGVmaW5lZFxuICAgIH0pO1xuICB9XG5cbiAgLyoqXG4gICAqIExvZyBBUEkgcmVxdWVzdC9yZXNwb25zZVxuICAgKi9cbiAgYXBpKG1ldGhvZDogc3RyaW5nLCB1cmw6IHN0cmluZywgc3RhdHVzQ29kZTogbnVtYmVyLCBkdXJhdGlvbjogbnVtYmVyLCBjb250ZXh0PzogUGFydGlhbDxMb2dDb250ZXh0Pik6IHZvaWQge1xuICAgIGNvbnN0IGxldmVsOiBMb2dMZXZlbCA9IHN0YXR1c0NvZGUgPj0gNTAwID8gJ2Vycm9yJyA6IHN0YXR1c0NvZGUgPj0gNDAwID8gJ3dhcm4nIDogJ2luZm8nO1xuICAgIFxuICAgIHRoaXMud3JpdGVMb2coe1xuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICBsZXZlbCxcbiAgICAgIG1lc3NhZ2U6IGAke21ldGhvZH0gJHt1cmx9ICR7c3RhdHVzQ29kZX0gJHtkdXJhdGlvbn1tc2AsXG4gICAgICBjb250ZXh0OiB7XG4gICAgICAgIC4uLnRoaXMuY29udGV4dCxcbiAgICAgICAgLi4uY29udGV4dCxcbiAgICAgICAgbWV0YWRhdGE6IHtcbiAgICAgICAgICBtZXRob2QsXG4gICAgICAgICAgdXJsLFxuICAgICAgICAgIHN0YXR1c0NvZGU6IHN0YXR1c0NvZGUudG9TdHJpbmcoKSxcbiAgICAgICAgICBkdXJhdGlvbjogZHVyYXRpb24udG9TdHJpbmcoKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSk7XG4gIH1cblxuICAvKipcbiAgICogTG9nIGRhdGFiYXNlIG9wZXJhdGlvblxuICAgKi9cbiAgZGF0YWJhc2Uob3BlcmF0aW9uOiBzdHJpbmcsIHRhYmxlOiBzdHJpbmcsIGR1cmF0aW9uOiBudW1iZXIsIGNvbnRleHQ/OiBQYXJ0aWFsPExvZ0NvbnRleHQ+KTogdm9pZCB7XG4gICAgdGhpcy5kZWJ1ZyhgREIgJHtvcGVyYXRpb259ICR7dGFibGV9ICR7ZHVyYXRpb259bXNgLCB7XG4gICAgICAuLi5jb250ZXh0LFxuICAgICAgbWV0YWRhdGE6IHtcbiAgICAgICAgb3BlcmF0aW9uLFxuICAgICAgICB0YWJsZSxcbiAgICAgICAgZHVyYXRpb246IGR1cmF0aW9uLnRvU3RyaW5nKClcbiAgICAgIH1cbiAgICB9KTtcbiAgfVxuXG4gIC8qKlxuICAgKiBMb2cgYXV0aGVudGljYXRpb24gZXZlbnRcbiAgICovXG4gIGF1dGgoZXZlbnQ6IHN0cmluZywgdXNlcklkPzogc3RyaW5nLCBzdWNjZXNzOiBib29sZWFuID0gdHJ1ZSwgY29udGV4dD86IFBhcnRpYWw8TG9nQ29udGV4dD4pOiB2b2lkIHtcbiAgICBjb25zdCBsZXZlbDogTG9nTGV2ZWwgPSBzdWNjZXNzID8gJ2luZm8nIDogJ3dhcm4nO1xuICAgIFxuICAgIHRoaXMud3JpdGVMb2coe1xuICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICBsZXZlbCxcbiAgICAgIG1lc3NhZ2U6IGBBdXRoICR7ZXZlbnR9ICR7c3VjY2VzcyA/ICdzdWNjZXNzJyA6ICdmYWlsZWQnfSR7dXNlcklkID8gYCBmb3IgdXNlciAke3VzZXJJZH1gIDogJyd9YCxcbiAgICAgIGNvbnRleHQ6IHtcbiAgICAgICAgLi4udGhpcy5jb250ZXh0LFxuICAgICAgICAuLi5jb250ZXh0LFxuICAgICAgICB1c2VySWQsXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgICAgICAgZXZlbnQsXG4gICAgICAgICAgc3VjY2Vzczogc3VjY2Vzcy50b1N0cmluZygpXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9KTtcbiAgfVxuXG4gIC8qKlxuICAgKiBMb2cgcGVyZm9ybWFuY2UgbWV0cmljXG4gICAqL1xuICBwZXJmb3JtYW5jZShtZXRyaWM6IHN0cmluZywgdmFsdWU6IG51bWJlciwgdW5pdDogc3RyaW5nID0gJ21zJywgY29udGV4dD86IFBhcnRpYWw8TG9nQ29udGV4dD4pOiB2b2lkIHtcbiAgICB0aGlzLmluZm8oYFBlcmZvcm1hbmNlICR7bWV0cmljfTogJHt2YWx1ZX0ke3VuaXR9YCwge1xuICAgICAgLi4uY29udGV4dCxcbiAgICAgIG1ldGFkYXRhOiB7XG4gICAgICAgIG1ldHJpYyxcbiAgICAgICAgdmFsdWU6IHZhbHVlLnRvU3RyaW5nKCksXG4gICAgICAgIHVuaXRcbiAgICAgIH1cbiAgICB9KTtcbiAgfVxufVxuXG4vLyBDcmVhdGUgc2luZ2xldG9uIGxvZ2dlciBpbnN0YW5jZVxuY29uc3QgbG9nZ2VyID0gbmV3IExvZ2dlcigpO1xuXG4vLyBDb252ZW5pZW5jZSBmdW5jdGlvbnMgZm9yIGNvbW1vbiBsb2dnaW5nIHBhdHRlcm5zXG5leHBvcnQgY29uc3QgbG9nID0ge1xuICBkZWJ1ZzogKG1lc3NhZ2U6IHN0cmluZywgY29udGV4dD86IFBhcnRpYWw8TG9nQ29udGV4dD4pID0+IGxvZ2dlci5kZWJ1ZyhtZXNzYWdlLCBjb250ZXh0KSxcbiAgaW5mbzogKG1lc3NhZ2U6IHN0cmluZywgY29udGV4dD86IFBhcnRpYWw8TG9nQ29udGV4dD4pID0+IGxvZ2dlci5pbmZvKG1lc3NhZ2UsIGNvbnRleHQpLFxuICB3YXJuOiAobWVzc2FnZTogc3RyaW5nLCBjb250ZXh0PzogUGFydGlhbDxMb2dDb250ZXh0PikgPT4gbG9nZ2VyLndhcm4obWVzc2FnZSwgY29udGV4dCksXG4gIGVycm9yOiAobWVzc2FnZTogc3RyaW5nLCBlcnJvcj86IEVycm9yLCBjb250ZXh0PzogUGFydGlhbDxMb2dDb250ZXh0PikgPT4gbG9nZ2VyLmVycm9yKG1lc3NhZ2UsIGVycm9yLCBjb250ZXh0KSxcbiAgYXBpOiAobWV0aG9kOiBzdHJpbmcsIHVybDogc3RyaW5nLCBzdGF0dXNDb2RlOiBudW1iZXIsIGR1cmF0aW9uOiBudW1iZXIsIGNvbnRleHQ/OiBQYXJ0aWFsPExvZ0NvbnRleHQ+KSA9PiBcbiAgICBsb2dnZXIuYXBpKG1ldGhvZCwgdXJsLCBzdGF0dXNDb2RlLCBkdXJhdGlvbiwgY29udGV4dCksXG4gIGRhdGFiYXNlOiAob3BlcmF0aW9uOiBzdHJpbmcsIHRhYmxlOiBzdHJpbmcsIGR1cmF0aW9uOiBudW1iZXIsIGNvbnRleHQ/OiBQYXJ0aWFsPExvZ0NvbnRleHQ+KSA9PiBcbiAgICBsb2dnZXIuZGF0YWJhc2Uob3BlcmF0aW9uLCB0YWJsZSwgZHVyYXRpb24sIGNvbnRleHQpLFxuICBhdXRoOiAoZXZlbnQ6IHN0cmluZywgdXNlcklkPzogc3RyaW5nLCBzdWNjZXNzPzogYm9vbGVhbiwgY29udGV4dD86IFBhcnRpYWw8TG9nQ29udGV4dD4pID0+IFxuICAgIGxvZ2dlci5hdXRoKGV2ZW50LCB1c2VySWQsIHN1Y2Nlc3MsIGNvbnRleHQpLFxuICBwZXJmb3JtYW5jZTogKG1ldHJpYzogc3RyaW5nLCB2YWx1ZTogbnVtYmVyLCB1bml0Pzogc3RyaW5nLCBjb250ZXh0PzogUGFydGlhbDxMb2dDb250ZXh0PikgPT4gXG4gICAgbG9nZ2VyLnBlcmZvcm1hbmNlKG1ldHJpYywgdmFsdWUsIHVuaXQsIGNvbnRleHQpXG59O1xuXG5leHBvcnQgeyBsb2dnZXIsIExvZ2dlciB9O1xuZXhwb3J0IHR5cGUgeyBMb2dMZXZlbCwgTG9nQ29udGV4dCwgTG9nRW50cnkgfTtcbiJdLCJtYXBwaW5ncyI6Ijs7QUFBQTs7OztBQUFBO0FBQUEsU0FBQUEsZUFBQTtFQUFBLElBQUFDLElBQUE7RUFBQSxJQUFBQyxJQUFBO0VBQUEsSUFBQUMsTUFBQSxPQUFBQyxRQUFBO0VBQUEsSUFBQUMsR0FBQTtFQUFBLElBQUFDLFlBQUE7SUFBQUwsSUFBQTtJQUFBTSxZQUFBO01BQUE7UUFBQUMsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO0lBQUE7SUFBQUUsS0FBQTtNQUFBO1FBQUFDLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFJLElBQUE7UUFBQUMsSUFBQTtVQUFBTixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBSyxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtJQUFBO0lBQUFPLFNBQUE7TUFBQTtRQUFBRCxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO0lBQUE7SUFBQVcsQ0FBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtJQUFBO0lBQUFDLENBQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtJQUFBO0lBQUFDLENBQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtJQUFBO0lBQUFDLGNBQUE7TUFBQUMsSUFBQTtNQUFBQyxRQUFBO01BQUFDLEtBQUE7TUFBQUMsT0FBQTtNQUFBQyxjQUFBO01BQUFDLE9BQUE7SUFBQTtJQUFBQyxlQUFBO0lBQUE1QixJQUFBO0VBQUE7RUFBQSxJQUFBNkIsUUFBQSxHQUFBNUIsTUFBQSxDQUFBRSxHQUFBLE1BQUFGLE1BQUEsQ0FBQUUsR0FBQTtFQUFBLEtBQUEwQixRQUFBLENBQUE5QixJQUFBLEtBQUE4QixRQUFBLENBQUE5QixJQUFBLEVBQUFDLElBQUEsS0FBQUEsSUFBQTtJQUFBNkIsUUFBQSxDQUFBOUIsSUFBQSxJQUFBSyxZQUFBO0VBQUE7RUFBQSxJQUFBMEIsY0FBQSxHQUFBRCxRQUFBLENBQUE5QixJQUFBO0VBQUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUE0QkEsSUFBQWdDLE1BQUE7QUFBQTtBQUFBLGNBQUFqQyxjQUFBLEdBQUFvQixDQUFBO0VBQUE7RUFBQXBCLGNBQUEsR0FBQXFCLENBQUE7RUFBQSxTQUFBWSxPQUFBO0lBQUE7SUFBQWpDLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFDVSxLQUFBYyxZQUFZLEdBQUdDLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDQyxRQUFRLEtBQUssWUFBWTtJQUFDO0lBQUFyQyxjQUFBLEdBQUFvQixDQUFBO0lBQ3JELEtBQUFrQixRQUFRO0lBQWM7SUFBQSxDQUFBdEMsY0FBQSxHQUFBc0IsQ0FBQSxVQUFBYSxPQUFPLENBQUNDLEdBQUcsQ0FBQ0csU0FBc0I7SUFBQTtJQUFBLENBQUF2QyxjQUFBLEdBQUFzQixDQUFBLFVBQUssSUFBSSxDQUFDWSxZQUFZO0lBQUE7SUFBQSxDQUFBbEMsY0FBQSxHQUFBc0IsQ0FBQSxVQUFHLE1BQU07SUFBQTtJQUFBLENBQUF0QixjQUFBLEdBQUFzQixDQUFBLFVBQUcsT0FBTyxFQUFDO0lBQUM7SUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7SUFDbkcsS0FBQW9CLE9BQU8sR0FBZSxFQUFFO0lBQUM7SUFBQXhDLGNBQUEsR0FBQW9CLENBQUE7SUFFekIsS0FBQXFCLGFBQWEsR0FBNkI7TUFDaERDLEtBQUssRUFBRSxDQUFDO01BQ1JDLElBQUksRUFBRSxDQUFDO01BQ1BDLElBQUksRUFBRSxDQUFDO01BQ1BDLEtBQUssRUFBRTtLQUNSO0VBa1BIO0VBaFBFOzs7RUFBQTtFQUFBN0MsY0FBQSxHQUFBb0IsQ0FBQTtFQUdBYSxNQUFBLENBQUFhLFNBQUEsQ0FBQUMsVUFBVSxHQUFWLFVBQVdQLE9BQTRCO0lBQUE7SUFBQXhDLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFDckMsSUFBSSxDQUFDb0IsT0FBTyxHQUFBUSxRQUFBLENBQUFBLFFBQUEsS0FBUSxJQUFJLENBQUNSLE9BQU8sR0FBS0EsT0FBTyxDQUFFO0VBQ2hELENBQUM7RUFFRDs7O0VBQUE7RUFBQXhDLGNBQUEsR0FBQW9CLENBQUE7RUFHQWEsTUFBQSxDQUFBYSxTQUFBLENBQUFHLFlBQVksR0FBWjtJQUFBO0lBQUFqRCxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ0UsSUFBSSxDQUFDb0IsT0FBTyxHQUFHLEVBQUU7RUFDbkIsQ0FBQztFQUVEOzs7RUFBQTtFQUFBeEMsY0FBQSxHQUFBb0IsQ0FBQTtFQUdBYSxNQUFBLENBQUFhLFNBQUEsQ0FBQUksS0FBSyxHQUFMLFVBQU1WLE9BQTRCO0lBQUE7SUFBQXhDLGNBQUEsR0FBQXFCLENBQUE7SUFDaEMsSUFBTThCLFdBQVc7SUFBQTtJQUFBLENBQUFuRCxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSWEsTUFBTSxFQUFFO0lBQUM7SUFBQWpDLGNBQUEsR0FBQW9CLENBQUE7SUFDakMrQixXQUFXLENBQUNYLE9BQU8sR0FBQVEsUUFBQSxDQUFBQSxRQUFBLEtBQVEsSUFBSSxDQUFDUixPQUFPLEdBQUtBLE9BQU8sQ0FBRTtJQUFDO0lBQUF4QyxjQUFBLEdBQUFvQixDQUFBO0lBQ3REK0IsV0FBVyxDQUFDYixRQUFRLEdBQUcsSUFBSSxDQUFDQSxRQUFRO0lBQUM7SUFBQXRDLGNBQUEsR0FBQW9CLENBQUE7SUFDckMrQixXQUFXLENBQUNqQixZQUFZLEdBQUcsSUFBSSxDQUFDQSxZQUFZO0lBQUM7SUFBQWxDLGNBQUEsR0FBQW9CLENBQUE7SUFDN0MsT0FBTytCLFdBQVc7RUFDcEIsQ0FBQztFQUVEOzs7RUFBQTtFQUFBbkQsY0FBQSxHQUFBb0IsQ0FBQTtFQUdRYSxNQUFBLENBQUFhLFNBQUEsQ0FBQU0sU0FBUyxHQUFqQixVQUFrQkMsS0FBZTtJQUFBO0lBQUFyRCxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQy9CLE9BQU8sSUFBSSxDQUFDcUIsYUFBYSxDQUFDWSxLQUFLLENBQUMsSUFBSSxJQUFJLENBQUNaLGFBQWEsQ0FBQyxJQUFJLENBQUNILFFBQVEsQ0FBQztFQUN2RSxDQUFDO0VBRUQ7OztFQUFBO0VBQUF0QyxjQUFBLEdBQUFvQixDQUFBO0VBR1FhLE1BQUEsQ0FBQWEsU0FBQSxDQUFBUSxjQUFjLEdBQXRCLFVBQXVCQyxLQUFlO0lBQUE7SUFBQXZELGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFDcEMsSUFBSSxJQUFJLENBQUNjLFlBQVksRUFBRTtNQUFBO01BQUFsQyxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQ3JCO01BQ0EsT0FBT29DLElBQUksQ0FBQ0MsU0FBUyxDQUFDRixLQUFLLENBQUM7SUFDOUIsQ0FBQyxNQUFNO01BQUE7TUFBQXZELGNBQUEsR0FBQXNCLENBQUE7TUFDTDtNQUNBLElBQU1vQyxTQUFTO01BQUE7TUFBQSxDQUFBMUQsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHLElBQUl1QyxJQUFJLENBQUNKLEtBQUssQ0FBQ0csU0FBUyxDQUFDLENBQUNFLGtCQUFrQixFQUFFO01BQ2hFLElBQU1QLEtBQUs7TUFBQTtNQUFBLENBQUFyRCxjQUFBLEdBQUFvQixDQUFBLFFBQUdtQyxLQUFLLENBQUNGLEtBQUssQ0FBQ1EsV0FBVyxFQUFFLENBQUNDLE1BQU0sQ0FBQyxDQUFDLENBQUM7TUFDakQsSUFBTXRCLE9BQU87TUFBQTtNQUFBLENBQUF4QyxjQUFBLEdBQUFvQixDQUFBLFFBQUdtQyxLQUFLLENBQUNmLE9BQU87TUFBQTtNQUFBLENBQUF4QyxjQUFBLEdBQUFzQixDQUFBLFVBQUcsS0FBQXlDLE1BQUEsQ0FBS0MsTUFBTSxDQUFDQyxPQUFPLENBQUNWLEtBQUssQ0FBQ2YsT0FBTyxDQUFDLENBQUMwQixHQUFHLENBQUMsVUFBQ0MsRUFBTTtRQUFBO1FBQUFuRSxjQUFBLEdBQUFxQixDQUFBO1lBQUwrQyxDQUFDO1VBQUE7VUFBQSxDQUFBcEUsY0FBQSxHQUFBb0IsQ0FBQSxRQUFBK0MsRUFBQTtVQUFFRSxDQUFDO1VBQUE7VUFBQSxDQUFBckUsY0FBQSxHQUFBb0IsQ0FBQSxRQUFBK0MsRUFBQTtRQUFBO1FBQUFuRSxjQUFBLEdBQUFvQixDQUFBO1FBQU0sVUFBQTJDLE1BQUEsQ0FBR0ssQ0FBQyxPQUFBTCxNQUFBLENBQUlNLENBQUMsQ0FBRTtNQUFYLENBQVcsQ0FBQyxDQUFDQyxJQUFJLENBQUMsSUFBSSxDQUFDLE1BQUc7TUFBQTtNQUFBLENBQUF0RSxjQUFBLEdBQUFzQixDQUFBLFVBQUcsRUFBRTtNQUFDO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQ25ILE9BQU8sR0FBQTJDLE1BQUEsQ0FBR0wsU0FBUyxPQUFBSyxNQUFBLENBQUlWLEtBQUssT0FBQVUsTUFBQSxDQUFJUixLQUFLLENBQUNnQixPQUFPLEVBQUFSLE1BQUEsQ0FBR3ZCLE9BQU8sQ0FBRTtJQUMzRDtFQUNGLENBQUM7RUFFRDs7O0VBQUE7RUFBQXhDLGNBQUEsR0FBQW9CLENBQUE7RUFHUWEsTUFBQSxDQUFBYSxTQUFBLENBQUEwQixRQUFRLEdBQWhCLFVBQWlCakIsS0FBZTtJQUFBO0lBQUF2RCxjQUFBLEdBQUFxQixDQUFBO0lBQzlCLElBQU1vRCxTQUFTO0lBQUE7SUFBQSxDQUFBekUsY0FBQSxHQUFBb0IsQ0FBQSxRQUFHLElBQUksQ0FBQ2tDLGNBQWMsQ0FBQ0MsS0FBSyxDQUFDO0lBQUM7SUFBQXZELGNBQUEsR0FBQW9CLENBQUE7SUFFN0MsUUFBUW1DLEtBQUssQ0FBQ0YsS0FBSztNQUNqQixLQUFLLE9BQU87UUFBQTtRQUFBckQsY0FBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtRQUNWc0QsT0FBTyxDQUFDaEMsS0FBSyxDQUFDK0IsU0FBUyxDQUFDO1FBQUM7UUFBQXpFLGNBQUEsR0FBQW9CLENBQUE7UUFDekI7TUFDRixLQUFLLE1BQU07UUFBQTtRQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtRQUNUc0QsT0FBTyxDQUFDL0IsSUFBSSxDQUFDOEIsU0FBUyxDQUFDO1FBQUM7UUFBQXpFLGNBQUEsR0FBQW9CLENBQUE7UUFDeEI7TUFDRixLQUFLLE1BQU07UUFBQTtRQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtRQUNUc0QsT0FBTyxDQUFDOUIsSUFBSSxDQUFDNkIsU0FBUyxDQUFDO1FBQUM7UUFBQXpFLGNBQUEsR0FBQW9CLENBQUE7UUFDeEI7TUFDRixLQUFLLE9BQU87UUFBQTtRQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQTtRQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtRQUNWc0QsT0FBTyxDQUFDN0IsS0FBSyxDQUFDNEIsU0FBUyxDQUFDO1FBQUM7UUFBQXpFLGNBQUEsR0FBQW9CLENBQUE7UUFDekI7SUFDSjtJQUVBO0lBQUE7SUFBQXBCLGNBQUEsR0FBQW9CLENBQUE7SUFDQTtJQUFJO0lBQUEsQ0FBQXBCLGNBQUEsR0FBQXNCLENBQUEsY0FBSSxDQUFDWSxZQUFZO0lBQUE7SUFBQSxDQUFBbEMsY0FBQSxHQUFBc0IsQ0FBQSxVQUFJaUMsS0FBSyxDQUFDRixLQUFLLEtBQUssT0FBTyxHQUFFO01BQUE7TUFBQXJELGNBQUEsR0FBQXNCLENBQUE7TUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7TUFDaEQsSUFBSSxDQUFDdUQscUJBQXFCLENBQUNwQixLQUFLLENBQUM7SUFDbkMsQ0FBQztJQUFBO0lBQUE7TUFBQXZELGNBQUEsR0FBQXNCLENBQUE7SUFBQTtFQUNILENBQUM7RUFFRDs7O0VBQUE7RUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7RUFHUWEsTUFBQSxDQUFBYSxTQUFBLENBQUE2QixxQkFBcUIsR0FBN0IsVUFBOEJwQixLQUFlO0lBQUE7SUFBQXZELGNBQUEsR0FBQXFCLENBQUE7O0lBQzNDO0lBQ0E7SUFBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtJQUNBLElBQUk7TUFBQTtNQUFBcEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUNGLElBQUltQyxLQUFLLENBQUNWLEtBQUssRUFBRTtRQUFBO1FBQUE3QyxjQUFBLEdBQUFzQixDQUFBO1FBQ2YsSUFBTXVCLEtBQUs7UUFBQTtRQUFBLENBQUE3QyxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSXdELEtBQUssQ0FBQ3JCLEtBQUssQ0FBQ1YsS0FBSyxDQUFDMEIsT0FBTyxDQUFDO1FBQUM7UUFBQXZFLGNBQUEsR0FBQW9CLENBQUE7UUFDN0N5QixLQUFLLENBQUNoQyxJQUFJLEdBQUcwQyxLQUFLLENBQUNWLEtBQUssQ0FBQ2hDLElBQUk7UUFBQztRQUFBYixjQUFBLEdBQUFvQixDQUFBO1FBQzlCeUIsS0FBSyxDQUFDZ0MsS0FBSyxHQUFHdEIsS0FBSyxDQUFDVixLQUFLLENBQUNnQyxLQUFLO1FBRS9CO1FBQUE7UUFBQTdFLGNBQUEsR0FBQW9CLENBQUE7UUFDQTtRQUFJO1FBQUEsQ0FBQXBCLGNBQUEsR0FBQXNCLENBQUEsa0JBQU93RCxNQUFNLEtBQUssV0FBVztRQUFBO1FBQUEsQ0FBQTlFLGNBQUEsR0FBQXNCLENBQUEsV0FBSXdELE1BQU0sQ0FBQ0MsTUFBTSxHQUFFO1VBQUE7VUFBQS9FLGNBQUEsR0FBQXNCLENBQUE7VUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7VUFDbEQwRCxNQUFNLENBQUNDLE1BQU0sQ0FBQ0MsZ0JBQWdCLENBQUNuQyxLQUFLLEVBQUU7WUFDcENvQyxLQUFLLEVBQUU7Y0FDTEMsUUFBUSxFQUFFM0IsS0FBSztjQUNmZixPQUFPLEVBQUVlLEtBQUssQ0FBQ2Y7YUFDaEI7WUFDRDJDLElBQUksRUFBRTtjQUNKQyxNQUFNLEVBQUUsUUFBUTtjQUNoQkMsU0FBUztjQUFFO2NBQUEsQ0FBQXJGLGNBQUEsR0FBQXNCLENBQUE7Y0FBQTtjQUFBLENBQUF0QixjQUFBLEdBQUFzQixDQUFBLFlBQUE2QyxFQUFBLEdBQUFaLEtBQUssQ0FBQ2YsT0FBTztjQUFBO2NBQUEsQ0FBQXhDLGNBQUEsR0FBQXNCLENBQUEsV0FBQTZDLEVBQUE7Y0FBQTtjQUFBLENBQUFuRSxjQUFBLEdBQUFzQixDQUFBO2NBQUE7Y0FBQSxDQUFBdEIsY0FBQSxHQUFBc0IsQ0FBQSxXQUFBNkMsRUFBQSxDQUFFa0IsU0FBUztjQUFBO2NBQUEsQ0FBQXJGLGNBQUEsR0FBQXNCLENBQUEsV0FBSSxTQUFTOztXQUVuRCxDQUFDO1FBQ0osQ0FBQztRQUFBO1FBQUE7VUFBQXRCLGNBQUEsR0FBQXNCLENBQUE7UUFBQTtNQUNILENBQUM7TUFBQTtNQUFBO1FBQUF0QixjQUFBLEdBQUFzQixDQUFBO01BQUE7SUFDSCxDQUFDLENBQUMsT0FBT3VCLEtBQUssRUFBRTtNQUFBO01BQUE3QyxjQUFBLEdBQUFvQixDQUFBO01BQ2Q7TUFDQXNELE9BQU8sQ0FBQzdCLEtBQUssQ0FBQyx5Q0FBeUMsRUFBRUEsS0FBSyxDQUFDO0lBQ2pFO0VBQ0YsQ0FBQztFQUVEOzs7RUFBQTtFQUFBN0MsY0FBQSxHQUFBb0IsQ0FBQTtFQUdBYSxNQUFBLENBQUFhLFNBQUEsQ0FBQUosS0FBSyxHQUFMLFVBQU02QixPQUFlLEVBQUUvQixPQUE2QjtJQUFBO0lBQUF4QyxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ2xELElBQUksQ0FBQyxJQUFJLENBQUNnQyxTQUFTLENBQUMsT0FBTyxDQUFDLEVBQUU7TUFBQTtNQUFBcEQsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBO0lBQUEsQ0FBTztJQUFBO0lBQUE7TUFBQXBCLGNBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUVyQyxJQUFJLENBQUNvRCxRQUFRLENBQUM7TUFDWmQsU0FBUyxFQUFFLElBQUlDLElBQUksRUFBRSxDQUFDMkIsV0FBVyxFQUFFO01BQ25DakMsS0FBSyxFQUFFLE9BQU87TUFDZGtCLE9BQU8sRUFBQUEsT0FBQTtNQUNQL0IsT0FBTyxFQUFBUSxRQUFBLENBQUFBLFFBQUEsS0FBTyxJQUFJLENBQUNSLE9BQU8sR0FBS0EsT0FBTztLQUN2QyxDQUFDO0VBQ0osQ0FBQztFQUVEOzs7RUFBQTtFQUFBeEMsY0FBQSxHQUFBb0IsQ0FBQTtFQUdBYSxNQUFBLENBQUFhLFNBQUEsQ0FBQUgsSUFBSSxHQUFKLFVBQUs0QixPQUFlLEVBQUUvQixPQUE2QjtJQUFBO0lBQUF4QyxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ2pELElBQUksQ0FBQyxJQUFJLENBQUNnQyxTQUFTLENBQUMsTUFBTSxDQUFDLEVBQUU7TUFBQTtNQUFBcEQsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBO0lBQUEsQ0FBTztJQUFBO0lBQUE7TUFBQXBCLGNBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUVwQyxJQUFJLENBQUNvRCxRQUFRLENBQUM7TUFDWmQsU0FBUyxFQUFFLElBQUlDLElBQUksRUFBRSxDQUFDMkIsV0FBVyxFQUFFO01BQ25DakMsS0FBSyxFQUFFLE1BQU07TUFDYmtCLE9BQU8sRUFBQUEsT0FBQTtNQUNQL0IsT0FBTyxFQUFBUSxRQUFBLENBQUFBLFFBQUEsS0FBTyxJQUFJLENBQUNSLE9BQU8sR0FBS0EsT0FBTztLQUN2QyxDQUFDO0VBQ0osQ0FBQztFQUVEOzs7RUFBQTtFQUFBeEMsY0FBQSxHQUFBb0IsQ0FBQTtFQUdBYSxNQUFBLENBQUFhLFNBQUEsQ0FBQUYsSUFBSSxHQUFKLFVBQUsyQixPQUFlLEVBQUUvQixPQUE2QjtJQUFBO0lBQUF4QyxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ2pELElBQUksQ0FBQyxJQUFJLENBQUNnQyxTQUFTLENBQUMsTUFBTSxDQUFDLEVBQUU7TUFBQTtNQUFBcEQsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBO0lBQUEsQ0FBTztJQUFBO0lBQUE7TUFBQXBCLGNBQUEsR0FBQXNCLENBQUE7SUFBQTtJQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUVwQyxJQUFJLENBQUNvRCxRQUFRLENBQUM7TUFDWmQsU0FBUyxFQUFFLElBQUlDLElBQUksRUFBRSxDQUFDMkIsV0FBVyxFQUFFO01BQ25DakMsS0FBSyxFQUFFLE1BQU07TUFDYmtCLE9BQU8sRUFBQUEsT0FBQTtNQUNQL0IsT0FBTyxFQUFBUSxRQUFBLENBQUFBLFFBQUEsS0FBTyxJQUFJLENBQUNSLE9BQU8sR0FBS0EsT0FBTztLQUN2QyxDQUFDO0VBQ0osQ0FBQztFQUVEOzs7RUFBQTtFQUFBeEMsY0FBQSxHQUFBb0IsQ0FBQTtFQUdBYSxNQUFBLENBQUFhLFNBQUEsQ0FBQUQsS0FBSyxHQUFMLFVBQU0wQixPQUFlLEVBQUUxQixLQUFhLEVBQUVMLE9BQTZCO0lBQUE7SUFBQXhDLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFDakUsSUFBSSxDQUFDLElBQUksQ0FBQ2dDLFNBQVMsQ0FBQyxPQUFPLENBQUMsRUFBRTtNQUFBO01BQUFwRCxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQUE7SUFBQSxDQUFPO0lBQUE7SUFBQTtNQUFBcEIsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQUF0QixjQUFBLEdBQUFvQixDQUFBO0lBRXJDLElBQUksQ0FBQ29ELFFBQVEsQ0FBQztNQUNaZCxTQUFTLEVBQUUsSUFBSUMsSUFBSSxFQUFFLENBQUMyQixXQUFXLEVBQUU7TUFDbkNqQyxLQUFLLEVBQUUsT0FBTztNQUNka0IsT0FBTyxFQUFBQSxPQUFBO01BQ1AvQixPQUFPLEVBQUFRLFFBQUEsQ0FBQUEsUUFBQSxLQUFPLElBQUksQ0FBQ1IsT0FBTyxHQUFLQSxPQUFPLENBQUU7TUFDeENLLEtBQUssRUFBRUEsS0FBSztNQUFBO01BQUEsQ0FBQTdDLGNBQUEsR0FBQXNCLENBQUEsV0FBRztRQUNiVCxJQUFJLEVBQUVnQyxLQUFLLENBQUNoQyxJQUFJO1FBQ2hCMEQsT0FBTyxFQUFFMUIsS0FBSyxDQUFDMEIsT0FBTztRQUN0Qk0sS0FBSyxFQUFFaEMsS0FBSyxDQUFDZ0M7T0FDZDtNQUFBO01BQUEsQ0FBQTdFLGNBQUEsR0FBQXNCLENBQUEsV0FBR0gsU0FBUztLQUNkLENBQUM7RUFDSixDQUFDO0VBRUQ7OztFQUFBO0VBQUFuQixjQUFBLEdBQUFvQixDQUFBO0VBR0FhLE1BQUEsQ0FBQWEsU0FBQSxDQUFBeUMsR0FBRyxHQUFILFVBQUlDLE1BQWMsRUFBRUMsR0FBVyxFQUFFQyxVQUFrQixFQUFFQyxRQUFnQixFQUFFbkQsT0FBNkI7SUFBQTtJQUFBeEMsY0FBQSxHQUFBcUIsQ0FBQTtJQUNsRyxJQUFNZ0MsS0FBSztJQUFBO0lBQUEsQ0FBQXJELGNBQUEsR0FBQW9CLENBQUEsUUFBYXNFLFVBQVUsSUFBSSxHQUFHO0lBQUE7SUFBQSxDQUFBMUYsY0FBQSxHQUFBc0IsQ0FBQSxXQUFHLE9BQU87SUFBQTtJQUFBLENBQUF0QixjQUFBLEdBQUFzQixDQUFBLFdBQUdvRSxVQUFVLElBQUksR0FBRztJQUFBO0lBQUEsQ0FBQTFGLGNBQUEsR0FBQXNCLENBQUEsV0FBRyxNQUFNO0lBQUE7SUFBQSxDQUFBdEIsY0FBQSxHQUFBc0IsQ0FBQSxXQUFHLE1BQU07SUFBQztJQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUUxRixJQUFJLENBQUNvRCxRQUFRLENBQUM7TUFDWmQsU0FBUyxFQUFFLElBQUlDLElBQUksRUFBRSxDQUFDMkIsV0FBVyxFQUFFO01BQ25DakMsS0FBSyxFQUFBQSxLQUFBO01BQ0xrQixPQUFPLEVBQUUsR0FBQVIsTUFBQSxDQUFHeUIsTUFBTSxPQUFBekIsTUFBQSxDQUFJMEIsR0FBRyxPQUFBMUIsTUFBQSxDQUFJMkIsVUFBVSxPQUFBM0IsTUFBQSxDQUFJNEIsUUFBUSxPQUFJO01BQ3ZEbkQsT0FBTyxFQUFBUSxRQUFBLENBQUFBLFFBQUEsQ0FBQUEsUUFBQSxLQUNGLElBQUksQ0FBQ1IsT0FBTyxHQUNaQSxPQUFPO1FBQ1ZvRCxRQUFRLEVBQUU7VUFDUkosTUFBTSxFQUFBQSxNQUFBO1VBQ05DLEdBQUcsRUFBQUEsR0FBQTtVQUNIQyxVQUFVLEVBQUVBLFVBQVUsQ0FBQ0csUUFBUSxFQUFFO1VBQ2pDRixRQUFRLEVBQUVBLFFBQVEsQ0FBQ0UsUUFBUTs7TUFDNUI7S0FFSixDQUFDO0VBQ0osQ0FBQztFQUVEOzs7RUFBQTtFQUFBN0YsY0FBQSxHQUFBb0IsQ0FBQTtFQUdBYSxNQUFBLENBQUFhLFNBQUEsQ0FBQWdELFFBQVEsR0FBUixVQUFTQyxTQUFpQixFQUFFQyxLQUFhLEVBQUVMLFFBQWdCLEVBQUVuRCxPQUE2QjtJQUFBO0lBQUF4QyxjQUFBLEdBQUFxQixDQUFBO0lBQUFyQixjQUFBLEdBQUFvQixDQUFBO0lBQ3hGLElBQUksQ0FBQ3NCLEtBQUssQ0FBQyxNQUFBcUIsTUFBQSxDQUFNZ0MsU0FBUyxPQUFBaEMsTUFBQSxDQUFJaUMsS0FBSyxPQUFBakMsTUFBQSxDQUFJNEIsUUFBUSxPQUFJLEVBQUEzQyxRQUFBLENBQUFBLFFBQUEsS0FDOUNSLE9BQU87TUFDVm9ELFFBQVEsRUFBRTtRQUNSRyxTQUFTLEVBQUFBLFNBQUE7UUFDVEMsS0FBSyxFQUFBQSxLQUFBO1FBQ0xMLFFBQVEsRUFBRUEsUUFBUSxDQUFDRSxRQUFROztJQUM1QixHQUNEO0VBQ0osQ0FBQztFQUVEOzs7RUFBQTtFQUFBN0YsY0FBQSxHQUFBb0IsQ0FBQTtFQUdBYSxNQUFBLENBQUFhLFNBQUEsQ0FBQW1ELElBQUksR0FBSixVQUFLQyxLQUFhLEVBQUVDLE1BQWUsRUFBRUMsT0FBdUIsRUFBRTVELE9BQTZCO0lBQUE7SUFBQXhDLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFBdEQsSUFBQWdGLE9BQUE7TUFBQTtNQUFBcEcsY0FBQSxHQUFBc0IsQ0FBQTtNQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtNQUFBZ0YsT0FBQSxPQUF1QjtJQUFBO0lBQUE7SUFBQTtNQUFBcEcsY0FBQSxHQUFBc0IsQ0FBQTtJQUFBO0lBQzFELElBQU0rQixLQUFLO0lBQUE7SUFBQSxDQUFBckQsY0FBQSxHQUFBb0IsQ0FBQSxRQUFhZ0YsT0FBTztJQUFBO0lBQUEsQ0FBQXBHLGNBQUEsR0FBQXNCLENBQUEsV0FBRyxNQUFNO0lBQUE7SUFBQSxDQUFBdEIsY0FBQSxHQUFBc0IsQ0FBQSxXQUFHLE1BQU07SUFBQztJQUFBdEIsY0FBQSxHQUFBb0IsQ0FBQTtJQUVsRCxJQUFJLENBQUNvRCxRQUFRLENBQUM7TUFDWmQsU0FBUyxFQUFFLElBQUlDLElBQUksRUFBRSxDQUFDMkIsV0FBVyxFQUFFO01BQ25DakMsS0FBSyxFQUFBQSxLQUFBO01BQ0xrQixPQUFPLEVBQUUsUUFBQVIsTUFBQSxDQUFRbUMsS0FBSyxPQUFBbkMsTUFBQSxDQUFJcUMsT0FBTztNQUFBO01BQUEsQ0FBQXBHLGNBQUEsR0FBQXNCLENBQUEsV0FBRyxTQUFTO01BQUE7TUFBQSxDQUFBdEIsY0FBQSxHQUFBc0IsQ0FBQSxXQUFHLFFBQVEsR0FBQXlDLE1BQUEsQ0FBR29DLE1BQU07TUFBQTtNQUFBLENBQUFuRyxjQUFBLEdBQUFzQixDQUFBLFdBQUcsYUFBQXlDLE1BQUEsQ0FBYW9DLE1BQU0sQ0FBRTtNQUFBO01BQUEsQ0FBQW5HLGNBQUEsR0FBQXNCLENBQUEsV0FBRyxFQUFFLEVBQUU7TUFDaEdrQixPQUFPLEVBQUFRLFFBQUEsQ0FBQUEsUUFBQSxDQUFBQSxRQUFBLEtBQ0YsSUFBSSxDQUFDUixPQUFPLEdBQ1pBLE9BQU87UUFDVjJELE1BQU0sRUFBQUEsTUFBQTtRQUNOUCxRQUFRLEVBQUU7VUFDUk0sS0FBSyxFQUFBQSxLQUFBO1VBQ0xFLE9BQU8sRUFBRUEsT0FBTyxDQUFDUCxRQUFROztNQUMxQjtLQUVKLENBQUM7RUFDSixDQUFDO0VBRUQ7OztFQUFBO0VBQUE3RixjQUFBLEdBQUFvQixDQUFBO0VBR0FhLE1BQUEsQ0FBQWEsU0FBQSxDQUFBdUQsV0FBVyxHQUFYLFVBQVlDLE1BQWMsRUFBRUMsS0FBYSxFQUFFQyxJQUFtQixFQUFFaEUsT0FBNkI7SUFBQTtJQUFBeEMsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtJQUFsRCxJQUFBb0YsSUFBQTtNQUFBO01BQUF4RyxjQUFBLEdBQUFzQixDQUFBO01BQUF0QixjQUFBLEdBQUFvQixDQUFBO01BQUFvRixJQUFBLE9BQW1CO0lBQUE7SUFBQTtJQUFBO01BQUF4RyxjQUFBLEdBQUFzQixDQUFBO0lBQUE7SUFBQXRCLGNBQUEsR0FBQW9CLENBQUE7SUFDNUQsSUFBSSxDQUFDdUIsSUFBSSxDQUFDLGVBQUFvQixNQUFBLENBQWV1QyxNQUFNLFFBQUF2QyxNQUFBLENBQUt3QyxLQUFLLEVBQUF4QyxNQUFBLENBQUd5QyxJQUFJLENBQUUsRUFBQXhELFFBQUEsQ0FBQUEsUUFBQSxLQUM3Q1IsT0FBTztNQUNWb0QsUUFBUSxFQUFFO1FBQ1JVLE1BQU0sRUFBQUEsTUFBQTtRQUNOQyxLQUFLLEVBQUVBLEtBQUssQ0FBQ1YsUUFBUSxFQUFFO1FBQ3ZCVyxJQUFJLEVBQUFBOztJQUNMLEdBQ0Q7RUFDSixDQUFDO0VBQUE7RUFBQXhHLGNBQUEsR0FBQW9CLENBQUE7RUFDSCxPQUFBYSxNQUFDO0FBQUQsQ0FBQyxDQTVQRDtBQTRQQztBQUFBakMsY0FBQSxHQUFBb0IsQ0FBQTtBQXFCZ0JxRixPQUFBLENBQUF4RSxNQUFBLEdBQUFBLE1BQUE7QUFuQmpCO0FBQ0EsSUFBTXlFLE1BQU07QUFBQTtBQUFBLENBQUExRyxjQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSWEsTUFBTSxFQUFFO0FBQUM7QUFBQWpDLGNBQUEsR0FBQW9CLENBQUE7QUFrQm5CcUYsT0FBQSxDQUFBQyxNQUFBLEdBQUFBLE1BQUE7QUFoQlQ7QUFBQTtBQUFBMUcsY0FBQSxHQUFBb0IsQ0FBQTtBQUNhcUYsT0FBQSxDQUFBRSxHQUFHLEdBQUc7RUFDakJqRSxLQUFLLEVBQUUsU0FBQUEsQ0FBQzZCLE9BQWUsRUFBRS9CLE9BQTZCO0lBQUE7SUFBQXhDLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFBSyxPQUFBc0YsTUFBTSxDQUFDaEUsS0FBSyxDQUFDNkIsT0FBTyxFQUFFL0IsT0FBTyxDQUFDO0VBQTlCLENBQThCO0VBQ3pGRyxJQUFJLEVBQUUsU0FBQUEsQ0FBQzRCLE9BQWUsRUFBRS9CLE9BQTZCO0lBQUE7SUFBQXhDLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFBSyxPQUFBc0YsTUFBTSxDQUFDL0QsSUFBSSxDQUFDNEIsT0FBTyxFQUFFL0IsT0FBTyxDQUFDO0VBQTdCLENBQTZCO0VBQ3ZGSSxJQUFJLEVBQUUsU0FBQUEsQ0FBQzJCLE9BQWUsRUFBRS9CLE9BQTZCO0lBQUE7SUFBQXhDLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFBSyxPQUFBc0YsTUFBTSxDQUFDOUQsSUFBSSxDQUFDMkIsT0FBTyxFQUFFL0IsT0FBTyxDQUFDO0VBQTdCLENBQTZCO0VBQ3ZGSyxLQUFLLEVBQUUsU0FBQUEsQ0FBQzBCLE9BQWUsRUFBRTFCLEtBQWEsRUFBRUwsT0FBNkI7SUFBQTtJQUFBeEMsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtJQUFLLE9BQUFzRixNQUFNLENBQUM3RCxLQUFLLENBQUMwQixPQUFPLEVBQUUxQixLQUFLLEVBQUVMLE9BQU8sQ0FBQztFQUFyQyxDQUFxQztFQUMvRytDLEdBQUcsRUFBRSxTQUFBQSxDQUFDQyxNQUFjLEVBQUVDLEdBQVcsRUFBRUMsVUFBa0IsRUFBRUMsUUFBZ0IsRUFBRW5ELE9BQTZCO0lBQUE7SUFBQXhDLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFDcEcsT0FBQXNGLE1BQU0sQ0FBQ25CLEdBQUcsQ0FBQ0MsTUFBTSxFQUFFQyxHQUFHLEVBQUVDLFVBQVUsRUFBRUMsUUFBUSxFQUFFbkQsT0FBTyxDQUFDO0VBQXRELENBQXNEO0VBQ3hEc0QsUUFBUSxFQUFFLFNBQUFBLENBQUNDLFNBQWlCLEVBQUVDLEtBQWEsRUFBRUwsUUFBZ0IsRUFBRW5ELE9BQTZCO0lBQUE7SUFBQXhDLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFDMUYsT0FBQXNGLE1BQU0sQ0FBQ1osUUFBUSxDQUFDQyxTQUFTLEVBQUVDLEtBQUssRUFBRUwsUUFBUSxFQUFFbkQsT0FBTyxDQUFDO0VBQXBELENBQW9EO0VBQ3REeUQsSUFBSSxFQUFFLFNBQUFBLENBQUNDLEtBQWEsRUFBRUMsTUFBZSxFQUFFQyxPQUFpQixFQUFFNUQsT0FBNkI7SUFBQTtJQUFBeEMsY0FBQSxHQUFBcUIsQ0FBQTtJQUFBckIsY0FBQSxHQUFBb0IsQ0FBQTtJQUNyRixPQUFBc0YsTUFBTSxDQUFDVCxJQUFJLENBQUNDLEtBQUssRUFBRUMsTUFBTSxFQUFFQyxPQUFPLEVBQUU1RCxPQUFPLENBQUM7RUFBNUMsQ0FBNEM7RUFDOUM2RCxXQUFXLEVBQUUsU0FBQUEsQ0FBQ0MsTUFBYyxFQUFFQyxLQUFhLEVBQUVDLElBQWEsRUFBRWhFLE9BQTZCO0lBQUE7SUFBQXhDLGNBQUEsR0FBQXFCLENBQUE7SUFBQXJCLGNBQUEsR0FBQW9CLENBQUE7SUFDdkYsT0FBQXNGLE1BQU0sQ0FBQ0wsV0FBVyxDQUFDQyxNQUFNLEVBQUVDLEtBQUssRUFBRUMsSUFBSSxFQUFFaEUsT0FBTyxDQUFDO0VBQWhEO0NBQ0giLCJpZ25vcmVMaXN0IjpbXX0=